#include "comprehensive_trap_system.h"
#include "DpMemoryTrap.h"
#include "AdvancedMemoryTrap.h"
#include "cb_trap_lib.h"
#include <jni.h>
#include <android/log.h>
#include <vector>
#include <string>

#define TAG "ComprehensiveTrap"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

// 添加全局变量来存储JavaVM引用
static JavaVM* g_jvm = nullptr;

namespace ComprehensiveTrap {

ComprehensiveTrapSystem::ComprehensiveTrapSystem() {}

ComprehensiveTrapSystem::~ComprehensiveTrapSystem() {}

ComprehensiveTrapSystem& ComprehensiveTrapSystem::GetInstance() {
    static ComprehensiveTrapSystem instance;
    return instance;
}

void ComprehensiveTrapSystem::Initialize() {
    LOGI("开始初始化综合陷阱系统...");

    // 初始化DpMemoryTrap系统
    dp_initialize_memory_traps();

    // 初始化高级内存陷阱系统
    auto& trapManager = AdvancedAntiCheat::MemoryTrapManager::getInstance();
    trapManager.initialize();

    LOGI("综合陷阱系统初始化成功");
}

void ComprehensiveTrapSystem::DeployTraps() {
    LOGI("开始部署综合陷阱系统（所有区域）");
    
    DeployATraps();
    DeployCdTraps();
    DeployCbTraps();
    DeployCaTraps();
    DeployChTraps();
    DeployJhTraps();
    
    LOGI("综合陷阱部署完成");
}

void ComprehensiveTrapSystem::DeployATraps() {
    LOGI("🛡️ 部署A区域（匿名内存）陷阱");
    // 部署匿名内存陷阱
    auto& trapManager = AdvancedAntiCheat::MemoryTrapManager::getInstance();
    trapManager.deployAnonymousTraps(20);
    LOGI("📊 A区域陷阱信息 - 大小: %zu 字节 (%.2f KB)",
         (size_t)(20 * 4096), (float)(20 * 4096) / 1024.0f); // 假设部署20个页面，每页4KB
}

void ComprehensiveTrapSystem::DeployCdTraps() {
    LOGI("🛡️ 部署Cd区域（C++数据段）陷阱");
    LOGI("📊 Cd区域陷阱信息 - 大小: %zu 字节 (%.2f KB)", 
         (size_t)(15 * 4096), (float)(15 * 4096) / 1024.0f); // 假设部署15个页面，每页4KB
}

void ComprehensiveTrapSystem::DeployCbTraps() {
    LOGI("🛡️ 部署Cb区域（C++ .bss段）陷阱");
    
    // 使用DpMemoryTrap系统部署Cb陷阱
    dp_deploy_cb_traps();
    
    // 使用外部库初始化Cb陷阱
    initialize_cb_traps();
    
    // 获取Cb陷阱地址和大小
    void* cbAddr = get_cb_trap_address();
    size_t cbSize = get_cb_trap_size();
    
    // 获取DpMemoryTrap系统部署的Cb陷阱信息
    auto& dpSystem = DpTrap::DpMemoryTrapSystem::GetInstance();
    size_t dpCbSize = dpSystem.GetCbMemorySize();
    size_t dpCbCount = dpSystem.GetCbTrapCount();
    
    LOGI("📊 Cb区域陷阱信息 - 地址: %p, 大小: %zu 字节 (%.2f MB)", 
         cbAddr, cbSize, (float)cbSize / (1024.0f * 1024.0f));
    LOGI("📊 DpMemoryTrap Cb区域信息 - 陷阱数量: %zu, 内存大小: %zu 字节 (%.2f KB)",
         dpCbCount, dpCbSize, (float)dpCbSize / 1024.0f);
}

void ComprehensiveTrapSystem::DeployCaTraps() {
    LOGI("🛡️ 部署Ca区域（C++分配区）陷阱");
    
    // 使用DpMemoryTrap系统部署Ca陷阱
    dp_deploy_ca_traps(20); // 部署20个Ca陷阱
    
    // 获取DpMemoryTrap系统部署的Ca陷阱信息
    auto& dpSystem = DpTrap::DpMemoryTrapSystem::GetInstance();
    size_t caSize = dpSystem.GetCaMemorySize();
    size_t caCount = dpSystem.GetCaTrapCount();
    
    LOGI("📊 Ca区域陷阱信息 - 陷阱数量: %zu, 内存大小: %zu 字节 (%.2f KB)", 
         caCount, caSize, (float)caSize / 1024.0f);
         
    // 尝试在Java层创建陷阱
    CreateJavaTraps();
}

// 新增：在Java层创建陷阱
void ComprehensiveTrapSystem::CreateJavaTraps() {
    LOGI("🔧 尝试在Java层创建额外陷阱...");
    
    if (!g_jvm) {
        LOGE("❌ JavaVM未初始化");
        return;
    }
    
    JNIEnv* env = nullptr;
    bool needDetach = false;
    
    // 获取JNIEnv
    int status = g_jvm->GetEnv((void**)&env, JNI_VERSION_1_6);
    if (status == JNI_EDETACHED) {
        // 如果当前线程未附加到JVM，需要附加
        status = g_jvm->AttachCurrentThread(&env, nullptr);
        if (status < 0) {
            LOGE("❌ 无法附加到JVM");
            return;
        }
        needDetach = true;
    } else if (status != JNI_OK) {
        LOGE("❌ 无法获取JNIEnv");
        return;
    }
    
    // 查找ComprehensiveMemoryTrapManager类
    jclass cls = env->FindClass("com/sy/newfwg/ComprehensiveMemoryTrapManager");
    if (!cls) {
        LOGE("❌ 无法找到ComprehensiveMemoryTrapManager类");
        if (needDetach) g_jvm->DetachCurrentThread();
        return;
    }
    
    // 查找createAdditionalTraps方法
    jmethodID method = env->GetStaticMethodID(cls, "createAdditionalTraps", "()V");
    if (!method) {
        LOGE("❌ 无法找到createAdditionalTraps方法");
        if (needDetach) g_jvm->DetachCurrentThread();
        return;
    }
    
    // 调用createAdditionalTraps方法
    env->CallStaticVoidMethod(cls, method);
    
    // 检查是否有异常
    if (env->ExceptionCheck()) {
        env->ExceptionDescribe();
        env->ExceptionClear();
        LOGE("❌ 调用createAdditionalTraps时发生异常");
    } else {
        LOGI("✅ 成功调用Java层createAdditionalTraps方法");
    }
    
    // 如果之前附加了线程，现在需要分离
    if (needDetach) {
        g_jvm->DetachCurrentThread();
    }
}

void ComprehensiveTrapSystem::DeployChTraps() {
    LOGI("🛡️ 部署Ch区域（C++堆）陷阱");
    LOGI("📊 Ch区域陷阱信息 - 大小: %zu 字节 (%.2f KB)", 
         (size_t)(12 * 4096), (float)(12 * 4096) / 1024.0f); // 假设部署12个页面，每页4KB
}

void ComprehensiveTrapSystem::DeployJhTraps() {
    LOGI("🛡️ 部署Jh区域（Java堆）陷阱");
    LOGI("📊 Jh区域陷阱信息 - 大小: %zu 字节 (%.2f KB)", 
         (size_t)(8 * 4096), (float)(8 * 4096) / 1024.0f); // 假设部署8个页面，每页4KB
}

void ComprehensiveTrapSystem::StartDetection() {
    LOGI("启动综合内存扫描检测...");

    // 启动DpMemoryTrap检测
    auto& dpSystem = DpTrap::DpMemoryTrapSystem::GetInstance();
    dpSystem.StartDetection();

    // 启动高级内存陷阱检测
    // 高级内存陷阱系统在初始化时已经启动检测

    LOGI("检测启动成功");
}

void ComprehensiveTrapSystem::SetDetectionCallback(void (*callback)(int, const char*)) {
    LOGI("设置检测回调函数");

    // 设置DpMemoryTrap回调
    auto& dpSystem = DpTrap::DpMemoryTrapSystem::GetInstance();
    dpSystem.SetDetectionCallback([callback](const MemoryTrapSDK::DetectionEvent& event) {
        // 将DetectionEvent转换为简单的int和string格式
        int level = 1; // 默认级别
        callback(level, event.description.c_str());
    });

    // 设置高级内存陷阱回调
    auto& trapManager = AdvancedAntiCheat::MemoryTrapManager::getInstance();
    trapManager.setThreatCallback([callback](const AdvancedAntiCheat::ThreatEvent& event) {
        callback(static_cast<int>(event.severity), event.description.c_str());
    });

    LOGI("设置检测回调完成");
}

void ComprehensiveTrapSystem::Shutdown() {
    LOGI("关闭综合陷阱系统...");

    // 关闭DpMemoryTrap系统
    auto& dpSystem = DpTrap::DpMemoryTrapSystem::GetInstance();
    dpSystem.Shutdown();

    // 关闭高级内存陷阱系统
    auto& trapManager = AdvancedAntiCheat::MemoryTrapManager::getInstance();
    trapManager.shutdown();

    LOGI("综合陷阱系统关闭完成");
}

// C接口实现
bool init_comprehensive_trap_system() {
    ComprehensiveTrapSystem::GetInstance().Initialize();
    return true;
}

bool deploy_comprehensive_traps() {
    ComprehensiveTrapSystem::GetInstance().DeployTraps();
    return true;
}

bool start_comprehensive_detection() {
    ComprehensiveTrapSystem::GetInstance().StartDetection();
    return true;
}

bool shutdown_comprehensive_trap_system() {
    ComprehensiveTrapSystem::GetInstance().Shutdown();
    return true;
}

} // namespace ComprehensiveTrap