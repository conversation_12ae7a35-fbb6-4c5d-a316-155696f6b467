#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=44176, tid=37196
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-2961a0b7a478d13d52172d246e5e53fd-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:16:54 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 2.725152 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x000002507a65f8f0):  JavaThread "Start Level: Equinox Container: d9a3308d-a603-492d-b2a5-5d94efd9e891" daemon [_thread_in_vm, id=37196, stack(0x0000009f88f00000,0x0000009f89000000) (1024K)]

Stack: [0x0000009f88f00000,0x0000009f89000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0x8a41ee]
V  [jvm.dll+0x670575]
V  [jvm.dll+0x1e474c]
V  [jvm.dll+0x1e451e]
V  [jvm.dll+0x672e72]
V  [jvm.dll+0x672c92]
V  [jvm.dll+0x670f4e]
V  [jvm.dll+0x2695bf]
V  [jvm.dll+0x67b1b5]
V  [jvm.dll+0x21563d]
V  [jvm.dll+0x215bba]
V  [jvm.dll+0x2165e5]
V  [jvm.dll+0x20bbae]
V  [jvm.dll+0x5ae58c]
V  [jvm.dll+0x821706]
V  [jvm.dll+0x4718d6]
V  [jvm.dll+0x4777a8]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 1131  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.7 (0 bytes) @ 0x0000025007b48591 [0x0000025007b484c0+0x00000000000000d1]
J 1789 c1 java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class; java.base@21.0.7 (43 bytes) @ 0x0000025000317bfc [0x00000250003178a0+0x000000000000035c]
J 2442 c1 org.eclipse.osgi.internal.loader.ModuleClassLoader.defineClass(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult; (67 bytes) @ 0x000002500048d33c [0x000002500048d020+0x000000000000031c]
J 2347 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.defineClass(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;Lorg/eclipse/osgi/storage/bundlefile/BundleEntry;Ljava/util/List;)Ljava/lang/Class; (636 bytes) @ 0x0000025000457764 [0x0000025000455cc0+0x0000000000001aa4]
J 2149 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findClassImpl(Ljava/lang/String;Lorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;Ljava/util/List;)Ljava/lang/Class; (343 bytes) @ 0x00000250003d1d54 [0x00000250003ce7a0+0x00000000000035b4]
J 1966 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(Ljava/lang/String;[Lorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;Ljava/util/List;)Ljava/lang/Class; (55 bytes) @ 0x000002500037e764 [0x000002500037e5e0+0x0000000000000184]
J 2148 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClassImpl(Ljava/lang/String;Ljava/util/List;)Ljava/lang/Class; (132 bytes) @ 0x00000250003cd714 [0x00000250003cd1e0+0x0000000000000534]
J 1961 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(Ljava/lang/String;)Ljava/lang/Class; (210 bytes) @ 0x000002500037a0a4 [0x00000250003793e0+0x0000000000000cc4]
J 1959 c1 org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(Ljava/lang/String;)Ljava/lang/Class; (166 bytes) @ 0x0000025000376dec [0x0000025000376240+0x0000000000000bac]
J 1869 c1 org.eclipse.osgi.internal.loader.BundleLoader.findClass0(Ljava/lang/String;ZZ)Ljava/lang/Class; (491 bytes) @ 0x000002500033fd54 [0x000002500033cda0+0x0000000000002fb4]
J 1868 c1 org.eclipse.osgi.internal.loader.BundleLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; (8 bytes) @ 0x000002500033c3cc [0x000002500033c340+0x000000000000008c]
J 1906 c1 org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; (36 bytes) @ 0x0000025000351ccc [0x0000025000351aa0+0x000000000000022c]
J 1023 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.7 (7 bytes) @ 0x0000025000194554 [0x0000025000194440+0x0000000000000114]
v  ~StubRoutines::call_stub 0x000002500753100d
j  org.eclipse.jdt.internal.core.JavaModelManager$8.optionsChanged(Lorg/eclipse/osgi/service/debug/DebugOptions;)V+140
j  org.eclipse.osgi.internal.debug.FrameworkDebugOptions.addingService(Lorg/osgi/framework/ServiceReference;)Lorg/eclipse/osgi/service/debug/DebugOptionsListener;+16
j  org.eclipse.osgi.internal.debug.FrameworkDebugOptions.addingService(Lorg/osgi/framework/ServiceReference;)Ljava/lang/Object;+5
j  org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(Lorg/osgi/framework/ServiceReference;Lorg/osgi/framework/ServiceEvent;)Ljava/lang/Object;+8
j  org.osgi.util.tracker.ServiceTracker$Tracked.customizerAdding(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+9
j  org.osgi.util.tracker.AbstractTracked.trackAdding(Ljava/lang/Object;Ljava/lang/Object;)V+8
j  org.osgi.util.tracker.AbstractTracked.track(Ljava/lang/Object;Ljava/lang/Object;)V+83
j  org.osgi.util.tracker.ServiceTracker$Tracked.serviceChanged(Lorg/osgi/framework/ServiceEvent;)V+67
J 1838 c1 org.eclipse.osgi.internal.serviceregistry.FilteredServiceListener.serviceChanged(Lorg/osgi/framework/ServiceEvent;)V (296 bytes) @ 0x00000250003320b4 [0x000002500032f7a0+0x0000000000002914]
J 1951 c1 org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V (577 bytes) @ 0x000002500036c7d4 [0x0000025000368f00+0x00000000000038d4]
J 2096 c1 org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(Ljava/util/Set;Lorg/eclipse/osgi/framework/eventmgr/EventDispatcher;ILjava/lang/Object;)V (69 bytes) @ 0x00000250003bc454 [0x00000250003bbf40+0x0000000000000514]
j  org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ILjava/lang/Object;)V+67
j  org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEventPrivileged(Lorg/osgi/framework/ServiceEvent;)V+320
j  org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.publishServiceEvent(Lorg/osgi/framework/ServiceEvent;)V+8
j  org.eclipse.osgi.internal.serviceregistry.ServiceRegistrationImpl.register(Ljava/util/Dictionary;)V+137
j  org.eclipse.osgi.internal.serviceregistry.ServiceRegistry.registerService(Lorg/eclipse/osgi/internal/framework/BundleContextImpl;[Ljava/lang/String;Ljava/lang/Object;Ljava/util/Dictionary;)Lorg/eclipse/osgi/internal/serviceregistry/ServiceRegistrationImpl;+317
j  org.eclipse.osgi.internal.framework.BundleContextImpl.registerService([Ljava/lang/String;Ljava/lang/Object;Ljava/util/Dictionary;)Lorg/osgi/framework/ServiceRegistration;+15
j  org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(Ljava/lang/String;Ljava/lang/Object;Ljava/util/Dictionary;)Lorg/osgi/framework/ServiceRegistration;+15
j  org.eclipse.osgi.internal.framework.BundleContextImpl.registerService(Ljava/lang/Class;Ljava/lang/Object;Ljava/util/Dictionary;)Lorg/osgi/framework/ServiceRegistration;+7
j  org.eclipse.jdt.internal.core.JavaModelManager.registerDebugOptionsListener(Lorg/osgi/framework/BundleContext;)V+32
j  org.eclipse.jdt.core.JavaCore.start(Lorg/osgi/framework/BundleContext;)V+6
j  org.eclipse.osgi.internal.framework.BundleContextImpl$2.run()Ljava/lang/Void;+23
j  org.eclipse.osgi.internal.framework.BundleContextImpl$2.run()Ljava/lang/Object;+1
J 1166 c1 java.security.AccessController.executePrivileged(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object; java.base@21.0.7 (65 bytes) @ 0x00000250001d6dc4 [0x00000250001d6c60+0x0000000000000164]
j  java.security.AccessController.doPrivileged(Ljava/security/PrivilegedExceptionAction;)Ljava/lang/Object;+9 java.base@21.0.7
j  org.eclipse.osgi.internal.framework.BundleContextImpl.startActivator(Lorg/osgi/framework/BundleActivator;)V+9
j  org.eclipse.osgi.internal.framework.BundleContextImpl.start()V+141
j  org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0()V+35
j  org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker()V+4
j  org.eclipse.osgi.container.Module.doStart([Lorg/eclipse/osgi/container/Module$StartOptions;)Lorg/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent;+200
J 2365 c1 org.eclipse.osgi.container.Module.start([Lorg/eclipse/osgi/container/Module$StartOptions;)V (662 bytes) @ 0x000002500046143c [0x000002500045f340+0x00000000000020fc]
j  org.eclipse.osgi.framework.util.SecureAction.start(Lorg/eclipse/osgi/container/Module;[Lorg/eclipse/osgi/container/Module$StartOptions;)V+8
J 2083 c1 org.eclipse.osgi.internal.hooks.EclipseLazyStarter.postFindLocalClass(Ljava/lang/String;Ljava/lang/Class;Lorg/eclipse/osgi/internal/loader/classpath/ClasspathManager;)V (394 bytes) @ 0x00000250003b2414 [0x00000250003b1440+0x0000000000000fd4]
J 1961 c1 org.eclipse.osgi.internal.loader.classpath.ClasspathManager.findLocalClass(Ljava/lang/String;)Ljava/lang/Class; (210 bytes) @ 0x000002500037a3f4 [0x00000250003793e0+0x0000000000001014]
J 1959 c1 org.eclipse.osgi.internal.loader.BundleLoader.findLocalClass(Ljava/lang/String;)Ljava/lang/Class; (166 bytes) @ 0x0000025000376dec [0x0000025000376240+0x0000000000000bac]
J 2147 c1 org.eclipse.osgi.internal.loader.sources.SingleSourcePackage.loadClass(Ljava/lang/String;)Ljava/lang/Class; (9 bytes) @ 0x00000250003ccdcc [0x00000250003cccc0+0x000000000000010c]
J 1869 c1 org.eclipse.osgi.internal.loader.BundleLoader.findClass0(Ljava/lang/String;ZZ)Ljava/lang/Class; (491 bytes) @ 0x000002500033fc5c [0x000002500033cda0+0x0000000000002ebc]
J 1868 c1 org.eclipse.osgi.internal.loader.BundleLoader.findClass(Ljava/lang/String;)Ljava/lang/Class; (8 bytes) @ 0x000002500033c3cc [0x000002500033c340+0x000000000000008c]
J 1906 c1 org.eclipse.osgi.internal.loader.ModuleClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; (36 bytes) @ 0x0000025000351ccc [0x0000025000351aa0+0x000000000000022c]
J 1023 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.7 (7 bytes) @ 0x0000025000194554 [0x0000025000194440+0x0000000000000114]
v  ~StubRoutines::call_stub 0x000002500753100d
j  java.lang.Class.getDeclaredConstructors0(Z)[Ljava/lang/reflect/Constructor;+0 java.base@21.0.7
j  java.lang.Class.privateGetDeclaredConstructors(Z)[Ljava/lang/reflect/Constructor;+52 java.base@21.0.7
j  java.lang.Class.getConstructor0([Ljava/lang/Class;I)Ljava/lang/reflect/Constructor;+14 java.base@21.0.7
j  java.lang.Class.getConstructor([Ljava/lang/Class;)Ljava/lang/reflect/Constructor;+24 java.base@21.0.7
j  org.eclipse.osgi.internal.framework.BundleContextImpl.loadBundleActivator()Lorg/osgi/framework/BundleActivator;+105
j  org.eclipse.osgi.internal.framework.BundleContextImpl.start()V+18
j  org.eclipse.osgi.internal.framework.EquinoxBundle.startWorker0()V+35
j  org.eclipse.osgi.internal.framework.EquinoxBundle$EquinoxModule.startWorker()V+4
j  org.eclipse.osgi.container.Module.doStart([Lorg/eclipse/osgi/container/Module$StartOptions;)Lorg/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent;+200
j  org.eclipse.osgi.container.Module.start([Lorg/eclipse/osgi/container/Module$StartOptions;)V+460
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run()V+83
j  org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(Ljava/lang/Runnable;)V+1
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ILjava/util/List;Z)V+193
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ILjava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V+19
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(Lorg/eclipse/osgi/container/Module;I[Lorg/osgi/framework/FrameworkListener;)V+358
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(Lorg/eclipse/osgi/container/Module;[Lorg/osgi/framework/FrameworkListener;ILjava/lang/Integer;)V+32
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V+15
j  org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(Ljava/util/Set;Lorg/eclipse/osgi/framework/eventmgr/EventDispatcher;ILjava/lang/Object;)V+48
j  org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run()V+26
v  ~StubRoutines::call_stub 0x000002500753100d

Compiled method (n/a) 2836 1131     n 0       java.lang.ClassLoader::defineClass1 (native)
 total in heap  [0x0000025007b48310,0x0000025007b48798] = 1160
 relocation     [0x0000025007b48470,0x0000025007b484a8] = 56
 main code      [0x0000025007b484c0,0x0000025007b48796] = 726
 stub code      [0x0000025007b48796,0x0000025007b48798] = 2

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000002500f431000} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # parm0:    rdx:rdx   = 'java/lang/ClassLoader'
  # parm1:    r8:r8     = 'java/lang/String'
  # parm2:    r9:r9     = '[B'
  # parm3:    rdi       = int
  # parm4:    rsi       = int
  # parm5:    rcx:rcx   = 'java/security/ProtectionDomain'
  # parm6:    [sp+0xa0]   = 'java/lang/String'  (sp of caller)
  0x0000025007b484c0: 448b 5208 | 49bb 0000 | 000f 5002 | 0000 4d03 | d349 3bc2 | 0f84 0600 

  0x0000025007b484d8: ;   {runtime_call ic_miss_stub}
  0x0000025007b484d8: 0000 e9a1 | 5fa3 ff90 
[Verified Entry Point]
  0x0000025007b484e0: 8984 2400 | 80ff ff55 | 488b ec48 | 81ec 9000 | 0000 6690 | 4181 7f20 | 0100 0000 

  0x0000025007b484fc: ;   {runtime_call StubRoutines (final stubs)}
  0x0000025007b484fc: 7405 e8dd | 04a2 ff48 | 837d 1000 | 488d 4510 | 480f 4445 | 1048 8944 | 2440 4889 | 4c24 7048 
  0x0000025007b4851c: 83f9 0048 | 8d44 2470 | 480f 4444 | 2470 4889 | 4424 3848 | 8974 2430 | 4889 7c24 | 284c 894c 
  0x0000025007b4853c: 2458 4983 | f900 488d | 4424 5848 | 0f44 4424 | 5848 8944 | 2420 4c89 | 4424 5049 | 83f8 004c 
  0x0000025007b4855c: 8d4c 2450 | 4c0f 444c | 2450 4889 | 5424 4848 | 83fa 004c | 8d44 2448 | 4c0f 4444 

  0x0000025007b48578: ;   {oop(a 'java/lang/Class'{0x00000000d7294d40} = 'java/lang/ClassLoader')}
  0x0000025007b48578: 2448 49be | 404d 29d7 | 0000 0000 | 4c89 7424 | 784c 8d74 | 2478 498b | d6c5 f877 

  0x0000025007b48594: ;   {internal_word}
  0x0000025007b48594: 49ba 9185 | b407 5002 | 0000 4d89 | 97a0 0300 | 0049 89a7 | 9803 0000 

  0x0000025007b485ac: ;   {external_word}
  0x0000025007b485ac: 49ba 3d5b | 44ef ff7f | 0000 4180 | 3a00 0f84 | 4e00 0000 | 5241 5041 

  0x0000025007b485c4: ;   {metadata({method} {0x000002500f431000} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000025007b485c4: 5148 baf8 | 0f43 0f50 | 0200 0049 | 8bcf 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 
  0x0000025007b485e4: ;   {runtime_call}
  0x0000025007b485e4: 48b8 504b | eeee ff7f | 0000 ffd0 | 4883 c408 | e90c 0000 

  0x0000025007b485f8: ;   {runtime_call}
  0x0000025007b485f8: 0048 b850 | 4bee eeff | 7f00 00ff | d048 83c4 | 2041 5941 | 585a 498d | 8fb8 0300 | 0041 c787 
  0x0000025007b48618: 4404 0000 | 0400 0000 

  0x0000025007b48620: ;   {runtime_call}
  0x0000025007b48620: 48b8 ac16 | 4e00 f87f | 0000 ffd0 | c5f8 7741 | c787 4404 | 0000 0500 | 0000 f083 | 4424 c000 
  0x0000025007b48640: 493b af48 | 0400 000f | 870e 0000 | 0041 83bf | 4004 0000 | 000f 842b | 0000 00c5 | f877 4889 
  0x0000025007b48660: 45f8 498b | cf4c 8be4 | 4883 ec20 | 4883 e4f0 

  0x0000025007b48670: ;   {runtime_call}
  0x0000025007b48670: 48b8 00d1 | b9ee ff7f | 0000 ffd0 | 498b e44d | 33e4 488b | 45f8 41c7 | 8744 0400 | 0008 0000 
  0x0000025007b48690: 0041 83bf | c004 0000 | 020f 84ca 

  0x0000025007b4869c: ;   {external_word}
  0x0000025007b4869c: 0000 0049 | ba3d 5b44 | efff 7f00 | 0041 803a | 000f 844c | 0000 0048 

  0x0000025007b486b4: ;   {metadata({method} {0x000002500f431000} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000025007b486b4: 8945 f848 | baf8 0f43 | 0f50 0200 | 0049 8bcf | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 
  0x0000025007b486d4: ;   {runtime_call}
  0x0000025007b486d4: ec08 48b8 | 504b eeee | ff7f 0000 | ffd0 4883 | c408 e90c 

  0x0000025007b486e8: ;   {runtime_call}
  0x0000025007b486e8: 0000 0048 | b850 4bee | eeff 7f00 | 00ff d048 | 83c4 2048 | 8b45 f849 | c787 9803 | 0000 0000 
  0x0000025007b48708: 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4885 c00f | 8425 0000 | 00a8 030f | 8508 0000 
  0x0000025007b48728: 0048 8b00 | e915 0000 | 00a8 010f | 8509 0000 | 0048 8b40 | fee9 0400 | 0000 488b | 40ff 498b 
  0x0000025007b48748: 8f28 0400 | 00c7 8100 | 0100 0000 | 0000 00c9 | 4983 7f08 | 000f 8501 | 0000 00c3 

  0x0000025007b48764: ;   {runtime_call StubRoutines (initial stubs)}
  0x0000025007b48764: e997 879e | ffc5 f877 | 4889 45f8 | 4c8b e448 | 83ec 2048 

  0x0000025007b48778: ;   {runtime_call}
  0x0000025007b48778: 83e4 f048 | b830 83ee | eeff 7f00 | 00ff d049 | 8be4 4d33 | e448 8b45 | f8e9 09ff | ffff f4f4 
[/MachCode]


Compiled method (c1) 2860 1789       3       java.lang.ClassLoader::defineClass (43 bytes)
 total in heap  [0x0000025000317690,0x0000025000317ff0] = 2400
 relocation     [0x00000250003177f0,0x0000025000317890] = 160
 main code      [0x00000250003178a0,0x0000025000317d40] = 1184
 stub code      [0x0000025000317d40,0x0000025000317db8] = 120
 metadata       [0x0000025000317db8,0x0000025000317de8] = 48
 scopes data    [0x0000025000317de8,0x0000025000317ed8] = 240
 scopes pcs     [0x0000025000317ed8,0x0000025000317fb8] = 224
 dependencies   [0x0000025000317fb8,0x0000025000317fc8] = 16
 nul chk table  [0x0000025000317fc8,0x0000025000317ff0] = 40

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000002500f431380} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # this:     rdx:rdx   = 'java/lang/ClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9:r9     = '[B'
  # parm2:    rdi       = int
  # parm3:    rsi       = int
  # parm4:    rcx:rcx   = 'java/security/ProtectionDomain'
  #           [sp+0xc0]  (sp of caller)
  0x00000250003178a0: 448b 5208 | 49bb 0000 | 000f 5002 | 0000 4d03 | d34c 3bd0 

  0x00000250003178b4: ;   {runtime_call ic_miss_stub}
  0x00000250003178b4: 0f85 c66b | 2607 660f | 1f44 0000 
[Verified Entry Point]
  0x00000250003178c0: 8984 2400 | 80ff ff55 | 4881 ecb0 | 0000 0090 | 4181 7f20 | 0100 0000 

  0x00000250003178d8: ;   {runtime_call StubRoutines (final stubs)}
  0x00000250003178d8: 7405 e801 | 1125 0748 | 8954 2478 

  0x00000250003178e4: ;   {metadata(method data for {method} {0x000002500f431380} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x00000250003178e4: 48bb 3895 | 3650 5002 | 0000 8b83 | cc00 0000 | 83c0 0289 | 83cc 0000 | 0025 fe07 | 0000 85c0 
  0x0000025000317904: 0f84 5803 | 0000 89b4 | 2494 0000 | 0089 bc24 | 9000 0000 | 4c89 8c24 | 8800 0000 

  0x0000025000317920: ;   {metadata(method data for {method} {0x000002500f431380} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000025000317920: 488b da48 | b838 9536 | 5050 0200 | 0048 8380 | 1001 0000 | 0149 8bd8 | 4c8b c34c | 8bc9 488b 
  0x0000025000317940: ca48 8bd1 | 4889 9c24 | 8000 0000 

  0x000002500031794c: ;   {optimized virtual_call}
  0x000002500031794c: 6666 90e8 

  0x0000025000317950: ; ImmutableOopMap {[120]=Oop [128]=Oop [136]=Oop }
                      ;*invokevirtual preDefineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@4
  0x0000025000317950: 4c33 f1ff 

  0x0000025000317954: ;   {other}
  0x0000025000317954: 0f1f 8400 | c402 0000 | 4889 8424 | 9800 0000 | 488b 5424 

  0x0000025000317968: ;   {metadata(method data for {method} {0x000002500f431380} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000025000317968: 7849 b838 | 9536 5050 | 0200 0049 | 8380 4801 

  0x0000025000317978: ;   {metadata(method data for {method} {0x000002500f431068} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000025000317978: 0000 0149 | b850 9c36 | 5050 0200 | 0041 8b90 | cc00 0000 | 83c2 0241 | 8990 cc00 | 0000 81e2 
  0x0000025000317998: feff 1f00 | 85d2 0f84 | df02 0000 

  0x00000250003179a4: ; implicit exception: dispatches to 0x0000025000317ca4
  0x00000250003179a4: 483b 004c 

  0x00000250003179a8: ;   {metadata(method data for {method} {0x000002500f431068} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x00000250003179a8: 8bc0 48ba | 509c 3650 | 5002 0000 | 4883 8210 | 0100 0001 | 448b 4010 

  0x00000250003179c0: ;   {metadata(method data for {method} {0x000002500f431068} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x00000250003179c0: 4d85 c048 | ba50 9c36 | 5050 0200 | 0048 c7c6 | 5801 0000 | 7507 48c7 | c648 0100 | 0048 8b3c 
  0x00000250003179e0: 3248 8d7f | 0148 893c | 320f 850f 

  0x00000250003179ec: ;   {oop(nullptr)}
  0x00000250003179ec: 0000 0048 | bb00 0000 | 0000 0000 | 00e9 a901 | 0000 493b | 0049 8bd0 

  0x0000025000317a04: ;   {metadata(method data for {method} {0x000002500f431068} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000025000317a04: 48be 509c | 3650 5002 | 0000 4883 | 8668 0100 | 0001 418b | 500c 4885 

  0x0000025000317a1c: ;   {metadata(method data for {method} {0x000002500f431068} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000025000317a1c: d248 be50 | 9c36 5050 | 0200 0048 | c7c7 b001 | 0000 7507 | 48c7 c7a0 | 0100 0048 | 8b1c 3e48 
  0x0000025000317a3c: 8d5b 0148 | 891c 3e0f | 850f 0000 

  0x0000025000317a48: ;   {oop(nullptr)}
  0x0000025000317a48: 0048 bb00 | 0000 0000 | 0000 00e9 | 4f01 0000 

  0x0000025000317a58: ;   {metadata(method data for {method} {0x000002500f431068} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000025000317a58: 48be 509c | 3650 5002 | 0000 4883 | 86c0 0100 | 0001 483b | 024c 8bc2 

  0x0000025000317a70: ;   {metadata(method data for {method} {0x000002500f431068} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000025000317a70: 48be 509c | 3650 5002 | 0000 4883 | 86f8 0100 

  0x0000025000317a80: ;   {metadata(method data for {method} {0x000002500f01a838} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000025000317a80: 0001 49b8 | 888d 3650 | 5002 0000 | 418b b0cc | 0000 0083 | c602 4189 | b0cc 0000 | 0081 e6fe 
  0x0000025000317aa0: ff1f 0085 | f60f 8408 | 0200 004c 

  0x0000025000317aac: ;   {metadata(method data for {method} {0x000002500f01a838} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000025000317aac: 8bc2 48be | 888d 3650 | 5002 0000 | 4883 8610 | 0100 0001 

  0x0000025000317ac0: ;   {metadata(method data for {method} {0x000002500f424c08} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000025000317ac0: 49b8 7826 | 3650 5002 | 0000 418b | b0cc 0000 | 0083 c602 | 4189 b0cc | 0000 0081 | e6fe ff1f 
  0x0000025000317ae0: 0085 f60f | 84eb 0100 | 008b 7238 

  0x0000025000317aec: ; implicit exception: dispatches to 0x0000025000317cf5
  0x0000025000317aec: 483b 064c 

  0x0000025000317af0: ;   {metadata(method data for {method} {0x000002500f424c08} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000025000317af0: 8bc6 48bf | 7826 3650 | 5002 0000 | 458b 4008 | 49ba 0000 | 000f 5002 | 0000 4d03 | c24c 3b87 
  0x0000025000317b10: 2001 0000 | 750d 4883 | 8728 0100 | 0001 e960 | 0000 004c | 3b87 3001 | 0000 750d | 4883 8738 
  0x0000025000317b30: 0100 0001 | e94a 0000 | 0048 83bf | 2001 0000 | 0075 174c | 8987 2001 | 0000 48c7 | 8728 0100 
  0x0000025000317b50: 0001 0000 | 00e9 2900 | 0000 4883 | bf30 0100 | 0000 7517 | 4c89 8730 | 0100 0048 | c787 3801 
  0x0000025000317b70: 0000 0100 | 0000 e908 | 0000 0048 | 8387 1001 | 0000 014c | 8bc2 488b | d60f 1f40 | 0048 b8ff 
  0x0000025000317b90: ffff ffff 

  0x0000025000317b94: ;   {virtual_call}
  0x0000025000317b94: ffff ffe8 

  0x0000025000317b98: ; ImmutableOopMap {[120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual toExternalForm {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.net.URL::toExternalForm@5
                      ; - java.net.URL::toString@1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
  0x0000025000317b98: 642c 3207 

  0x0000025000317b9c: ;   {other}
  0x0000025000317b9c: 0f1f 8400 | 0c05 0001 | 488b d848 | 8b84 2498 | 0000 008b | b424 9400 | 0000 8bbc | 2490 0000 
  0x0000025000317bbc: 004c 8b8c | 2488 0000 | 004c 8b84 | 2480 0000 | 0048 8b54 

  0x0000025000317bd0: ;   {metadata(method data for {method} {0x000002500f431380} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000025000317bd0: 2478 48b9 | 3895 3650 | 5002 0000 | 4883 8180 | 0100 0001 | 4c8b da49 | 8bd3 488b | c848 891c 
  0x0000025000317bf0: 2466 0f1f 

  0x0000025000317bf4: ;   {static_call}
  0x0000025000317bf4: 4400 00e8 

  0x0000025000317bf8: ; ImmutableOopMap {[120]=Oop [152]=Oop }
                      ;*invokestatic defineClass1 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@27
  0x0000025000317bf8: e408 8307 

  0x0000025000317bfc: ;   {other}
  0x0000025000317bfc: 0f1f 8400 | 6c05 0002 | 488b 5424 

  0x0000025000317c08: ;   {metadata(method data for {method} {0x000002500f431380} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000025000317c08: 7849 b838 | 9536 5050 | 0200 0049 | 8380 9001 | 0000 014c | 8bc0 4c8b | 8c24 9800 | 0000 488b 
  0x0000025000317c28: 5424 7848 | 8984 24a0 | 0000 000f 

  0x0000025000317c34: ;   {optimized virtual_call}
  0x0000025000317c34: 1f40 00e8 

  0x0000025000317c38: ; ImmutableOopMap {[160]=Oop }
                      ;*invokevirtual postDefineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClass@37
  0x0000025000317c38: e4a4 f9ff 

  0x0000025000317c3c: ;   {other}
  0x0000025000317c3c: 0f1f 8400 | ac05 0003 | 488b 8424 | a000 0000 | 4881 c4b0 | 0000 005d 

  0x0000025000317c54: ;   {poll_return}
  0x0000025000317c54: 493b a748 | 0400 000f | 8799 0000 

  0x0000025000317c60: ;   {metadata({method} {0x000002500f431380} 'defineClass' '(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000025000317c60: 00c3 49ba | 7813 430f | 5002 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x0000025000317c78: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000025000317c78: ffe8 02b9 

  0x0000025000317c7c: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rcx=Oop [120]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::defineClass@-1
  0x0000025000317c7c: 3107 e987 

  0x0000025000317c80: ;   {metadata({method} {0x000002500f431068} 'defineClassSourceLocation' '(Ljava/security/ProtectionDomain;)Ljava/lang/String;' in 'java/lang/ClassLoader')}
  0x0000025000317c80: fcff ff49 | ba60 1043 | 0f50 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000025000317c98: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000025000317c98: ffff e8e1 

  0x0000025000317c9c: ; ImmutableOopMap {rax=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::defineClassSourceLocation@-1
                      ; - java.lang.ClassLoader::defineClass@12
  0x0000025000317c9c: b831 07e9 | 00fd ffff 

  0x0000025000317ca4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000025000317ca4: e8d7 6731 

  0x0000025000317ca8: ; ImmutableOopMap {rax=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual getCodeSource {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@1
                      ; - java.lang.ClassLoader::defineClass@12
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000025000317ca8: 07e8 d267 

  0x0000025000317cac: ; ImmutableOopMap {rax=Oop r8=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual getLocation {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@12
                      ; - java.lang.ClassLoader::defineClass@12
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000025000317cac: 3107 e8cd 

  0x0000025000317cb0: ; ImmutableOopMap {rdx=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual toString {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
                      ;   {metadata({method} {0x000002500f01a838} 'toString' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000025000317cb0: 6731 0749 | ba30 a801 | 0f50 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000025000317cc8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000025000317cc8: ffff e8b1 

  0x0000025000317ccc: ; ImmutableOopMap {rdx=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*synchronization entry
                      ; - java.net.URL::toString@-1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
  0x0000025000317ccc: b831 07e9 | d7fd ffff 

  0x0000025000317cd4: ;   {metadata({method} {0x000002500f424c08} 'toExternalForm' '()Ljava/lang/String;' in 'java/net/URL')}
  0x0000025000317cd4: 49ba 004c | 420f 5002 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x0000025000317ce8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000025000317ce8: ffff ffe8 

  0x0000025000317cec: ; ImmutableOopMap {rdx=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*synchronization entry
                      ; - java.net.URL::toExternalForm@-1
                      ; - java.net.URL::toString@1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
  0x0000025000317cec: 90b8 3107 | e9f4 fdff 

  0x0000025000317cf4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000025000317cf4: ffe8 8667 

  0x0000025000317cf8: ; ImmutableOopMap {rdx=Oop rsi=Oop [120]=Oop [128]=Oop [136]=Oop [152]=Oop }
                      ;*invokevirtual toExternalForm {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.net.URL::toExternalForm@5
                      ; - java.net.URL::toString@1
                      ; - java.lang.ClassLoader::defineClassSourceLocation@22
                      ; - java.lang.ClassLoader::defineClass@12
                      ;   {internal_word}
  0x0000025000317cf8: 3107 49ba | 547c 3100 | 5002 0000 | 4d89 9760 

  0x0000025000317d08: ;   {runtime_call SafepointBlob}
  0x0000025000317d08: 0400 00e9 | f0cd 2607 | 498b 87f8 | 0400 0049 | c787 f804 | 0000 0000 | 0000 49c7 | 8700 0500 
  0x0000025000317d28: 0000 0000 | 0048 81c4 | b000 0000 

  0x0000025000317d34: ;   {runtime_call unwind_exception Runtime1 stub}
  0x0000025000317d34: 5de9 c687 | 2807 f4f4 | f4f4 f4f4 
[Stub Code]
  0x0000025000317d40: ;   {no_reloc}
  0x0000025000317d40: 0f1f 4400 

  0x0000025000317d44: ;   {static_stub}
  0x0000025000317d44: 0048 bb00 | 0000 0000 

  0x0000025000317d4c: ;   {runtime_call nmethod}
  0x0000025000317d4c: 0000 00e9 | fbff ffff 

  0x0000025000317d54: ;   {static_stub}
  0x0000025000317d54: 48bb 0000 | 0000 0000 

  0x0000025000317d5c: ;   {runtime_call nmethod}
  0x0000025000317d5c: 0000 e9fb 

  0x0000025000317d60: ;   {static_stub}
  0x0000025000317d60: ffff ff48 | bb00 0000 | 0000 0000 

  0x0000025000317d6c: ;   {runtime_call nmethod}
  0x0000025000317d6c: 00e9 fbff 

  0x0000025000317d70: ;   {static_stub}
  0x0000025000317d70: ffff 48bb | 0000 0000 | 0000 0000 

  0x0000025000317d7c: ;   {runtime_call nmethod}
  0x0000025000317d7c: e9fb ffff 

  0x0000025000317d80: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x0000025000317d80: ffe8 fa84 

  0x0000025000317d84: ;   {external_word}
  0x0000025000317d84: 3107 48b9 | 907f 1aef | ff7f 0000 | 4883 e4f0 

  0x0000025000317d94: ;   {runtime_call}
  0x0000025000317d94: 48b8 e044 | ddee ff7f | 0000 ffd0 

  0x0000025000317da0: ;   {section_word}
  0x0000025000317da0: f449 baa1 | 7d31 0050 | 0200 0041 

  0x0000025000317dac: ;   {runtime_call DeoptimizationBlob}
  0x0000025000317dac: 52e9 eebf | 2607 f4f4 | f4f4 f4f4 
[/MachCode]


Compiled method (c1) 2920 2442   !   3       org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass (67 bytes)
 total in heap  [0x000002500048ce10,0x000002500048d9f0] = 3040
 relocation     [0x000002500048cf70,0x000002500048d018] = 168
 main code      [0x000002500048d020,0x000002500048d620] = 1536
 stub code      [0x000002500048d620,0x000002500048d690] = 112
 oops           [0x000002500048d690,0x000002500048d698] = 8
 metadata       [0x000002500048d698,0x000002500048d6d8] = 64
 scopes data    [0x000002500048d6d8,0x000002500048d828] = 336
 scopes pcs     [0x000002500048d828,0x000002500048d938] = 272
 dependencies   [0x000002500048d938,0x000002500048d950] = 24
 handler table  [0x000002500048d950,0x000002500048d9c8] = 120
 nul chk table  [0x000002500048d9c8,0x000002500048d9f0] = 40

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader'
  # this:     rdx:rdx   = 'org/eclipse/osgi/internal/loader/ModuleClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9:r9     = '[B'
  # parm2:    rdi:rdi   = 'org/eclipse/osgi/internal/loader/classpath/ClasspathEntry'
  #           [sp+0xb0]  (sp of caller)
  0x000002500048d020: 448b 5208 | 49bb 0000 | 000f 5002 | 0000 4d03 | d34c 3bd0 

  0x000002500048d034: ;   {runtime_call ic_miss_stub}
  0x000002500048d034: 0f85 4614 | 0f07 660f | 1f44 0000 
[Verified Entry Point]
  0x000002500048d040: 8984 2400 | 80ff ff55 | 4881 eca0 | 0000 0090 | 4181 7f20 | 0100 0000 

  0x000002500048d058: ;   {runtime_call StubRoutines (final stubs)}
  0x000002500048d058: 7405 e881 

  0x000002500048d05c: ;   {metadata(method data for {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d05c: b90d 0748 | be90 73b6 | 5050 0200 | 008b 9ecc | 0000 0083 | c302 899e | cc00 0000 | 81e3 fe07 
  0x000002500048d07c: 0000 85db | 0f84 5304 | 0000 4889 | 7c24 704c | 894c 2468 

  0x000002500048d090: ;   {metadata(method data for {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d090: 488b f248 | bb90 73b6 | 5050 0200 | 008b 7608 | 49ba 0000 | 000f 5002 | 0000 4903 | f248 3bb3 
  0x000002500048d0b0: 2001 0000 | 750d 4883 | 8328 0100 | 0001 e960 | 0000 0048 | 3bb3 3001 | 0000 750d | 4883 8338 
  0x000002500048d0d0: 0100 0001 | e94a 0000 | 0048 83bb | 2001 0000 | 0075 1748 | 89b3 2001 | 0000 48c7 | 8328 0100 
  0x000002500048d0f0: 0001 0000 | 00e9 2900 | 0000 4883 | bb30 0100 | 0000 7517 | 4889 b330 | 0100 0048 | c783 3801 
  0x000002500048d110: 0000 0100 | 0000 e908 | 0000 0048 | 8383 1001 

  0x000002500048d120: ;   {metadata(method data for {method} {0x0000025050620068} 'getClassLoadingLock' '(Ljava/lang/String;)Ljava/lang/Object;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d120: 0000 0148 | bec8 9891 | 5050 0200 | 008b 9ecc | 0000 0083 | c302 899e | cc00 0000 | 81e3 feff 
  0x000002500048d140: 1f00 85db | 0f84 b003 | 0000 8b72 | 5048 3b06 

  0x000002500048d150: ;   {metadata(method data for {method} {0x0000025050620068} 'getClassLoadingLock' '(Ljava/lang/String;)Ljava/lang/Object;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d150: 488b de48 | b8c8 9891 | 5050 0200 | 0048 8380 | 1001 0000 | 0149 8bd8 | 4c8b c348 | 8954 2458 
  0x000002500048d170: 488b d648 | 895c 2460 | 0f1f 8000 

  0x000002500048d17c: ;   {optimized virtual_call}
  0x000002500048d17c: 0000 00e8 

  0x000002500048d180: ; ImmutableOopMap {[88]=Oop [96]=Oop [104]=Oop [112]=Oop }
                      ;*invokevirtual getLock {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::getClassLoadingLock@5 (line 437)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@8 (line 312)
  0x000002500048d180: 7c0e ecff 

  0x000002500048d184: ;   {other}
  0x000002500048d184: 0f1f 8400 | 7403 0000 | 4889 4424 | 7848 8d94 | 2480 0000 | 0048 8bf0 | 4889 7208 

  0x000002500048d1a0: ; implicit exception: dispatches to 0x000002500048d520
  0x000002500048d1a0: 488b 0648 | 83c8 0148 | 8902 f048 | 0fb1 160f | 8412 0000 | 0048 2bc4 | 4825 07f0 | ffff 4889 
  0x000002500048d1c0: 020f 855e | 0300 0049 | ff87 4805 | 0000 488b 

  0x000002500048d1d0: ;   {metadata(method data for {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d1d0: 5424 5848 | be90 73b6 | 5050 0200 | 0048 8386 | 4801 0000 

  0x000002500048d1e4: ;   {metadata(method data for {method} {0x000002500f431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002500048d1e4: 0148 bab8 | bf23 5050 | 0200 008b | b2cc 0000 | 0083 c602 | 89b2 cc00 | 0000 81e6 | feff 1f00 
  0x000002500048d204: 85f6 0f84 | 2c03 0000 

  0x000002500048d20c: ;   {metadata(method data for {method} {0x000002500f431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002500048d20c: 48ba b8bf | 2350 5002 | 0000 4883 | 8210 0100 | 0001 488b | 5424 600f 

  0x000002500048d224: ;   {static_call}
  0x000002500048d224: 1f40 00e8 

  0x000002500048d228: ; ImmutableOopMap {[88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop [136]=Oop }
                      ;*invokestatic checkName {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findLoadedClass@1
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@17 (line 313)
  0x000002500048d228: 9469 7307 

  0x000002500048d22c: ;   {other}
  0x000002500048d22c: 0f1f 8400 | 1c04 0001 

  0x000002500048d234: ;   {metadata(method data for {method} {0x000002500f431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002500048d234: 85c0 49b8 | b8bf 2350 | 5002 0000 | 48c7 c220 | 0100 0075 | 0748 c7c2 | 3001 0000 | 498b 3410 
  0x000002500048d254: 488d 7601 | 4989 3410 | 0f85 0f00 

  0x000002500048d260: ;   {oop(nullptr)}
  0x000002500048d260: 0000 48be | 0000 0000 | 0000 0000 | e936 0000 | 0048 8b54 

  0x000002500048d274: ;   {metadata(method data for {method} {0x000002500f431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002500048d274: 2458 49b8 | b8bf 2350 | 5002 0000 | 4983 8040 | 0100 0001 | 4c8b 4424 | 6048 8b54 | 2458 0f1f 
  0x000002500048d294: ;   {optimized virtual_call}
  0x000002500048d294: 4400 00e8 

  0x000002500048d298: ; ImmutableOopMap {[88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop [136]=Oop }
                      ;*invokevirtual findLoadedClass0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findLoadedClass@11
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@17 (line 313)
  0x000002500048d298: 246d 6607 

  0x000002500048d29c: ;   {other}
  0x000002500048d29c: 0f1f 8400 | 8c04 0002 | 488b f048 

  0x000002500048d2a8: ;   {metadata(method data for {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d2a8: 85f6 49b8 | 9073 b650 | 5002 0000 | 49c7 c190 | 0100 0074 | 0749 c7c1 | 8001 0000 | 4b8b 3c08 
  0x000002500048d2c8: 488d 7f01 | 4b89 3c08 | 0f84 0e00 | 0000 488b | de41 b800 | 0000 00e9 | 6900 0000 | 488b 7c24 
  0x000002500048d2e8: 704c 8b4c | 2468 4c8b | 4424 6048 | 8b54 2458 

  0x000002500048d2f8: ; implicit exception: dispatches to 0x000002500048d559
  0x000002500048d2f8: 418b 710c 

  0x000002500048d2fc: ; implicit exception: dispatches to 0x000002500048d55e
  0x000002500048d2fc: 483b 0748 

  0x000002500048d300: ;   {metadata(method data for {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d300: 8bcf 48bb | 9073 b650 | 5002 0000 | 4883 83b8 | 0100 0001 | 8b4f 1048 

  0x000002500048d318: ;   {metadata(method data for {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d318: 8bfa 48bb | 9073 b650 | 5002 0000 | 4883 83d8 | 0100 0001 | bf00 0000 | 0066 0f1f 

  0x000002500048d334: ;   {optimized virtual_call}
  0x000002500048d334: 4400 00e8 

  0x000002500048d338: ; ImmutableOopMap {[120]=Oop [136]=Oop }
                      ;*invokevirtual defineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@37 (line 315)
  0x000002500048d338: 84a5 e8ff 

  0x000002500048d33c: ;   {other}
  0x000002500048d33c: 0f1f 8400 | 2c05 0003 | 488b d841 | b801 0000 | 0048 8d84 | 2480 0000 | 0048 8b10 | 4885 d20f 
  0x000002500048d35c: 840f 0000 | 0048 8b70 | 08f0 480f | b116 0f85 | f301 0000 | 49ff 8f48 

  0x000002500048d374: ;   {metadata(method data for {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d374: 0500 0048 | ba90 73b6 | 5050 0200 | 00ff 8210 

  0x000002500048d384: ;   {metadata('org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult')}
  0x000002500048d384: 0200 0048 | ba18 c611 | 1050 0200 | 0049 8b87 | b801 0000 | 488d 7818 | 493b bfc8 | 0100 000f 
  0x000002500048d3a4: 87d0 0100 | 0049 89bf | b801 0000 | 48c7 0001 | 0000 0048 | 8bca 49ba | 0000 000f | 5002 0000 
  0x000002500048d3c4: 492b ca89 | 4808 4833 | c989 480c | 4833 c948 | 8948 1048 

  0x000002500048d3d8: ;   {metadata(method data for {method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d3d8: 8bf0 48bf | 9073 b650 | 5002 0000 | 4883 8728 | 0200 0001 

  0x000002500048d3ec: ;   {metadata(method data for {method} {0x00000250507406d0} '<init>' '(Ljava/lang/Class;Z)V' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult')}
  0x000002500048d3ec: 48be 803e | c550 5002 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | ff1f 0085 
  0x000002500048d40c: ff0f 8473 | 0100 0048 

  0x000002500048d414: ;   {metadata(method data for {method} {0x00000250507406d0} '<init>' '(Ljava/lang/Class;Z)V' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult')}
  0x000002500048d414: 8bf0 48bf | 803e c550 | 5002 0000 | 4883 8710 | 0100 0001 

  0x000002500048d428: ;   {metadata(method data for {method} {0x000002500f470e38} '<init>' '()V' in 'java/lang/Object')}
  0x000002500048d428: 48be 20e2 | 0050 5002 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | ff1f 0085 
  0x000002500048d448: ff0f 8458 | 0100 004c | 8bd3 4489 | 5010 488d | 3048 c1ee | 0948 bf00 | 0046 7150 | 0200 00c6 
  0x000002500048d468: 043e 0041 | 83e0 0144 | 8840 0c48 | 81c4 a000 

  0x000002500048d478: ;   {poll_return}
  0x000002500048d478: 0000 5d49 | 3ba7 4804 | 0000 0f87 | 4001 0000 | c349 8b87 | f804 0000 | 4d33 d24d | 8997 f804 
  0x000002500048d498: 0000 4d33 | d24d 8997 | 0005 0000 | 488b f048 | 8d84 2480 | 0000 0048 | 8b38 4885 | ff0f 840f 
  0x000002500048d4b8: 0000 0048 | 8b58 08f0 | 480f b13b | 0f85 1401 | 0000 49ff | 8f48 0500 | 0048 8bc6 | e938 0100 
  0x000002500048d4d8: ;   {metadata({method} {0x00000250505db230} 'defineClass' '(Ljava/lang/String;[BLorg/eclipse/osgi/internal/loader/classpath/ClasspathEntry;)Lorg/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d4d8: 0049 ba28 | b25d 5050 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002500048d4f0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002500048d4f0: e88b 601a 

  0x000002500048d4f4: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rdi=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@-1 (line 310)
  0x000002500048d4f4: 07e9 8cfb 

  0x000002500048d4f8: ;   {metadata({method} {0x0000025050620068} 'getClassLoadingLock' '(Ljava/lang/String;)Ljava/lang/Object;' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader')}
  0x000002500048d4f8: ffff 49ba | 6000 6250 | 5002 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002500048d510: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002500048d510: ffe8 6a60 

  0x000002500048d514: ; ImmutableOopMap {rdx=Oop r8=Oop [104]=Oop [112]=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::getClassLoadingLock@-1 (line 437)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@8 (line 312)
  0x000002500048d514: 1a07 e92f 

  0x000002500048d518: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002500048d518: fcff ffe8 

  0x000002500048d51c: ; ImmutableOopMap {rdx=Oop r8=Oop rsi=Oop [104]=Oop [112]=Oop }
                      ;*invokevirtual getLock {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::getClassLoadingLock@5 (line 437)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@8 (line 312)
  0x000002500048d51c: 600f 1a07 

  0x000002500048d520: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002500048d520: e85b 0f1a 

  0x000002500048d524: ; ImmutableOopMap {rsi=Oop [88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@14 (line 312)
  0x000002500048d524: 0748 8974 | 2408 4889 

  0x000002500048d52c: ;   {runtime_call monitorenter_nofpu Runtime1 stub}
  0x000002500048d52c: 1424 e84d 

  0x000002500048d530: ; ImmutableOopMap {rsi=Oop [88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop [136]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@14 (line 312)
  0x000002500048d530: 3f1a 07e9 | 96fc ffff 

  0x000002500048d538: ;   {metadata({method} {0x000002500f431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002500048d538: 49ba 8014 | 430f 5002 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002500048d54c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002500048d54c: ffff ffe8 

  0x000002500048d550: ; ImmutableOopMap {[88]=Oop [96]=Oop [104]=Oop [112]=Oop [120]=Oop [136]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::findLoadedClass@-1
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@17 (line 313)
  0x000002500048d550: 2c60 1a07 | e9b3 fcff 

  0x000002500048d558: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002500048d558: ffe8 220f 

  0x000002500048d55c: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rdi=Oop [120]=Oop [136]=Oop }
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@32 (line 315)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002500048d55c: 1a07 e81d 

  0x000002500048d560: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rdi=Oop [120]=Oop [136]=Oop }
                      ;*invokevirtual getDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@34 (line 315)
  0x000002500048d560: 0f1a 0748 | 8d84 2480 | 0000 0048 

  0x000002500048d56c: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x000002500048d56c: 8904 24e8 | 0c45 1a07 | e9fe fdff | ff48 8bd2 

  0x000002500048d57c: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002500048d57c: e8ff 171a 

  0x000002500048d580: ; ImmutableOopMap {rbx=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@55 (line 319)
  0x000002500048d580: 07e9 51fe 

  0x000002500048d584: ;   {metadata({method} {0x00000250507406d0} '<init>' '(Ljava/lang/Class;Z)V' in 'org/eclipse/osgi/internal/loader/ModuleClassLoader$DefineClassResult')}
  0x000002500048d584: ffff 49ba | c806 7450 | 5002 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002500048d59c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002500048d59c: ffe8 de5f 

  0x000002500048d5a0: ; ImmutableOopMap {rbx=Oop rax=Oop }
                      ;*synchronization entry
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader$DefineClassResult::<init>@-1 (line 96)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@63 (line 319)
  0x000002500048d5a0: 1a07 e96c 

  0x000002500048d5a4: ;   {metadata({method} {0x000002500f470e38} '<init>' '()V' in 'java/lang/Object')}
  0x000002500048d5a4: feff ff49 | ba30 0e47 | 0f50 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002500048d5bc: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002500048d5bc: ffff e8bd 

  0x000002500048d5c0: ; ImmutableOopMap {rbx=Oop rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.Object::<init>@-1
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader$DefineClassResult::<init>@1 (line 96)
                      ; - org.eclipse.osgi.internal.loader.ModuleClassLoader::defineClass@63 (line 319)
  0x000002500048d5c0: 5f1a 07e9 | 87fe ffff 

  0x000002500048d5c8: ;   {internal_word}
  0x000002500048d5c8: 49ba 7bd4 | 4800 5002 | 0000 4d89 | 9760 0400 

  0x000002500048d5d8: ;   {runtime_call SafepointBlob}
  0x000002500048d5d8: 00e9 2275 | 0f07 488d | 8424 8000 | 0000 4889 

  0x000002500048d5e8: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x000002500048d5e8: 0424 e891 | 441a 07e9 | ddfe ffff | 498b 87f8 | 0400 0049 | c787 f804 | 0000 0000 | 0000 49c7 
  0x000002500048d608: 8700 0500 | 0000 0000 | 0048 81c4 | a000 0000 

  0x000002500048d618: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002500048d618: 5de9 e22e | 1107 f4f4 
[Stub Code]
  0x000002500048d620: ;   {no_reloc}
  0x000002500048d620: 48bb 0000 | 0000 0000 

  0x000002500048d628: ;   {runtime_call nmethod}
  0x000002500048d628: 0000 e9fb 

  0x000002500048d62c: ;   {static_stub}
  0x000002500048d62c: ffff ff48 | bb00 0000 | 0000 0000 

  0x000002500048d638: ;   {runtime_call nmethod}
  0x000002500048d638: 00e9 fbff 

  0x000002500048d63c: ;   {static_stub}
  0x000002500048d63c: ffff 48bb | 0000 0000 | 0000 0000 

  0x000002500048d648: ;   {runtime_call nmethod}
  0x000002500048d648: e9fb ffff 

  0x000002500048d64c: ;   {static_stub}
  0x000002500048d64c: ff48 bb00 | 0000 0000 

  0x000002500048d654: ;   {runtime_call nmethod}
  0x000002500048d654: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x000002500048d65c: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002500048d65c: e81f 2c1a 

  0x000002500048d660: ;   {external_word}
  0x000002500048d660: 0748 b990 | 7f1a efff | 7f00 0048 

  0x000002500048d66c: ;   {runtime_call}
  0x000002500048d66c: 83e4 f048 | b8e0 44dd | eeff 7f00 | 00ff d0f4 
[Deopt Handler Code]
  0x000002500048d67c: ;   {section_word}
  0x000002500048d67c: 49ba 7cd6 | 4800 5002 | 0000 4152 

  0x000002500048d688: ;   {runtime_call DeoptimizationBlob}
  0x000002500048d688: e913 670f | 07f4 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002505484f730, length=21, elements={
0x000002506f3d9c20, 0x00000250799422b0, 0x000002507a653970, 0x000002507a65b710,
0x000002507a65cb00, 0x000002507a65d190, 0x000002507a65deb0, 0x000002507a662cb0,
0x000002507a697d30, 0x000002507a65e540, 0x000002507a65d820, 0x000002507a65f260,
0x000002507a65ebd0, 0x000002507a65f8f0, 0x000002507a65c470, 0x0000025054a9d6b0,
0x0000025054a9bc70, 0x000002505480dfc0, 0x0000025054a9a8c0, 0x0000025054a9c300,
0x0000025054a9c990
}

Java Threads: ( => current thread )
  0x000002506f3d9c20 JavaThread "main"                              [_thread_blocked, id=35808, stack(0x0000009f87700000,0x0000009f87800000) (1024K)]
  0x00000250799422b0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=19908, stack(0x0000009f87b00000,0x0000009f87c00000) (1024K)]
  0x000002507a653970 JavaThread "Finalizer"                  daemon [_thread_blocked, id=41544, stack(0x0000009f87c00000,0x0000009f87d00000) (1024K)]
  0x000002507a65b710 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=23064, stack(0x0000009f87d00000,0x0000009f87e00000) (1024K)]
  0x000002507a65cb00 JavaThread "Attach Listener"            daemon [_thread_blocked, id=30836, stack(0x0000009f87e00000,0x0000009f87f00000) (1024K)]
  0x000002507a65d190 JavaThread "Service Thread"             daemon [_thread_blocked, id=16344, stack(0x0000009f87f00000,0x0000009f88000000) (1024K)]
  0x000002507a65deb0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=43296, stack(0x0000009f88000000,0x0000009f88100000) (1024K)]
  0x000002507a662cb0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=39496, stack(0x0000009f88100000,0x0000009f88200000) (1024K)]
  0x000002507a697d30 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=45128, stack(0x0000009f88200000,0x0000009f88300000) (1024K)]
  0x000002507a65e540 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=20252, stack(0x0000009f88300000,0x0000009f88400000) (1024K)]
  0x000002507a65d820 JavaThread "Notification Thread"        daemon [_thread_blocked, id=10544, stack(0x0000009f88500000,0x0000009f88600000) (1024K)]
  0x000002507a65f260 JavaThread "Active Thread: Equinox Container: d9a3308d-a603-492d-b2a5-5d94efd9e891"        [_thread_blocked, id=17284, stack(0x0000009f88d00000,0x0000009f88e00000) (1024K)]
  0x000002507a65ebd0 JavaThread "Framework Event Dispatcher: Equinox Container: d9a3308d-a603-492d-b2a5-5d94efd9e891" daemon [_thread_blocked, id=19356, stack(0x0000009f88e00000,0x0000009f88f00000) (1024K)]
=>0x000002507a65f8f0 JavaThread "Start Level: Equinox Container: d9a3308d-a603-492d-b2a5-5d94efd9e891" daemon [_thread_in_vm, id=37196, stack(0x0000009f88f00000,0x0000009f89000000) (1024K)]
  0x000002507a65c470 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=29428, stack(0x0000009f89100000,0x0000009f89200000) (1024K)]
  0x0000025054a9d6b0 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=30676, stack(0x0000009f89200000,0x0000009f89300000) (1024K)]
  0x0000025054a9bc70 JavaThread "Worker-JM"                         [_thread_blocked, id=45144, stack(0x0000009f89300000,0x0000009f89400000) (1024K)]
  0x000002505480dfc0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=45268, stack(0x0000009f88400000,0x0000009f88500000) (1024K)]
  0x0000025054a9a8c0 JavaThread "Worker-0"                          [_thread_blocked, id=45764, stack(0x0000009f88600000,0x0000009f88700000) (1024K)]
  0x0000025054a9c300 JavaThread "Worker-1"                          [_thread_blocked, id=45260, stack(0x0000009f89400000,0x0000009f89500000) (1024K)]
  0x0000025054a9c990 JavaThread "Worker-2"                          [_thread_blocked, id=29192, stack(0x0000009f89500000,0x0000009f89600000) (1024K)]
Total: 21

Other Threads:
  0x000002507a652ab0 VMThread "VM Thread"                           [id=37632, stack(0x0000009f87a00000,0x0000009f87b00000) (1024K)]
  0x000002507986d210 WatcherThread "VM Periodic Task Thread"        [id=33768, stack(0x0000009f87900000,0x0000009f87a00000) (1024K)]
  0x0000025071f3f340 WorkerThread "GC Thread#0"                     [id=31296, stack(0x0000009f87800000,0x0000009f87900000) (1024K)]
  0x000002507aa0d8a0 WorkerThread "GC Thread#1"                     [id=29800, stack(0x0000009f88700000,0x0000009f88800000) (1024K)]
  0x000002507aa0dc40 WorkerThread "GC Thread#2"                     [id=27468, stack(0x0000009f88800000,0x0000009f88900000) (1024K)]
  0x000002507aa0dfe0 WorkerThread "GC Thread#3"                     [id=44200, stack(0x0000009f88900000,0x0000009f88a00000) (1024K)]
  0x000002505449cb60 WorkerThread "GC Thread#4"                     [id=36256, stack(0x0000009f88a00000,0x0000009f88b00000) (1024K)]
  0x000002505449cf00 WorkerThread "GC Thread#5"                     [id=41804, stack(0x0000009f88b00000,0x0000009f88c00000) (1024K)]
  0x000002505449d2a0 WorkerThread "GC Thread#6"                     [id=46824, stack(0x0000009f88c00000,0x0000009f88d00000) (1024K)]
  0x00000250546785d0 WorkerThread "GC Thread#7"                     [id=46556, stack(0x0000009f89000000,0x0000009f89100000) (1024K)]
Total: 10

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45c308] Metaspace_lock - owner thread: 0x000002507a65f8f0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002500f000000-0x000002500fba0000-0x000002500fba0000), size 12189696, SharedBaseAddress: 0x000002500f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000025010000000-0x0000025050000000, reserved size: 1073741824
Narrow klass base: 0x000002500f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 12773K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 33% used [0x00000000d5580000,0x00000000d5dfc850,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767cdb0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 4936K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x00000000804d22f8,0x0000000084300000)
 Metaspace       used 17212K, committed 17856K, reserved 1114112K
  class space    used 1669K, committed 1984K, reserved 1048576K

Card table byte_map: [0x0000025071860000,0x0000025071c70000] _byte_map_base: 0x0000025071460000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x0000025075350000, 0x0000025077350000)
 End Bits:   [0x0000025077350000, 0x0000025079350000)

Polling page: 0x0000025071540000

Metaspace:

Usage:
  Non-class:     15.18 MB used.
      Class:      1.63 MB used.
       Both:     16.81 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      15.50 MB ( 24%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.94 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      17.44 MB (  2%) committed. 

Chunk freelists:
   Non-Class:  15.61 MB
       Class:  14.11 MB
        Both:  29.72 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 470.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 279.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 1083.
num_chunk_merges: 0.
num_chunk_splits: 696.
num_chunks_enlarged: 450.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1142Kb max_used=1142Kb free=118857Kb
 bounds [0x0000025007ad0000, 0x0000025007d40000, 0x000002500f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=4936Kb max_used=4936Kb free=115063Kb
 bounds [0x0000025000000000, 0x00000250004e0000, 0x0000025007530000]
CodeHeap 'non-nmethods': size=5760Kb used=1266Kb max_used=1296Kb free=4493Kb
 bounds [0x0000025007530000, 0x00000250077a0000, 0x0000025007ad0000]
 total_blobs=3092 nmethods=2551 adapters=447
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 2.644 Thread 0x000002507a697d30 2542       3       org.lombokweb.asm.SymbolTable::addConstant (317 bytes)
Event: 2.645 Thread 0x000002507a697d30 nmethod 2542 0x00000250004cbd90 code [0x00000250004cc340, 0x00000250004cf490]
Event: 2.645 Thread 0x000002507a697d30 2543       3       org.lombokweb.asm.MethodVisitor::visitLabel (16 bytes)
Event: 2.645 Thread 0x000002507a697d30 nmethod 2543 0x00000250004cff10 code [0x00000250004d00c0, 0x00000250004d02c8]
Event: 2.645 Thread 0x000002507a697d30 2544       3       org.lombokweb.asm.Label::accept (69 bytes)
Event: 2.646 Thread 0x000002507a697d30 nmethod 2544 0x00000250004d0390 code [0x00000250004d0580, 0x00000250004d0ab8]
Event: 2.662 Thread 0x000002505480dfc0 2545       4       org.eclipse.osgi.internal.hookregistry.ClassLoaderHook::recordClassDefine (1 bytes)
Event: 2.662 Thread 0x000002505480dfc0 nmethod 2545 0x0000025007bec710 code [0x0000025007bec8a0, 0x0000025007bec928]
Event: 2.670 Thread 0x000002505480dfc0 2546       4       java.lang.String::<init> (99 bytes)
Event: 2.673 Thread 0x000002505480dfc0 nmethod 2546 0x0000025007beca10 code [0x0000025007becbe0, 0x0000025007bed1a0]
Event: 2.673 Thread 0x000002507a697d30 2547       3       java.lang.ThreadLocal$ThreadLocalMap::nextIndex (15 bytes)
Event: 2.673 Thread 0x000002507a697d30 nmethod 2547 0x00000250004d0c90 code [0x00000250004d0e20, 0x00000250004d0f58]
Event: 2.677 Thread 0x000002507a697d30 2548       3       java.lang.invoke.DirectMethodHandle$Holder::newInvokeSpecial (21 bytes)
Event: 2.677 Thread 0x000002507a697d30 nmethod 2548 0x00000250004d1010 code [0x00000250004d1200, 0x00000250004d1980]
Event: 2.678 Thread 0x000002507a697d30 2549       3       org.lombokweb.asm.SymbolTable::addConstantIntegerOrFloat (21 bytes)
Event: 2.678 Thread 0x000002507a697d30 nmethod 2549 0x00000250004d1b10 code [0x00000250004d1ce0, 0x00000250004d1f48]
Event: 2.678 Thread 0x000002507a697d30 2550       1       lombok.patcher.scripts.AddFieldScript::access$0 (5 bytes)
Event: 2.678 Thread 0x000002507a697d30 nmethod 2550 0x0000025007bed490 code [0x0000025007bed620, 0x0000025007bed6d0]
Event: 2.687 Thread 0x000002507a697d30 2551       1       org.eclipse.osgi.internal.loader.sources.PackageSource::getId (5 bytes)
Event: 2.687 Thread 0x000002507a697d30 nmethod 2551 0x0000025007bed790 code [0x0000025007bed920, 0x0000025007bed9e8]

GC Heap History (8 events):
Event: 0.837 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4278K, committed 4480K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}
Event: 0.843 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3463K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71e1f28,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 8K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080002000,0x0000000084300000)
 Metaspace       used 4278K, committed 4480K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}
Event: 1.525 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 29063K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71e1f28,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 8K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080002000,0x0000000084300000)
 Metaspace       used 8132K, committed 8448K, reserved 1114112K
  class space    used 843K, committed 960K, reserved 1048576K
}
Event: 1.529 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4083K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767cdc0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 1064K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 1% used [0x0000000080000000,0x000000008010a0c0,0x0000000084300000)
 Metaspace       used 8132K, committed 8448K, reserved 1114112K
  class space    used 843K, committed 960K, reserved 1048576K
}
Event: 2.187 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29683K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767cdc0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 1064K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 1% used [0x0000000080000000,0x000000008010a0c0,0x0000000084300000)
 Metaspace       used 12908K, committed 13440K, reserved 1114112K
  class space    used 1330K, committed 1536K, reserved 1048576K
}
Event: 2.190 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4070K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d7279970,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 2908K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 4% used [0x0000000080000000,0x00000000802d73c8,0x0000000084300000)
 Metaspace       used 12908K, committed 13440K, reserved 1114112K
  class space    used 1330K, committed 1536K, reserved 1048576K
}
Event: 2.631 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29670K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d7279970,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 2908K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 4% used [0x0000000080000000,0x00000000802d73c8,0x0000000084300000)
 Metaspace       used 15861K, committed 16512K, reserved 1114112K
  class space    used 1607K, committed 1920K, reserved 1048576K
}
Event: 2.635 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4083K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767cdb0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 4936K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x00000000804d22f8,0x0000000084300000)
 Metaspace       used 15861K, committed 16512K, reserved 1114112K
  class space    used 1607K, committed 1920K, reserved 1048576K
}

Dll operation events (9 events):
Event: 0.017 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.047 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.139 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.144 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.147 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.150 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.170 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.291 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.511 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll

Deoptimization events (20 events):
Event: 2.262 Thread 0x000002507a65f8f0 DEOPT PACKING pc=0x0000025007bc8620 sp=0x0000009f88ff7570
Event: 2.262 Thread 0x000002507a65f8f0 DEOPT UNPACKING pc=0x0000025007583aa2 sp=0x0000009f88ff7560 mode 2
Event: 2.297 Thread 0x000002507a65f8f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000025007ae14e8 relative=0x00000000000000c8
Event: 2.297 Thread 0x000002507a65f8f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000025007ae14e8 method=java.lang.String.charAt(I)C @ 4 c2
Event: 2.297 Thread 0x000002507a65f8f0 DEOPT PACKING pc=0x0000025007ae14e8 sp=0x0000009f88ff8b20
Event: 2.297 Thread 0x000002507a65f8f0 DEOPT UNPACKING pc=0x0000025007583aa2 sp=0x0000009f88ff8ac0 mode 2
Event: 2.297 Thread 0x000002507a65f8f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000025007b0b874 relative=0x0000000000000054
Event: 2.297 Thread 0x000002507a65f8f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000025007b0b874 method=java.lang.CharacterData.of(I)Ljava/lang/CharacterData; @ 4 c2
Event: 2.297 Thread 0x000002507a65f8f0 DEOPT PACKING pc=0x0000025007b0b874 sp=0x0000009f88ff8a80
Event: 2.297 Thread 0x000002507a65f8f0 DEOPT UNPACKING pc=0x0000025007583aa2 sp=0x0000009f88ff8a18 mode 2
Event: 2.518 Thread 0x000002507a65f8f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000025007bd41d0 relative=0x0000000000000b90
Event: 2.518 Thread 0x000002507a65f8f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000025007bd41d0 method=jdk.internal.org.objectweb.asm.Frame.execute(IILjdk/internal/org/objectweb/asm/Symbol;Ljdk/internal/org/objectweb/asm/SymbolTable;)V @ 1 c2
Event: 2.518 Thread 0x000002507a65f8f0 DEOPT PACKING pc=0x0000025007bd41d0 sp=0x0000009f88ff79f0
Event: 2.518 Thread 0x000002507a65f8f0 DEOPT UNPACKING pc=0x0000025007583aa2 sp=0x0000009f88ff7978 mode 2
Event: 2.585 Thread 0x000002507a65f8f0 DEOPT PACKING pc=0x0000025000394135 sp=0x0000009f88ff4a30
Event: 2.585 Thread 0x000002507a65f8f0 DEOPT UNPACKING pc=0x0000025007584242 sp=0x0000009f88ff3f40 mode 0
Event: 2.602 Thread 0x000002507a65f8f0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000025007be15ec relative=0x00000000000007ec
Event: 2.602 Thread 0x000002507a65f8f0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000025007be15ec method=org.lombokweb.asm.ClassReader.readUTF8(I[C)Ljava/lang/String; @ 11 c2
Event: 2.602 Thread 0x000002507a65f8f0 DEOPT PACKING pc=0x0000025007be15ec sp=0x0000009f88fef140
Event: 2.602 Thread 0x000002507a65f8f0 DEOPT UNPACKING pc=0x0000025007583aa2 sp=0x0000009f88fef128 mode 2

Classes loaded (20 events):
Event: 2.526 Loading class java/util/zip/GZIPInputStream
Event: 2.526 Loading class java/util/zip/GZIPInputStream done
Event: 2.544 Loading class java/io/StringReader
Event: 2.544 Loading class java/io/StringReader done
Event: 2.547 Loading class java/net/SocketTimeoutException
Event: 2.548 Loading class java/io/InterruptedIOException
Event: 2.548 Loading class java/io/InterruptedIOException done
Event: 2.548 Loading class java/net/SocketTimeoutException done
Event: 2.548 Loading class java/net/SocketException
Event: 2.548 Loading class java/net/SocketException done
Event: 2.548 Loading class java/net/UnknownHostException
Event: 2.548 Loading class java/net/UnknownHostException done
Event: 2.548 Loading class java/net/ProtocolException
Event: 2.548 Loading class java/net/ProtocolException done
Event: 2.575 Loading class java/lang/ThreadLocal$SuppliedThreadLocal
Event: 2.575 Loading class java/lang/ThreadLocal$SuppliedThreadLocal done
Event: 2.584 Loading class java/io/FileWriter
Event: 2.584 Loading class java/io/FileWriter done
Event: 2.584 Loading class java/lang/NegativeArraySizeException
Event: 2.584 Loading class java/lang/NegativeArraySizeException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 2.197 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d564db78}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d564db78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.255 Thread 0x000002507a65f8f0 Exception <a 'java/io/FileNotFoundException'{0x00000000d57b5e70}> (0x00000000d57b5e70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.255 Thread 0x000002507a65f8f0 Exception <a 'java/io/FileNotFoundException'{0x00000000d57bd5e0}> (0x00000000d57bd5e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 2.341 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5d76418}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5d76418) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.360 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e54858}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000d5e54858) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.361 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e5c0a0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000d5e5c0a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.361 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e60490}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, long)'> (0x00000000d5e60490) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.362 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5e640b0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000d5e640b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.370 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5ebc648}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5ebc648) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.389 Thread 0x000002507a65f8f0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5f65b30}: Found class java.lang.Object, but interface was expected> (0x00000000d5f65b30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 2.391 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5f776c0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d5f776c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.392 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5f7f198}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5f7f198) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.392 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5f83158}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5f83158) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.392 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5f86c98}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d5f86c98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.394 Thread 0x0000025054a9a8c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5f13ed0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d5f13ed0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.394 Thread 0x0000025054a9a8c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5f1ac08}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5f1ac08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.395 Thread 0x0000025054a9a8c0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5f1e528}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5f1e528) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.470 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d60c2a30}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d60c2a30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.518 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d62bbc88}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000d62bbc88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 2.519 Thread 0x000002507a65f8f0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d62bf348}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000d62bf348) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 1.360 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.360 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.500 Executing VM operation: ICBufferFull
Event: 1.500 Executing VM operation: ICBufferFull done
Event: 1.525 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1.529 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.671 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.678 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.969 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.969 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.187 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.190 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 2.282 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.282 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.285 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.285 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.298 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 2.298 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 2.631 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 2.635 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.058 Thread 0x000002506f3d9c20 Thread added: 0x000002507a65d190
Event: 0.058 Thread 0x000002506f3d9c20 Thread added: 0x000002507a65deb0
Event: 0.058 Thread 0x000002506f3d9c20 Thread added: 0x000002507a662cb0
Event: 0.059 Thread 0x000002506f3d9c20 Thread added: 0x000002507a697d30
Event: 0.081 Thread 0x000002506f3d9c20 Thread added: 0x000002507a65e540
Event: 0.127 Thread 0x000002507a697d30 Thread added: 0x000002507a83ac50
Event: 0.403 Thread 0x000002506f3d9c20 Thread added: 0x000002507a65d820
Event: 0.491 Thread 0x000002507a83ac50 Thread added: 0x0000025054056230
Event: 1.294 Thread 0x000002506f3d9c20 Thread added: 0x000002507a65f260
Event: 1.505 Thread 0x000002506f3d9c20 Thread added: 0x000002507a65ebd0
Event: 1.508 Thread 0x000002506f3d9c20 Thread added: 0x000002507a65f8f0
Event: 1.539 Thread 0x000002507a65f8f0 Thread added: 0x000002507a65c470
Event: 1.661 Thread 0x000002507a65f8f0 Thread added: 0x0000025054a9d6b0
Event: 2.026 Thread 0x000002507a65f8f0 Thread added: 0x0000025054a9bc70
Event: 2.078 Thread 0x0000025054056230 Thread exited: 0x0000025054056230
Event: 2.250 Thread 0x000002507a83ac50 Thread exited: 0x000002507a83ac50
Event: 2.309 Thread 0x000002507a697d30 Thread added: 0x000002505480dfc0
Event: 2.365 Thread 0x000002507a65f8f0 Thread added: 0x0000025054a9a8c0
Event: 2.395 Thread 0x0000025054a9a8c0 Thread added: 0x0000025054a9c300
Event: 2.672 Thread 0x0000025054a9a8c0 Thread added: 0x0000025054a9c990


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff8400b0000 - 0x00007ff8400f5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-2961a0b7a478d13d52172d246e5e53fd-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:28 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (4071M free)
TotalPageFile size 61318M (AvailPageFile size 576M)
current process WorkingSet (physical memory assigned to process): 124M, peak: 124M
current process commit charge ("private bytes"): 262M, peak: 262M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
