{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-te\\values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3430,3542,3696,3826,3941,4078,4203,4308,4546,4695,4807,4960,5092,5243,5406,5470,5540", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "3537,3691,3821,3936,4073,4198,4303,4403,4690,4802,4955,5087,5238,5401,5465,5535,5619"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4408", "endColumns": "137", "endOffsets": "4541"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "281,398,510,623,713,818,937,1015,1091,1182,1275,1370,1464,1564,1657,1752,1847,1938,2029,2118,2232,2336,2435,2550,2655,2770,2932,9666", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "393,505,618,708,813,932,1010,1086,1177,1270,1365,1459,1559,1652,1747,1842,1933,2024,2113,2227,2331,2430,2545,2650,2765,2927,3030,9744"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9749", "endColumns": "100", "endOffsets": "9845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,231,317,421,537,626,692,786,853,915,1008,1076,1139,1213,1278,1332,1453,1510,1572,1626,1705,1833,1921,2013,2128,2208,2290,2378,2445,2511,2586,2664,2754,2827,2903,2984,3053,3158,3235,3326,3419,3493,3570,3662,3717,3783,3867,3953,4016,4081,4145,4255,4367,4466,4585", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "226,312,416,532,621,687,781,848,910,1003,1071,1134,1208,1273,1327,1448,1505,1567,1621,1700,1828,1916,2008,2123,2203,2285,2373,2440,2506,2581,2659,2749,2822,2898,2979,3048,3153,3230,3321,3414,3488,3565,3657,3712,3778,3862,3948,4011,4076,4140,4250,4362,4461,4580,4663"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3035,3121,3225,3341,5624,5690,5784,5851,5913,6006,6074,6137,6211,6276,6330,6451,6508,6570,6624,6703,6831,6919,7011,7126,7206,7288,7376,7443,7509,7584,7662,7752,7825,7901,7982,8051,8156,8233,8324,8417,8491,8568,8660,8715,8781,8865,8951,9014,9079,9143,9253,9365,9464,9583", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,85,103,115,88,65,93,66,61,92,67,62,73,64,53,120,56,61,53,78,127,87,91,114,79,81,87,66,65,74,77,89,72,75,80,68,104,76,90,92,73,76,91,54,65,83,85,62,64,63,109,111,98,118,82", "endOffsets": "276,3116,3220,3336,3425,5685,5779,5846,5908,6001,6069,6132,6206,6271,6325,6446,6503,6565,6619,6698,6826,6914,7006,7121,7201,7283,7371,7438,7504,7579,7657,7747,7820,7896,7977,8046,8151,8228,8319,8412,8486,8563,8655,8710,8776,8860,8946,9009,9074,9138,9248,9360,9459,9578,9661"}}]}]}