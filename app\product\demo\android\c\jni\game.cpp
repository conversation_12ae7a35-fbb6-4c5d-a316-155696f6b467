#include "game.h"

#include <stdio.h>
#include <pthread.h>
#include <unistd.h>
#include <string.h>
#include "tp2_sdk.h"

#include <android/log.h>

typedef void* (*fpn_thread_func)(void *param);

int create_thread(fpn_thread_func thread_func, void *param)
{
    int err = 0;

    pthread_t tid = 0;
    pthread_attr_t attr;

    err = pthread_attr_init(&attr);
    if (err != 0) return -1;

    //该属性必须设置,否则在线程结束时,僵死的线程内存将得不到回收
    err = pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    if (err != 0) return err;

    err = pthread_create(&tid, &attr, thread_func, param);
    if (err != 0) return -1;

    err = pthread_attr_destroy(&attr);
    if (err != 0) return -1;

    return 0;
}

class MyTssInfoReceiver:public TssInfoReceiver
{
public:
    virtual void onReceive(int tssInfoType,const char* info)
    {

        char plain[256];
        memset(plain,0x00,sizeof(plain));
        if (tssInfoType == TSS_INFO_TYPE_DETECT_RESULT)
        {
            // 只关注TSS_INFO_TYPE_DETECT_RESULT

            int ret =tp2_dec_tss_info(info,plain,sizeof(plain));
            if (ret == -1) return;
            __android_log_print(ANDROID_LOG_DEBUG,"MTP","[C++  Cheat Info]:%d|%s", tssInfoType,plain);
        }
        else if (tssInfoType == TSS_INFO_TYPE_HEARTBEAT){
            // // 处理心跳，如果不关心，可以忽略
            int ret =tp2_dec_tss_info(info,plain,sizeof(plain));
            if (ret == -1) return;
            __android_log_print(ANDROID_LOG_DEBUG,"MTP","[C++  Cheat Info]:%d|%s", tssInfoType,plain);
        }

    }
};

MyTssInfoReceiver g_tss_info_receiver;

void game_start() {
    
    // 游戏启动的第一时间调用
    tp2_regist_tss_info_receiver(&g_tss_info_receiver);
    tp2_sdk_init_ex (19257, "d5ab8dc7ef67ca92e41d730982c5c602");

    // 用户登录时调用
    int account_type = ENTRY_ID_QZONE;      /*帐号类型*/
    int world_id = 101;             /*大区id*/
    char open_id[] = "B73B36366565F9E02C752";   /*与平台相关的用户标识*/
    char role_id[] = "paladin";         /*角色id*/
    tp2_setuserinfo(account_type, world_id, open_id, role_id);

    // 开启Android设备上应用安装列表扫描功能
    // 7.0及之后版本建议游戏在用户登录后调用
    TssSdkAntiDataInfo *data = (TssSdkAntiDataInfo *)tp2_sdk_ioctl(TssSDKCmd_CommQuery,"AllowAPKCollect");
    if (data != NULL)
    {
        tp2_free_anti_data(data);
    }
}

// 游戏切换到后台
int game_pause()
{
    return tp2_setgamestatus(TP2_GAME_STATUS_BACKEND);
}

// 游戏切换到前台
int game_resume()
{
    int ret = tp2_setgamestatus(TP2_GAME_STATUS_FRONTEND);
    return ret;
}


void* game_main(void *param)
{
    game_start();

    // 7.3版本之后的版本支持上报举报信息至ACE控制台
    const char* reported_account_id = "8B57B75C79A3E34E718C";   // 被举报人的账号 openid
    int reported_account_type = ENTRY_ID_QZONE;                 // 被举报人的账号类型
    int report_type = REPORT_TYPE_CHEAT;                                         // 举报的类型，辱骂、作弊等
    const char* report_scene = "5v5_mode";                              // 举报发生的场景, 游戏可自定义，会直接展示在控制台
    const char* report_reason = "肯定开透视了，我蹲草里不动都能看到我";      // 举报的详情信息，游戏可自定义
    // 举报信息最长为1024Byte，否则在上报时会截断
    char complaintMsg[1024] = {0};
    snprintf(complaintMsg, sizeof(complaintMsg), 
        "ReportComplaint:reportedId=%s;reportedType=%d;type=%d;scene=%s;reason=%s",
        reported_account_id,    // 被举报人的账号 openid
        reported_account_type,  // 被举报人的账号类型
        report_type,            // 举报的类型
        report_scene,           // 举报发生的场景
        report_reason           // 举报的详情信息
    );
	TssSdkAntiDataInfo *data = (TssSdkAntiDataInfo *)tp2_sdk_ioctl(TssSDKCmd_CommQuery, complaintMsg);
    if (data != NULL)
    {
        tp2_free_anti_data(data);
    }


    while (1)
    {
        //游戏主循环
        sleep(1);
    }

    return NULL;
}

int run_game()
{
    return create_thread(game_main, NULL);
}
