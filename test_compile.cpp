// 简化的编译测试文件
#include <iostream>
#include <vector>
#include <memory>
#include <random>
#include <cstdlib>
#include <cstring>

// 模拟Android日志
#define LOGI(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define LOGE(fmt, ...) printf("[ERROR] " fmt "\n", ##__VA_ARGS__)
#define LOGW(fmt, ...) printf("[WARN] " fmt "\n", ##__VA_ARGS__)

// 模拟mmap相关定义
#ifndef MAP_FAILED
#define MAP_FAILED ((void*)-1)
#endif

#ifndef MAP_PRIVATE
#define MAP_PRIVATE 0x02
#endif

#ifndef MAP_ANONYMOUS
#define MAP_ANONYMOUS 0x20
#endif

#ifndef PROT_READ
#define PROT_READ 0x1
#endif

#ifndef PROT_WRITE
#define PROT_WRITE 0x2
#endif

#ifndef MADV_WILLNEED
#define MADV_WILLNEED 3
#endif

// 模拟mmap函数（Windows环境下）
void* mmap(void* addr, size_t length, int prot, int flags, int fd, off_t offset) {
    (void)addr; (void)prot; (void)flags; (void)fd; (void)offset;
    return malloc(length);
}

int madvise(void* addr, size_t length, int advice) {
    (void)addr; (void)length; (void)advice;
    return 0;
}

// 测试腾讯策略的核心函数
class TencentStrategyTest {
private:
    std::vector<void*> caTraps_;
    size_t caMemorySize_ = 0;
    size_t caTrapCount_ = 0;

public:
    void CreateRandomTraps() {
        LOGI("🎯 [腾讯random_trap] 创建随机内存陷阱...");
        
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> size_dist(1024, 65536);
        std::uniform_int_distribution<> count_dist(20, 100);
        
        int trap_count = count_dist(gen);
        
        for (int i = 0; i < trap_count; i++) {
            size_t size = size_dist(gen);
            
            int alloc_method = i % 4;
            void* ptr = nullptr;
            
            switch (alloc_method) {
                case 0:
                    ptr = malloc(size);
                    break;
                case 1:
                    ptr = calloc(1, size);
                    break;
                case 2:
                    ptr = mmap(nullptr, size, PROT_READ | PROT_WRITE, 
                              MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
                    if (ptr == MAP_FAILED) ptr = nullptr;
                    break;
                case 3:
                    try {
                        ptr = new char[size];
                    } catch (...) {
                        ptr = nullptr;
                    }
                    break;
            }
            
            if (ptr) {
                FillRandomTrapData(ptr, size, i);
                
                caTraps_.push_back(ptr);
                caMemorySize_ += size;
                caTrapCount_++;
                
                LOGI("🎯 [random_trap] 陷阱[%d]: 方式=%d, 地址=%p, 大小=%zu", 
                     i, alloc_method, ptr, size);
            }
        }
        
        LOGI("✅ [random_trap] 创建完成: %d个随机陷阱", trap_count);
    }

    void FillRandomTrapData(void* ptr, size_t size, int trap_id) {
        uint32_t* data = static_cast<uint32_t*>(ptr);
        size_t count = size / sizeof(uint32_t);
        
        std::random_device rd;
        std::mt19937 gen(rd() + trap_id);
        std::uniform_int_distribution<uint32_t> value_dist(100, 999999);
        
        for (size_t i = 0; i < count; i++) {
            if (i % 4 == 0) {
                data[i] = 0xCAFE0000 + trap_id;
            } else if (i % 4 == 1) {
                data[i] = value_dist(gen);
            } else if (i % 4 == 2) {
                data[i] = 0xDEAD0000 + i;
            } else {
                data[i] = 0xBEEF0000 + (i * trap_id);
            }
        }
        
        size_t remaining = size % sizeof(uint32_t);
        if (remaining > 0) {
            uint8_t* byte_data = reinterpret_cast<uint8_t*>(ptr) + (count * sizeof(uint32_t));
            memset(byte_data, 0xCA + trap_id, remaining);
        }
    }

    void FillTencentPattern(void* ptr, size_t size) {
        uint32_t* data = static_cast<uint32_t*>(ptr);
        size_t count = size / sizeof(uint32_t);
        
        for (size_t i = 0; i < count; i++) {
            if (i % 8 == 0) {
                data[i] = 0x54535300 + i; // TSS前缀
            } else if (i % 8 == 1) {
                data[i] = 0x54503200 + i; // TP2前缀
            } else if (i % 8 == 2) {
                data[i] = 0xA4710000 + i; // ANTI前缀
            } else {
                data[i] = 0xCAFEBABE + i;
            }
        }
    }

    void PrintStats() {
        LOGI("📊 统计信息:");
        LOGI("  - CA陷阱数量: %zu", caTrapCount_);
        LOGI("  - CA内存大小: %.2f MB", caMemorySize_ / 1024.0 / 1024.0);
    }
};

int main() {
    LOGI("🚀 开始腾讯策略编译测试...");
    
    TencentStrategyTest test;
    test.CreateRandomTraps();
    test.PrintStats();
    
    LOGI("✅ 编译测试成功完成！");
    return 0;
}
