#include "memory_trap_sdk.h"
#include <android/log.h>
#include <sys/mman.h>
#include <unistd.h>
#include <cstring>
#include <chrono>
#include <vector>
#include <mutex>
#include <thread>
#include <cstdio>
#include <dirent.h>
#include <sys/ptrace.h>
#include <sys/stat.h>
#include <errno.h>

#define TAG "EnhancedDetector"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

namespace MemoryTrapSDK {

static std::vector<TrapInfo> g_traps;
static std::mutex g_traps_mutex;
static DetectionCallback g_callback = nullptr;
static std::thread g_monitor_thread;
static std::atomic<bool> g_monitoring{false};

// 方法1：检测GG特征文件
bool detectGGFiles() {
    const char* gg_paths[] = {
        "/data/data/com.dv.gameguardian",
        "/data/app/com.dv.gameguardian",
        "/system/app/GameGuardian",
        "/sdcard/GameGuardian",
        "/storage/emulated/0/GameGuardian",
        "/data/local/tmp/gg",
        "/data/data/catch_.me_.if_.you_.can_",  // GG隐藏包名
        "/sdcard/Android/data/com.dv.gameguardian"
    };

    LOGI("📁 开始检查GG特征文件...");

    bool found = false;
    for (const char* path : gg_paths) {
        struct stat st;
        LOGI("   检查路径: %s", path);

        if (stat(path, &st) == 0) {
            LOGW("🚨 发现GG文件！");
            LOGW("   路径: %s", path);
            LOGW("   类型: %s", S_ISDIR(st.st_mode) ? "目录" : "文件");
            LOGW("   大小: %ld 字节", st.st_size);
            LOGW("   修改时间: %ld", st.st_mtime);
            found = true;
        } else {
            LOGI("   ❌ 不存在: %s", path);
        }
    }

    if (found) {
        LOGW("🚨 GG特征文件检测：发现可疑文件！");
    } else {
        LOGI("✅ GG特征文件检测：未发现异常");
    }

    return found;
}

// 方法2：检测可疑端口
bool detectSuspiciousPorts() {
    LOGI("🌐 开始检查可疑网络端口...");

    FILE* f = fopen("/proc/net/tcp", "r");
    if (!f) {
        LOGE("❌ 无法打开/proc/net/tcp");
        return false;
    }

    char line[256];
    bool found = false;
    int line_count = 0;

    // 端口映射：十六进制 -> 十进制
    struct PortMap {
        const char* hex;
        int decimal;
        const char* desc;
    } suspicious_ports[] = {
        {"1F90", 8080, "HTTP代理"},
        {"22B8", 8888, "GG常用端口"},
        {"270F", 9999, "调试端口"},
        {"1F40", 8000, "Web服务"},
        {"1F41", 8001, "备用端口"}
    };

    LOGI("   扫描TCP连接表...");

    while (fgets(line, sizeof(line), f)) {
        line_count++;

        // 跳过标题行
        if (line_count == 1) {
            LOGI("   TCP表头: %s", line);
            continue;
        }

        // 检查每个可疑端口
        for (const auto& port : suspicious_ports) {
            if (strstr(line, port.hex)) {
                LOGW("🚨 发现可疑端口！");
                LOGW("   端口: %d (%s)", port.decimal, port.desc);
                LOGW("   十六进制: %s", port.hex);
                LOGW("   连接信息: %s", line);
                found = true;
            }
        }

        // 打印前10行连接信息用于分析
        if (line_count <= 10) {
            LOGI("   连接[%d]: %s", line_count-1, line);
        }
    }

    fclose(f);

    LOGI("   总共扫描了%d个TCP连接", line_count-1);

    if (found) {
        LOGW("🚨 可疑端口检测：发现异常端口！");
    } else {
        LOGI("✅ 可疑端口检测：未发现异常");
    }

    return found;
}

// 方法3：检测内存读取行为
bool detectMemoryReading() {
    LOGI("🧠 开始检查内存读取行为...");

    pid_t my_pid = getpid();
    char maps_path[256];
    char mem_path[256];
    snprintf(maps_path, sizeof(maps_path), "/proc/%d/maps", my_pid);
    snprintf(mem_path, sizeof(mem_path), "/proc/%d/mem", my_pid);

    LOGI("   当前进程PID: %d", my_pid);
    LOGI("   检查文件: %s", maps_path);
    LOGI("   检查文件: %s", mem_path);

    // 检查maps文件的访问时间
    struct stat maps_st, mem_st;
    bool maps_exists = (stat(maps_path, &maps_st) == 0);
    bool mem_exists = (stat(mem_path, &mem_st) == 0);

    LOGI("   maps文件存在: %s", maps_exists ? "是" : "否");
    LOGI("   mem文件存在: %s", mem_exists ? "是" : "否");

    if (maps_exists) {
        static time_t last_maps_access = 0;
        static time_t last_maps_modify = 0;

        LOGI("   maps访问时间: %ld", maps_st.st_atime);
        LOGI("   maps修改时间: %ld", maps_st.st_mtime);
        LOGI("   maps状态时间: %ld", maps_st.st_ctime);

        if (last_maps_access == 0) {
            last_maps_access = maps_st.st_atime;
            last_maps_modify = maps_st.st_mtime;
            LOGI("   初始化访问时间记录");
            return false;
        }

        if (maps_st.st_atime > last_maps_access + 2) {
            LOGW("🚨 检测到内存映射被异常访问！");
            LOGW("   上次访问: %ld", last_maps_access);
            LOGW("   本次访问: %ld", maps_st.st_atime);
            LOGW("   时间差: %ld秒", maps_st.st_atime - last_maps_access);
            last_maps_access = maps_st.st_atime;
            return true;
        }

        last_maps_access = maps_st.st_atime;
    }

    if (mem_exists) {
        LOGI("   mem访问时间: %ld", mem_st.st_atime);
        LOGI("   mem修改时间: %ld", mem_st.st_mtime);
    }

    LOGI("✅ 内存读取行为检测：未发现异常");
    return false;
}

// 方法4：检测ptrace使用
bool detectPtraceUsage() {
    LOGI("🔧 开始检查ptrace使用...");

    // 检查当前进程状态
    FILE* f = fopen("/proc/self/status", "r");
    if (f) {
        char line[256];
        while (fgets(line, sizeof(line), f)) {
            if (strstr(line, "TracerPid:")) {
                int tracer_pid;
                sscanf(line, "TracerPid:\t%d", &tracer_pid);
                LOGI("   当前TracerPid: %d", tracer_pid);

                if (tracer_pid != 0) {
                    LOGW("🚨 检测到被其他进程ptrace！");
                    LOGW("   调试器PID: %d", tracer_pid);
                    fclose(f);
                    return true;
                }
                break;
            }
        }
        fclose(f);
    }

    // 尝试ptrace自己，如果失败说明被其他进程ptrace了
    LOGI("   尝试PTRACE_TRACEME...");
    if (ptrace(PTRACE_TRACEME, 0, 0, 0) == -1) {
        LOGI("   PTRACE_TRACEME失败，错误码: %d (%s)", errno, strerror(errno));

        // 只有在特定错误码时才认为是被调试
        if (errno == EPERM) {
            LOGW("🚨 PTRACE_TRACEME被拒绝，可能被其他进程调试");
            return true;
        } else {
            LOGI("   PTRACE_TRACEME失败但不是被调试原因");
        }
    } else {
        LOGI("   PTRACE_TRACEME成功");
        // 立即detach
        ptrace(PTRACE_DETACH, 0, 0, 0);
    }

    LOGI("✅ ptrace使用检测：未发现异常");
    return false;
}

// 方法5：检测进程树异常
bool detectProcessTreeAnomaly() {
    LOGI("🌳 开始检查进程树异常...");

    pid_t my_pid = getpid();
    pid_t ppid = getppid();

    LOGI("   当前进程PID: %d", my_pid);
    LOGI("   父进程PID: %d", ppid);

    // 检查父进程信息
    char parent_comm[256] = {0};
    char parent_cmdline[512] = {0};

    char parent_comm_path[256];
    char parent_cmdline_path[256];
    snprintf(parent_comm_path, sizeof(parent_comm_path), "/proc/%d/comm", ppid);
    snprintf(parent_cmdline_path, sizeof(parent_cmdline_path), "/proc/%d/cmdline", ppid);

    // 读取父进程名
    FILE* f = fopen(parent_comm_path, "r");
    if (f) {
        fgets(parent_comm, sizeof(parent_comm) - 1, f);
        parent_comm[strcspn(parent_comm, "\n")] = 0;
        fclose(f);
        LOGI("   父进程名: %s", parent_comm);
    } else {
        LOGI("   无法读取父进程名");
    }

    // 读取父进程命令行
    f = fopen(parent_cmdline_path, "r");
    if (f) {
        fread(parent_cmdline, 1, sizeof(parent_cmdline) - 1, f);
        fclose(f);
        LOGI("   父进程命令行: %s", parent_cmdline);
    } else {
        LOGI("   无法读取父进程命令行");
    }

    // 检查祖父进程
    char parent_status_path[256];
    snprintf(parent_status_path, sizeof(parent_status_path), "/proc/%d/status", ppid);
    f = fopen(parent_status_path, "r");
    if (f) {
        char line[256];
        while (fgets(line, sizeof(line), f)) {
            if (strstr(line, "PPid:")) {
                int gppid;
                sscanf(line, "PPid:\t%d", &gppid);
                LOGI("   祖父进程PID: %d", gppid);

                // 读取祖父进程名
                char gparent_comm_path[256];
                snprintf(gparent_comm_path, sizeof(gparent_comm_path), "/proc/%d/comm", gppid);
                FILE* gf = fopen(gparent_comm_path, "r");
                if (gf) {
                    char gparent_comm[256] = {0};
                    fgets(gparent_comm, sizeof(gparent_comm) - 1, gf);
                    gparent_comm[strcspn(gparent_comm, "\n")] = 0;
                    LOGI("   祖父进程名: %s", gparent_comm);
                    fclose(gf);
                }
                break;
            }
        }
        fclose(f);
    }

    // 检查可疑的父进程
    const char* suspicious_names[] = {"gg", "guardian", "cheat", "hack", "mod", "trainer"};

    for (const char* name : suspicious_names) {
        if ((strlen(parent_comm) > 0 && strstr(parent_comm, name)) ||
            (strlen(parent_cmdline) > 0 && strstr(parent_cmdline, name))) {
            LOGW("🚨 检测到可疑父进程！");
            LOGW("   父进程名: %s", parent_comm);
            LOGW("   父进程命令行: %s", parent_cmdline);
            LOGW("   匹配关键词: %s", name);
            return true;
        }
    }

    LOGI("✅ 进程树异常检测：未发现异常");
    return false;
}

// 方法6：环境变量检测
bool detectSuspiciousEnv() {
    LOGI("🌍 开始检查环境变量...");

    const char* suspicious_vars[] = {
        "GG_VERSION", "GAMEGUARDIAN", "CHEAT_ENGINE",
        "FRIDA_VERSION", "XPOSED_BRIDGE", "SUBSTRATE_VERSION",
        "CYDIA_SUBSTRATE", "MOBILE_SUBSTRATE", "DEBUG_MODE"
    };

    LOGI("   检查%zu个可疑环境变量...", sizeof(suspicious_vars)/sizeof(suspicious_vars[0]));

    bool found = false;
    for (const char* var : suspicious_vars) {
        const char* value = getenv(var);
        if (value) {
            LOGW("🚨 发现可疑环境变量！");
            LOGW("   变量名: %s", var);
            LOGW("   变量值: %s", value);
            found = true;
        } else {
            LOGI("   ✓ %s: 未设置", var);
        }
    }

    // 检查一些常见的系统环境变量
    const char* system_vars[] = {"PATH", "LD_LIBRARY_PATH", "ANDROID_DATA", "ANDROID_ROOT"};
    LOGI("   检查系统环境变量...");

    for (const char* var : system_vars) {
        const char* value = getenv(var);
        if (value) {
            LOGI("   %s: %s", var, value);

            // 检查PATH中是否有可疑路径
            if (strcmp(var, "PATH") == 0) {
                if (strstr(value, "gg") || strstr(value, "guardian") ||
                    strstr(value, "cheat") || strstr(value, "frida")) {
                    LOGW("🚨 PATH中发现可疑路径！");
                    LOGW("   PATH: %s", value);
                    found = true;
                }
            }
        }
    }

    if (found) {
        LOGW("🚨 环境变量检测：发现可疑变量！");
    } else {
        LOGI("✅ 环境变量检测：未发现异常");
    }

    return found;
}

// 方法7：检测动态库注入
bool detectLibraryInjection() {
    LOGI("📚 开始检查动态库注入...");

    FILE* f = fopen("/proc/self/maps", "r");
    if (!f) {
        LOGE("❌ 无法打开/proc/self/maps");
        return false;
    }

    const char* suspicious_libs[] = {
        "libgg", "libguardian", "libcheat", "libhack",
        "frida", "xposed", "substrate", "cydia",
        "libhook", "libinject", "libmod"
    };

    char line[512];
    bool found = false;
    int total_maps = 0;
    int suspicious_count = 0;

    LOGI("   扫描内存映射中的动态库...");

    // 系统库白名单
    const char* system_lib_patterns[] = {
        "/system/", "/apex/", "/vendor/", "/product/",
        "libc.so", "libm.so", "libdl.so", "liblog.so",
        "libandroid", "libutils", "libcutils", "libbinder",
        "libui", "libgui", "libmedia", "libcamera",
        "libssl", "libcrypto", "libz", "libpng",
        "libjpeg", "libwebp", "libskia", "libharfbuzz"
    };

    while (fgets(line, sizeof(line), f)) {
        total_maps++;

        // 只检查包含.so的行（动态库）
        if (strstr(line, ".so")) {
            // 检查是否为系统库
            bool is_system_lib = false;
            for (const char* pattern : system_lib_patterns) {
                if (strstr(line, pattern)) {
                    is_system_lib = true;
                    break;
                }
            }

            if (is_system_lib) {
                // 系统库不打印，避免刷屏
                continue;
            }

            LOGI("   用户库[%d]: %s", total_maps, line);

            // 转换为小写进行检查
            char line_lower[512];
            for (int i = 0; line[i] && i < 511; i++) {
                line_lower[i] = tolower(line[i]);
            }
            line_lower[511] = 0;

            // 检查可疑关键词
            for (const char* lib : suspicious_libs) {
                if (strstr(line_lower, lib)) {
                    LOGW("🚨 检测到可疑动态库！");
                    LOGW("   原始内容: %s", line);
                    LOGW("   匹配关键词: %s", lib);
                    suspicious_count++;
                    found = true;
                }
            }
        }
    }

    fclose(f);

    LOGI("   总内存映射: %d个", total_maps);
    LOGI("   可疑库数量: %d个", suspicious_count);

    if (found) {
        LOGW("🚨 动态库注入检测：发现可疑库！");
    } else {
        LOGI("✅ 动态库注入检测：未发现异常");
    }

    return found;
}

// 方法8：检测系统调用异常
bool detectSyscallAnomaly() {
    LOGI("⚙️ 开始检查系统调用异常...");

    // 检查/proc/self/syscall文件
    FILE* f = fopen("/proc/self/syscall", "r");
    if (f) {
        char syscall_info[256];
        if (fgets(syscall_info, sizeof(syscall_info), f)) {
            LOGI("   当前系统调用: %s", syscall_info);
        }
        fclose(f);
    }

    // 检查文件描述符使用情况
    DIR* fd_dir = opendir("/proc/self/fd");
    if (fd_dir) {
        struct dirent* entry;
        int fd_count = 0;

        LOGI("   检查文件描述符使用情况...");
        while ((entry = readdir(fd_dir)) != nullptr) {
            if (isdigit(entry->d_name[0])) {
                fd_count++;

                // 读取fd指向的文件
                char fd_path[256];
                char link_target[256];
                snprintf(fd_path, sizeof(fd_path), "/proc/self/fd/%s", entry->d_name);

                ssize_t len = readlink(fd_path, link_target, sizeof(link_target) - 1);
                if (len > 0) {
                    link_target[len] = 0;
                    LOGI("   FD[%s]: %s", entry->d_name, link_target);

                    // 检查可疑的文件访问
                    if (strstr(link_target, "/proc/") && strstr(link_target, "/mem")) {
                        LOGW("🚨 检测到可疑的内存文件访问！");
                        LOGW("   文件描述符: %s", entry->d_name);
                        LOGW("   目标文件: %s", link_target);
                        closedir(fd_dir);
                        return true;
                    }
                }
            }
        }
        closedir(fd_dir);
        LOGI("   总文件描述符: %d个", fd_count);

        // Android应用正常情况下可能有很多FD，提高阈值
        if (fd_count > 200) {
            LOGW("🚨 文件描述符数量异常: %d个 (阈值: 200)", fd_count);
            return true;
        } else if (fd_count > 100) {
            LOGW("⚠️ 文件描述符数量较多: %d个 (正常范围内)", fd_count);
        }
    }

    // 检查系统调用频率
    static int call_count = 0;
    static auto last_check = std::chrono::steady_clock::now();

    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - last_check);

    call_count++;

    if (duration.count() >= 10) { // 每10秒统计一次
        int calls_per_sec = call_count / 10;
        LOGI("   系统调用频率: %d次/秒", calls_per_sec);

        // 提高阈值，Android应用正常情况下调用频率较高
        if (calls_per_sec > 100) {
            LOGW("🚨 检测到异常的系统调用频率: %d次/秒 (阈值: 100)", calls_per_sec);
            call_count = 0;
            last_check = now;
            return true;
        } else if (calls_per_sec > 50) {
            LOGW("⚠️ 系统调用频率较高: %d次/秒 (正常范围内)", calls_per_sec);
        }

        call_count = 0;
        last_check = now;
    }

    LOGI("✅ 系统调用异常检测：未发现异常");
    return false;
}

// 综合检测函数 - 优化版，减少误报
bool performComprehensiveDetection() {
    bool detected = false;
    std::string reasons;
    int detection_score = 0;  // 检测评分，多个弱信号组合判断

    // 高可信度检测（单独触发即报警）
    if (detectGGFiles()) {
        detected = true;
        reasons += "GG文件; ";
        detection_score += 100;  // 发现GG文件是强信号
    }

    if (detectSuspiciousPorts()) {
        detected = true;
        reasons += "可疑端口; ";
        detection_score += 80;   // 可疑端口是较强信号
    }

    if (detectProcessTreeAnomaly()) {
        detected = true;
        reasons += "进程树异常; ";
        detection_score += 90;   // 进程树异常是强信号
    }

    if (detectSuspiciousEnv()) {
        detected = true;
        reasons += "可疑环境变量; ";
        detection_score += 70;   // 环境变量是较强信号
    }

    if (detectLibraryInjection()) {
        detected = true;
        reasons += "动态库注入; ";
        detection_score += 60;   // 动态库注入是中等信号
    }

    // 低可信度检测（需要组合判断）
    bool memory_reading = detectMemoryReading();
    bool ptrace_usage = detectPtraceUsage();
    bool syscall_anomaly = detectSyscallAnomaly();

    if (memory_reading) {
        detection_score += 30;
        LOGI("   内存读取异常 (+30分)");
    }

    if (ptrace_usage) {
        detection_score += 40;
        LOGI("   ptrace使用异常 (+40分)");
    }

    if (syscall_anomaly) {
        detection_score += 20;
        LOGI("   系统调用异常 (+20分)");
    }

    // 只有当低可信度检测组合评分达到阈值时才报警
    if (!detected && detection_score >= 80) {
        detected = true;
        if (memory_reading) reasons += "内存读取; ";
        if (ptrace_usage) reasons += "ptrace使用; ";
        if (syscall_anomaly) reasons += "系统调用异常; ";
    }

    LOGI("🎯 检测评分: %d分 (阈值: 80分)", detection_score);
    
    if (detected) {
        LOGW("🚨🚨🚨 综合检测发现修改器！🚨🚨🚨");
        LOGW("检测原因: %s", reasons.c_str());
        
        // 触发回调
        if (g_callback && !g_traps.empty()) {
            DetectionEvent event;
            event.fault_address = nullptr;
            event.trap_info = &g_traps[0];
            event.timestamp = std::chrono::steady_clock::now();
            event.description = "综合检测发现修改器: " + reasons;
            event.analysis.is_modifier_scan = true;
            event.analysis.reason = reasons;
            
            g_callback(event);
        }
    }
    
    return detected;
}

// 增强的监控线程
void enhancedMonitor() {
    LOGI("🚀 增强检测线程启动");
    
    // 立即进行一次检测
    LOGI("🔍 启动时立即检测...");
    performComprehensiveDetection();
    
    int scan_count = 0;
    while (g_monitoring.load()) {
        scan_count++;
        LOGI("🔍 第%d次增强检测", scan_count);
        
        if (performComprehensiveDetection()) {
            // 检测到后，增加检测频率
            std::this_thread::sleep_for(std::chrono::seconds(5));
        } else {
            LOGI("✅ 第%d次检测完成，未发现异常", scan_count);
            std::this_thread::sleep_for(std::chrono::seconds(10));
        }
    }
    
    LOGI("🛑 增强检测线程结束");
}

// 设置陷阱
void setTrapsEnhanced(const std::vector<TrapInfo>& traps) {
    std::lock_guard<std::mutex> lock(g_traps_mutex);
    g_traps = traps;
    LOGI("设置了 %zu 个陷阱", traps.size());
}

// 设置回调
void setCallbackEnhanced(DetectionCallback callback) {
    g_callback = callback;
}

// 启动增强检测
bool startEnhancedDetection() {
    g_monitoring.store(true);
    g_monitor_thread = std::thread(enhancedMonitor);
    LOGI("🚀 增强检测系统启动成功");
    return true;
}

// 停止增强检测
void stopEnhancedDetection() {
    g_monitoring.store(false);
    if (g_monitor_thread.joinable()) {
        g_monitor_thread.join();
    }
    LOGI("🛑 增强检测系统已停止");
}

} // namespace MemoryTrapSDK
