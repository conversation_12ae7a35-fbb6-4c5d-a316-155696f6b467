#include "memory_trap_sdk.h"
#include <android/log.h>
#include <sys/mman.h>
#include <unistd.h>
#include <cstring>
#include <chrono>
#include <vector>
#include <mutex>

#define TAG "SimpleDetector"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

namespace MemoryTrapSDK {

// 简单的访问记录
struct SimpleAccess {
    void* addr;
    std::chrono::steady_clock::time_point time;
};

// 全局变量
static std::vector<SimpleAccess> g_access_history;
static std::mutex g_history_mutex;
static std::vector<TrapInfo> g_traps;
static std::mutex g_traps_mutex;
static DetectionCallback g_callback = nullptr;

// 简单的信号处理器
void simpleSignalHandler(int signum, siginfo_t* info, void* context) {
    if (!info) return;
    
    void* fault_addr = info->si_addr;
    LOGW("=== 检测到内存访问信号 ===");
    LOGW("地址: %p, 信号: %d", fault_addr, signum);
    
    // 1. 查找匹配的陷阱
    bool is_our_trap = false;
    TrapInfo* matched_trap = nullptr;
    
    {
        std::lock_guard<std::mutex> lock(g_traps_mutex);
        for (auto& trap : g_traps) {
            if (fault_addr >= trap.address &&
                fault_addr < (char*)trap.address + trap.size) {
                
                is_our_trap = true;
                matched_trap = &trap;
                LOGW("匹配到陷阱: %p, 区域: %d", trap.address, (int)trap.region);
                
                // 解除保护
                if (trap.is_protected) {
                    if (mprotect(trap.address, trap.size, PROT_READ | PROT_WRITE) == 0) {
                        trap.is_protected = false;
                        LOGW("解除陷阱保护成功");
                    }
                }
                break;
            }
        }
    }
    
    if (!is_our_trap) {
        LOGI("不是我们的陷阱，忽略");
        return;
    }
    
    // 2. 简单计数检测
    auto now = std::chrono::steady_clock::now();
    
    {
        std::lock_guard<std::mutex> lock(g_history_mutex);
        
        // 清理3秒前的记录
        auto cutoff = now - std::chrono::seconds(3);
        g_access_history.erase(
            std::remove_if(g_access_history.begin(), g_access_history.end(),
                          [cutoff](const SimpleAccess& access) {
                              return access.time < cutoff;
                          }),
            g_access_history.end()
        );
        
        // 添加当前访问
        g_access_history.push_back({fault_addr, now});
        
        size_t count = g_access_history.size();
        LOGW("最近3秒内访问次数: %zu", count);
        
        // 如果3秒内访问超过2次，判定为修改器
        if (count >= 2) {
            LOGW("*** 检测到修改器扫描！***");
            LOGW("触发地址: %p", fault_addr);
            LOGW("访问次数: %zu", count);
            
            // 触发回调
            if (g_callback && matched_trap) {
                DetectionEvent event;
                event.fault_address = fault_addr;
                event.trap_info = matched_trap;
                event.timestamp = now;
                event.description = "简单检测到修改器";
                event.analysis.is_modifier_scan = true;
                event.analysis.total_accesses = count;
                event.analysis.reason = "3秒内访问" + std::to_string(count) + "次";
                
                g_callback(event);
            }
            
            // 清空记录避免重复
            g_access_history.clear();
        }
    }
}

// 设置陷阱信息
void setTraps(const std::vector<TrapInfo>& traps) {
    std::lock_guard<std::mutex> lock(g_traps_mutex);
    g_traps = traps;
    LOGI("设置了 %zu 个陷阱", traps.size());
}

// 设置回调
void setCallback(DetectionCallback callback) {
    g_callback = callback;
}

// 安装信号处理器
bool installSimpleHandler() {
    struct sigaction sa;
    memset(&sa, 0, sizeof(sa));
    sa.sa_sigaction = simpleSignalHandler;
    sa.sa_flags = SA_SIGINFO | SA_RESTART;
    
    if (sigaction(SIGSEGV, &sa, nullptr) == -1) {
        LOGE("安装SIGSEGV处理器失败");
        return false;
    }
    
    LOGI("简单信号处理器安装成功");
    return true;
}

} // namespace MemoryTrapSDK
