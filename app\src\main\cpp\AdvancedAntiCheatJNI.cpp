#include <jni.h>
#include <string>
#include "AdvancedMemoryTrap.h"

using namespace AdvancedAntiCheat;

extern "C" {

// JNI函数声明
JNIEXPORT jboolean JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_initializeNative(JNIEnv *env, jobject thiz);

JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_shutdownNative(JNIEnv *env, jobject thiz);

JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_deployLayeredDefenseNative(JNIEnv *env, jobject thiz);

JNIEXPORT jint JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_getCurrentThreatLevelNative(JNIEnv *env, jobject thiz);

JNIEXPORT jstring JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_getStatisticsNative(JNIEnv *env, jobject thiz);

JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_adjustTrapsNative(JNIEnv *env, jobject thiz, jint threatLevel);

// 全局引用，用于回调Java方法
static JavaVM* g_jvm = nullptr;
static jobject g_callbackObject = nullptr;
static jmethodID g_onThreatDetectedMethod = nullptr;

// JNI_OnLoad由memory_trap_sdk_jni.cpp处理

// 威胁事件回调函数
void onThreatEventCallback(const ThreatEvent& event) {
    if (!g_jvm || !g_callbackObject || !g_onThreatDetectedMethod) {
        return;
    }
    
    JNIEnv* env;
    bool needDetach = false;
    
    // 获取JNI环境
    int status = g_jvm->GetEnv(reinterpret_cast<void**>(&env), JNI_VERSION_1_6);
    if (status == JNI_EDETACHED) {
        if (g_jvm->AttachCurrentThread(&env, nullptr) != 0) {
            return;
        }
        needDetach = true;
    } else if (status != JNI_OK) {
        return;
    }
    
    // 创建Java字符串
    jstring description = env->NewStringUTF(event.description.c_str());
    jstring reason = env->NewStringUTF(event.primaryReason.c_str());
    
    // 调用Java回调方法
    env->CallVoidMethod(g_callbackObject, g_onThreatDetectedMethod,
        static_cast<jint>(event.severity),
        description,
        reason,
        reinterpret_cast<jlong>(event.triggerAddress));
    
    // 清理本地引用
    env->DeleteLocalRef(description);
    env->DeleteLocalRef(reason);
    
    // 如果需要，分离线程
    if (needDetach) {
        g_jvm->DetachCurrentThread();
    }
}

// 初始化高级反作弊系统
JNIEXPORT jboolean JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_initializeNative(JNIEnv *env, jobject thiz) {
    LOGI("🚀 [JNI] 开始初始化高级反作弊系统...");

    // 保存回调对象的全局引用
    if (g_callbackObject) {
        env->DeleteGlobalRef(g_callbackObject);
    }
    g_callbackObject = env->NewGlobalRef(thiz);
    LOGI("✅ [JNI] 回调对象已保存");

    // 初始化内存陷阱管理器 - 只使用新的高级反作弊系统
    LOGI("🔧 [JNI] 初始化内存陷阱管理器...");
    auto& trapManager = MemoryTrapManager::getInstance();
    if (!trapManager.initialize()) {
        LOGE("❌ [JNI] 内存陷阱管理器初始化失败");
        return JNI_FALSE;
    }
    LOGI("✅ [JNI] 内存陷阱管理器初始化成功");

    // 设置威胁事件回调
    LOGI("📞 [JNI] 设置威胁事件回调...");
    trapManager.setThreatCallback(onThreatEventCallback);
    LOGI("✅ [JNI] 威胁事件回调已设置");

    // 部署分层防御
    LOGI("🛡️ [JNI] 部署分层防御策略...");
    trapManager.deployLayeredDefense();
    LOGI("✅ [JNI] 分层防御策略部署完成");

    LOGI("🎉 [JNI] 高级反作弊系统初始化完成");
    return JNI_TRUE;
}

// 关闭系统
JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_shutdownNative(JNIEnv *env, jobject thiz) {
    auto& trapManager = MemoryTrapManager::getInstance();
    trapManager.shutdown();
    
    // 清理全局引用
    if (g_callbackObject) {
        env->DeleteGlobalRef(g_callbackObject);
        g_callbackObject = nullptr;
    }
}

// 部署分层防御
JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_deployLayeredDefenseNative(JNIEnv *env, jobject thiz) {
    auto& trapManager = MemoryTrapManager::getInstance();
    trapManager.deployLayeredDefense();
}

// 获取当前威胁级别
JNIEXPORT jint JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_getCurrentThreatLevelNative(JNIEnv *env, jobject thiz) {
    auto& defenseSystem = DefenseSystem::getInstance();
    return static_cast<jint>(defenseSystem.getCurrentThreatLevel());
}

// 获取统计信息
JNIEXPORT jstring JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_getStatisticsNative(JNIEnv *env, jobject thiz) {
    auto& trapManager = MemoryTrapManager::getInstance();
    auto stats = trapManager.getStatistics();
    
    // 构建统计信息字符串
    std::string statsStr = "总陷阱数: " + std::to_string(stats.totalTraps) + 
                          ", 活跃陷阱: " + std::to_string(stats.activeTraps) +
                          ", 总触发次数: " + std::to_string(stats.totalTriggers) +
                          ", 最近一小时: " + std::to_string(stats.lastHourTriggers);
    
    return env->NewStringUTF(statsStr.c_str());
}

// 调整陷阱
JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_adjustTrapsNative(JNIEnv *env, jobject thiz, jint threatLevel) {
    auto& trapManager = MemoryTrapManager::getInstance();
    trapManager.adjustTrapsBasedOnThreat(static_cast<ThreatLevel>(threatLevel));
}

// 测试函数 - 手动触发陷阱
JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_triggerTestTrapNative(JNIEnv *env, jobject thiz) {
    // 创建一个测试陷阱并立即触发
    auto& trapManager = MemoryTrapManager::getInstance();
    
    // 创建诱饵值陷阱
    auto trap = trapManager.createValueDecoy(MemoryRegion::CH_CPP_HEAP, 12345);
    trap.triggerCallback = [](void* addr, const AccessRecord& record) {
        ThreatEvent event;
        event.severity = ThreatLevel::MEDIUM;
        event.description = "测试陷阱触发";
        event.primaryReason = "手动测试触发";
        event.triggerAddress = addr;
        event.region = MemoryRegion::CH_CPP_HEAP;
        event.trapType = TrapType::VALUE_DECOY;
        
        // 调用回调
        onThreatEventCallback(event);
    };
    
    trapManager.addTrap(std::move(trap));
    
    // 模拟访问陷阱
    if (trap.address) {
        volatile int* ptr = static_cast<int*>(trap.address);
        volatile int value = *ptr; // 触发读取
        (void)value; // 避免未使用变量警告
    }
}

// 获取行为分析结果
JNIEXPORT jstring JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_getBehaviorAnalysisNative(JNIEnv *env, jobject thiz) {
    auto& trapManager = MemoryTrapManager::getInstance();
    
    // 这里需要访问BehaviorAnalyzer，但它是private的
    // 为了演示，我们返回一个简单的分析结果
    std::string analysisStr = "行为分析: 正常模式, 威胁级别: 低";
    
    return env->NewStringUTF(analysisStr.c_str());
}

// 设置陷阱密度
JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_setTrapDensityNative(JNIEnv *env, jobject thiz, 
    jint chCount, jint jhCount, jint aCount, jint cdCount, jint caCount) {
    
    auto& trapManager = MemoryTrapManager::getInstance();
    
    // 清除现有陷阱
    trapManager.shutdown();
    trapManager.initialize();
    
    // 部署指定数量的陷阱
    if (chCount > 0) trapManager.deployChHeapTraps(chCount);
    if (jhCount > 0) trapManager.deployJhJavaTraps(jhCount);
    if (aCount > 0) trapManager.deployAnonymousTraps(aCount);
    if (cdCount > 0) trapManager.deployDataSegmentTraps(cdCount);
    if (caCount > 0) trapManager.deployCppAllocTraps(caCount);
}

// 激活/停用所有陷阱
JNIEXPORT void JNICALL
Java_com_sy_newfwg_AdvancedAntiCheat_setTrapsActiveNative(JNIEnv *env, jobject thiz, jboolean active) {
    auto& trapManager = MemoryTrapManager::getInstance();
    
    if (active) {
        trapManager.activateAllTraps();
    } else {
        trapManager.deactivateAllTraps();
    }
}

} // extern "C"
