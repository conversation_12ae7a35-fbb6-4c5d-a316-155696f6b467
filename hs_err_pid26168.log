#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 8256 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=26168, tid=39668
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-4ea3ec03923acd65041d504ea01e5a54-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 11:41:57 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 0.094429 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x00000292923bc460):  JavaThread "main"             [_thread_in_vm, id=39668, stack(0x0000009d7ab00000,0x0000009d7ac00000) (1024K)]

Stack: [0x0000009d7ab00000,0x0000009d7ac00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xbfa77]
V  [jvm.dll+0x854ff9]
V  [jvm.dll+0x855936]
V  [jvm.dll+0x85544c]
V  [jvm.dll+0x85a40f]
V  [jvm.dll+0x3ef3a9]
V  [jvm.dll+0x488739]
C  0x000002929f5cc96b

The last pc belongs to native method entry point (kind = native) (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.Thread.start0()V+0 java.base@21.0.7
j  java.lang.Thread.start()V+23 java.base@21.0.7
j  jdk.internal.ref.CleanerImpl.start(Ljava/lang/ref/Cleaner;Ljava/util/concurrent/ThreadFactory;)V+49 java.base@21.0.7
j  java.lang.ref.Cleaner.create(Ljava/util/concurrent/ThreadFactory;)Ljava/lang/ref/Cleaner;+21 java.base@21.0.7
j  jdk.internal.ref.CleanerFactory.<clinit>()V+7 java.base@21.0.7
v  ~StubRoutines::call_stub 0x000002929f5c100d
j  java.lang.invoke.MethodHandleNatives$CallSiteContext.make(Ljava/lang/invoke/CallSite;)Ljava/lang/invoke/MethodHandleNatives$CallSiteContext;+8 java.base@21.0.7
j  java.lang.invoke.CallSite.<init>(Ljava/lang/invoke/MethodHandle;)V+6 java.base@21.0.7
j  java.lang.invoke.ConstantCallSite.<init>(Ljava/lang/invoke/MethodHandle;)V+2 java.base@21.0.7
j  java.lang.invoke.InnerClassLambdaMetafactory.buildCallSite()Ljava/lang/invoke/CallSite;+106 java.base@21.0.7
j  java.lang.invoke.LambdaMetafactory.metafactory(Ljava/lang/invoke/MethodHandles$Lookup;Ljava/lang/String;Ljava/lang/invoke/MethodType;Ljava/lang/invoke/MethodType;Ljava/lang/invoke/MethodHandle;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/CallSite;+67 java.base@21.0.7
j  java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+20 java.base@21.0.7
j  java.lang.invoke.Invokers$Holder.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+28 java.base@21.0.7
j  java.lang.invoke.BootstrapMethodInvoker.invoke(Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;+319 java.base@21.0.7
j  java.lang.invoke.CallSite.makeSite(Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/invoke/MethodType;Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/invoke/CallSite;+8 java.base@21.0.7
j  java.lang.invoke.MethodHandleNatives.linkCallSiteImpl(Ljava/lang/Class;Ljava/lang/invoke/MethodHandle;Ljava/lang/String;Ljava/lang/invoke/MethodType;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/invoke/MemberName;+6 java.base@21.0.7
j  java.lang.invoke.MethodHandleNatives.linkCallSite(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/invoke/MemberName;+45 java.base@21.0.7
v  ~StubRoutines::call_stub 0x000002929f5c100d
j  jdk.internal.module.DefaultRoots.compute(Ljava/lang/module/ModuleFinder;Ljava/lang/module/ModuleFinder;)Ljava/util/Set;+11 java.base@21.0.7
j  jdk.internal.module.ModuleBootstrap.boot2()Ljava/lang/ModuleLayer;+727 java.base@21.0.7
j  jdk.internal.module.ModuleBootstrap.boot()Ljava/lang/ModuleLayer;+64 java.base@21.0.7
j  java.lang.System.initPhase2(ZZ)I+0 java.base@21.0.7
v  ~StubRoutines::call_stub 0x000002929f5c100d
native method entry point (kind = native)  [0x000002929f5cc540, 0x000002929f5cce88]  2376 bytes
[MachCode]
  0x000002929f5cc540: 488b 4b08 | 0fb7 492e | 584c 8d74 | ccf8 6800 | 0000 0068 | 0000 0000 | 5055 488b | ec41 5568 
  0x000002929f5cc560: 0000 0000 | 4c8b 6b08 | 4d8d 6d38 | 5348 8b53 | 0848 8b52 | 0848 8b52 | 1848 8b52 | 7048 8b12 
  0x000002929f5cc580: 5248 8b53 | 1048 85d2 | 0f84 0700 | 0000 4881 | c208 0100 | 0052 488b | 5308 488b | 5208 488b 
  0x000002929f5cc5a0: 5210 5249 | 8bc6 482b | c548 c1e8 | 0350 6800 | 0000 0068 | 0000 0000 | 4889 2424 | 41c6 8771 
  0x000002929f5cc5c0: 0400 0001 | 488b 4310 | 4885 c074 | 208b 88cc | 0000 0083 | c102 8988 | cc00 0000 | 2388 e000 
  0x000002929f5cc5e0: 0000 0f84 | f207 0000 | e9cf 0000 | 0048 8b43 | 1848 85c0 | 0f85 b000 | 0000 e805 | 0000 00e9 
  0x000002929f5cc600: 9900 0000 | 488b d348 | 8d44 2408 | 4c89 6dc0 | 498b cfc5 | f877 4989 | afa8 0300 | 0049 8987 
  0x000002929f5cc620: 9803 0000 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 9051 b8ee | ff7f 0000 
  0x000002929f5cc640: ffd0 4883 | c408 e90c | 0000 0048 | b890 51b8 | eeff 7f00 | 00ff d048 | 83c4 2049 | c787 9803 
  0x000002929f5cc660: 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 | 0049 c787 | a003 0000 | 0000 0000 | c5f8 7749 
  0x000002929f5cc680: 837f 0800 | 0f84 0500 | 0000 e971 | 48ff ff4c | 8b6d c04c | 8b75 c84e | 8d74 f500 | c348 8b43 
  0x000002929f5cc6a0: 1848 85c0 | 0f84 1200 | 0000 8b48 | 0883 c102 | 8948 0823 | 481c 0f84 | 1e07 0000 | 493b a7e0 
  0x000002929f5cc6c0: 0400 000f | 8748 0000 | 0089 8424 | 00f0 ffff | 8984 2400 | e0ff ff89 | 8424 00d0 | ffff 8984 
  0x000002929f5cc6e0: 2400 c0ff | ff89 8424 | 00b0 ffff | 8984 2400 | a0ff ff89 | 8424 0090 | ffff 8984 | 2400 80ff 
  0x000002929f5cc700: ff49 3ba7 | d804 0000 | 7607 4989 | a7e0 0400 | 0041 c687 | 7104 0000 | 0049 ba3d | 5b44 efff 
  0x000002929f5cc720: 7f00 0041 | 803a 000f | 843e 0000 | 0048 8b55 | e849 8bcf | 4883 ec20 | 40f6 c40f | 0f84 1900 
  0x000002929f5cc740: 0000 4883 | ec08 48b8 | 504b eeee | ff7f 0000 | ffd0 4883 | c408 e90c | 0000 0048 | b850 4bee 
  0x000002929f5cc760: eeff 7f00 | 00ff d048 | 83c4 2048 | 8b5d e84c | 8b5b 0845 | 0fb7 5b2e | 41c1 e303 | 492b e348 
  0x000002929f5cc780: 83ec 2048 | 83e4 f04c | 8b5b 604d | 85db 0f85 | ab00 0000 | e805 0000 | 00e9 9900 | 0000 488b 
  0x000002929f5cc7a0: d348 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 | 8987 9803 | 0000 4883 
  0x000002929f5cc7c0: ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 a078 | b8ee ff7f | 0000 ffd0 | 4883 c408 
  0x000002929f5cc7e0: e90c 0000 | 0048 b8a0 | 78b8 eeff | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 | 0000 0000 
  0x000002929f5cc800: 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 | 7749 837f | 0800 0f84 
  0x000002929f5cc820: 0500 0000 | e9d7 46ff | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c348 | 8b5d e84c | 8b5b 6041 
  0x000002929f5cc840: ffd3 488b | 5de8 4889 | 4518 448b | 5b28 41f6 | c308 0f84 | 1b00 0000 | 4c8b 5b08 | 4d8b 5b08 
  0x000002929f5cc860: 4d8b 5b18 | 4d8b 5b70 | 4d8b 1b4c | 895d 1048 | 8d55 1048 | 8b43 5849 | baa0 a0ee | eeff 7f00 
  0x000002929f5cc880: 0049 3bc2 | 0f85 ab00 | 0000 e805 | 0000 00e9 | 9900 0000 | 488b d348 | 8d44 2408 | 4c89 6dc0 
  0x000002929f5cc8a0: 498b cfc5 | f877 4989 | afa8 0300 | 0049 8987 | 9803 0000 | 4883 ec20 | 40f6 c40f | 0f84 1900 
  0x000002929f5cc8c0: 0000 4883 | ec08 48b8 | a078 b8ee | ff7f 0000 | ffd0 4883 | c408 e90c | 0000 0048 | b8a0 78b8 
  0x000002929f5cc8e0: eeff 7f00 | 00ff d048 | 83c4 2049 | c787 9803 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 
  0x000002929f5cc900: 0049 c787 | a003 0000 | 0000 0000 | c5f8 7749 | 837f 0800 | 0f84 0500 | 0000 e9e1 | 45ff ff4c 
  0x000002929f5cc920: 8b6d c04c | 8b75 c84e | 8d74 f500 | c348 8b5d | e848 8b43 | 5849 8d8f | b803 0000 | c5f8 7749 
  0x000002929f5cc940: 89af a803 | 0000 49ba | 3cc9 5c9f | 9202 0000 | 4d89 97a0 | 0300 0049 | 89a7 9803 | 0000 41c7 
  0x000002929f5cc960: 8744 0400 | 0004 0000 | 00ff d0c5 | f877 4883 | ec10 c5fb | 1104 2448 | 83ec 1048 | 8904 2448 
  0x000002929f5cc980: c744 2408 | 0000 0000 | 41c7 8744 | 0400 0005 | 0000 00f0 | 8344 24c0 | 0049 3baf | 4804 0000 
  0x000002929f5cc9a0: 0f87 0e00 | 0000 4183 | bf40 0400 | 0000 0f84 | 2000 0000 | 498b cf4c | 8be4 4883 | ec20 4883 
  0x000002929f5cc9c0: e4f0 48b8 | 00d1 b9ee | ff7f 0000 | ffd0 498b | e44d 33e4 | 41c7 8744 | 0400 0008 | 0000 0049 
  0x000002929f5cc9e0: c787 9803 | 0000 0000 | 0000 49c7 | 87a8 0300 | 0000 0000 | 0049 c787 | a003 0000 | 0000 0000 
  0x000002929f5cca00: c5f8 774d | 8b9f 2804 | 0000 41c7 | 8300 0100 | 0000 0000 | 0049 bbda | a05c 9f92 | 0200 004c 
  0x000002929f5cca20: 3b5d 180f | 854b 0000 | 0048 8b04 | 2448 83c4 | 1048 85c0 | 0f84 2500 | 0000 a803 | 0f85 0800 
  0x000002929f5cca40: 0000 488b | 00e9 1500 | 0000 a801 | 0f85 0900 | 0000 488b | 40fe e904 | 0000 0048 | 8b40 ff48 
  0x000002929f5cca60: 8945 1048 | 83ec 1048 | 8904 2448 | c744 2408 | 0000 0000 | 4183 bfc0 | 0400 0002 | 0f85 bf00 
  0x000002929f5cca80: 0000 4881 | ec80 0000 | 0048 8944 | 2478 4889 | 4c24 7048 | 8954 2468 | 4889 5c24 | 6048 896c 
  0x000002929f5ccaa0: 2450 4889 | 7424 4848 | 897c 2440 | 4c89 4424 | 384c 894c | 2430 4c89 | 5424 284c | 895c 2420 
  0x000002929f5ccac0: 4c89 6424 | 184c 896c | 2410 4c89 | 7424 084c | 893c 244c | 8be4 4883 | ec20 4883 | e4f0 48b8 
  0x000002929f5ccae0: 3083 eeee | ff7f 0000 | ffd0 498b | e44c 8b3c | 244c 8b74 | 2408 4c8b | 6c24 104c | 8b64 2418 
  0x000002929f5ccb00: 4c8b 5c24 | 204c 8b54 | 2428 4c8b | 4c24 304c | 8b44 2438 | 488b 7c24 | 4048 8b74 | 2448 488b 
  0x000002929f5ccb20: 6c24 5048 | 8b5c 2460 | 488b 5424 | 6848 8b4c | 2470 488b | 4424 7848 | 81c4 8000 | 0000 4d33 
  0x000002929f5ccb40: e448 8b5d | e84c 8b6b | 084d 8d6d | 3849 837f | 0800 0f84 | bb00 0000 | e805 0000 | 00e9 9600 
  0x000002929f5ccb60: 0000 488d | 4424 084c | 896d c049 | 8bcf c5f8 | 7749 89af | a803 0000 | 4989 8798 | 0300 0048 
  0x000002929f5ccb80: 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec | 0848 b840 | 96b8 eeff | 7f00 00ff | d048 83c4 
  0x000002929f5ccba0: 08e9 0c00 | 0000 48b8 | 4096 b8ee | ff7f 0000 | ffd0 4883 | c420 49c7 | 8798 0300 | 0000 0000 
  0x000002929f5ccbc0: 0049 c787 | a803 0000 | 0000 0000 | 49c7 87a0 | 0300 0000 | 0000 00c5 | f877 4983 | 7f08 000f 
  0x000002929f5ccbe0: 8405 0000 | 00e9 1643 | ffff 4c8b | 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 48b9 907f | 1aef ff7f 
  0x000002929f5ccc00: 0000 4883 | e4f0 48b8 | e044 ddee | ff7f 0000 | ffd0 f444 | 8b5b 2841 | f6c3 200f | 8444 0100 
  0x000002929f5ccc20: 0048 8d55 | a84c 8b5a | 084d 85db | 0f85 bb00 | 0000 e805 | 0000 00e9 | 9600 0000 | 488d 4424 
  0x000002929f5ccc40: 084c 896d | c049 8bcf | c5f8 7749 | 89af a803 | 0000 4989 | 8798 0300 | 0048 83ec | 2040 f6c4 
  0x000002929f5ccc60: 0f0f 8419 | 0000 0048 | 83ec 0848 | b870 95b8 | eeff 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 
  0x000002929f5ccc80: 48b8 7095 | b8ee ff7f | 0000 ffd0 | 4883 c420 | 49c7 8798 | 0300 0000 | 0000 0049 | c787 a803 
  0x000002929f5ccca0: 0000 0000 | 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 | 000f 8405 | 0000 00e9 
  0x000002929f5cccc0: 3c42 ffff | 4c8b 6dc0 | 4c8b 75c8 | 4e8d 74f5 | 00c3 48b9 | 907f 1aef | ff7f 0000 | 4883 e4f0 
  0x000002929f5ccce0: 48b8 e044 | ddee ff7f | 0000 ffd0 | f44c 896d | c048 8d02 | 4c8b 4a08 | 48c7 4208 | 0000 0000 
  0x000002929f5ccd00: 4c8b 004d | 85c0 0f84 | 0b00 0000 | f04d 0fb1 | 010f 850c | 0000 0049 | ff8f 4805 | 0000 e93e 
  0x000002929f5ccd20: 0000 004c | 894a 0848 | 8bca 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 9068 
  0x000002929f5ccd40: b8ee ff7f | 0000 ffd0 | 4883 c408 | e90c 0000 | 0048 b890 | 68b8 eeff | 7f00 00ff | d048 83c4 
  0x000002929f5ccd60: 204c 8b6d | c049 ba3d | 5b44 efff | 7f00 0041 | 803a 000f | 843e 0000 | 0048 8b55 | e849 8bcf 
  0x000002929f5ccd80: 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 504b eeee | ff7f 0000 | ffd0 4883 
  0x000002929f5ccda0: c408 e90c | 0000 0048 | b850 4bee | eeff 7f00 | 00ff d048 | 83c4 2048 | 8b04 2448 | 83c4 10c5 
  0x000002929f5ccdc0: fb10 0424 | 4883 c410 | 4c8b 5d18 | 41ff d34c | 8b5d f8c9 | 5f49 8be3 | ffe7 ba00 | 0000 00e8 
  0x000002929f5ccde0: 0500 0000 | e996 0000 | 0048 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 
  0x000002929f5cce00: 8987 9803 | 0000 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 505c | b8ee ff7f 
  0x000002929f5cce20: 0000 ffd0 | 4883 c408 | e90c 0000 | 0048 b850 | 5cb8 eeff | 7f00 00ff | d048 83c4 | 2049 c787 
  0x000002929f5cce40: 9803 0000 | 0000 0000 | 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 
  0x000002929f5cce60: 7749 837f | 0800 0f84 | 0500 0000 | e98f 40ff | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c348 
  0x000002929f5cce80: 8b5d e8e9 | 34f8 ffff 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000292ed66d560, length=10, elements={
0x00000292923bc460, 0x0000029294e41170, 0x0000029294e42030, 0x0000029294e44f00,
0x00000292ed594fc0, 0x00000292ed595cd0, 0x00000292ed597730, 0x00000292ed5984b0,
0x0000029294e4df30, 0x00000292ed677010
}
_to_delete_list=0x00000292ed594780, length=9, elements={
0x00000292923bc460, 0x0000029294e41170, 0x0000029294e42030, 0x0000029294e44f00,
0x00000292ed594fc0, 0x00000292ed595cd0, 0x00000292ed597730, 0x00000292ed5984b0,
0x0000029294e4df30
}

Java Threads: ( => current thread )
=>0x00000292923bc460 JavaThread "main"                              [_thread_in_vm, id=39668, stack(0x0000009d7ab00000,0x0000009d7ac00000) (1024K)]
  0x0000029294e41170 JavaThread "Reference Handler"          daemon [_thread_blocked, id=46664, stack(0x0000009d7af00000,0x0000009d7b000000) (1024K)]
  0x0000029294e42030 JavaThread "Finalizer"                  daemon [_thread_blocked, id=17820, stack(0x0000009d7b000000,0x0000009d7b100000) (1024K)]
  0x0000029294e44f00 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=24736, stack(0x0000009d7b100000,0x0000009d7b200000) (1024K)]
  0x00000292ed594fc0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=17604, stack(0x0000009d7b200000,0x0000009d7b300000) (1024K)]
  0x00000292ed595cd0 JavaThread "Service Thread"             daemon [_thread_blocked, id=40164, stack(0x0000009d7b300000,0x0000009d7b400000) (1024K)]
  0x00000292ed597730 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=45420, stack(0x0000009d7b400000,0x0000009d7b500000) (1024K)]
  0x00000292ed5984b0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=21432, stack(0x0000009d7b500000,0x0000009d7b600000) (1024K)]
  0x0000029294e4df30 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=44924, stack(0x0000009d7b600000,0x0000009d7b700000) (1024K)]
  0x00000292ed677010 JavaThread "Common-Cleaner"             daemon [_thread_new, id=46416, stack(0x0000000000000000,0x0000000000000000) (0B)]
Total: 10

Other Threads:
  0x00000292ed5923b0 VMThread "VM Thread"                           [id=30444, stack(0x0000009d7ae00000,0x0000009d7af00000) (1024K)]
  0x0000029294f44ef0 WatcherThread "VM Periodic Task Thread"        [id=28820, stack(0x0000009d7ad00000,0x0000009d7ae00000) (1024K)]
  0x0000029294f36b30 WorkerThread "GC Thread#0"                     [id=46700, stack(0x0000009d7ac00000,0x0000009d7ad00000) (1024K)]
Total: 3

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45e308] Threads_lock - owner thread: 0x00000292923bc460

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x00000292ac000000-0x00000292acba0000-0x00000292acba0000), size 12189696, SharedBaseAddress: 0x00000292ac000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000292ad000000-0x00000292ed000000, reserved size: 1073741824
Narrow klass base: 0x00000292ac000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 2560K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 10% used [0x00000000d5580000,0x00000000d5800140,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 177K, committed 384K, reserved 1114112K
  class space    used 7K, committed 128K, reserved 1048576K

Card table byte_map: [0x0000029294840000,0x0000029294c50000] _byte_map_base: 0x0000029294440000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x00000292a7340000, 0x00000292a9340000)
 End Bits:   [0x00000292a9340000, 0x00000292ab340000)

Polling page: 0x0000029294440000

Metaspace:

Usage:
  Non-class:    170.30 KB used.
      Class:      7.40 KB used.
       Both:    177.70 KB used.

Virtual space:
  Non-class space:       64.00 MB reserved,     256.00 KB ( <1%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     128.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,     384.00 KB ( <1%) committed. 

Chunk freelists:
   Non-Class:  12.00 MB
       Class:  15.75 MB
        Both:  27.74 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 6.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 6.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 7.
num_chunk_merges: 0.
num_chunk_splits: 5.
num_chunks_enlarged: 2.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=23Kb max_used=23Kb free=119976Kb
 bounds [0x000002929fb60000, 0x000002929fdd0000, 0x00000292a7090000]
CodeHeap 'profiled nmethods': size=120000Kb used=50Kb max_used=50Kb free=119949Kb
 bounds [0x0000029298090000, 0x0000029298300000, 0x000002929f5c0000]
CodeHeap 'non-nmethods': size=5760Kb used=1122Kb max_used=1122Kb free=4637Kb
 bounds [0x000002929f5c0000, 0x000002929f830000, 0x000002929fb60000]
 total_blobs=425 nmethods=63 adapters=269
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.075 Thread 0x0000029294e4df30   36       3       java.util.concurrent.ConcurrentHashMap::addCount (280 bytes)
Event: 0.075 Thread 0x0000029294e4df30 nmethod 36 0x000002929809a590 code [0x000002929809a7e0, 0x000002929809b0c0]
Event: 0.076 Thread 0x0000029294e4df30   38       3       java.util.concurrent.ConcurrentHashMap::casTabAt (21 bytes)
Event: 0.076 Thread 0x0000029294e4df30 nmethod 38 0x000002929809b390 code [0x000002929809b520, 0x000002929809b650]
Event: 0.078 Thread 0x0000029294e4df30   40       3       java.util.concurrent.ConcurrentHashMap::spread (10 bytes)
Event: 0.078 Thread 0x0000029294e4df30 nmethod 40 0x000002929809b710 code [0x000002929809b8a0, 0x000002929809b998]
Event: 0.081 Thread 0x0000029294e4df30   48       1       jdk.internal.util.StrongReferenceKey::get (5 bytes)
Event: 0.081 Thread 0x0000029294e4df30 nmethod 48 0x000002929fb63990 code [0x000002929fb63b20, 0x000002929fb63be8]
Event: 0.081 Thread 0x0000029294e4df30   51       1       java.lang.Enum::ordinal (5 bytes)
Event: 0.081 Thread 0x0000029294e4df30 nmethod 51 0x000002929fb64290 code [0x000002929fb64420, 0x000002929fb644e8]
Event: 0.082 Thread 0x0000029294e4df30   55       1       java.lang.invoke.MethodType::returnType (5 bytes)
Event: 0.082 Thread 0x0000029294e4df30 nmethod 55 0x000002929fb65290 code [0x000002929fb65420, 0x000002929fb654e8]
Event: 0.082 Thread 0x0000029294e4df30   57       3       java.lang.ref.ReferenceQueue::headIsNull (13 bytes)
Event: 0.082 Thread 0x0000029294e4df30 nmethod 57 0x000002929809ba10 code [0x000002929809bba0, 0x000002929809bd00]
Event: 0.082 Thread 0x0000029294e4df30   59       3       jdk.internal.util.ReferencedKeyMap::removeStaleReferences (30 bytes)
Event: 0.083 Thread 0x0000029294e4df30 nmethod 59 0x000002929809bd90 code [0x000002929809bf60, 0x000002929809c2c0]
Event: 0.083 Thread 0x0000029294e4df30   56       3       java.util.concurrent.ConcurrentHashMap::putIfAbsent (8 bytes)
Event: 0.083 Thread 0x0000029294e4df30 nmethod 56 0x000002929809c410 code [0x000002929809c5c0, 0x000002929809c700]
Event: 0.083 Thread 0x0000029294e4df30   63       3       java.lang.Math::min (11 bytes)
Event: 0.084 Thread 0x0000029294e4df30 nmethod 63 0x000002929809c790 code [0x000002929809c920, 0x000002929809ca58]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.023 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.055 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll

Deoptimization events (0 events):
No events

Classes loaded (14 events):
Event: 0.054 Loading class sun/nio/cs/GBK
Event: 0.055 Loading class sun/nio/cs/GBK done
Event: 0.055 Loading class sun/nio/cs/DoubleByte$Decoder
Event: 0.055 Loading class sun/nio/cs/DelegatableDecoder
Event: 0.056 Loading class sun/nio/cs/DelegatableDecoder done
Event: 0.056 Loading class sun/nio/cs/DoubleByte$Decoder done
Event: 0.056 Loading class sun/nio/cs/GBK$DecodeHolder
Event: 0.056 Loading class sun/nio/cs/GBK$DecodeHolder done
Event: 0.057 Loading class sun/nio/cs/DoubleByte
Event: 0.057 Loading class sun/nio/cs/DoubleByte done
Event: 0.060 Loading class sun/nio/cs/DoubleByte$Encoder
Event: 0.061 Loading class sun/nio/cs/DoubleByte$Encoder done
Event: 0.061 Loading class sun/nio/cs/GBK$EncodeHolder
Event: 0.061 Loading class sun/nio/cs/GBK$EncodeHolder done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (9 events):
Event: 0.037 Thread 0x00000292923bc460 Thread added: 0x00000292923bc460
Event: 0.065 Thread 0x00000292923bc460 Thread added: 0x0000029294e41170
Event: 0.065 Thread 0x00000292923bc460 Thread added: 0x0000029294e42030
Event: 0.065 Thread 0x00000292923bc460 Thread added: 0x0000029294e44f00
Event: 0.065 Thread 0x00000292923bc460 Thread added: 0x00000292ed594fc0
Event: 0.065 Thread 0x00000292923bc460 Thread added: 0x00000292ed595cd0
Event: 0.065 Thread 0x00000292923bc460 Thread added: 0x00000292ed597730
Event: 0.065 Thread 0x00000292923bc460 Thread added: 0x00000292ed5984b0
Event: 0.065 Thread 0x00000292923bc460 Thread added: 0x0000029294e4df30


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-4ea3ec03923acd65041d504ea01e5a54-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 18:53 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (5326M free)
TotalPageFile size 61318M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 39M, peak: 39M
current process commit charge ("private bytes"): 224M, peak: 224M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
