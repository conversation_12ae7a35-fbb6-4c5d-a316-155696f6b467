# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.4.2"
  }
  digests {
    sha256: "A\230;\352\262\226\005\200\223W7.xf\361X$P\267T\322\032N=\351=\260\252\240!Q\202"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.9.0"
  }
  digests {
    sha256: "\213\332>\343\250\210\207\325Ofy\373kl\327\210b\237s#J\311\034\213\276\331$\347!\354\205\270"
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.5.1"
  }
  digests {
    sha256: "K\004\264-,\037\201\300/\257\017{n\234\311\376\336\020\375\356\217f\023l\324\271\237\210\350\364\214\017"
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.5.7"
  }
  digests {
    sha256: "Y\307A\020\271\223x\210^\320bB\370\242m\243\005\352M\312S\355`\0265@\335\016.<fu"
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.4.2"
  }
  digests {
    sha256: "\312\ax\310\250\325\247\037s\215\201\000\202\205\372\305T\337\t\266\210\2205_h\027<8C\346\025?"
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.0"
  }
  digests {
    sha256: "-\345(\326\211\216\225\357\002\r\"\331\377\337\235\037w\313\335\223\371-9\337\252]\\C\260\303\021\310"
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.0.0"
  }
  digests {
    sha256: "\257\aW1\244\321{\352\242\236i\031Jk\252\365\363Q(\024\301\232\241\270\226]\031 \260\267\373\372"
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.0.0"
  }
  digests {
    sha256: "9aj=fU\037\301\205\205\021,\341\321*\024\254\017\265\210\250\232e(\315\227\204-\246\"\036\302"
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.1.0"
  }
  digests {
    sha256: "c*\016T\aF\035\347t@\223R\224\016)*)\0207rB\a\247\207\202\fw\332\367\323;r"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.5.1"
  }
  digests {
    sha256: "3\260\327=\302\360(\374\3535\231\272\312\276V<=\266\322o5\023\330\211YXcSjJ\310\300"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.5.1"
  }
  digests {
    sha256: "\024\242}_\270\241Ck\033}\354\030\276\272\246l\203\f\333\274\216(\250\034\345\370[|3\263\256\235"
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.0"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.8.22"
  }
  digests {
    sha256: "\003\245\303\226\\\303pQ\022\216d\344gH\343\224\266\275L\227\372\201\306\336o\307+\375D\343B\033"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.8.22"
  }
  digests {
    sha256: "\320\3026^$7\357p\363E\206\325\017\005WC\367\227\026\274\376e\344\274r9\315\322f\236\367\305"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "23.0.0"
  }
  digests {
    sha256: "{\017\031r@\202\313\374\274f\345\253\352+\233\311,\360\212\036\241\036\031\0313\355C\200\036\263\315\005"
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.3.0"
  }
  digests {
    sha256: "\253\375)\310Un[\3202Z\237v\232\271\351\321T\377JU\025\304v\315\325\242\250([\033\031\334"
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.9.0"
  }
  digests {
    sha256: "\025B\241\337{\351\b\311_\356\221\270\333\300?\331t\365?\021\330J\205\330\201\371ZRU\034@Q"
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "9999.0-empty-to-avoid-conflict-with-guava"
  }
  digests {
    sha256: "\263r\2407\324#\n\245\177\276\377\336\363\017\326\022?\234\f-\270]\n\316\320\f\221\271t\363?\231"
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.1.0"
  }
  digests {
    sha256: "\376\0227\277\002\235\006>\177)\3769\256\257s\357t\310\260\243e\204\206\374)\323\305C&e8\211"
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.1.0"
  }
  digests {
    sha256: "\335wa[\323\335\'Z\373\021\266-\362[\256F\261\vJ\021|\323yC\257E\275\313\370uXR"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.5.1"
  }
  digests {
    sha256: " \255\025 \366%\317E^j\375r\220\230\203\006\323\251\210n\372\231>\b`\373\253\364\273?{\332"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.5.1"
  }
  digests {
    sha256: "\204\201\024\037\227\360\346!=\323?\314\211\247\204\304\275\021\246\377}Gy\241\317j\016\237\275\277$\340"
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.5.1"
  }
  digests {
    sha256: "\356y!\003\312$\213\372\361P\304Z\223\207\036L\367\350\316\272\271\220\340\366/}\345\324\377/ \237"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.7.3"
  }
  digests {
    sha256: "Y\377\373&\276\341,2\332\334\372]B\f*}\270]2SQ\201(\261p\357\332rf\023%m"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.7.3"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.7.3"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.7.3"
  }
  digests {
    sha256: "\032\263\254\303\217>sU\304\371\321\354b\020zF\372s\310\231\363\a\r\005^]Cs\337\346~\022"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-play-services"
    version: "1.7.3"
  }
  digests {
    sha256: "d\326\352\032M\025\242\300\225\r\251\246\037I\352|Q&\216\347L;\035\335\304c\346\225TD\033$"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-tasks"
    version: "18.2.0"
  }
  digests {
    sha256: "\177*\252\217P h\352\365CV\312\222\256\300Bq\326\347\304\026\305,E\300\3224@\374\275\026T"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-basement"
    version: "18.5.0"
  }
  digests {
    sha256: "\243\337n\373)\276\262\377x\373|\336q*\307s\275l\334\337\311\203<\220\030\225\200O`\234\231^"
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.1.0"
  }
  digests {
    sha256: "\250;Y|\322@\235\301w\367\3712\367\304\b.\367\265P\213i\266\304\272q`~u\333\331\231q"
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.4.0"
  }
  digests {
    sha256: "2\361uX\213\326-\365g$HQm\236O=I5\320\002\f\r\225\027\225\214\237\373\327\302\a\345"
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.0.0"
  }
  digests {
    sha256: "\377\b\035-\267\335(\256\305\237t\223LQO\272\364\256Z\254RXI_\341\ra*6\"\370v"
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.6.1"
  }
  digests {
    sha256: "\202\306_Q0\252\253\303\252\002\314x\377}u\362a\354\341\236i\270\376\306\340wqr\363\346\311\215"
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.1.4"
  }
  digests {
    sha256: "\r\367\024\300\265\036Tq\016\277tn\264i\3233\027k\273<\262\237\200w]\303\312N\263\026%\022"
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.2.0"
  }
  digests {
    sha256: "\241\340Y\263\274\vC\245\215\354\016\376\315\312\250\234\202\322\274\245R\352[\254\366elF\350S\025~"
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.0.4"
  }
  digests {
    sha256: ">G\177M\3421\345\213%\365\251\222\363\276E\351}3,4\243\232\236>}Kx\256\n\302%o"
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
}
library {
  maven_library {
    groupId: "com.google.code.gson"
    artifactId: "gson"
    version: "2.8.6"
  }
  digests {
    sha256: "\310\373H9\005M(\v03\370\000\321\365\251}\342\360(\353\213\242\353E\212\322\207\3456\363\362_"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth"
    version: "21.4.0"
  }
  digests {
    sha256: "I8\360\026?\244\326R6z\372\315\365\230\2262\345\025\224O\3035N\f\374J\035@x\223\322\241"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-api-phone"
    version: "18.0.2"
  }
  digests {
    sha256: "\277\3378\016t\000\251\331P\313\244\336\334\025+\033\316aJ\20750\t\350Q\bt(\222|\302@"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-auth-base"
    version: "18.0.10"
  }
  digests {
    sha256: "\330\277\362\311\215#\2263\373U\002)\345\'w\224%\277\265\037\360K\226\254\017\022\323\254\272\v\037B"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-base"
    version: "18.5.0"
  }
  digests {
    sha256: "Y\245\300\302\332\0221\035u\331e\316\037A\224\230Sk\032\026\177\262\217\367\337\302\337\331\316\372AW"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-fido"
    version: "20.0.1"
  }
  digests {
    sha256: "\263Ro\f\256\332\251lRl\304\311\272\224\262)\332\325\257\343\0372\364ol\326h\2406$\375B"
  }
}
library {
  maven_library {
    groupId: "com.android.installreferrer"
    artifactId: "installreferrer"
    version: "2.2"
  }
  digests {
    sha256: "q\022\345\222\304)\224\227\207\301h\305\254br\021D\247;d\220PhEW\375\275\272]\323\266j"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-bom"
    version: "33.15.0"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-analytics"
    version: "22.4.0"
  }
  digests {
    sha256: "\256\361,\0355\271$\"q>E\366\360P\036\303\024\372\221t\301\224\250}\227\361\343\336\200\222\370\236"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-messaging"
    version: "24.1.1"
  }
  digests {
    sha256: "K\301\327\263\205\307\r\374\006\330c\302\321\020|<\206cS\247\030\020\364\036D\227\250\355\335\231\027\216"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common"
    version: "21.0.0"
  }
  digests {
    sha256: "7\222\207\327\027\023qQ$\223h\0339\216x\303A\2633\317\227N\350\032\030\261\325n|.8]"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-common-ktx"
    version: "21.0.0"
  }
  digests {
    sha256: "%\374\200\311\273\236\313\026r\220\207\030\302\224\257\314J\301\344tsg{\340`\307\225\340O\022\000f"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders"
    version: "17.0.0"
  }
  digests {
    sha256: "(*Zp?\233~\265e\b\335\351~\251\030\351]s1\213\025pP\364W\367\250m\312u\001P"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations"
    version: "18.0.0"
  }
  digests {
    sha256: "\225\006:\337\261\177\376I+\022\366\216s\002\bs\201M\201w\252U0i\312\326N\021\330\307\222\222"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement"
    version: "22.4.0"
  }
  digests {
    sha256: "\207\312\312\260e\236\221\362\316C[I\232\006\f\375\n\200t\350\302Z\212z~\237\242\276>G\aN"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-api"
    version: "22.4.0"
  }
  digests {
    sha256: "}\337\a\f\351FP\0166;\304\rV6\355\206P\256\365\232\003\251\3768|<\356\375\265\271\373\215"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk"
    version: "22.4.0"
  }
  digests {
    sha256: "\313\202\273_v\tQ\177\352\002 \3263\220\006\375]p\317\300\260\224\256\225\003\340\234\214{\002\331\202"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-ads-identifier"
    version: "18.0.0"
  }
  digests {
    sha256: "s\f\233g\344\370]\2738I\364\363\344*PG\316\341\365\234#&F\235\001\331m\017\275!\030\032"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-base"
    version: "22.4.0"
  }
  digests {
    sha256: "\245@4%\257\333\231\271\267\ry\020\336~\211l\235QH\001b\207m\246\227S\227\376y\331\203\323"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-impl"
    version: "22.4.0"
  }
  digests {
    sha256: "\327\000*\357A\362E\351}\'f\303\246m\312\031\352\vL\375\217$l;\212~\t\300\r\322|K"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-measurement-sdk-api"
    version: "22.4.0"
  }
  digests {
    sha256: "\276\004\002 $\336?\273/\231\227\033\364\2144\341\324\r\226S\311T\3567\301\361@s\320\205\247\316"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-stats"
    version: "17.0.2"
  }
  digests {
    sha256: "\335C\024\245?I\243x\354\024a\003\323b2\271luEM)Rc6\314\275\3612\224\027d\323"
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "T\nn-\307\2167\006\025\367\177\207,\017;p\n\032\312\035x\244\251\257}(\b\005\bH\306c"
  }
}
library {
  maven_library {
    groupId: "androidx.privacysandbox.ads"
    artifactId: "ads-adservices-java"
    version: "1.1.0-beta11"
  }
  digests {
    sha256: "\262\210]\265\335\255\233E\177\224\322\020A\332S&}\250\232\337\3448$+\240}\321\034\217\246>t"
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "guava"
    version: "31.1-android"
  }
  digests {
    sha256: "2\254.\327\t\331m\'\213].>\\\352\027\217\244\223\2319\305%\373du2\360\0230\215\263\t"
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "failureaccess"
    version: "1.0.1"
  }
  digests {
    sha256: "\241q\356LsM\322\332\203~K\026\276\235\364f\032\372\267*A\255\2571\353\204\337\332\3716\312&"
  }
}
library {
  maven_library {
    groupId: "com.google.code.findbugs"
    artifactId: "jsr305"
    version: "3.0.2"
  }
  digests {
    sha256: "vj\322\240x?&\207\226,\212\327L\356\3148\242\213\237r\242\320\205\356C\213x\023\351(\320\307"
  }
}
library {
  maven_library {
    groupId: "org.checkerframework"
    artifactId: "checker-qual"
    version: "3.12.0"
  }
  digests {
    sha256: "\377\020xZ\302\243W\354]\351\302\223\313\230*,\273`\\\003\t\352L\301\313\233\233\306\333\347\363\313"
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.26.0"
  }
  digests {
    sha256: "S\315\374\v\353-vo\340;x\360\261\035\002\005T\315A\230y\322\003\200\303\217\241\334\362\272\033P"
  }
}
library {
  maven_library {
    groupId: "com.google.j2objc"
    artifactId: "j2objc-annotations"
    version: "1.3"
  }
  digests {
    sha256: "!\2570\311\"g\275a\"\300\340\264\322\f\314\266d\0327\352\371V\306T\016\304q\325\204\346J{"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-components"
    version: "18.0.0"
  }
  digests {
    sha256: "\307\304\212:\200\364JI\236\275ds\274\374}\325\244^\365#\372\276$\024\246%\025\377<d\tr"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-installations-interop"
    version: "17.1.1"
  }
  digests {
    sha256: "\372\306Ph\017y!\364\253\222\360\273!\240\2224\261,\332\357\253,\367\001\210(\035~\245+\215\255"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-measurement-connector"
    version: "19.0.0"
  }
  digests {
    sha256: "\333\247Mk\371FG\3569{\367\257\262\253\a\366\376\215\023\025~Vx_\245@\242\241>\330,\231"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-annotations"
    version: "16.2.0"
  }
  digests {
    sha256: "F\366\325\337\335,\317<@\336\211z\024\275\227y1L3\031\364K\3751\347\340\242\r\223Z^>"
  }
}
library {
  maven_library {
    groupId: "javax.inject"
    artifactId: "javax.inject"
    version: "1"
  }
  digests {
    sha256: "\221\307pD\245\fH\0266\303-\221o\330\234\221\030\247!\2259\004R\310\020e\b\017\225}\347\377"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-datatransport"
    version: "18.2.0"
  }
  digests {
    sha256: "\262\262\217k\241s\365\340\304\376=6\242\327\356R9\307\254\277AC\265I\354\3601\243H[5\273"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-json"
    version: "18.0.0"
  }
  digests {
    sha256: "\200\256\316~\036\365\211W\312/\301\225{\311 \216\311*:\225( \0231\323\306>1\202W\017\227"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-encoders-proto"
    version: "16.0.0"
  }
  digests {
    sha256: ")=\271j\r\035C\3603\026x\201\2668\330\375\350D\344\345I_Q\001\317R)We)^\016"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-iid-interop"
    version: "17.1.0"
  }
  digests {
    sha256: "\v|7!\310Kb\347\004\0250r9\355J\177\231\211\211\bK\362\203?\220\271\365\276\243\tZ\005"
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-api"
    version: "3.1.0"
  }
  digests {
    sha256: "}\257\303\237\016\2505G3f\254\303F\367\346\177\310D?Ld]\231\234>\230{\312\326\270\214{"
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-backend-cct"
    version: "3.1.9"
  }
  digests {
    sha256: "\a\243 %\246[\b\356~\021\321M\3059\247X\266g\023\360\301\3219\000\250\025\231\316\255\272\364M"
  }
}
library {
  maven_library {
    groupId: "com.google.android.datatransport"
    artifactId: "transport-runtime"
    version: "3.1.9"
  }
  digests {
    sha256: "At\\[\217B}$C\220\025\270Oe\032\324 q\211\221\206}*\374&,&@\t\270\002\301"
  }
}
library {
  maven_library {
    groupId: "com.google.android.gms"
    artifactId: "play-services-cloud-messaging"
    version: "17.2.0"
  }
  digests {
    sha256: "\'%^\177\351pd\203\201k\025\215\262\\\363\031\366\242j\005f\376\377AY|\350\200z5\0167"
  }
}
library {
  maven_library {
    groupId: "com.google.firebase"
    artifactId: "firebase-core"
    version: "21.1.0"
  }
  digests {
    sha256: "kr\200\347\353\023\252@\311\237\270\224\002\232\204\3034\226a\004~\262\320:\234\362E\371\027R\342\300"
  }
}
library {
  digests {
    sha256: "e\261\350\270\370rE\336\311\243\203\n\330\312$\310\350N\031QwK3\232\267,h\227\355\220\234\337"
  }
}
library_dependencies {
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
  library_dep_index: 8
  library_dep_index: 9
  library_dep_index: 10
  library_dep_index: 11
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 14
}
library_dependencies {
  library_index: 1
  library_dep_index: 15
}
library_dependencies {
  library_index: 15
  library_dep_index: 16
}
library_dependencies {
  library_index: 16
  library_dep_index: 17
  library_dep_index: 18
}
library_dependencies {
  library_index: 2
  library_dep_index: 1
  library_dep_index: 19
  library_dep_index: 11
  library_dep_index: 20
  library_dep_index: 12
  library_dep_index: 21
  library_dep_index: 22
}
library_dependencies {
  library_index: 19
  library_dep_index: 16
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
}
library_dependencies {
  library_index: 20
  library_dep_index: 1
  library_dep_index: 23
}
library_dependencies {
  library_index: 12
  library_dep_index: 1
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 24
  library_dep_index: 1
}
library_dependencies {
  library_index: 25
  library_dep_index: 1
  library_dep_index: 24
}
library_dependencies {
  library_index: 26
  library_dep_index: 1
}
library_dependencies {
  library_index: 21
  library_dep_index: 1
  library_dep_index: 11
}
library_dependencies {
  library_index: 22
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 16
  library_dep_index: 2
}
library_dependencies {
  library_index: 3
  library_dep_index: 1
}
library_dependencies {
  library_index: 4
  library_dep_index: 1
  library_dep_index: 11
  library_dep_index: 2
  library_dep_index: 12
  library_dep_index: 13
  library_dep_index: 27
  library_dep_index: 8
  library_dep_index: 28
  library_dep_index: 16
}
library_dependencies {
  library_index: 13
  library_dep_index: 1
  library_dep_index: 16
  library_dep_index: 27
}
library_dependencies {
  library_index: 27
  library_dep_index: 1
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 8
  library_dep_index: 16
  library_dep_index: 30
}
library_dependencies {
  library_index: 29
  library_dep_index: 24
  library_dep_index: 25
  library_dep_index: 26
}
library_dependencies {
  library_index: 8
  library_dep_index: 1
  library_dep_index: 24
  library_dep_index: 26
  library_dep_index: 16
}
library_dependencies {
  library_index: 30
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 33
}
library_dependencies {
  library_index: 31
  library_dep_index: 34
}
library_dependencies {
  library_index: 34
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 17
  library_dep_index: 33
}
library_dependencies {
  library_index: 32
  library_dep_index: 30
  library_dep_index: 34
  library_dep_index: 31
  library_dep_index: 35
}
library_dependencies {
  library_index: 35
  library_dep_index: 31
  library_dep_index: 32
  library_dep_index: 36
  library_dep_index: 33
}
library_dependencies {
  library_index: 36
  library_dep_index: 37
}
library_dependencies {
  library_index: 37
  library_dep_index: 11
  library_dep_index: 2
  library_dep_index: 5
}
library_dependencies {
  library_index: 5
  library_dep_index: 4
  library_dep_index: 1
  library_dep_index: 19
  library_dep_index: 11
  library_dep_index: 22
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 27
  library_dep_index: 38
  library_dep_index: 8
  library_dep_index: 39
  library_dep_index: 16
}
library_dependencies {
  library_index: 38
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 29
  library_dep_index: 13
  library_dep_index: 11
}
library_dependencies {
  library_index: 39
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 40
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 11
}
library_dependencies {
  library_index: 33
  library_dep_index: 16
  library_dep_index: 41
}
library_dependencies {
  library_index: 41
  library_dep_index: 16
}
library_dependencies {
  library_index: 28
  library_dep_index: 1
}
library_dependencies {
  library_index: 6
  library_dep_index: 11
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 42
  library_dep_index: 43
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 11
}
library_dependencies {
  library_index: 43
  library_dep_index: 42
  library_dep_index: 44
  library_dep_index: 11
}
library_dependencies {
  library_index: 44
  library_dep_index: 1
}
library_dependencies {
  library_index: 7
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 40
}
library_dependencies {
  library_index: 9
  library_dep_index: 11
  library_dep_index: 1
  library_dep_index: 45
  library_dep_index: 2
  library_dep_index: 46
}
library_dependencies {
  library_index: 45
  library_dep_index: 12
  library_dep_index: 46
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 28
}
library_dependencies {
  library_index: 10
  library_dep_index: 9
  library_dep_index: 11
  library_dep_index: 2
}
library_dependencies {
  library_index: 14
  library_dep_index: 1
}
library_dependencies {
  library_index: 47
  library_dep_index: 1
  library_dep_index: 0
  library_dep_index: 48
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 2
  library_dep_index: 7
  library_dep_index: 51
  library_dep_index: 19
  library_dep_index: 5
  library_dep_index: 12
  library_dep_index: 52
  library_dep_index: 53
  library_dep_index: 42
  library_dep_index: 54
}
library_dependencies {
  library_index: 48
  library_dep_index: 1
}
library_dependencies {
  library_index: 49
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 40
  library_dep_index: 11
}
library_dependencies {
  library_index: 50
  library_dep_index: 0
  library_dep_index: 2
  library_dep_index: 55
}
library_dependencies {
  library_index: 51
  library_dep_index: 2
  library_dep_index: 11
  library_dep_index: 56
}
library_dependencies {
  library_index: 56
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 57
  library_dep_index: 38
  library_dep_index: 58
  library_dep_index: 59
}
library_dependencies {
  library_index: 57
  library_dep_index: 1
}
library_dependencies {
  library_index: 58
  library_dep_index: 1
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 40
  library_dep_index: 11
}
library_dependencies {
  library_index: 53
  library_dep_index: 1
  library_dep_index: 2
  library_dep_index: 11
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
  library_dep_index: 5
  library_dep_index: 52
  library_dep_index: 2
  library_dep_index: 11
}
library_dependencies {
  library_index: 61
  library_dep_index: 5
  library_dep_index: 38
  library_dep_index: 62
  library_dep_index: 63
  library_dep_index: 64
  library_dep_index: 37
  library_dep_index: 65
  library_dep_index: 36
}
library_dependencies {
  library_index: 62
  library_dep_index: 64
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 64
  library_dep_index: 11
  library_dep_index: 2
  library_dep_index: 5
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 63
  library_dep_index: 11
  library_dep_index: 64
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 65
  library_dep_index: 64
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 67
  library_dep_index: 68
  library_dep_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
  library_dep_index: 73
}
library_dependencies {
  library_index: 68
  library_dep_index: 74
  library_dep_index: 75
  library_dep_index: 76
}
library_dependencies {
  library_index: 74
  library_dep_index: 11
  library_dep_index: 56
  library_dep_index: 77
  library_dep_index: 37
  library_dep_index: 78
  library_dep_index: 79
  library_dep_index: 80
  library_dep_index: 81
}
library_dependencies {
  library_index: 77
  library_dep_index: 37
}
library_dependencies {
  library_index: 78
  library_dep_index: 37
}
library_dependencies {
  library_index: 79
  library_dep_index: 11
  library_dep_index: 2
  library_dep_index: 82
  library_dep_index: 83
  library_dep_index: 77
  library_dep_index: 64
  library_dep_index: 37
  library_dep_index: 78
  library_dep_index: 81
  library_dep_index: 36
  library_dep_index: 84
}
library_dependencies {
  library_index: 82
  library_dep_index: 1
  library_dep_index: 22
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 83
}
library_dependencies {
  library_index: 83
  library_dep_index: 1
  library_dep_index: 20
  library_dep_index: 22
  library_dep_index: 82
  library_dep_index: 23
  library_dep_index: 16
  library_dep_index: 31
  library_dep_index: 82
}
library_dependencies {
  library_index: 81
  library_dep_index: 56
  library_dep_index: 37
}
library_dependencies {
  library_index: 84
  library_dep_index: 85
  library_dep_index: 23
  library_dep_index: 86
  library_dep_index: 87
  library_dep_index: 88
  library_dep_index: 89
}
library_dependencies {
  library_index: 80
  library_dep_index: 37
  library_dep_index: 78
}
library_dependencies {
  library_index: 75
  library_dep_index: 77
  library_dep_index: 37
  library_dep_index: 78
  library_dep_index: 80
  library_dep_index: 36
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 90
  library_dep_index: 73
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 84
  library_dep_index: 16
}
library_dependencies {
  library_index: 70
  library_dep_index: 35
  library_dep_index: 90
  library_dep_index: 93
  library_dep_index: 1
  library_dep_index: 20
  library_dep_index: 16
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 90
  library_dep_index: 93
  library_dep_index: 1
  library_dep_index: 88
}
library_dependencies {
  library_index: 93
  library_dep_index: 94
}
library_dependencies {
  library_index: 71
  library_dep_index: 70
  library_dep_index: 33
  library_dep_index: 90
  library_dep_index: 93
}
library_dependencies {
  library_index: 73
  library_dep_index: 36
  library_dep_index: 93
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 90
  library_dep_index: 91
  library_dep_index: 16
}
library_dependencies {
  library_index: 91
  library_dep_index: 36
  library_dep_index: 93
}
library_dependencies {
  library_index: 92
  library_dep_index: 37
  library_dep_index: 93
}
library_dependencies {
  library_index: 76
  library_dep_index: 11
  library_dep_index: 37
  library_dep_index: 78
  library_dep_index: 79
}
library_dependencies {
  library_index: 69
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 90
  library_dep_index: 95
  library_dep_index: 72
  library_dep_index: 96
  library_dep_index: 97
  library_dep_index: 98
  library_dep_index: 73
  library_dep_index: 91
  library_dep_index: 92
  library_dep_index: 1
  library_dep_index: 99
  library_dep_index: 100
  library_dep_index: 101
  library_dep_index: 64
  library_dep_index: 37
  library_dep_index: 36
  library_dep_index: 102
  library_dep_index: 81
  library_dep_index: 88
  library_dep_index: 16
}
library_dependencies {
  library_index: 95
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 100
  library_dep_index: 1
}
library_dependencies {
  library_index: 99
  library_dep_index: 1
}
library_dependencies {
  library_index: 101
  library_dep_index: 99
  library_dep_index: 1
  library_dep_index: 94
  library_dep_index: 72
  library_dep_index: 97
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
}
library_dependencies {
  library_index: 97
  library_dep_index: 1
  library_dep_index: 72
}
library_dependencies {
  library_index: 100
  library_dep_index: 99
  library_dep_index: 101
  library_dep_index: 72
  library_dep_index: 96
  library_dep_index: 1
}
library_dependencies {
  library_index: 96
  library_dep_index: 1
  library_dep_index: 72
}
library_dependencies {
  library_index: 98
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 102
  library_dep_index: 37
  library_dep_index: 36
}
library_dependencies {
  library_index: 103
  library_dep_index: 68
}
library_dependencies {
  library_index: 104
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 50
  dependency_index: 66
  dependency_index: 67
  dependency_index: 68
  dependency_index: 69
  dependency_index: 103
  dependency_index: 104
  dependency_index: 60
  dependency_index: 61
  dependency_index: 47
}
