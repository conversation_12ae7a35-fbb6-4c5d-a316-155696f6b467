#ifndef MEMORY_TRAP_SDK_H
#define MEMORY_TRAP_SDK_H

#include <vector>
#include <memory>
#include <mutex>
#include <thread>
#include <atomic>
#include <chrono>
#include <signal.h>
#include <string>
#include <cstdint>
#include <functional>
#include <queue>

namespace MemoryTrapSDK {

/**
 * 内存区域类型枚举
 * 基于GG修改器的内存区域分类
 */
enum class MemoryRegion {
    ANONYMOUS,      // A: Anonymous - 匿名映射（最重要）
    CPP_DATA,       // Cd: C++ .data - 全局变量段
    CPP_BSS,        // Cb: C++ .bss - 未初始化数据段
    CPP_HEAP,       // Ch: C++ heap - C++堆
    CPP_ALLOC,      // Ca: C++ alloc - C++分配
    JAVA_HEAP,      // Jh: Java heap - Java堆
    STACK,          // S: Stack - 栈
    OTHER           // 其他区域
};

/**
 * 陷阱配置结构
 */
struct TrapConfig {
    MemoryRegion region;        // 内存区域类型
    size_t trap_count;          // 陷阱数量
    size_t trap_size;           // 每个陷阱大小
    uint32_t target_value;      // 目标值（如1111）
    uint32_t fill_interval;     // 填充间隔（每N个位置放一个目标值）
    bool enable_protection;     // 是否启用内存保护
};

/**
 * 陷阱信息结构
 */
struct TrapInfo {
    void* address;              // 陷阱地址
    size_t size;                // 陷阱大小
    MemoryRegion region;        // 所属内存区域
    uint32_t target_count;      // 目标值数量
    bool is_protected;          // 是否受保护
    std::chrono::steady_clock::time_point created_time; // 创建时间
};

/**
 * 访问记录结构
 */
struct AccessRecord {
    void* addr;                 // 访问地址
    std::chrono::steady_clock::time_point time; // 访问时间
    uint32_t step_size;         // 与前一次访问的步长
};

/**
 * 行为分析结果
 */
struct BehaviorAnalysis {
    bool is_modifier_scan;      // 是否为修改器扫描
    float step_ratio;           // 有效步长比例
    int access_frequency;       // 访问频率（次/秒）
    bool is_sequential;         // 是否连续访问
    size_t total_accesses;      // 总访问次数
    std::string reason;         // 判定原因
};

/**
 * 检测事件结构
 */
struct DetectionEvent {
    void* fault_address;        // 故障地址
    TrapInfo* trap_info;        // 相关陷阱信息
    std::chrono::steady_clock::time_point timestamp; // 时间戳
    std::string description;    // 事件描述
    BehaviorAnalysis analysis;  // 行为分析结果
};

/**
 * 检测回调函数类型
 */
using DetectionCallback = std::function<void(const DetectionEvent& event)>;

/**
 * 陷阱管理器类
 * 负责创建、管理和监控内存陷阱
 */
class TrapManager {
public:
    TrapManager();
    ~TrapManager();

    /**
     * 初始化陷阱管理器
     * @return 是否成功
     */
    bool initialize();

    /**
     * 创建指定区域的陷阱
     * @param config 陷阱配置
     * @return 是否成功
     */
    bool createTraps(const TrapConfig& config);

    /**
     * 销毁所有陷阱
     */
    void destroyTraps();

    /**
     * 启动检测
     * @return 是否成功
     */
    bool startDetection();

    /**
     * 停止检测
     */
    void stopDetection();

    /**
     * 设置检测回调函数
     * @param callback 检测回调函数
     */
    void setDetectionCallback(DetectionCallback callback);

    /**
     * 获取活动陷阱数量
     * @return 活动陷阱数量
     */
    size_t getActiveTrapCount() const;

    /**
     * 获取总陷阱数量
     * @return 总陷阱数量
     */
    size_t getTotalTrapCount() const;
    
    /**
     * 获取陷阱信息
     * @return 陷阱信息列表
     */
    std::vector<TrapInfo> getTrapInfos() const;
    
    /**
     * 清理资源
     */
    void cleanup();

private:
    // 内部实现
    struct Impl {
        // 成员变量
        std::vector<TrapInfo> traps;
        DetectionCallback callback;
        mutable std::mutex traps_mutex;
        std::atomic<bool> is_detecting{false};

        // 信号处理相关
        struct sigaction original_sigsegv;
        struct sigaction original_sigbus;
        static Impl* instance;

        // 统计信息
        std::atomic<size_t> detection_count{0};
        std::chrono::steady_clock::time_point last_detection_time;

        // 行为分析相关
        std::vector<AccessRecord> access_history;
        std::mutex history_mutex;
        static const size_t MAX_HISTORY_SIZE = 10;  // 最多保留10条记录

        // 异步分析相关
        std::queue<void*> analysis_queue;
        std::mutex queue_mutex;
        std::thread analysis_thread;
        std::atomic<bool> analysis_running{false};
        
        // 成员函数
        bool startDetection(DetectionCallback callback);
        void stopDetection();
        void cleanup();
        std::vector<TrapInfo> getTrapInfos();
        
        // 辅助函数
        size_t createAnonymousTraps(const TrapConfig& config);
        size_t createDataSegmentTraps(const TrapConfig& config);
        size_t createBssSegmentTraps(const TrapConfig& config);
        uint32_t fillTrapData(void* addr, size_t size, uint32_t target_value, uint32_t interval);
        bool protectTrap(void* addr, size_t size);
        
        // 构造和析构函数
        Impl();
        ~Impl();
        
        // 信号处理相关函数
        static void signalHandler(int signum, siginfo_t* info, void* context);
        static bool installSignalHandler();
        static void uninstallSignalHandler();
        static void initAnalysisThread();
        static void stopAnalysisThread();
        static void analyzeAccessPattern(void* fault_addr);
    };
    std::unique_ptr<Impl> impl_;
    
    // 禁止拷贝
    TrapManager(const TrapManager&) = delete;
    TrapManager& operator=(const TrapManager&) = delete;
};

/**
 * 内存陷阱SDK主类
 * 提供简单易用的API接口
 */
class MemoryTrapSDK {
public:
    /**
     * 获取SDK实例（单例模式）
     */
    static MemoryTrapSDK& getInstance();

    // 禁用拷贝构造和赋值
    MemoryTrapSDK(const MemoryTrapSDK&) = delete;
    MemoryTrapSDK& operator=(const MemoryTrapSDK&) = delete;

    /**
     * 初始化SDK
     * @return 是否成功
     */
    bool initialize();

    /**
     * 快速部署针对GG修改器的陷阱
     * @param target_value 目标值（默认1111）
     * @param callback 检测回调
     * @return 部署的陷阱总数
     */
    size_t deployAntiGGTraps(uint32_t target_value = 1111, DetectionCallback callback = nullptr);

    /**
     * 自定义部署陷阱
     * @param configs 陷阱配置列表
     * @param callback 检测回调
     * @return 部署的陷阱总数
     */
    size_t deployCustomTraps(const std::vector<TrapConfig>& configs, DetectionCallback callback = nullptr);

    /**
     * 启动检测
     * @param callback 检测回调函数
     * @return 是否成功
     */
    bool startDetection(DetectionCallback callback);
    
    /**
     * 启动检测（使用已设置的回调）
     * @return 是否成功
     */
    bool startDetection();
    
    /**
     * 停止检测
     */
    void stopDetection();
    
    /**
     * 设置检测回调函数
     * @param callback 检测回调函数
     */
    void setDetectionCallback(DetectionCallback callback);
    
    /**
     * 获取活动陷阱数量
     * @return 活动陷阱数量
     */
    size_t getActiveTrapCount() const;

    /**
     * 获取总陷阱数量
     * @return 总陷阱数量
     */
    size_t getTotalTrapCount() const;

    /**
     * 获取统计信息
     */
    struct Statistics {
        size_t total_traps;         // 总陷阱数
        size_t protected_traps;     // 受保护陷阱数
        size_t detection_count;     // 检测次数
        std::chrono::steady_clock::time_point last_detection; // 最后检测时间
    };
    Statistics getStatistics() const;

    /**
     * 清理SDK
     */
    void cleanup();

private:
    MemoryTrapSDK();
    ~MemoryTrapSDK();

    std::unique_ptr<TrapManager> trap_manager_;
    DetectionCallback detection_callback_;
    mutable std::mutex mutex_;
    std::atomic<bool> is_initialized_{false};
    std::atomic<bool> is_detecting_{false};
    Statistics statistics_;
};

// 获取陷阱访问统计
std::string getTrapAccessStatistics();

    // 全局函数
    bool initializeMemoryTrapSystem();
    void shutdownMemoryTrapSystem();

} // namespace MemoryTrapSDK

#endif // MEMORY_TRAP_SDK_H
