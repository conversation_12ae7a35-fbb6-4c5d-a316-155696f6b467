#include <iostream>
#include <vector>
#include <cstring>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <fstream>
#include <sstream>

/**
 * 简单的内存扫描测试工具
 * 用于测试我们的内存陷阱系统是否能正确检测到扫描行为
 */

class MemoryScanner {
private:
    pid_t target_pid;
    
public:
    MemoryScanner(pid_t pid) : target_pid(pid) {}
    
    // 获取进程的内存映射信息
    std::vector<std::pair<uintptr_t, uintptr_t>> getMemoryMaps() {
        std::vector<std::pair<uintptr_t, uintptr_t>> maps;
        
        std::string maps_file = "/proc/" + std::to_string(target_pid) + "/maps";
        std::ifstream file(maps_file);
        std::string line;
        
        while (std::getline(file, line)) {
            // 解析内存映射行，格式：address_start-address_end permissions ...
            size_t dash_pos = line.find('-');
            if (dash_pos != std::string::npos) {
                std::string start_str = line.substr(0, dash_pos);
                std::string end_str = line.substr(dash_pos + 1);
                size_t space_pos = end_str.find(' ');
                if (space_pos != std::string::npos) {
                    end_str = end_str.substr(0, space_pos);
                }
                
                uintptr_t start = std::stoull(start_str, nullptr, 16);
                uintptr_t end = std::stoull(end_str, nullptr, 16);
                
                // 只关注可读写的内存区域
                if (line.find("rw") != std::string::npos) {
                    maps.push_back({start, end});
                }
            }
        }
        
        return maps;
    }
    
    // 扫描指定值
    void scanForValue(uint32_t target_value) {
        std::cout << "🔍 开始扫描目标值: " << target_value << std::endl;
        
        auto maps = getMemoryMaps();
        std::cout << "📍 找到 " << maps.size() << " 个可读写内存区域" << std::endl;
        
        std::string mem_file = "/proc/" + std::to_string(target_pid) + "/mem";
        int fd = open(mem_file.c_str(), O_RDONLY);
        
        if (fd == -1) {
            std::cerr << "❌ 无法打开内存文件: " << mem_file << std::endl;
            return;
        }
        
        int found_count = 0;
        
        for (const auto& map : maps) {
            uintptr_t start = map.first;
            uintptr_t end = map.second;
            size_t size = end - start;
            
            std::cout << "🔍 扫描区域: 0x" << std::hex << start << " - 0x" << end 
                      << " (大小: " << std::dec << size << " 字节)" << std::endl;
            
            // 分块读取内存
            const size_t chunk_size = 4096;
            std::vector<uint8_t> buffer(chunk_size);
            
            for (uintptr_t addr = start; addr < end; addr += chunk_size) {
                size_t read_size = std::min(chunk_size, (size_t)(end - addr));
                
                if (pread(fd, buffer.data(), read_size, addr) == (ssize_t)read_size) {
                    // 在缓冲区中搜索目标值
                    for (size_t i = 0; i <= read_size - sizeof(uint32_t); i += sizeof(uint32_t)) {
                        uint32_t value = *reinterpret_cast<uint32_t*>(buffer.data() + i);
                        if (value == target_value) {
                            uintptr_t found_addr = addr + i;
                            std::cout << "🎯 找到目标值 " << target_value 
                                      << " 在地址: 0x" << std::hex << found_addr << std::dec << std::endl;
                            found_count++;
                        }
                    }
                }
                
                // 添加小延迟，模拟真实的扫描行为
                usleep(1000); // 1ms
            }
        }
        
        close(fd);
        std::cout << "✅ 扫描完成，共找到 " << found_count << " 个匹配项" << std::endl;
    }
    
    // 连续扫描多个值（模拟GG修改器的行为）
    void continuousScan() {
        std::vector<uint32_t> common_values = {1111, 999, 1000, 1234, 9999, 12345};
        
        std::cout << "🚀 开始连续扫描（模拟GG修改器）..." << std::endl;
        
        for (uint32_t value : common_values) {
            std::cout << "\n--- 扫描值: " << value << " ---" << std::endl;
            scanForValue(value);
            sleep(2); // 每次扫描间隔2秒
        }
    }
};

int main(int argc, char* argv[]) {
    if (argc != 2) {
        std::cout << "用法: " << argv[0] << " <目标进程PID>" << std::endl;
        std::cout << "示例: " << argv[0] << " 12345" << std::endl;
        return 1;
    }
    
    pid_t target_pid = std::atoi(argv[1]);
    
    std::cout << "🎯 内存扫描测试工具" << std::endl;
    std::cout << "目标进程PID: " << target_pid << std::endl;
    
    MemoryScanner scanner(target_pid);
    
    std::cout << "\n选择测试模式:" << std::endl;
    std::cout << "1. 扫描单个值 (1111)" << std::endl;
    std::cout << "2. 连续扫描多个值" << std::endl;
    std::cout << "请输入选择 (1-2): ";
    
    int choice;
    std::cin >> choice;
    
    switch (choice) {
        case 1:
            scanner.scanForValue(1111);
            break;
        case 2:
            scanner.continuousScan();
            break;
        default:
            std::cout << "无效选择" << std::endl;
            return 1;
    }
    
    return 0;
}
