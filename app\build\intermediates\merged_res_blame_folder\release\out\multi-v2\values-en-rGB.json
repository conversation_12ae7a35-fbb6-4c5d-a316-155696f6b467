{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-en-rGB\\values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,381,481,589,673,773,888,966,1041,1132,1225,1320,1414,1514,1607,1702,1796,1887,1978,2060,2163,2266,2365,2470,2574,2678,2834,9315", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "376,476,584,668,768,883,961,1036,1127,1220,1315,1409,1509,1602,1697,1791,1882,1973,2055,2158,2261,2360,2465,2569,2673,2829,2929,9393"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4285", "endColumns": "137", "endOffsets": "4418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9398", "endColumns": "100", "endOffsets": "9494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3303,3408,3560,3685,3792,3943,4066,4182,4423,4582,4687,4839,4964,5110,5258,5321,5383", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "3403,3555,3680,3787,3938,4061,4177,4280,4577,4682,4834,4959,5105,5253,5316,5378,5457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,402,517,596,661,751,818,877,967,1031,1094,1163,1227,1281,1393,1451,1513,1567,1639,1761,1848,1929,2039,2116,2197,2288,2355,2421,2491,2568,2655,2726,2803,2872,2941,3032,3104,3193,3282,3356,3428,3514,3564,3630,3710,3794,3856,3920,3983,4083,4180,4272,4371", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "222,299,397,512,591,656,746,813,872,962,1026,1089,1158,1222,1276,1388,1446,1508,1562,1634,1756,1843,1924,2034,2111,2192,2283,2350,2416,2486,2563,2650,2721,2798,2867,2936,3027,3099,3188,3277,3351,3423,3509,3559,3625,3705,3789,3851,3915,3978,4078,4175,4267,4366,4444"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2934,3011,3109,3224,5462,5527,5617,5684,5743,5833,5897,5960,6029,6093,6147,6259,6317,6379,6433,6505,6627,6714,6795,6905,6982,7063,7154,7221,7287,7357,7434,7521,7592,7669,7738,7807,7898,7970,8059,8148,8222,8294,8380,8430,8496,8576,8660,8722,8786,8849,8949,9046,9138,9237", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,76,97,114,78,64,89,66,58,89,63,62,68,63,53,111,57,61,53,71,121,86,80,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,77", "endOffsets": "272,3006,3104,3219,3298,5522,5612,5679,5738,5828,5892,5955,6024,6088,6142,6254,6312,6374,6428,6500,6622,6709,6790,6900,6977,7058,7149,7216,7282,7352,7429,7516,7587,7664,7733,7802,7893,7965,8054,8143,8217,8289,8375,8425,8491,8571,8655,8717,8781,8844,8944,9041,9133,9232,9310"}}]}]}