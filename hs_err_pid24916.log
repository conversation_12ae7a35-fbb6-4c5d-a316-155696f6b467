#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=24916, tid=42448
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-84b1e07cd02b50c122ef8ebde43307f8-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:21:12 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 0.868330 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000002d1b5a95d70):  JavaThread "main"             [_thread_in_vm, id=42448, stack(0x000000f184300000,0x000000f184400000) (1024K)]

Stack: [0x000000f184300000,0x000000f184400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0x8a41ee]
V  [jvm.dll+0x670575]
V  [jvm.dll+0x1e474c]
V  [jvm.dll+0x1e451e]
V  [jvm.dll+0x672e72]
V  [jvm.dll+0x672c92]
V  [jvm.dll+0x670f4e]
V  [jvm.dll+0x3becef]
V  [jvm.dll+0x20d78b]
V  [jvm.dll+0x5ae5b6]
V  [jvm.dll+0x821706]
V  [jvm.dll+0x4718d6]
V  [jvm.dll+0x4777a8]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 1128  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.7 (0 bytes) @ 0x000002d1c07b9691 [0x000002d1c07b95c0+0x00000000000000d1]
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;+27 java.base@21.0.7
j  java.security.SecureClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/CodeSource;)Ljava/lang/Class;+12 java.base@21.0.7
j  java.net.URLClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class;+220 java.base@21.0.7
j  java.net.URLClassLoader$1.run()Ljava/lang/Class;+43 java.base@21.0.7
j  java.net.URLClassLoader$1.run()Ljava/lang/Object;+1 java.base@21.0.7
J 1165 c1 java.security.AccessController.executePrivileged(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object; java.base@21.0.7 (65 bytes) @ 0x000002d1b8e478c4 [0x000002d1b8e47760+0x0000000000000164]
j  java.security.AccessController.doPrivileged(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;)Ljava/lang/Object;+13 java.base@21.0.7
j  java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class;+13 java.base@21.0.7
J 1183 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class; java.base@21.0.7 (121 bytes) @ 0x000002d1b8e4ce24 [0x000002d1b8e4c860+0x00000000000005c4]
J 1013 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.7 (7 bytes) @ 0x000002d1b8e03454 [0x000002d1b8e03340+0x0000000000000114]
v  ~StubRoutines::call_stub 0x000002d1c01a100d
j  org.eclipse.osgi.storage.BundleInfo.restoreGeneration(JLjava/io/File;ZLorg/eclipse/osgi/storage/ContentProvider$Type;ZLjava/util/Map;JZ)Lorg/eclipse/osgi/storage/BundleInfo$Generation;+8
j  org.eclipse.osgi.storage.Storage.loadGenerations(Ljava/io/DataInputStream;[Ljava/lang/String;)Ljava/util/Map;+642
j  org.eclipse.osgi.storage.Storage.<init>(Lorg/eclipse/osgi/internal/framework/EquinoxContainer;[Ljava/lang/String;)V+561
j  org.eclipse.osgi.storage.Storage.createStorage(Lorg/eclipse/osgi/internal/framework/EquinoxContainer;)Lorg/eclipse/osgi/storage/Storage;+11
j  org.eclipse.osgi.internal.framework.EquinoxContainer.<init>(Ljava/util/Map;Lorg/osgi/framework/connect/ModuleConnector;)V+146
j  org.eclipse.osgi.launch.Equinox.<init>(Ljava/util/Map;Lorg/osgi/framework/connect/ModuleConnector;)V+10
j  org.eclipse.osgi.launch.Equinox.<init>(Ljava/util/Map;)V+3
j  org.eclipse.core.runtime.adaptor.EclipseStarter.startup([Ljava/lang/String;Ljava/lang/Runnable;)Lorg/osgi/framework/BundleContext;+28
j  org.eclipse.core.runtime.adaptor.EclipseStarter.run([Ljava/lang/String;Ljava/lang/Runnable;)Ljava/lang/Object;+21
j  java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+11 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x000002d1ce08a800.invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+54 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x000002d1ce002c00.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+22 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invokeImpl(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+72 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+23 java.base@21.0.7
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+102 java.base@21.0.7
j  org.eclipse.equinox.launcher.Main.invokeFramework([Ljava/lang/String;[Ljava/net/URL;)V+155
j  org.eclipse.equinox.launcher.Main.basicRun([Ljava/lang/String;)V+191
j  org.eclipse.equinox.launcher.Main.run([Ljava/lang/String;)I+4
j  org.eclipse.equinox.launcher.Main.main([Ljava/lang/String;)V+10
v  ~StubRoutines::call_stub 0x000002d1c01a100d

Compiled method (n/a) 898 1128     n 0       java.lang.ClassLoader::defineClass1 (native)
 total in heap  [0x000002d1c07b9410,0x000002d1c07b9898] = 1160
 relocation     [0x000002d1c07b9570,0x000002d1c07b95a8] = 56
 main code      [0x000002d1c07b95c0,0x000002d1c07b9896] = 726
 stub code      [0x000002d1c07b9896,0x000002d1c07b9898] = 2

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000002d1cd431000} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # parm0:    rdx:rdx   = 'java/lang/ClassLoader'
  # parm1:    r8:r8     = 'java/lang/String'
  # parm2:    r9:r9     = '[B'
  # parm3:    rdi       = int
  # parm4:    rsi       = int
  # parm5:    rcx:rcx   = 'java/security/ProtectionDomain'
  # parm6:    [sp+0xa0]   = 'java/lang/String'  (sp of caller)
  0x000002d1c07b95c0: 448b 5208 | 49bb 0000 | 00cd d102 | 0000 4d03 | d349 3bc2 | 0f84 0600 

  0x000002d1c07b95d8: ;   {runtime_call ic_miss_stub}
  0x000002d1c07b95d8: 0000 e9a1 | 4ea3 ff90 
[Verified Entry Point]
  0x000002d1c07b95e0: 8984 2400 | 80ff ff55 | 488b ec48 | 81ec 9000 | 0000 6690 | 4181 7f20 | 0100 0000 

  0x000002d1c07b95fc: ;   {runtime_call StubRoutines (final stubs)}
  0x000002d1c07b95fc: 7405 e8dd | f3a1 ff48 | 837d 1000 | 488d 4510 | 480f 4445 | 1048 8944 | 2440 4889 | 4c24 7048 
  0x000002d1c07b961c: 83f9 0048 | 8d44 2470 | 480f 4444 | 2470 4889 | 4424 3848 | 8974 2430 | 4889 7c24 | 284c 894c 
  0x000002d1c07b963c: 2458 4983 | f900 488d | 4424 5848 | 0f44 4424 | 5848 8944 | 2420 4c89 | 4424 5049 | 83f8 004c 
  0x000002d1c07b965c: 8d4c 2450 | 4c0f 444c | 2450 4889 | 5424 4848 | 83fa 004c | 8d44 2448 | 4c0f 4444 

  0x000002d1c07b9678: ;   {oop(a 'java/lang/Class'{0x0000000080000918} = 'java/lang/ClassLoader')}
  0x000002d1c07b9678: 2448 49be | 1809 0080 | 0000 0000 | 4c89 7424 | 784c 8d74 | 2478 498b | d6c5 f877 

  0x000002d1c07b9694: ;   {internal_word}
  0x000002d1c07b9694: 49ba 9196 | 7bc0 d102 | 0000 4d89 | 97a0 0300 | 0049 89a7 | 9803 0000 

  0x000002d1c07b96ac: ;   {external_word}
  0x000002d1c07b96ac: 49ba 3d5b | 44ef ff7f | 0000 4180 | 3a00 0f84 | 4e00 0000 | 5241 5041 

  0x000002d1c07b96c4: ;   {metadata({method} {0x000002d1cd431000} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1c07b96c4: 5148 baf8 | 0f43 cdd1 | 0200 0049 | 8bcf 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 
  0x000002d1c07b96e4: ;   {runtime_call}
  0x000002d1c07b96e4: 48b8 504b | eeee ff7f | 0000 ffd0 | 4883 c408 | e90c 0000 

  0x000002d1c07b96f8: ;   {runtime_call}
  0x000002d1c07b96f8: 0048 b850 | 4bee eeff | 7f00 00ff | d048 83c4 | 2041 5941 | 585a 498d | 8fb8 0300 | 0041 c787 
  0x000002d1c07b9718: 4404 0000 | 0400 0000 

  0x000002d1c07b9720: ;   {runtime_call}
  0x000002d1c07b9720: 48b8 ac16 | 4e00 f87f | 0000 ffd0 | c5f8 7741 | c787 4404 | 0000 0500 | 0000 f083 | 4424 c000 
  0x000002d1c07b9740: 493b af48 | 0400 000f | 870e 0000 | 0041 83bf | 4004 0000 | 000f 842b | 0000 00c5 | f877 4889 
  0x000002d1c07b9760: 45f8 498b | cf4c 8be4 | 4883 ec20 | 4883 e4f0 

  0x000002d1c07b9770: ;   {runtime_call}
  0x000002d1c07b9770: 48b8 00d1 | b9ee ff7f | 0000 ffd0 | 498b e44d | 33e4 488b | 45f8 41c7 | 8744 0400 | 0008 0000 
  0x000002d1c07b9790: 0041 83bf | c004 0000 | 020f 84ca 

  0x000002d1c07b979c: ;   {external_word}
  0x000002d1c07b979c: 0000 0049 | ba3d 5b44 | efff 7f00 | 0041 803a | 000f 844c | 0000 0048 

  0x000002d1c07b97b4: ;   {metadata({method} {0x000002d1cd431000} 'defineClass1' '(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1c07b97b4: 8945 f848 | baf8 0f43 | cdd1 0200 | 0049 8bcf | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 
  0x000002d1c07b97d4: ;   {runtime_call}
  0x000002d1c07b97d4: ec08 48b8 | 504b eeee | ff7f 0000 | ffd0 4883 | c408 e90c 

  0x000002d1c07b97e8: ;   {runtime_call}
  0x000002d1c07b97e8: 0000 0048 | b850 4bee | eeff 7f00 | 00ff d048 | 83c4 2048 | 8b45 f849 | c787 9803 | 0000 0000 
  0x000002d1c07b9808: 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4885 c00f | 8425 0000 | 00a8 030f | 8508 0000 
  0x000002d1c07b9828: 0048 8b00 | e915 0000 | 00a8 010f | 8509 0000 | 0048 8b40 | fee9 0400 | 0000 488b | 40ff 498b 
  0x000002d1c07b9848: 8f28 0400 | 00c7 8100 | 0100 0000 | 0000 00c9 | 4983 7f08 | 000f 8501 | 0000 00c3 

  0x000002d1c07b9864: ;   {runtime_call StubRoutines (initial stubs)}
  0x000002d1c07b9864: e997 769e | ffc5 f877 | 4889 45f8 | 4c8b e448 | 83ec 2048 

  0x000002d1c07b9878: ;   {runtime_call}
  0x000002d1c07b9878: 83e4 f048 | b830 83ee | eeff 7f00 | 00ff d049 | 8be4 4d33 | e448 8b45 | f8e9 09ff | ffff f4f4 
[/MachCode]


Compiled method (c1) 921 1165       3       java.security.AccessController::executePrivileged (65 bytes)
 total in heap  [0x000002d1b8e47590,0x000002d1b8e47b80] = 1520
 relocation     [0x000002d1b8e476f0,0x000002d1b8e47758] = 104
 main code      [0x000002d1b8e47760,0x000002d1b8e47a08] = 680
 stub code      [0x000002d1b8e47a08,0x000002d1b8e47a60] = 88
 metadata       [0x000002d1b8e47a60,0x000002d1b8e47a70] = 16
 scopes data    [0x000002d1b8e47a70,0x000002d1b8e47ae8] = 120
 scopes pcs     [0x000002d1b8e47ae8,0x000002d1b8e47b68] = 128
 dependencies   [0x000002d1b8e47b68,0x000002d1b8e47b70] = 8
 nul chk table  [0x000002d1b8e47b70,0x000002d1b8e47b80] = 16

[Constant Pool (empty)]

[MachCode]
[Verified Entry Point]
  # {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController'
  # parm0:    rdx:rdx   = 'java/security/PrivilegedExceptionAction'
  # parm1:    r8:r8     = 'java/security/AccessControlContext'
  # parm2:    r9:r9     = 'java/lang/Class'
  #           [sp+0x50]  (sp of caller)
  0x000002d1b8e47760: 8984 2400 | 80ff ff55 | 4883 ec40 | 4181 7f20 | 0100 0000 

  0x000002d1b8e47774: ;   {runtime_call StubRoutines (final stubs)}
  0x000002d1b8e47774: 7405 e865 | 1239 074c | 8944 2430 

  0x000002d1b8e47780: ;   {metadata(method data for {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e47780: 48be 78d0 | 3110 d202 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | 0700 0085 
  0x000002d1b8e477a0: ff0f 84bf | 0100 004d 

  0x000002d1b8e477a8: ;   {metadata(method data for {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e477a8: 85c0 48be | 78d0 3110 | d202 0000 | 48c7 c710 | 0100 0074 | 0748 c7c7 | 2001 0000 | 488b 1c3e 
  0x000002d1b8e477c8: 488d 5b01 | 4889 1c3e | 4c89 4c24 | 2848 8954 | 2420 0f84 | 2400 0000 

  0x000002d1b8e477e0: ;   {metadata(method data for {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e477e0: 48be 78d0 | 3110 d202 | 0000 4883 | 8630 0100 | 0001 498b 

  0x000002d1b8e477f4: ;   {static_call}
  0x000002d1b8e477f4: d066 90e8 

  0x000002d1b8e477f8: ; ImmutableOopMap {[32]=Oop [40]=Oop [48]=Oop }
                      ;*invokestatic ensureMaterializedForStackWalk {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::executePrivileged@5
  0x000002d1b8e477f8: 645a 9707 

  0x000002d1b8e477fc: ;   {other}
  0x000002d1b8e477fc: 0f1f 8400 | 6c02 0000 

  0x000002d1b8e47804: ;   {metadata(method data for {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e47804: 48ba 78d0 | 3110 d202 | 0000 ff82 | 4001 0000 | 488b 5424 | 2048 3b02 

  0x000002d1b8e4781c: ;   {metadata(method data for {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e4781c: 488b f248 | bf78 d031 | 10d2 0200 | 008b 7608 | 49ba 0000 | 00cd d102 | 0000 4903 | f248 3bb7 
  0x000002d1b8e4783c: b001 0000 | 750d 4883 | 87b8 0100 | 0001 e960 | 0000 0048 | 3bb7 c001 | 0000 750d | 4883 87c8 
  0x000002d1b8e4785c: 0100 0001 | e94a 0000 | 0048 83bf | b001 0000 | 0075 1748 | 89b7 b001 | 0000 48c7 | 87b8 0100 
  0x000002d1b8e4787c: 0001 0000 | 00e9 2900 | 0000 4883 | bfc0 0100 | 0000 7517 | 4889 b7c0 | 0100 0048 | c787 c801 
  0x000002d1b8e4789c: 0000 0100 | 0000 e908 | 0000 0048 | 8387 a001 | 0000 0166 | 0f1f 4400 | 0048 b8ff | ffff ffff 
  0x000002d1b8e478bc: ;   {virtual_call}
  0x000002d1b8e478bc: ffff ffe8 

  0x000002d1b8e478c0: ; ImmutableOopMap {[40]=Oop [48]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::executePrivileged@29
  0x000002d1b8e478c0: 9c3f 3a07 

  0x000002d1b8e478c4: ;   {other}
  0x000002d1b8e478c4: 0f1f 8400 | 3403 0001 

  0x000002d1b8e478cc: ;   {metadata(method data for {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e478cc: 48be 78d0 | 3110 d202 | 0000 ff86 | d801 0000 | 4c8b 4c24 

  0x000002d1b8e478e0: ;   {metadata(method data for {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e478e0: 2848 be78 | d031 10d2 | 0200 0048 | 8386 3802 

  0x000002d1b8e478f0: ;   {metadata(method data for {method} {0x000002d1cd03d050} 'reachabilityFence' '(Ljava/lang/Object;)V' in 'java/lang/ref/Reference')}
  0x000002d1b8e478f0: 0000 0148 | be18 6206 | 10d2 0200 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 feff 
  0x000002d1b8e47910: 1f00 85ff | 0f84 7200 

  0x000002d1b8e47918: ;   {metadata(method data for {method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e47918: 0000 48be | 78d0 3110 | d202 0000 | 4883 8648 | 0200 0001 

  0x000002d1b8e4792c: ;   {metadata(method data for {method} {0x000002d1cd03d050} 'reachabilityFence' '(Ljava/lang/Object;)V' in 'java/lang/ref/Reference')}
  0x000002d1b8e4792c: 48be 1862 | 0610 d202 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | ff1f 0085 
  0x000002d1b8e4794c: ff0f 845a | 0000 0048 | 83c4 405d 

  0x000002d1b8e47958: ;   {poll_return}
  0x000002d1b8e47958: 493b a748 | 0400 000f | 8766 0000 

  0x000002d1b8e47964: ;   {metadata({method} {0x000002d1cd46cdc0} 'executePrivileged' '(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object;' in 'java/security/AccessController')}
  0x000002d1b8e47964: 00c3 49ba | b8cd 46cd | d102 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002d1b8e4797c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4797c: ffe8 7ebe 

  0x000002d1b8e47980: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop [48]=Oop }
                      ;*synchronization entry
                      ; - java.security.AccessController::executePrivileged@-1
  0x000002d1b8e47980: 4507 e920 

  0x000002d1b8e47984: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002d1b8e47984: feff ffe8 

  0x000002d1b8e47988: ; ImmutableOopMap {rdx=Oop [40]=Oop [48]=Oop }
                      ;*invokeinterface run {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.security.AccessController::executePrivileged@29
  0x000002d1b8e47988: 7467 4507 

  0x000002d1b8e4798c: ;   {metadata({method} {0x000002d1cd03d050} 'reachabilityFence' '(Ljava/lang/Object;)V' in 'java/lang/ref/Reference')}
  0x000002d1b8e4798c: 49ba 48d0 | 03cd d102 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002d1b8e479a0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e479a0: ffff ffe8 

  0x000002d1b8e479a4: ; ImmutableOopMap {rax=Oop r9=Oop }
                      ;*synchronization entry
                      ; - java.lang.ref.Reference::reachabilityFence@-1
                      ; - java.security.AccessController::executePrivileged@56
  0x000002d1b8e479a4: 58be 4507 | e96d ffff 

  0x000002d1b8e479ac: ;   {metadata({method} {0x000002d1cd03d050} 'reachabilityFence' '(Ljava/lang/Object;)V' in 'java/lang/ref/Reference')}
  0x000002d1b8e479ac: ff49 ba48 | d003 cdd1 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002d1b8e479c4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e479c4: e837 be45 

  0x000002d1b8e479c8: ; ImmutableOopMap {rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.ref.Reference::reachabilityFence@-1
                      ; - java.security.AccessController::executePrivileged@60
                      ;   {internal_word}
  0x000002d1b8e479c8: 07eb 8849 | ba58 79e4 | b8d1 0200 | 004d 8997 | 6004 0000 

  0x000002d1b8e479dc: ;   {runtime_call SafepointBlob}
  0x000002d1b8e479dc: e91f d13a | 0749 8b87 | f804 0000 | 49c7 87f8 | 0400 0000 | 0000 0049 | c787 0005 | 0000 0000 
  0x000002d1b8e479fc: 0000 4883 

  0x000002d1b8e47a00: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002d1b8e47a00: c440 5de9 | f857 4507 
[Stub Code]
  0x000002d1b8e47a08: ;   {no_reloc}
  0x000002d1b8e47a08: 0f1f 4400 

  0x000002d1b8e47a0c: ;   {static_stub}
  0x000002d1b8e47a0c: 0048 bb00 | 0000 0000 

  0x000002d1b8e47a14: ;   {runtime_call nmethod}
  0x000002d1b8e47a14: 0000 00e9 | fbff ffff 

  0x000002d1b8e47a1c: ;   {static_stub}
  0x000002d1b8e47a1c: 48bb 38cc | 46cd d102 

  0x000002d1b8e47a24: ;   {runtime_call I2C/C2I adapters}
  0x000002d1b8e47a24: 0000 e9b8 

  0x000002d1b8e47a28: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002d1b8e47a28: 613a 07e8 | d084 4507 

  0x000002d1b8e47a30: ;   {external_word}
  0x000002d1b8e47a30: 48b9 907f | 1aef ff7f | 0000 4883 

  0x000002d1b8e47a3c: ;   {runtime_call}
  0x000002d1b8e47a3c: e4f0 48b8 | e044 ddee | ff7f 0000 

  0x000002d1b8e47a48: ;   {section_word}
  0x000002d1b8e47a48: ffd0 f449 | ba4b 7ae4 | b8d1 0200 

  0x000002d1b8e47a54: ;   {runtime_call DeoptimizationBlob}
  0x000002d1b8e47a54: 0041 52e9 | 44c3 3a07 | f4f4 f4f4 
[/MachCode]


Compiled method (c1) 943 1183   !   3       java.lang.ClassLoader::loadClass (121 bytes)
 total in heap  [0x000002d1b8e4c590,0x000002d1b8e4db70] = 5600
 relocation     [0x000002d1b8e4c6f0,0x000002d1b8e4c858] = 360
 main code      [0x000002d1b8e4c860,0x000002d1b8e4d448] = 3048
 stub code      [0x000002d1b8e4d448,0x000002d1b8e4d520] = 216
 oops           [0x000002d1b8e4d520,0x000002d1b8e4d530] = 16
 metadata       [0x000002d1b8e4d530,0x000002d1b8e4d598] = 104
 scopes data    [0x000002d1b8e4d598,0x000002d1b8e4d7f8] = 608
 scopes pcs     [0x000002d1b8e4d7f8,0x000002d1b8e4d9b8] = 448
 dependencies   [0x000002d1b8e4d9b8,0x000002d1b8e4d9d8] = 32
 handler table  [0x000002d1b8e4d9d8,0x000002d1b8e4db58] = 384
 nul chk table  [0x000002d1b8e4db58,0x000002d1b8e4db70] = 24

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # this:     rdx:rdx   = 'java/lang/ClassLoader'
  # parm0:    r8:r8     = 'java/lang/String'
  # parm1:    r9        = boolean
  #           [sp+0xd0]  (sp of caller)
  0x000002d1b8e4c860: 448b 5208 | 49bb 0000 | 00cd d102 | 0000 4d03 | d34c 3bd0 

  0x000002d1b8e4c874: ;   {runtime_call ic_miss_stub}
  0x000002d1b8e4c874: 0f85 061c | 3a07 660f | 1f44 0000 
[Verified Entry Point]
  0x000002d1b8e4c880: 8984 2400 | 80ff ff55 | 4881 ecc0 | 0000 0090 | 4181 7f20 | 0100 0000 

  0x000002d1b8e4c898: ;   {runtime_call StubRoutines (final stubs)}
  0x000002d1b8e4c898: 7405 e841 | c138 0748 | 8954 2450 | 4c89 4424 

  0x000002d1b8e4c8a8: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4c8a8: 5848 be80 | eb31 10d2 | 0200 008b | becc 0000 | 0083 c702 | 89be cc00 | 0000 81e7 | fe07 0000 
  0x000002d1b8e4c8c8: 85ff 0f84 | 7409 0000 | 4489 4c24 | 6048 8bf2 

  0x000002d1b8e4c8d8: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4c8d8: 48bf 80eb | 3110 d202 | 0000 8b76 | 0849 ba00 | 0000 cdd1 | 0200 0049 | 03f2 483b | b720 0100 
  0x000002d1b8e4c8f8: 0075 0d48 | 8387 2801 | 0000 01e9 | 6000 0000 | 483b b730 | 0100 0075 | 0d48 8387 | 3801 0000 
  0x000002d1b8e4c918: 01e9 4a00 | 0000 4883 | bf20 0100 | 0000 7517 | 4889 b720 | 0100 0048 | c787 2801 | 0000 0100 
  0x000002d1b8e4c938: 0000 e929 | 0000 0048 | 83bf 3001 | 0000 0075 | 1748 89b7 | 3001 0000 | 48c7 8738 | 0100 0001 
  0x000002d1b8e4c958: 0000 00e9 | 0800 0000 | 4883 8710 | 0100 0001 | 498b f04c | 8bc6 488b | fa48 8bd7 

  0x000002d1b8e4c974: ;   {optimized virtual_call}
  0x000002d1b8e4c974: 6666 90e8 

  0x000002d1b8e4c978: ; ImmutableOopMap {[80]=Oop [88]=Oop }
                      ;*invokevirtual getClassLoadingLock {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::loadClass@2
  0x000002d1b8e4c978: 04ee fcff 

  0x000002d1b8e4c97c: ;   {other}
  0x000002d1b8e4c97c: 0f1f 8400 | ec03 0000 | 4889 4424 | 6848 8d94 | 24a0 0000 | 0048 8bf0 | 4889 7208 

  0x000002d1b8e4c998: ; implicit exception: dispatches to 0x000002d1b8e4d265
  0x000002d1b8e4c998: 488b 0648 | 83c8 0148 | 8902 f048 | 0fb1 160f | 8412 0000 | 0048 2bc4 | 4825 07f0 | ffff 4889 
  0x000002d1b8e4c9b8: 020f 85ab | 0800 0049 | ff87 4805 | 0000 488b 

  0x000002d1b8e4c9c8: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4c9c8: 5424 5048 | be80 eb31 | 10d2 0200 | 0048 8386 | 4801 0000 

  0x000002d1b8e4c9dc: ;   {metadata(method data for {method} {0x000002d1cd431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4c9dc: 0148 baa8 | ea23 10d2 | 0200 008b | b2cc 0000 | 0083 c602 | 89b2 cc00 | 0000 81e6 | feff 1f00 
  0x000002d1b8e4c9fc: 85f6 0f84 | 7908 0000 

  0x000002d1b8e4ca04: ;   {metadata(method data for {method} {0x000002d1cd431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4ca04: 48ba a8ea | 2310 d202 | 0000 4883 | 8210 0100 | 0001 488b | 5424 580f 

  0x000002d1b8e4ca1c: ;   {static_call}
  0x000002d1b8e4ca1c: 1f40 00e8 

  0x000002d1b8e4ca20: ; ImmutableOopMap {[80]=Oop [88]=Oop [104]=Oop [168]=Oop }
                      ;*invokestatic checkName {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findLoadedClass@1
                      ; - java.lang.ClassLoader::loadClass@10
  0x000002d1b8e4ca20: 9c48 ebff 

  0x000002d1b8e4ca24: ;   {other}
  0x000002d1b8e4ca24: 0f1f 8400 | 9404 0001 

  0x000002d1b8e4ca2c: ;   {metadata(method data for {method} {0x000002d1cd431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4ca2c: 85c0 49b8 | a8ea 2310 | d202 0000 | 48c7 c220 | 0100 0075 | 0748 c7c2 | 3001 0000 | 498b 3410 
  0x000002d1b8e4ca4c: 488d 7601 | 4989 3410 | 0f85 0f00 

  0x000002d1b8e4ca58: ;   {oop(nullptr)}
  0x000002d1b8e4ca58: 0000 48be | 0000 0000 | 0000 0000 | e936 0000 | 0048 8b54 

  0x000002d1b8e4ca6c: ;   {metadata(method data for {method} {0x000002d1cd431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4ca6c: 2450 49b8 | a8ea 2310 | d202 0000 | 4983 8040 | 0100 0001 | 4c8b 4424 | 5848 8b54 | 2450 0f1f 
  0x000002d1b8e4ca8c: ;   {optimized virtual_call}
  0x000002d1b8e4ca8c: 4400 00e8 

  0x000002d1b8e4ca90: ; ImmutableOopMap {[80]=Oop [88]=Oop [104]=Oop [168]=Oop }
                      ;*invokevirtual findLoadedClass0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findLoadedClass@11
                      ; - java.lang.ClassLoader::loadClass@10
  0x000002d1b8e4ca90: 2c6f 9107 

  0x000002d1b8e4ca94: ;   {other}
  0x000002d1b8e4ca94: 0f1f 8400 | 0405 0002 | 488b f048 

  0x000002d1b8e4caa0: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4caa0: 85f6 48bf | 80eb 3110 | d202 0000 | 48c7 c380 | 0100 0075 | 0748 c7c3 | 9001 0000 | 488b 041f 
  0x000002d1b8e4cac0: 488d 4001 | 4889 041f | 0f85 9605 | 0000 4889 

  0x000002d1b8e4cad0: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cad0: 7424 7048 | bf80 eb31 | 10d2 0200 | 0048 8387 | a001 0000 

  0x000002d1b8e4cae4: ;   {runtime_call}
  0x000002d1b8e4cae4: 0148 b8d0 | 8de7 eeff | 7f00 00ff 

  0x000002d1b8e4caf0: ;   {other}
  0x000002d1b8e4caf0: d00f 1f84 | 0000 0000 | 0048 8944 | 2478 488b | 5424 508b | 7218 4885 

  0x000002d1b8e4cb08: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cb08: f649 b880 | eb31 10d2 | 0200 0049 | c7c1 b001 | 0000 7407 | 49c7 c1c0 | 0100 004b | 8b3c 0848 
  0x000002d1b8e4cb28: 8d7f 014b | 893c 080f | 84d4 0000 | 0048 3b06 

  0x000002d1b8e4cb38: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cb38: 4c8b c649 | b980 eb31 | 10d2 0200 | 0045 8b40 | 0849 ba00 | 0000 cdd1 | 0200 004d | 03c2 4d3b 
  0x000002d1b8e4cb58: 81e0 0100 | 0075 0d49 | 8381 e801 | 0000 01e9 | 6000 0000 | 4d3b 81f0 | 0100 0075 | 0d49 8381 
  0x000002d1b8e4cb78: f801 0000 | 01e9 4a00 | 0000 4983 | b9e0 0100 | 0000 7517 | 4d89 81e0 | 0100 0049 | c781 e801 
  0x000002d1b8e4cb98: 0000 0100 | 0000 e929 | 0000 0049 | 83b9 f001 | 0000 0075 | 174d 8981 | f001 0000 | 49c7 81f8 
  0x000002d1b8e4cbb8: 0100 0001 | 0000 00e9 | 0800 0000 | 4983 81d0 | 0100 0001 | 4c8b 4424 | 5841 b900 | 0000 0048 
  0x000002d1b8e4cbd8: 8bd6 6666 | 9048 b888 | a206 cdd1 

  0x000002d1b8e4cbe4: ;   {virtual_call}
  0x000002d1b8e4cbe4: 0200 00e8 

  0x000002d1b8e4cbe8: ; ImmutableOopMap {[80]=Oop [88]=Oop [104]=Oop [112]=Oop [168]=Oop }
                      ;*invokevirtual loadClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::loadClass@38
  0x000002d1b8e4cbe8: 741b fdff 

  0x000002d1b8e4cbec: ;   {other}
  0x000002d1b8e4cbec: 0f1f 8400 | 5c06 0003 

  0x000002d1b8e4cbf4: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cbf4: 48ba 80eb | 3110 d202 | 0000 ff82 | 0802 0000 | e9d1 0000 

  0x000002d1b8e4cc08: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cc08: 0048 ba80 | eb31 10d2 | 0200 0048 | 8382 2002 

  0x000002d1b8e4cc18: ;   {metadata(method data for {method} {0x000002d1cd431430} 'findBootstrapClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cc18: 0000 0148 | ba30 7930 | 10d2 0200 | 008b b2cc | 0000 0083 | c602 89b2 | cc00 0000 | 81e6 feff 
  0x000002d1b8e4cc38: 1f00 85f6 | 0f84 6106 

  0x000002d1b8e4cc40: ;   {metadata(method data for {method} {0x000002d1cd431430} 'findBootstrapClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cc40: 0000 48ba | 3079 3010 | d202 0000 | 4883 8210 | 0100 0001 | 488b 5424 | 5866 0f1f 

  0x000002d1b8e4cc5c: ;   {static_call}
  0x000002d1b8e4cc5c: 4400 00e8 

  0x000002d1b8e4cc60: ; ImmutableOopMap {[80]=Oop [88]=Oop [104]=Oop [112]=Oop [168]=Oop }
                      ;*invokestatic checkName {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findBootstrapClassOrNull@1
                      ; - java.lang.ClassLoader::loadClass@47
  0x000002d1b8e4cc60: 1c21 3a07 

  0x000002d1b8e4cc64: ;   {other}
  0x000002d1b8e4cc64: 0f1f 8400 | d406 0004 

  0x000002d1b8e4cc6c: ;   {metadata(method data for {method} {0x000002d1cd431430} 'findBootstrapClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cc6c: 85c0 48ba | 3079 3010 | d202 0000 | 48c7 c620 | 0100 0075 | 0748 c7c6 | 3001 0000 | 488b 3c32 
  0x000002d1b8e4cc8c: 488d 7f01 | 4889 3c32 | 0f85 0f00 

  0x000002d1b8e4cc98: ;   {oop(nullptr)}
  0x000002d1b8e4cc98: 0000 48be | 0000 0000 | 0000 0000 | e92e 0000 

  0x000002d1b8e4cca8: ;   {metadata(method data for {method} {0x000002d1cd431430} 'findBootstrapClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cca8: 0048 ba30 | 7930 10d2 | 0200 0048 | 8382 4001 | 0000 0148 | 8b54 2458 | 0f1f 8000 

  0x000002d1b8e4ccc4: ;   {static_call}
  0x000002d1b8e4ccc4: 0000 00e8 

  0x000002d1b8e4ccc8: ; ImmutableOopMap {[80]=Oop [88]=Oop [104]=Oop [112]=Oop [168]=Oop }
                      ;*invokestatic findBootstrapClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::findBootstrapClassOrNull@10
                      ; - java.lang.ClassLoader::loadClass@47
  0x000002d1b8e4ccc8: b420 3a07 

  0x000002d1b8e4cccc: ;   {other}
  0x000002d1b8e4cccc: 0f1f 8400 | 3c07 0005 | 488b f048 

  0x000002d1b8e4ccd8: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4ccd8: 8bc6 48be | 80eb 3110 | d202 0000 | ff86 3002 | 0000 e91e | 0000 0049 | 8b87 f804 | 0000 4d33 
  0x000002d1b8e4ccf8: d24d 8997 | f804 0000 | 4d33 d24d | 8997 0005 | 0000 488b | c648 85c0 

  0x000002d1b8e4cd10: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cd10: 48be 80eb | 3110 d202 | 0000 48c7 | c758 0200 | 0074 0748 | c7c7 4802 | 0000 488b | 1c3e 488d 
  0x000002d1b8e4cd30: 5b01 4889 | 1c3e 0f84 | 0800 0000 | 488b f0e9 | 2003 0000 

  0x000002d1b8e4cd44: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cd44: 48be 80eb | 3110 d202 | 0000 4883 | 8668 0200 

  0x000002d1b8e4cd54: ;   {runtime_call}
  0x000002d1b8e4cd54: 0001 48b8 | d08d e7ee | ff7f 0000 

  0x000002d1b8e4cd60: ;   {other}
  0x000002d1b8e4cd60: ffd0 0f1f | 8400 0000 | 0000 488b 

  0x000002d1b8e4cd6c: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cd6c: 5424 5049 | b880 eb31 | 10d2 0200 | 008b 5208 | 49ba 0000 | 00cd d102 | 0000 4903 | d249 3b90 
  0x000002d1b8e4cd8c: 8802 0000 | 750d 4983 | 8090 0200 | 0001 e960 | 0000 0049 | 3b90 9802 | 0000 750d | 4983 80a0 
  0x000002d1b8e4cdac: 0200 0001 | e94a 0000 | 0049 83b8 | 8802 0000 | 0075 1749 | 8990 8802 | 0000 49c7 | 8090 0200 
  0x000002d1b8e4cdcc: 0001 0000 | 00e9 2900 | 0000 4983 | b898 0200 | 0000 7517 | 4989 9098 | 0200 0049 | c780 a002 
  0x000002d1b8e4cdec: 0000 0100 | 0000 e908 | 0000 0049 | 8380 7802 | 0000 014c | 8b44 2458 | 488b 5424 | 5048 8984 
  0x000002d1b8e4ce0c: 2480 0000 | 000f 1f40 | 0048 b8ff | ffff ffff 

  0x000002d1b8e4ce1c: ;   {virtual_call}
  0x000002d1b8e4ce1c: ffff ffe8 

  0x000002d1b8e4ce20: ; ImmutableOopMap {[80]=Oop [104]=Oop [168]=Oop }
                      ;*invokevirtual findClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::loadClass@69
  0x000002d1b8e4ce20: 3ceb 3907 

  0x000002d1b8e4ce24: ;   {other}
  0x000002d1b8e4ce24: 0f1f 8400 | 9408 0006 

  0x000002d1b8e4ce2c: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4ce2c: 49b8 80eb | 3110 d202 | 0000 4983 | 80b0 0200 

  0x000002d1b8e4ce3c: ;   {metadata(method data for {method} {0x000002d1cd1068a0} 'getParentDelegationTime' '()Ljdk/internal/perf/PerfCounter;' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4ce3c: 0001 49b8 | b0ef 3110 | d202 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002d1b8e4ce5c: ff1f 0085 | d20f 845d 

  0x000002d1b8e4ce64: ;   {oop(a 'jdk/internal/perf/PerfCounter'{0x00000000d7038688})}
  0x000002d1b8e4ce64: 0400 0048 | ba88 8603 | d700 0000 | 004c 8bc2 

  0x000002d1b8e4ce74: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4ce74: 48be 80eb | 3110 d202 

  0x000002d1b8e4ce7c: ;   {metadata('jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4ce7c: 0000 49ba | d05e 10cd | d102 0000 | 4c89 96d0 | 0200 0048 | 8386 d802 | 0000 014c | 8b84 2480 
  0x000002d1b8e4ce9c: 0000 0048 | 8b74 2478 

  0x000002d1b8e4cea4: ;   {metadata(method data for {method} {0x000002d1cd106690} 'addTime' '(J)V' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4cea4: 4c2b c648 | bed8 f031 | 10d2 0200 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 feff 
  0x000002d1b8e4cec4: 1f00 85ff | 0f84 1704 | 0000 488b 

  0x000002d1b8e4ced0: ;   {metadata(method data for {method} {0x000002d1cd106690} 'addTime' '(J)V' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4ced0: f248 bfd8 | f031 10d2 

  0x000002d1b8e4ced8: ;   {metadata('jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4ced8: 0200 0049 | bad0 5e10 | cdd1 0200 | 004c 8997 | 2001 0000 | 4883 8728 | 0100 0001 | 4889 8424 
  0x000002d1b8e4cef8: 8800 0000 

  0x000002d1b8e4cefc: ;   {optimized virtual_call}
  0x000002d1b8e4cefc: 6666 90e8 

  0x000002d1b8e4cf00: ; ImmutableOopMap {[80]=Oop [104]=Oop [136]=Oop [168]=Oop }
                      ;*invokevirtual add {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.perf.PerfCounter::addTime@2
                      ; - java.lang.ClassLoader::loadClass@82
  0x000002d1b8e4cf00: 7c42 ffff 

  0x000002d1b8e4cf04: ;   {other}
  0x000002d1b8e4cf04: 0f1f 8400 | 7409 0007 

  0x000002d1b8e4cf0c: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cf0c: 49b8 80eb | 3110 d202 | 0000 4983 | 80f8 0200 

  0x000002d1b8e4cf1c: ;   {metadata(method data for {method} {0x000002d1cd1068f8} 'getFindClassTime' '()Ljdk/internal/perf/PerfCounter;' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4cf1c: 0001 49b8 | 70f2 3110 | d202 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002d1b8e4cf3c: ff1f 0085 | d20f 84bf 

  0x000002d1b8e4cf44: ;   {oop(a 'jdk/internal/perf/PerfCounter'{0x00000000d70384d8})}
  0x000002d1b8e4cf44: 0300 0048 | bad8 8403 | d700 0000 | 004c 8bc2 

  0x000002d1b8e4cf54: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cf54: 48be 80eb | 3110 d202 

  0x000002d1b8e4cf5c: ;   {metadata('jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4cf5c: 0000 49ba | d05e 10cd | d102 0000 | 4c89 9618 | 0300 0048 | 8386 2003 | 0000 014c | 8b84 2480 
  0x000002d1b8e4cf7c: ;   {optimized virtual_call}
  0x000002d1b8e4cf7c: 0000 00e8 

  0x000002d1b8e4cf80: ; ImmutableOopMap {[80]=Oop [104]=Oop [136]=Oop [168]=Oop }
                      ;*invokevirtual addElapsedTimeFrom {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::loadClass@90
  0x000002d1b8e4cf80: 0905 0000 

  0x000002d1b8e4cf84: ;   {other}
  0x000002d1b8e4cf84: 0f1f 8400 | f409 0008 

  0x000002d1b8e4cf8c: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cf8c: 49b8 80eb | 3110 d202 | 0000 4983 | 8040 0300 

  0x000002d1b8e4cf9c: ;   {metadata(method data for {method} {0x000002d1cd106950} 'getFindClasses' '()Ljdk/internal/perf/PerfCounter;' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4cf9c: 0001 49b8 | 40f5 3110 | d202 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x000002d1b8e4cfbc: ff1f 0085 | d20f 8460 

  0x000002d1b8e4cfc4: ;   {oop(a 'jdk/internal/perf/PerfCounter'{0x00000000d70385b0})}
  0x000002d1b8e4cfc4: 0300 0048 | bab0 8503 | d700 0000 | 004c 8bc2 

  0x000002d1b8e4cfd4: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4cfd4: 48be 80eb | 3110 d202 

  0x000002d1b8e4cfdc: ;   {metadata('jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4cfdc: 0000 49ba | d05e 10cd | d102 0000 | 4c89 9660 | 0300 0048 | 8386 6803 

  0x000002d1b8e4cff4: ;   {metadata(method data for {method} {0x000002d1cd106740} 'increment' '()V' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4cff4: 0000 0149 | b868 f631 | 10d2 0200 | 0041 8bb0 | cc00 0000 | 83c6 0241 | 89b0 cc00 | 0000 81e6 
  0x000002d1b8e4d014: feff 1f00 | 85f6 0f84 | 2803 0000 

  0x000002d1b8e4d020: ;   {metadata(method data for {method} {0x000002d1cd106740} 'increment' '()V' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4d020: 4c8b c248 | be68 f631 | 10d2 0200 

  0x000002d1b8e4d02c: ;   {metadata('jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4d02c: 0049 bad0 | 5e10 cdd1 | 0200 004c | 8996 2001 | 0000 4883 | 8628 0100 | 0001 49c7 | c001 0000 
  0x000002d1b8e4d04c: ;   {optimized virtual_call}
  0x000002d1b8e4d04c: 0066 90e8 

  0x000002d1b8e4d050: ; ImmutableOopMap {[80]=Oop [104]=Oop [136]=Oop [168]=Oop }
                      ;*invokevirtual add {reexecute=0 rethrow=0 return_oop=0}
                      ; - jdk.internal.perf.PerfCounter::increment@2
                      ; - java.lang.ClassLoader::loadClass@96
  0x000002d1b8e4d050: 2c41 ffff 

  0x000002d1b8e4d054: ;   {other}
  0x000002d1b8e4d054: 0f1f 8400 | c40a 0009 | 488b b424 | 8800 0000 | 448b 4c24 | 6045 85c9 

  0x000002d1b8e4d06c: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d06c: 48b8 80eb | 3110 d202 | 0000 48c7 | c288 0300 | 0074 0748 | c7c2 9803 | 0000 488b | 3c10 488d 
  0x000002d1b8e4d08c: 7f01 4889 | 3c10 0f84 | 6d00 0000 | 488b 5424 

  0x000002d1b8e4d09c: ;   {metadata(method data for {method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d09c: 5048 b880 | eb31 10d2 | 0200 0048 | 8380 a803 

  0x000002d1b8e4d0ac: ;   {metadata(method data for {method} {0x000002d1cd4313d8} 'resolveClass' '(Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d0ac: 0000 0148 | b820 c72f | 10d2 0200 | 008b 90cc | 0000 0083 | c202 8990 | cc00 0000 | 81e2 feff 
  0x000002d1b8e4d0cc: 1f00 85d2 | 0f84 9302 | 0000 4885 

  0x000002d1b8e4d0d8: ;   {metadata(method data for {method} {0x000002d1cd4313d8} 'resolveClass' '(Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d0d8: f648 b820 | c72f 10d2 | 0200 0048 | c7c2 2001 | 0000 7407 | 48c7 c210 | 0100 0048 | 8b3c 1048 
  0x000002d1b8e4d0f8: 8d7f 0148 | 893c 100f | 8443 0000 | 0048 8d84 | 24a0 0000 | 0048 8b10 | 4885 d20f | 840f 0000 
  0x000002d1b8e4d118: 0048 8b78 | 08f0 480f | b117 0f85 | 6202 0000 | 49ff 8f48 | 0500 0048 | 8bc6 4881 | c4c0 0000 
  0x000002d1b8e4d138: ;   {poll_return}
  0x000002d1b8e4d138: 005d 493b | a748 0400 | 000f 8759 | 0200 00c3 | 4889 b424 | 9800 0000 

  0x000002d1b8e4d150: ;   {no_reloc}
  0x000002d1b8e4d150: e970 0200 | 0000 0000 | 0000 498b | 87b8 0100 | 0048 8d78 | 3049 3bbf | c801 0000 | 0f87 5d02 
  0x000002d1b8e4d170: 0000 4989 | bfb8 0100 | 0048 c700 | 0100 0000 | 488b ca49 | ba00 0000 | cdd1 0200 | 0049 2bca 
  0x000002d1b8e4d190: 8948 0848 | 33c9 8948 | 0c48 33c9 | 4889 4810 | 4889 4818 | 4889 4820 | 4889 4828 

  0x000002d1b8e4d1ac: ;   {metadata(method data for {method} {0x000002d1cd4313d8} 'resolveClass' '(Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d1ac: 488b d048 | be20 c72f | 10d2 0200 | 0048 8386 | 3001 0000 | 0148 8bd0 | 4889 8424 | 9000 0000 
  0x000002d1b8e4d1cc: ;   {optimized virtual_call}
  0x000002d1b8e4d1cc: 6666 90e8 

  0x000002d1b8e4d1d0: ; ImmutableOopMap {[104]=Oop [144]=Oop [152]=Oop [168]=Oop }
                      ;*invokespecial <init> {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::resolveClass@8
                      ; - java.lang.ClassLoader::loadClass@106
  0x000002d1b8e4d1d0: ac15 3a07 

  0x000002d1b8e4d1d4: ;   {other}
  0x000002d1b8e4d1d4: 0f1f 8400 | 440c 000a | 488b 8424 

  0x000002d1b8e4d1e0: ; ImmutableOopMap {rax=Oop [104]=Oop [152]=Oop [168]=Oop }
                      ;*athrow {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::resolveClass@11
                      ; - java.lang.ClassLoader::loadClass@106
  0x000002d1b8e4d1e0: 9000 0000 

  0x000002d1b8e4d1e4: ;   {section_word}
  0x000002d1b8e4d1e4: 48ba e4d1 | e4b8 d102 

  0x000002d1b8e4d1ec: ;   {runtime_call handle_exception_nofpu Runtime1 stub}
  0x000002d1b8e4d1ec: 0000 e80d | 2745 0790 | 498b 87f8 | 0400 004d | 33d2 4d89 | 97f8 0400 | 004d 33d2 | 4d89 9700 
  0x000002d1b8e4d20c: 0500 0048 | 8bf0 488d | 8424 a000 | 0000 488b | 3848 85ff | 0f84 0f00 | 0000 488b | 5808 f048 
  0x000002d1b8e4d22c: 0fb1 3b0f | 85a7 0100 | 0049 ff8f | 4805 0000 | 488b c6e9 | f301 0000 

  0x000002d1b8e4d244: ;   {metadata({method} {0x000002d1cd014090} 'loadClass' '(Ljava/lang/String;Z)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d244: 49ba 8840 | 01cd d102 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002d1b8e4d258: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d258: ffff ffe8 

  0x000002d1b8e4d25c: ; ImmutableOopMap {rdx=Oop r8=Oop [80]=Oop [88]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::loadClass@-1
  0x000002d1b8e4d25c: a065 4507 | e96b f6ff 

  0x000002d1b8e4d264: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002d1b8e4d264: ffe8 960e 

  0x000002d1b8e4d268: ; ImmutableOopMap {rsi=Oop [80]=Oop [88]=Oop [104]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::loadClass@7
  0x000002d1b8e4d268: 4507 4889 | 7424 0848 

  0x000002d1b8e4d270: ;   {runtime_call monitorenter_nofpu Runtime1 stub}
  0x000002d1b8e4d270: 8914 24e8 

  0x000002d1b8e4d274: ; ImmutableOopMap {rsi=Oop [80]=Oop [88]=Oop [104]=Oop [168]=Oop }
                      ;*monitorenter {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::loadClass@7
  0x000002d1b8e4d274: 8841 4507 | e949 f7ff 

  0x000002d1b8e4d27c: ;   {metadata({method} {0x000002d1cd431488} 'findLoadedClass' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d27c: ff49 ba80 | 1443 cdd1 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002d1b8e4d294: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d294: e867 6545 

  0x000002d1b8e4d298: ; ImmutableOopMap {[80]=Oop [88]=Oop [104]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::findLoadedClass@-1
                      ; - java.lang.ClassLoader::loadClass@10
  0x000002d1b8e4d298: 07e9 66f7 

  0x000002d1b8e4d29c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x000002d1b8e4d29c: ffff e85d 

  0x000002d1b8e4d2a0: ; ImmutableOopMap {rsi=Oop [80]=Oop [88]=Oop [104]=Oop [112]=Oop [168]=Oop }
                      ;*invokevirtual loadClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::loadClass@38
                      ;   {metadata({method} {0x000002d1cd431430} 'findBootstrapClassOrNull' '(Ljava/lang/String;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d2a0: 0e45 0749 | ba28 1443 | cdd1 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002d1b8e4d2b8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d2b8: ffff e841 

  0x000002d1b8e4d2bc: ; ImmutableOopMap {[80]=Oop [88]=Oop [104]=Oop [112]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::findBootstrapClassOrNull@-1
                      ; - java.lang.ClassLoader::loadClass@47
  0x000002d1b8e4d2bc: 6545 07e9 | 7ef9 ffff 

  0x000002d1b8e4d2c4: ;   {metadata({method} {0x000002d1cd1068a0} 'getParentDelegationTime' '()Ljdk/internal/perf/PerfCounter;' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4d2c4: 49ba 9868 | 10cd d102 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002d1b8e4d2d8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d2d8: ffff ffe8 

  0x000002d1b8e4d2dc: ; ImmutableOopMap {rax=Oop [80]=Oop [104]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.perf.PerfCounter::getParentDelegationTime@-1
                      ; - java.lang.ClassLoader::loadClass@74
  0x000002d1b8e4d2dc: 2065 4507 | e982 fbff 

  0x000002d1b8e4d2e4: ;   {metadata({method} {0x000002d1cd106690} 'addTime' '(J)V' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4d2e4: ff49 ba88 | 6610 cdd1 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002d1b8e4d2fc: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d2fc: e8ff 6445 

  0x000002d1b8e4d300: ; ImmutableOopMap {rax=Oop rdx=Oop [80]=Oop [104]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.perf.PerfCounter::addTime@-1
                      ; - java.lang.ClassLoader::loadClass@82
  0x000002d1b8e4d300: 07e9 c8fb 

  0x000002d1b8e4d304: ;   {metadata({method} {0x000002d1cd1068f8} 'getFindClassTime' '()Ljdk/internal/perf/PerfCounter;' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4d304: ffff 49ba | f068 10cd | d102 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x000002d1b8e4d31c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d31c: ffe8 de64 

  0x000002d1b8e4d320: ; ImmutableOopMap {[80]=Oop [104]=Oop [136]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.perf.PerfCounter::getFindClassTime@-1
                      ; - java.lang.ClassLoader::loadClass@85
  0x000002d1b8e4d320: 4507 e920 

  0x000002d1b8e4d324: ;   {metadata({method} {0x000002d1cd106950} 'getFindClasses' '()Ljdk/internal/perf/PerfCounter;' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4d324: fcff ff49 | ba48 6910 | cdd1 0200 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x000002d1b8e4d33c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d33c: ffff e8bd 

  0x000002d1b8e4d340: ; ImmutableOopMap {[80]=Oop [104]=Oop [136]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.perf.PerfCounter::getFindClasses@-1
                      ; - java.lang.ClassLoader::loadClass@93
  0x000002d1b8e4d340: 6445 07e9 | 7ffc ffff 

  0x000002d1b8e4d348: ;   {metadata({method} {0x000002d1cd106740} 'increment' '()V' in 'jdk/internal/perf/PerfCounter')}
  0x000002d1b8e4d348: 49ba 3867 | 10cd d102 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x000002d1b8e4d35c: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d35c: ffff ffe8 

  0x000002d1b8e4d360: ; ImmutableOopMap {rdx=Oop [80]=Oop [104]=Oop [136]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.perf.PerfCounter::increment@-1
                      ; - java.lang.ClassLoader::loadClass@96
  0x000002d1b8e4d360: 9c64 4507 | e9b7 fcff 

  0x000002d1b8e4d368: ;   {metadata({method} {0x000002d1cd4313d8} 'resolveClass' '(Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x000002d1b8e4d368: ff49 bad0 | 1343 cdd1 | 0200 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x000002d1b8e4d380: ;   {runtime_call counter_overflow Runtime1 stub}
  0x000002d1b8e4d380: e87b 6445 

  0x000002d1b8e4d384: ; ImmutableOopMap {rsi=Oop [104]=Oop [168]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::resolveClass@-1
                      ; - java.lang.ClassLoader::loadClass@106
  0x000002d1b8e4d384: 07e9 4cfd | ffff 488d | 8424 a000 | 0000 4889 

  0x000002d1b8e4d394: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x000002d1b8e4d394: 0424 e865 | 4945 07e9 | 8ffd ffff 

  0x000002d1b8e4d3a0: ;   {internal_word}
  0x000002d1b8e4d3a0: 49ba 3ad1 | e4b8 d102 | 0000 4d89 | 9760 0400 

  0x000002d1b8e4d3b0: ;   {runtime_call SafepointBlob}
  0x000002d1b8e4d3b0: 00e9 4a77 

  0x000002d1b8e4d3b4: ;   {metadata(nullptr)}
  0x000002d1b8e4d3b4: 3a07 48ba | 0000 0000 | 0000 0000 | b800 0f05 

  0x000002d1b8e4d3c4: ;   {runtime_call load_klass_patching Runtime1 stub}
  0x000002d1b8e4d3c4: 0ae8 b653 

  0x000002d1b8e4d3c8: ; ImmutableOopMap {[104]=Oop [152]=Oop [168]=Oop }
                      ;*new {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::resolveClass@4
                      ; - java.lang.ClassLoader::loadClass@106
  0x000002d1b8e4d3c8: 4507 e981 | fdff ff48 

  0x000002d1b8e4d3d0: ;   {runtime_call fast_new_instance Runtime1 stub}
  0x000002d1b8e4d3d0: 8bd2 e829 

  0x000002d1b8e4d3d4: ; ImmutableOopMap {[104]=Oop [152]=Oop [168]=Oop }
                      ;*new {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::resolveClass@4
                      ; - java.lang.ClassLoader::loadClass@106
  0x000002d1b8e4d3d4: 1645 07e9 | d0fd ffff | 488d 8424 | a000 0000 | 4889 0424 

  0x000002d1b8e4d3e8: ;   {runtime_call monitorexit_nofpu Runtime1 stub}
  0x000002d1b8e4d3e8: e813 4945 | 07e9 4afe | ffff 488b | 7424 70e9 | f3f8 ffff | 488b 7424 | 70e9 e9f8 | ffff 488b 
  0x000002d1b8e4d408: 7424 70e9 | dff8 ffff | 488b 7424 | 70e9 d5f8 | ffff 498b | 87f8 0400 | 0049 c787 | f804 0000 
  0x000002d1b8e4d428: 0000 0000 | 49c7 8700 | 0500 0000 | 0000 0048 | 81c4 c000 

  0x000002d1b8e4d43c: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000002d1b8e4d43c: 0000 5de9 | bcfd 4407 | f4f4 f4f4 
[Stub Code]
  0x000002d1b8e4d448: ;   {no_reloc}
  0x000002d1b8e4d448: 0f1f 4400 

  0x000002d1b8e4d44c: ;   {static_stub}
  0x000002d1b8e4d44c: 0048 bb00 | 0000 0000 

  0x000002d1b8e4d454: ;   {runtime_call nmethod}
  0x000002d1b8e4d454: 0000 00e9 | fbff ffff 

  0x000002d1b8e4d45c: ;   {static_stub}
  0x000002d1b8e4d45c: 9048 bb00 | 0000 0000 

  0x000002d1b8e4d464: ;   {runtime_call nmethod}
  0x000002d1b8e4d464: 0000 00e9 | fbff ffff 

  0x000002d1b8e4d46c: ;   {static_stub}
  0x000002d1b8e4d46c: 9048 bb00 | 0000 0000 

  0x000002d1b8e4d474: ;   {runtime_call nmethod}
  0x000002d1b8e4d474: 0000 00e9 | fbff ffff 

  0x000002d1b8e4d47c: ;   {static_stub}
  0x000002d1b8e4d47c: 9048 bb00 | 0000 0000 

  0x000002d1b8e4d484: ;   {runtime_call nmethod}
  0x000002d1b8e4d484: 0000 00e9 | fbff ffff 

  0x000002d1b8e4d48c: ;   {static_stub}
  0x000002d1b8e4d48c: 9048 bbe0 | 6610 cdd1 

  0x000002d1b8e4d494: ;   {runtime_call I2C/C2I adapters}
  0x000002d1b8e4d494: 0200 00e9 | 4b7f 3a07 

  0x000002d1b8e4d49c: ;   {static_stub}
  0x000002d1b8e4d49c: 9048 bb00 | 0000 0000 

  0x000002d1b8e4d4a4: ;   {runtime_call nmethod}
  0x000002d1b8e4d4a4: 0000 00e9 | fbff ffff 

  0x000002d1b8e4d4ac: ;   {static_stub}
  0x000002d1b8e4d4ac: 48bb 0000 | 0000 0000 

  0x000002d1b8e4d4b4: ;   {runtime_call nmethod}
  0x000002d1b8e4d4b4: 0000 e9fb 

  0x000002d1b8e4d4b8: ;   {static_stub}
  0x000002d1b8e4d4b8: ffff ff48 | bb00 0000 | 0000 0000 

  0x000002d1b8e4d4c4: ;   {runtime_call nmethod}
  0x000002d1b8e4d4c4: 00e9 fbff 

  0x000002d1b8e4d4c8: ;   {static_stub}
  0x000002d1b8e4d4c8: ffff 48bb | 0000 0000 | 0000 0000 

  0x000002d1b8e4d4d4: ;   {runtime_call nmethod}
  0x000002d1b8e4d4d4: e9fb ffff 

  0x000002d1b8e4d4d8: ;   {static_stub}
  0x000002d1b8e4d4d8: ff48 bb00 | 0000 0000 

  0x000002d1b8e4d4e0: ;   {runtime_call nmethod}
  0x000002d1b8e4d4e0: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x000002d1b8e4d4e8: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x000002d1b8e4d4e8: e813 2a45 

  0x000002d1b8e4d4ec: ;   {external_word}
  0x000002d1b8e4d4ec: 0748 b990 | 7f1a efff | 7f00 0048 

  0x000002d1b8e4d4f8: ;   {runtime_call}
  0x000002d1b8e4d4f8: 83e4 f048 | b8e0 44dd | eeff 7f00 | 00ff d0f4 
[Deopt Handler Code]
  0x000002d1b8e4d508: ;   {section_word}
  0x000002d1b8e4d508: 49ba 08d5 | e4b8 d102 | 0000 4152 

  0x000002d1b8e4d514: ;   {runtime_call DeoptimizationBlob}
  0x000002d1b8e4d514: e987 683a | 07f4 f4f4 | f4f4 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002d20ed55f80, length=13, elements={
0x000002d1b5a95d70, 0x000002d1ccb32cd0, 0x000002d20e9e36e0, 0x000002d20e9e6a90,
0x000002d20e9e7a20, 0x000002d20e9e95e0, 0x000002d20e9ea030, 0x000002d20e9ec020,
0x000002d1ccb3c030, 0x000002d20eabf590, 0x000002d20ecfff00, 0x000002d20ed4c520,
0x000002d21433a990
}

Java Threads: ( => current thread )
=>0x000002d1b5a95d70 JavaThread "main"                              [_thread_in_vm, id=42448, stack(0x000000f184300000,0x000000f184400000) (1024K)]
  0x000002d1ccb32cd0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=4824, stack(0x000000f184700000,0x000000f184800000) (1024K)]
  0x000002d20e9e36e0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=46136, stack(0x000000f184800000,0x000000f184900000) (1024K)]
  0x000002d20e9e6a90 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=36748, stack(0x000000f184900000,0x000000f184a00000) (1024K)]
  0x000002d20e9e7a20 JavaThread "Attach Listener"            daemon [_thread_blocked, id=22752, stack(0x000000f184a00000,0x000000f184b00000) (1024K)]
  0x000002d20e9e95e0 JavaThread "Service Thread"             daemon [_thread_blocked, id=42556, stack(0x000000f184b00000,0x000000f184c00000) (1024K)]
  0x000002d20e9ea030 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=42912, stack(0x000000f184c00000,0x000000f184d00000) (1024K)]
  0x000002d20e9ec020 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=39464, stack(0x000000f184d00000,0x000000f184e00000) (1024K)]
  0x000002d1ccb3c030 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=32256, stack(0x000000f184e00000,0x000000f184f00000) (1024K)]
  0x000002d20eabf590 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=27188, stack(0x000000f184f00000,0x000000f185000000) (1024K)]
  0x000002d20ecfff00 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=24008, stack(0x000000f185000000,0x000000f185100000) (1024K)]
  0x000002d20ed4c520 JavaThread "Notification Thread"        daemon [_thread_blocked, id=19132, stack(0x000000f185100000,0x000000f185200000) (1024K)]
  0x000002d21433a990 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=12616, stack(0x000000f185200000,0x000000f185300000) (1024K)]
Total: 13

Other Threads:
  0x000002d1b5ad4fa0 VMThread "VM Thread"                           [id=17848, stack(0x000000f184600000,0x000000f184700000) (1024K)]
  0x000002d1b5ac5100 WatcherThread "VM Periodic Task Thread"        [id=6100, stack(0x000000f184500000,0x000000f184600000) (1024K)]
  0x000002d1b5ab72c0 WorkerThread "GC Thread#0"                     [id=32568, stack(0x000000f184400000,0x000000f184500000) (1024K)]
  0x000002d2140cc010 WorkerThread "GC Thread#1"                     [id=43632, stack(0x000000f185300000,0x000000f185400000) (1024K)]
  0x000002d2140cc3b0 WorkerThread "GC Thread#2"                     [id=46908, stack(0x000000f185400000,0x000000f185500000) (1024K)]
  0x000002d2143e3e50 WorkerThread "GC Thread#3"                     [id=40388, stack(0x000000f185500000,0x000000f185600000) (1024K)]
  0x000002d2143e5680 WorkerThread "GC Thread#4"                     [id=30548, stack(0x000000f185600000,0x000000f185700000) (1024K)]
  0x000002d2143e5a20 WorkerThread "GC Thread#5"                     [id=16884, stack(0x000000f185700000,0x000000f185800000) (1024K)]
  0x000002d2142a78a0 WorkerThread "GC Thread#6"                     [id=27908, stack(0x000000f185800000,0x000000f185900000) (1024K)]
Total: 9

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45c308] Metaspace_lock - owner thread: 0x000002d1b5a95d70

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002d1cd000000-0x000002d1cdba0000-0x000002d1cdba0000), size 12189696, SharedBaseAddress: 0x000002d1cd000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002d1ce000000-0x000002d20e000000, reserved size: 1073741824
Narrow klass base: 0x000002d1cd000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 8664K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 20% used [0x00000000d5580000,0x00000000d5a981c0,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71ddf38,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 5301K, committed 5504K, reserved 1114112K
  class space    used 554K, committed 640K, reserved 1048576K

Card table byte_map: [0x000002d1c7e50000,0x000002d1c8260000] _byte_map_base: 0x000002d1c7a50000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x000002d1c8510000, 0x000002d1ca510000)
 End Bits:   [0x000002d1ca510000, 0x000002d1cc510000)

Polling page: 0x000002d1b5990000

Metaspace:

Usage:
  Non-class:      4.64 MB used.
      Class:    554.23 KB used.
       Both:      5.18 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       4.75 MB (  7%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     640.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       5.38 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  10.56 MB
       Class:  15.36 MB
        Both:  25.92 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 188.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 86.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 282.
num_chunk_merges: 0.
num_chunk_splits: 179.
num_chunks_enlarged: 113.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=508Kb max_used=508Kb free=119491Kb
 bounds [0x000002d1c0740000, 0x000002d1c09b0000, 0x000002d1c7c70000]
CodeHeap 'profiled nmethods': size=120000Kb used=1982Kb max_used=1982Kb free=118018Kb
 bounds [0x000002d1b8c70000, 0x000002d1b8ee0000, 0x000002d1c01a0000]
CodeHeap 'non-nmethods': size=5760Kb used=1205Kb max_used=1229Kb free=4555Kb
 bounds [0x000002d1c01a0000, 0x000002d1c0410000, 0x000002d1c0740000]
 total_blobs=1682 nmethods=1212 adapters=377
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.842 Thread 0x000002d1ccb3c030 1201       3       sun.net.util.IPAddressUtil::checkHost (156 bytes)
Event: 0.844 Thread 0x000002d1ccb3c030 nmethod 1201 0x000002d1b8e54110 code [0x000002d1b8e545a0, 0x000002d1b8e561f0]
Event: 0.844 Thread 0x000002d1ccb3c030 1202       3       sun.net.util.IPAddressUtil::scan (92 bytes)
Event: 0.844 Thread 0x000002d1ccb3c030 nmethod 1202 0x000002d1b8e56d10 code [0x000002d1b8e56f80, 0x000002d1b8e57a78]
Event: 0.844 Thread 0x000002d1ccb3c030 1203       3       java.lang.reflect.Constructor::newInstanceWithCaller (51 bytes)
Event: 0.844 Thread 0x000002d1ccb3c030 nmethod 1203 0x000002d1b8e57f90 code [0x000002d1b8e58160, 0x000002d1b8e58438]
Event: 0.853 Thread 0x000002d1ccb3c030 1204       3       jdk.internal.org.objectweb.asm.MethodVisitor::<init> (78 bytes)
Event: 0.853 Thread 0x000002d1ccb3c030 nmethod 1204 0x000002d1b8e58590 code [0x000002d1b8e587c0, 0x000002d1b8e58f18]
Event: 0.854 Thread 0x000002d20ecfff00 1205       4       java.util.ArrayList::iterator (9 bytes)
Event: 0.855 Thread 0x000002d20ecfff00 nmethod 1205 0x000002d1c07bdb90 code [0x000002d1c07bdd20, 0x000002d1c07bde28]
Event: 0.856 Thread 0x000002d1ccb3c030 1206       3       java.util.ArrayList::toArray (12 bytes)
Event: 0.856 Thread 0x000002d1ccb3c030 nmethod 1206 0x000002d1b8e59210 code [0x000002d1b8e593c0, 0x000002d1b8e595a0]
Event: 0.857 Thread 0x000002d1ccb3c030 1207   !   3       jdk.internal.loader.URLClassPath$JarLoader::getResource (40 bytes)
Event: 0.857 Thread 0x000002d1ccb3c030 nmethod 1207 0x000002d1b8e59690 code [0x000002d1b8e598a0, 0x000002d1b8e59e30]
Event: 0.861 Thread 0x000002d20ecfff00 1208       4       java.lang.StringLatin1::toUpperCase (186 bytes)
Event: 0.863 Thread 0x000002d1ccb3c030 1210       3       java.net.URL::getDefaultPort (8 bytes)
Event: 0.863 Thread 0x000002d1ccb3c030 nmethod 1210 0x000002d1b8e5a010 code [0x000002d1b8e5a1c0, 0x000002d1b8e5a3b0]
Event: 0.864 Thread 0x000002d1ccb3c030 1211       3       java.io.FilterInputStream::<init> (10 bytes)
Event: 0.864 Thread 0x000002d1ccb3c030 nmethod 1211 0x000002d1b8e5a490 code [0x000002d1b8e5a640, 0x000002d1b8e5a828]
Event: 0.865 Thread 0x000002d1ccb3c030 1212   !   3       java.util.concurrent.ConcurrentHashMap::computeIfAbsent (576 bytes)

GC Heap History (2 events):
Event: 0.724 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4277K, committed 4480K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}
Event: 0.730 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3447K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71ddf38,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 4277K, committed 4480K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}

Dll operation events (8 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.034 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.091 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.095 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.097 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.100 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.115 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.187 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (20 events):
Event: 0.771 Thread 0x000002d1b5a95d70 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002d1c07a9be4 relative=0x00000000000002a4
Event: 0.771 Thread 0x000002d1b5a95d70 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002d1c07a9be4 method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.771 Thread 0x000002d1b5a95d70 DEOPT PACKING pc=0x000002d1c07a9be4 sp=0x000000f1843fe7b0
Event: 0.771 Thread 0x000002d1b5a95d70 DEOPT UNPACKING pc=0x000002d1c01f3aa2 sp=0x000000f1843fe648 mode 2
Event: 0.773 Thread 0x000002d1b5a95d70 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002d1c07a6f2c relative=0x000000000000034c
Event: 0.773 Thread 0x000002d1b5a95d70 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002d1c07a6f2c method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.773 Thread 0x000002d1b5a95d70 DEOPT PACKING pc=0x000002d1c07a6f2c sp=0x000000f1843fe750
Event: 0.773 Thread 0x000002d1b5a95d70 DEOPT UNPACKING pc=0x000002d1c01f3aa2 sp=0x000000f1843fe658 mode 2
Event: 0.773 Thread 0x000002d1b5a95d70 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002d1c07a4554 relative=0x00000000000000b4
Event: 0.773 Thread 0x000002d1b5a95d70 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002d1c07a4554 method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.773 Thread 0x000002d1b5a95d70 DEOPT PACKING pc=0x000002d1c07a4554 sp=0x000000f1843fe7d0
Event: 0.773 Thread 0x000002d1b5a95d70 DEOPT UNPACKING pc=0x000002d1c01f3aa2 sp=0x000000f1843fe768 mode 2
Event: 0.773 Thread 0x000002d1b5a95d70 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002d1c078c1dc relative=0x00000000000005bc
Event: 0.773 Thread 0x000002d1b5a95d70 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002d1c078c1dc method=java.util.Collections$UnmodifiableCollection$1.<init>(Ljava/util/Collections$UnmodifiableCollection;)V @ 17 c2
Event: 0.773 Thread 0x000002d1b5a95d70 DEOPT PACKING pc=0x000002d1c078c1dc sp=0x000000f1843fe800
Event: 0.773 Thread 0x000002d1b5a95d70 DEOPT UNPACKING pc=0x000002d1c01f3aa2 sp=0x000000f1843fe748 mode 2
Event: 0.861 Thread 0x000002d1b5a95d70 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002d1c078ce88 relative=0x00000000000001a8
Event: 0.861 Thread 0x000002d1b5a95d70 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002d1c078ce88 method=java.lang.invoke.VarHandle.checkAccessModeThenIsDirect(Ljava/lang/invoke/VarHandle$AccessDescriptor;)Z @ 4 c2
Event: 0.861 Thread 0x000002d1b5a95d70 DEOPT PACKING pc=0x000002d1c078ce88 sp=0x000000f1843fe7b0
Event: 0.861 Thread 0x000002d1b5a95d70 DEOPT UNPACKING pc=0x000002d1c01f3aa2 sp=0x000000f1843fe6e8 mode 2

Classes loaded (20 events):
Event: 0.850 Loading class sun/nio/ch/NativeThreadSet
Event: 0.850 Loading class sun/nio/ch/NativeThreadSet done
Event: 0.850 Loading class sun/nio/ch/FileLockImpl
Event: 0.850 Loading class java/nio/channels/FileLock
Event: 0.850 Loading class java/nio/channels/FileLock done
Event: 0.850 Loading class sun/nio/ch/FileLockImpl done
Event: 0.850 Loading class sun/nio/ch/NativeThread
Event: 0.850 Loading class sun/nio/ch/NativeThread done
Event: 0.850 Loading class sun/nio/ch/FileLockTable
Event: 0.850 Loading class sun/nio/ch/FileLockTable done
Event: 0.850 Loading class sun/nio/ch/FileKey
Event: 0.850 Loading class sun/nio/ch/FileKey done
Event: 0.850 Loading class sun/nio/ch/FileLockTable$FileLockReference
Event: 0.850 Loading class sun/nio/ch/FileLockTable$FileLockReference done
Event: 0.853 Loading class java/io/DeleteOnExitHook
Event: 0.854 Loading class java/io/DeleteOnExitHook done
Event: 0.854 Loading class java/io/DeleteOnExitHook$1
Event: 0.854 Loading class java/io/DeleteOnExitHook$1 done
Event: 0.855 Loading class java/util/ComparableTimSort
Event: 0.855 Loading class java/util/ComparableTimSort done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.508 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d6498640}: sun/net/www/protocol/c/Handler> (0x00000000d6498640) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.521 Thread 0x000002d1b5a95d70 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f2750}> (0x00000000d64f2750) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.522 Thread 0x000002d1b5a95d70 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f3c68}> (0x00000000d64f3c68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.523 Thread 0x000002d1b5a95d70 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f4aa8}> (0x00000000d64f4aa8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.524 Thread 0x000002d1b5a95d70 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f57a0}> (0x00000000d64f57a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.531 Thread 0x000002d1b5a95d70 Exception <a 'java/io/FileNotFoundException'{0x00000000d652fae8}> (0x00000000d652fae8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.541 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d656b398}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d656b398) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.544 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d657e9e0}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d657e9e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.546 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6595c38}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6595c38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.573 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d662a118}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000d662a118) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.676 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6aa4b98}: 'java.lang.ClassLoader java.lang.ClassLoader.getPlatformClassLoader(java.lang.Class)'> (0x00000000d6aa4b98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.735 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d55be028}: sun/net/www/protocol/c/Handler> (0x00000000d55be028) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.735 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d55bf3a8}: sun/net/www/protocol/f/Handler> (0x00000000d55bf3a8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.735 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d55c0528}: sun/net/www/protocol/c/Handler> (0x00000000d55c0528) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.746 Thread 0x000002d1b5a95d70 Exception <a 'java/io/FileNotFoundException'{0x00000000d55e54e8}> (0x00000000d55e54e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.771 Thread 0x000002d1b5a95d70 Implicit null exception at 0x000002d1c07a4949 to 0x000002d1c07a5308
Event: 0.771 Thread 0x000002d1b5a95d70 Implicit null exception at 0x000002d1c07a99a0 to 0x000002d1c07a9bcc
Event: 0.772 Thread 0x000002d1b5a95d70 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57918b0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d57918b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.773 Thread 0x000002d1b5a95d70 Implicit null exception at 0x000002d1c07a6c40 to 0x000002d1c07a6f14
Event: 0.773 Thread 0x000002d1b5a95d70 Implicit null exception at 0x000002d1c07a44c0 to 0x000002d1c07a4545

ZGC Phase Switch (0 events):
No events

VM Operations (14 events):
Event: 0.070 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.070 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.126 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.126 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.474 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.474 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.485 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.485 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.708 Executing VM operation: ICBufferFull
Event: 0.708 Executing VM operation: ICBufferFull done
Event: 0.717 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.717 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.724 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 0.730 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (13 events):
Event: 0.024 Thread 0x000002d1b5a95d70 Thread added: 0x000002d1b5a95d70
Event: 0.043 Thread 0x000002d1b5a95d70 Thread added: 0x000002d1ccb32cd0
Event: 0.043 Thread 0x000002d1b5a95d70 Thread added: 0x000002d20e9e36e0
Event: 0.043 Thread 0x000002d1b5a95d70 Thread added: 0x000002d20e9e6a90
Event: 0.044 Thread 0x000002d1b5a95d70 Thread added: 0x000002d20e9e7a20
Event: 0.044 Thread 0x000002d1b5a95d70 Thread added: 0x000002d20e9e95e0
Event: 0.044 Thread 0x000002d1b5a95d70 Thread added: 0x000002d20e9ea030
Event: 0.044 Thread 0x000002d1b5a95d70 Thread added: 0x000002d20e9ec020
Event: 0.044 Thread 0x000002d1b5a95d70 Thread added: 0x000002d1ccb3c030
Event: 0.062 Thread 0x000002d1b5a95d70 Thread added: 0x000002d20eabf590
Event: 0.180 Thread 0x000002d1ccb3c030 Thread added: 0x000002d20ecfff00
Event: 0.318 Thread 0x000002d1b5a95d70 Thread added: 0x000002d20ed4c520
Event: 0.406 Thread 0x000002d20ecfff00 Thread added: 0x000002d21433a990


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-84b1e07cd02b50c122ef8ebde43307f8-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:32 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (2532M free)
TotalPageFile size 61318M (AvailPageFile size 1177M)
current process WorkingSet (physical memory assigned to process): 88M, peak: 88M
current process commit charge ("private bytes"): 239M, peak: 239M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
