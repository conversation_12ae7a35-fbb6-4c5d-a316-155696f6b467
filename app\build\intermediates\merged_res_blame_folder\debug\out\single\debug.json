[{"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\xml_data_extraction_rules.xml.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\xml\\data_extraction_rules.xml"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-mdpi_ic_launcher.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\layout_activity_main.xml.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\layout\\activity_main.xml"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\drawable_ic_launcher_background.xml.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\drawable\\ic_launcher_background.xml"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\xml_backup_rules.xml.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\xml\\backup_rules.xml"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\mipmap-hdpi_ic_launcher.webp.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\src\\main\\res\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\res\\merged\\debug\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "F:\\obj_project\\NewFWG-2\\app\\build\\generated\\res\\pngs\\debug\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}]