{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-uz\\values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,238,321,416,543,627,691,794,864,931,1040,1107,1166,1240,1303,1357,1472,1530,1592,1646,1721,1850,1940,2029,2140,2222,2304,2390,2457,2523,2596,2674,2760,2832,2909,2984,3055,3149,3228,3324,3418,3492,3568,3654,3707,3773,3858,3949,4011,4075,4138,4240,4331,4427,4519", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,94,126,83,63,102,69,66,108,66,58,73,62,53,114,57,61,53,74,128,89,88,110,81,81,85,66,65,72,77,85,71,76,74,70,93,78,95,93,73,75,85,52,65,84,90,61,63,62,101,90,95,91,82", "endOffsets": "233,316,411,538,622,686,789,859,926,1035,1102,1161,1235,1298,1352,1467,1525,1587,1641,1716,1845,1935,2024,2135,2217,2299,2385,2452,2518,2591,2669,2755,2827,2904,2979,3050,3144,3223,3319,3413,3487,3563,3649,3702,3768,3853,3944,4006,4070,4133,4235,4326,4422,4514,4597"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2959,3042,3137,3264,5589,5653,5756,5826,5893,6002,6069,6128,6202,6265,6319,6434,6492,6554,6608,6683,6812,6902,6991,7102,7184,7266,7352,7419,7485,7558,7636,7722,7794,7871,7946,8017,8111,8190,8286,8380,8454,8530,8616,8669,8735,8820,8911,8973,9037,9100,9202,9293,9389,9481", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,82,94,126,83,63,102,69,66,108,66,58,73,62,53,114,57,61,53,74,128,89,88,110,81,81,85,66,65,72,77,85,71,76,74,70,93,78,95,93,73,75,85,52,65,84,90,61,63,62,101,90,95,91,82", "endOffsets": "283,3037,3132,3259,3343,5648,5751,5821,5888,5997,6064,6123,6197,6260,6314,6429,6487,6549,6603,6678,6807,6897,6986,7097,7179,7261,7347,7414,7480,7553,7631,7717,7789,7866,7941,8012,8106,8185,8281,8375,8449,8525,8611,8664,8730,8815,8906,8968,9032,9095,9197,9288,9384,9476,9559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "288,393,488,588,670,770,887,972,1050,1141,1234,1329,1423,1517,1610,1705,1800,1891,1983,2067,2177,2283,2383,2491,2597,2699,2860,9564", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "388,483,583,665,765,882,967,1045,1136,1229,1324,1418,1512,1605,1700,1795,1886,1978,2062,2172,2278,2378,2486,2592,2694,2855,2954,9643"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3348,3453,3603,3732,3841,3986,4119,4239,4495,4667,4775,4934,5066,5220,5382,5448,5509", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "3448,3598,3727,3836,3981,4114,4234,4340,4662,4770,4929,5061,5215,5377,5443,5504,5584"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4345", "endColumns": "149", "endOffsets": "4490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9648", "endColumns": "100", "endOffsets": "9744"}}]}]}