{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-vi\\values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,220,298,397,511,591,654,748,822,881,967,1028,1086,1150,1211,1265,1382,1439,1499,1553,1628,1755,1839,1917,2017,2101,2179,2270,2337,2403,2471,2547,2628,2707,2782,2855,2931,3020,3097,3188,3282,3356,3426,3519,3568,3634,3719,3805,3867,3931,3994,4093,4198,4296,4401", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,98,113,79,62,93,73,58,85,60,57,63,60,53,116,56,59,53,74,126,83,77,99,83,77,90,66,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,77", "endOffsets": "215,293,392,506,586,649,743,817,876,962,1023,1081,1145,1206,1260,1377,1434,1494,1548,1623,1750,1834,1912,2012,2096,2174,2265,2332,2398,2466,2542,2623,2702,2777,2850,2926,3015,3092,3183,3277,3351,3421,3514,3563,3629,3714,3800,3862,3926,3989,4088,4193,4291,4396,4474"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2963,3041,3140,3254,5568,5631,5725,5799,5858,5944,6005,6063,6127,6188,6242,6359,6416,6476,6530,6605,6732,6816,6894,6994,7078,7156,7247,7314,7380,7448,7524,7605,7684,7759,7832,7908,7997,8074,8165,8259,8333,8403,8496,8545,8611,8696,8782,8844,8908,8971,9070,9175,9273,9378", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,77,98,113,79,62,93,73,58,85,60,57,63,60,53,116,56,59,53,74,126,83,77,99,83,77,90,66,65,67,75,80,78,74,72,75,88,76,90,93,73,69,92,48,65,84,85,61,63,62,98,104,97,104,77", "endOffsets": "265,3036,3135,3249,3329,5626,5720,5794,5853,5939,6000,6058,6122,6183,6237,6354,6411,6471,6525,6600,6727,6811,6889,6989,7073,7151,7242,7309,7375,7443,7519,7600,7679,7754,7827,7903,7992,8069,8160,8254,8328,8398,8491,8540,8606,8691,8777,8839,8903,8966,9065,9170,9268,9373,9451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3334,3436,3598,3723,3832,3997,4127,4246,4478,4651,4758,4915,5045,5204,5353,5421,5485", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "3431,3593,3718,3827,3992,4122,4241,4345,4646,4753,4910,5040,5199,5348,5416,5480,5563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4350", "endColumns": "127", "endOffsets": "4473"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "270,377,479,588,672,775,894,972,1048,1139,1232,1327,1421,1521,1614,1709,1803,1894,1985,2069,2173,2281,2382,2487,2602,2707,2864,9456", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "372,474,583,667,770,889,967,1043,1134,1227,1322,1416,1516,1609,1704,1798,1889,1980,2064,2168,2276,2377,2482,2597,2702,2859,2958,9536"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9541", "endColumns": "100", "endOffsets": "9637"}}]}]}