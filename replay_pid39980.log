JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 350 ciObject found
instanceKlass org/apache/groovy/dateutil/extensions/DateUtilStaticExtensions
instanceKlass org/apache/groovy/dateutil/extensions/DateUtilExtensions
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener$$Lambda$204
instanceKlass org/codehaus/groovy/runtime/metaclass/MethodHelper
instanceKlass java/time/zone/ZoneRules
instanceKlass java/time/chrono/Era
instanceKlass java/time/chrono/Chronology
instanceKlass java/time/format/DateTimeFormatter
instanceKlass java/time/temporal/ValueRange
instanceKlass java/time/temporal/TemporalQuery
instanceKlass java/time/MonthDay
instanceKlass java/time/Duration
instanceKlass java/time/Period
instanceKlass java/time/Instant
instanceKlass java/time/YearMonth
instanceKlass java/time/Year
instanceKlass java/time/OffsetDateTime
instanceKlass java/time/LocalTime
instanceKlass java/time/OffsetTime
instanceKlass java/time/ZonedDateTime
instanceKlass java/time/chrono/ChronoZonedDateTime
instanceKlass java/time/LocalDateTime
instanceKlass java/time/chrono/ChronoLocalDateTime
instanceKlass java/time/LocalDate
instanceKlass java/time/chrono/ChronoLocalDate
instanceKlass java/time/temporal/TemporalAdjuster
instanceKlass java/time/chrono/ChronoPeriod
instanceKlass java/time/temporal/TemporalAmount
instanceKlass java/time/temporal/TemporalUnit
instanceKlass java/time/temporal/Temporal
instanceKlass java/time/temporal/TemporalAccessor
instanceKlass java/time/temporal/TemporalField
instanceKlass org/apache/groovy/datetime/extensions/DateTimeStaticExtensions
instanceKlass org/apache/groovy/datetime/extensions/DateTimeExtensions
instanceKlass org/codehaus/groovy/runtime/m12n/ExtensionModule
instanceKlass org/codehaus/groovy/runtime/m12n/PropertiesModuleFactory
instanceKlass org/codehaus/groovy/util/URLStreams
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$DefaultModuleListener
instanceKlass org/codehaus/groovy/runtime/m12n/ExtensionModuleScanner
instanceKlass java/time/ZoneId
instanceKlass org/codehaus/groovy/runtime/DefaultGroovyStaticMethods
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$Lambda$203
instanceKlass org/codehaus/groovy/runtime/RangeInfo
instanceKlass java/util/stream/Stream$Builder
instanceKlass java/util/function/LongFunction
instanceKlass java/util/function/DoubleFunction
instanceKlass java/util/function/ToLongFunction
instanceKlass java/util/function/ToIntFunction
instanceKlass java/util/function/ToDoubleFunction
instanceKlass java/util/function/DoublePredicate
instanceKlass java/util/function/IntPredicate
instanceKlass java/util/function/LongPredicate
instanceKlass java/util/stream/DoubleStream
instanceKlass java/util/stream/LongStream
instanceKlass java/util/OptionalInt
instanceKlass java/util/OptionalLong
instanceKlass java/util/OptionalDouble
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$Lambda$202
instanceKlass org/codehaus/groovy/runtime/NumberAwareComparator
instanceKlass org/codehaus/groovy/runtime/EncodingGroovyMethods
instanceKlass org/codehaus/groovy/reflection/CachedClass$CachedMethodComparatorByName
instanceKlass org/codehaus/groovy/reflection/CachedMethod$MyComparator
instanceKlass org/codehaus/groovy/runtime/DefaultGroovyMethodsSupport
instanceKlass org/codehaus/groovy/ast/Variable
instanceKlass org/codehaus/groovy/vmplugin/v8/Java8
instanceKlass org/codehaus/groovy/vmplugin/VMPluginFactory$$Lambda$201
instanceKlass org/codehaus/groovy/vmplugin/VMPluginFactory
instanceKlass org/codehaus/groovy/reflection/ReflectionUtils
instanceKlass java/util/stream/AbstractSpinedBuffer
instanceKlass java/util/stream/Node$Builder
instanceKlass java/util/stream/Node$OfDouble
instanceKlass java/util/stream/Node$OfLong
instanceKlass java/util/stream/Node$OfInt
instanceKlass java/util/stream/Node$OfPrimitive
instanceKlass java/util/stream/Nodes$EmptyNode
instanceKlass java/util/stream/Node
instanceKlass java/util/stream/Nodes
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$200
instanceKlass java/util/function/IntFunction
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$199
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$198
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$197
instanceKlass org/codehaus/groovy/runtime/memoize/MemoizeCache
instanceKlass org/codehaus/groovy/reflection/CachedClass$3$$Lambda$196
instanceKlass org/codehaus/groovy/reflection/stdclasses/CachedSAMClass$$Lambda$195
instanceKlass org/codehaus/groovy/reflection/stdclasses/CachedSAMClass$$Lambda$194
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass sun/invoke/util/ValueConversions$WrapperCache
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller$T
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller$T
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller$2
instanceKlass java/lang/invoke/MethodHandleImpl$BindCaller
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl$$Lambda$193
instanceKlass java/util/stream/IntStream
instanceKlass org/codehaus/groovy/transform/trait/Traits$Implemented
instanceKlass org/codehaus/groovy/util/ReferenceType$HardRef
instanceKlass org/codehaus/groovy/util/ManagedReference
instanceKlass org/codehaus/groovy/reflection/ClassInfo$GlobalClassSet
instanceKlass org/apache/groovy/util/SystemUtil
instanceKlass org/codehaus/groovy/reflection/GroovyClassValue
instanceKlass org/codehaus/groovy/reflection/GroovyClassValueFactory
instanceKlass org/codehaus/groovy/reflection/ClassInfo$1
instanceKlass org/codehaus/groovy/reflection/GroovyClassValue$ComputeValue
instanceKlass org/codehaus/groovy/util/ComplexKeyHashMap$Entry
instanceKlass org/codehaus/groovy/util/ComplexKeyHashMap$EntryIterator
instanceKlass org/codehaus/groovy/reflection/ReflectionCache
instanceKlass java/lang/Process
instanceKlass java/util/Timer
instanceKlass java/util/TimerTask
instanceKlass groovy/lang/groovydoc/Groovydoc
instanceKlass groovy/lang/ListWithDefault
instanceKlass groovy/lang/Range
instanceKlass groovy/util/BufferedIterator
instanceKlass org/codehaus/groovy/reflection/GeneratedMetaMethod$DgmMethodRecord
instanceKlass groovy/lang/MetaClassRegistry$MetaClassCreationHandle
instanceKlass org/codehaus/groovy/runtime/m12n/ExtensionModuleRegistry
instanceKlass org/codehaus/groovy/util/Reference
instanceKlass org/codehaus/groovy/util/ReferenceManager
instanceKlass org/codehaus/groovy/util/ReferenceBundle
instanceKlass org/codehaus/groovy/util/ManagedConcurrentLinkedQueue
instanceKlass groovy/lang/MetaClassRegistryChangeEventListener
instanceKlass org/codehaus/groovy/runtime/m12n/ExtensionModuleScanner$ExtensionModuleListener
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaClassRegistryImpl
instanceKlass org/codehaus/groovy/runtime/InvokerHelper
instanceKlass org/gradle/internal/extensibility/ExtensionsStorage
instanceKlass org/gradle/api/plugins/ExtraPropertiesExtension
instanceKlass org/gradle/internal/extensibility/DefaultConvention
instanceKlass org/gradle/api/internal/plugins/ExtensionContainerInternal
instanceKlass org/gradle/api/internal/coerce/StringToEnumTransformer
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaMethodIndex
instanceKlass org/codehaus/groovy/vmplugin/VMPlugin
instanceKlass org/codehaus/groovy/reflection/ClassInfo
instanceKlass org/codehaus/groovy/util/Finalizable
instanceKlass org/codehaus/groovy/ast/ASTNode
instanceKlass org/codehaus/groovy/ast/NodeMetaDataHandler
instanceKlass groovy/lang/groovydoc/GroovydocHolder
instanceKlass groovyjarjarasm/asm/Opcodes
instanceKlass groovyjarjarasm/asm/ClassVisitor
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Entry
instanceKlass org/codehaus/groovy/util/FastArray
instanceKlass org/codehaus/groovy/runtime/metaclass/MetaMethodIndex$Header
instanceKlass org/codehaus/groovy/reflection/CachedClass
instanceKlass org/codehaus/groovy/util/SingleKeyHashMap$Copier
instanceKlass groovy/lang/MetaClassImpl$MethodIndexAction
instanceKlass org/codehaus/groovy/runtime/callsite/CallSite
instanceKlass org/codehaus/groovy/reflection/ParameterTypes
instanceKlass org/codehaus/groovy/util/ComplexKeyHashMap
instanceKlass groovy/lang/MetaClassImpl
instanceKlass groovy/lang/MutableMetaClass
instanceKlass org/gradle/internal/metaobject/BeanDynamicObject$MetaClassAdapter
instanceKlass org/gradle/api/internal/coerce/PropertySetTransformer
instanceKlass org/gradle/api/internal/coerce/MethodArgumentsTransformer
instanceKlass org/gradle/vcs/VcsMappings
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VersionControlSettingsServices
instanceKlass org/gradle/plugin/internal/PluginUsePluginServiceRegistry$SettingsScopeServices
instanceKlass org/gradle/internal/service/scopes/SettingsScopeServices$1
instanceKlass org/gradle/initialization/IncludedBuildSpec
instanceKlass org/gradle/vcs/SourceControl
instanceKlass org/gradle/plugin/management/PluginManagementSpec
instanceKlass org/gradle/initialization/DefaultProjectDescriptor
instanceKlass org/gradle/api/initialization/ProjectDescriptor
instanceKlass org/gradle/normalization/InputNormalizationHandler
instanceKlass org/gradle/api/ProjectState
instanceKlass org/gradle/api/attributes/DocsType
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemAttributesDescriber
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$TargetJvmEnvironmentDisambiguationRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$TargetJvmEnvironmentCompatibilityRules
instanceKlass org/gradle/api/attributes/java/TargetJvmEnvironment
instanceKlass org/gradle/api/internal/attributes/DefaultOrderedDisambiguationRule
instanceKlass org/gradle/api/internal/attributes/DefaultOrderedCompatibilityRule
instanceKlass org/gradle/api/internal/attributes/AttributeMatchingRules
instanceKlass org/gradle/api/attributes/java/TargetJvmVersion
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$BundlingDisambiguationRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$BundlingCompatibilityRules
instanceKlass org/gradle/api/attributes/Bundling
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$$Lambda$192
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$LibraryElementsDisambiguationRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$LibraryElementsCompatibilityRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$1
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$UsageDisambiguationRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport$UsageCompatibilityRules
instanceKlass org/gradle/api/internal/artifacts/JavaEcosystemSupport
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingHandler$$Lambda$191
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingHandler$$Lambda$190
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DependencyResolutionScopeServices$$Lambda$189
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingHandler
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/DefaultRootComponentMetadataBuilder$MetadataHolder
instanceKlass org/gradle/internal/component/local/model/DefaultLocalComponentMetadata
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfigurationContainer$$Lambda$188
instanceKlass org/gradle/api/artifacts/Configuration$Namer
instanceKlass org/gradle/api/internal/artifacts/configurations/MutationValidator
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationInternal
instanceKlass org/gradle/internal/deprecation/DeprecatableConfiguration
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationsProvider
instanceKlass org/gradle/api/internal/file/copy/CopySpecSource
instanceKlass org/gradle/api/file/CopySpec
instanceKlass org/gradle/api/file/CopyProcessingSpec
instanceKlass org/gradle/api/file/ContentFilterable
instanceKlass org/gradle/api/file/CopySourceSpec
instanceKlass org/gradle/api/artifacts/ConfigurablePublishArtifact
instanceKlass org/gradle/api/internal/attributes/AttributeDesugaring
instanceKlass org/gradle/api/internal/artifacts/transform/ConsumerProvidedVariantFinder
instanceKlass org/gradle/api/internal/artifacts/transform/VariantSelector
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultArtifactTransforms
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultConfigurationResolver$$Lambda$187
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/ResolvedConfigurationBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/DependencyArtifactsVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultConfigurationResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/projectresult/ResolvedLocalComponentsResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ShortCircuitEmptyConfigurationResolver
instanceKlass org/gradle/api/artifacts/result/ResolutionResult
instanceKlass org/gradle/api/internal/artifacts/ResolveContext
instanceKlass org/gradle/api/artifacts/ResolvedConfiguration
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/VisitedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ErrorHandlingConfigurationResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet$TransformSourceVisitor
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformationNodeFactory
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$$Lambda$186
instanceKlass org/gradle/api/internal/artifacts/transform/TransformedProjectArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet$Artifacts
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$$Lambda$185
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$Factory
instanceKlass org/gradle/api/internal/artifacts/transform/AbstractTransformedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/transform/ExtraExecutionGraphDependenciesResolverFactory
instanceKlass org/gradle/api/internal/artifacts/transform/VariantDefinition
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedVariant
instanceKlass org/gradle/api/internal/artifacts/type/DefaultArtifactTypeRegistry
instanceKlass org/gradle/api/artifacts/transform/TransformParameters$None
instanceKlass org/gradle/api/artifacts/transform/TransformParameters
instanceKlass org/gradle/api/artifacts/transform/TransformAction
instanceKlass org/gradle/api/artifacts/transform/TransformSpec
instanceKlass org/gradle/api/artifacts/transform/VariantTransform
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultVariantTransformRegistry$RecordingRegistration
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultVariantTransformRegistry
instanceKlass org/gradle/api/internal/artifacts/ArtifactTransformRegistration
instanceKlass org/gradle/api/internal/artifacts/transform/Transformer
instanceKlass org/gradle/api/internal/tasks/properties/PropertyVisitor
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformationRegistrationFactory
instanceKlass org/gradle/api/reflect/InjectionPointQualifier
instanceKlass org/gradle/api/internal/tasks/properties/AbstractPropertyNode
instanceKlass org/gradle/api/internal/tasks/properties/bean/RuntimeBeanNodeFactory
instanceKlass org/gradle/api/internal/tasks/properties/DefaultPropertyWalker
instanceKlass org/gradle/api/internal/tasks/properties/DefaultTypeMetadataStore$$Lambda$184
instanceKlass org/gradle/api/internal/tasks/properties/DefaultTypeMetadataStore$$Lambda$183
instanceKlass org/gradle/api/internal/tasks/properties/TypeMetadata
instanceKlass org/gradle/internal/reflect/PropertyMetadata
instanceKlass org/gradle/api/internal/tasks/properties/DefaultTypeMetadataStore
instanceKlass org/gradle/api/internal/tasks/properties/TypeMetadataStore
instanceKlass org/gradle/api/internal/tasks/properties/InspectionSchemeFactory$InspectionSchemeImpl
instanceKlass org/gradle/api/internal/tasks/properties/InspectionScheme
instanceKlass org/apache/commons/lang/builder/HashCodeBuilder
instanceKlass com/google/common/base/Equivalence$Wrapper
instanceKlass org/gradle/internal/reflect/Methods
instanceKlass java/util/stream/Collectors$$Lambda$182
instanceKlass java/util/stream/Collectors$$Lambda$181
instanceKlass java/util/stream/Collectors$$Lambda$180
instanceKlass java/util/stream/Collectors$$Lambda$179
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadataStore$$Lambda$178
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$$Lambda$177
instanceKlass org/gradle/internal/scripts/ScriptOrigin
instanceKlass org/gradle/api/internal/PolymorphicDomainObjectContainerInternal
instanceKlass org/gradle/api/ExtensiblePolymorphicDomainObjectContainer
instanceKlass org/gradle/api/internal/rules/NamedDomainObjectFactoryRegistry
instanceKlass org/gradle/util/internal/ConfigureUtil$WrappedConfigureAction
instanceKlass org/gradle/util/internal/ClosureBackedAction
instanceKlass org/gradle/internal/reflect/AnnotationCategory$1
instanceKlass org/gradle/work/NormalizeLineEndings
instanceKlass org/gradle/api/tasks/IgnoreEmptyDirectories
instanceKlass org/gradle/api/tasks/Optional
instanceKlass org/gradle/api/tasks/PathSensitive
instanceKlass org/gradle/api/tasks/CompileClasspath
instanceKlass org/gradle/api/tasks/Classpath
instanceKlass org/gradle/api/tasks/SkipWhenEmpty
instanceKlass org/gradle/work/Incremental
instanceKlass org/gradle/api/tasks/UntrackedTask
instanceKlass org/gradle/work/DisableCachingByDefault
instanceKlass org/gradle/api/artifacts/transform/CacheableTransform
instanceKlass org/gradle/api/tasks/CacheableTask
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadataStore$1
instanceKlass org/gradle/internal/reflect/validation/TypeValidationContext
instanceKlass org/gradle/internal/reflect/annotations/TypeAnnotationMetadata
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadataStore
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$$Lambda$176
instanceKlass org/gradle/api/internal/AbstractTask
instanceKlass org/gradle/api/internal/TaskInternal
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$$Lambda$175
instanceKlass org/gradle/internal/execution/caching/CachingDisabledReason
instanceKlass org/gradle/internal/execution/DeferredExecutionHandler
instanceKlass org/gradle/internal/execution/UnitOfWork
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformerInvocationFactory
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$ArtifactTransformResolutionGradleUserHomeServices$1
instanceKlass org/gradle/internal/execution/workspace/impl/DefaultImmutableWorkspaceProvider$$Lambda$174
instanceKlass org/gradle/internal/execution/workspace/impl/DefaultImmutableWorkspaceProvider
instanceKlass org/gradle/cache/ManualEvictionInMemoryCache
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory$CrossBuildCacheRetainingDataFromPreviousBuild
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGradleUserHomeScopeServices$$Lambda$173
instanceKlass org/gradle/internal/Try
instanceKlass org/gradle/internal/snapshot/impl/ImplementationSnapshotSerializer
instanceKlass org/gradle/internal/execution/history/impl/FileSystemSnapshotSerializer
instanceKlass org/gradle/internal/execution/history/impl/FileCollectionFingerprintSerializer
instanceKlass org/gradle/internal/execution/history/PreviousExecutionState
instanceKlass org/gradle/internal/execution/history/impl/DefaultExecutionHistoryStore
instanceKlass org/gradle/api/internal/changedetection/state/DefaultExecutionHistoryCacheAccess
instanceKlass org/gradle/internal/execution/ExecutionResult
instanceKlass org/gradle/internal/execution/UnitOfWork$ExecutionRequest
instanceKlass org/gradle/internal/execution/steps/ExecuteStep
instanceKlass org/gradle/internal/execution/steps/RemovePreviousOutputsStep
instanceKlass org/gradle/internal/execution/steps/ResolveInputChangesStep
instanceKlass org/gradle/internal/execution/steps/TimeoutStep
instanceKlass org/gradle/internal/execution/steps/CreateOutputsStep
instanceKlass org/gradle/internal/execution/steps/StoreExecutionStateStep
instanceKlass org/gradle/internal/execution/steps/BroadcastChangingOutputsStep
instanceKlass org/gradle/internal/execution/history/AfterExecutionState
instanceKlass org/gradle/internal/execution/history/OutputExecutionState
instanceKlass org/gradle/internal/execution/steps/SkipUpToDateStep
instanceKlass org/gradle/internal/execution/steps/IncrementalChangesContext
instanceKlass org/gradle/internal/execution/steps/CachingContext
instanceKlass org/gradle/internal/execution/steps/ValidationFinishedContext
instanceKlass org/gradle/internal/execution/steps/BeforeExecutionContext
instanceKlass org/gradle/internal/execution/steps/PreviousExecutionContext
instanceKlass org/gradle/internal/execution/steps/WorkspaceContext
instanceKlass org/gradle/internal/execution/history/changes/IncrementalInputProperties
instanceKlass org/gradle/internal/execution/fingerprint/InputFingerprinter$InputVisitor
instanceKlass org/gradle/internal/execution/steps/ResolveChangesStep
instanceKlass org/gradle/internal/execution/steps/CachingResult
instanceKlass org/gradle/internal/execution/ExecutionEngine$Result
instanceKlass org/gradle/internal/execution/steps/UpToDateResult
instanceKlass org/gradle/internal/execution/steps/AfterExecutionResult
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$NoOpCachingStateStep
instanceKlass org/gradle/internal/reflect/validation/ValidationProblemBuilder
instanceKlass org/gradle/internal/execution/steps/ValidateStep
instanceKlass org/gradle/internal/execution/history/BeforeExecutionState
instanceKlass org/gradle/internal/execution/history/InputExecutionState
instanceKlass org/gradle/internal/execution/history/ExecutionState
instanceKlass org/gradle/internal/execution/UnitOfWork$ImplementationVisitor
instanceKlass org/gradle/internal/execution/steps/BuildOperationStep
instanceKlass org/gradle/internal/execution/steps/Result
instanceKlass org/gradle/internal/execution/steps/RemoveUntrackedExecutionStateStep
instanceKlass org/gradle/internal/execution/steps/LoadPreviousExecutionStateStep
instanceKlass org/gradle/internal/execution/steps/AssignWorkspaceStep
instanceKlass org/gradle/internal/execution/steps/IdentityCacheStep
instanceKlass org/gradle/internal/execution/steps/IdentityContext
instanceKlass org/gradle/internal/execution/steps/ExecutionRequestContext
instanceKlass org/gradle/internal/execution/steps/Context
instanceKlass org/gradle/internal/execution/steps/IdentifyStep
instanceKlass org/gradle/internal/execution/ExecutionEngine$Request
instanceKlass org/gradle/internal/execution/impl/DefaultExecutionEngine
instanceKlass org/gradle/internal/id/UniqueId$1
instanceKlass com/google/common/base/Ascii
instanceKlass com/google/common/io/BaseEncoding$Alphabet
instanceKlass com/google/common/io/BaseEncoding
instanceKlass org/gradle/internal/id/UniqueId
instanceKlass sun/misc/ProxyGenerator$1
instanceKlass org/gradle/internal/execution/timeout/Timeout
instanceKlass org/gradle/internal/execution/timeout/impl/DefaultTimeoutHandler
instanceKlass org/gradle/internal/execution/history/impl/DefaultOverlappingOutputDetector
instanceKlass org/gradle/internal/execution/UnitOfWork$OutputVisitor
instanceKlass org/gradle/internal/execution/impl/DefaultOutputSnapshotter
instanceKlass org/gradle/internal/execution/fingerprint/impl/DefaultInputFingerprinter
instanceKlass org/gradle/internal/execution/fingerprint/impl/DefaultFileCollectionFingerprinterRegistry$$Lambda$172
instanceKlass org/gradle/internal/execution/fingerprint/impl/DefaultFileCollectionFingerprinterRegistry
instanceKlass org/gradle/api/internal/changedetection/state/CachingFileSystemLocationSnapshotHasher
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingInputStreamHasher
instanceKlass org/gradle/api/tasks/CompileClasspathNormalizer
instanceKlass org/gradle/api/tasks/ClasspathNormalizer
instanceKlass org/gradle/internal/fingerprint/IgnoredPathInputNormalizer
instanceKlass org/gradle/internal/fingerprint/NameOnlyInputNormalizer
instanceKlass org/gradle/internal/fingerprint/RelativePathInputNormalizer
instanceKlass org/gradle/internal/execution/fingerprint/impl/DefaultFileNormalizationSpec
instanceKlass org/gradle/internal/execution/fingerprint/FileNormalizationSpec
instanceKlass org/gradle/internal/fingerprint/AbsolutePathInputNormalizer
instanceKlass org/gradle/api/tasks/FileNormalizer
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$$Lambda$171
instanceKlass org/gradle/internal/execution/fingerprint/impl/FingerprinterRegistration
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$$Lambda$170
instanceKlass org/gradle/internal/fingerprint/FileSystemLocationFingerprint
instanceKlass org/gradle/internal/fingerprint/impl/AbstractDirectorySensitiveFingerprintingStrategy$$Lambda$169
instanceKlass org/gradle/internal/fingerprint/DirectorySensitivity$$Lambda$168
instanceKlass org/gradle/internal/fingerprint/DirectorySensitivity$$Lambda$167
instanceKlass org/gradle/internal/fingerprint/DirectorySensitivity$$Lambda$166
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$$Lambda$165
instanceKlass org/gradle/internal/snapshot/FileSystemLocationSnapshot$FileSystemLocationSnapshotVisitor
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$1
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingFileSystemLocationSnapshotHasher$1
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingFileSystemLocationSnapshotHasher
instanceKlass org/gradle/internal/fingerprint/hashing/FileSystemLocationSnapshotHasher$1
instanceKlass java/util/Spliterators$ArraySpliterator
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations$$Lambda$164
instanceKlass org/gradle/internal/normalization/java/ApiClassExtractor$$Lambda$163
instanceKlass org/gradle/internal/normalization/java/ApiMemberWriterFactory
instanceKlass org/gradle/internal/normalization/java/impl/ApiMemberWriter
instanceKlass org/gradle/internal/normalization/java/ApiClassExtractor
instanceKlass org/gradle/api/internal/changedetection/state/AbiExtractingClasspathResourceHasher
instanceKlass org/gradle/internal/fingerprint/classpath/CompileClasspathFingerprinter
instanceKlass org/gradle/internal/fingerprint/hashing/FileSystemLocationSnapshotHasher
instanceKlass org/gradle/api/internal/changedetection/state/SplitResourceSnapshotterCacheService
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$BuildSessionServices$$Lambda$162
instanceKlass org/gradle/internal/execution/history/changes/ChangeVisitor
instanceKlass org/gradle/internal/execution/history/changes/InputFileChanges
instanceKlass org/gradle/internal/execution/history/changes/ChangeContainer
instanceKlass org/gradle/internal/execution/history/changes/DefaultExecutionStateChangeDetector
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/GradlePluginVariantsSupport$TargetGradleVersionDisambiguationRule
instanceKlass org/gradle/api/internal/attributes/DefaultCompatibilityRuleChain$ExceptionHandler
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/GradlePluginVariantsSupport$TargetGradleVersionCompatibilityRule
instanceKlass org/gradle/api/attributes/AttributeCompatibilityRule
instanceKlass org/gradle/api/attributes/plugin/GradlePluginApiVersion
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/GradlePluginVariantsSupport
instanceKlass org/gradle/api/internal/attributes/DefaultDisambiguationRuleChain$ExceptionHandler
instanceKlass org/gradle/internal/action/DefaultConfigurableRules
instanceKlass org/gradle/internal/action/ConfigurableRules
instanceKlass org/gradle/api/artifacts/CacheableRule
instanceKlass org/gradle/internal/snapshot/impl/AbstractArraySnapshot
instanceKlass org/gradle/internal/snapshot/impl/AbstractScalarValueSnapshot
instanceKlass org/gradle/api/internal/DefaultActionConfiguration
instanceKlass org/gradle/internal/action/DefaultConfigurableRule
instanceKlass org/gradle/internal/action/InstantiatingAction
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/PlatformSupport$$Lambda$161
instanceKlass org/gradle/api/ActionConfiguration
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/PlatformSupport$ComponentCategoryDisambiguationRule
instanceKlass org/gradle/api/internal/ReusableAction
instanceKlass org/gradle/api/attributes/AttributeDisambiguationRule
instanceKlass org/gradle/api/internal/attributes/MultipleCandidatesResult
instanceKlass org/gradle/api/attributes/MultipleCandidatesDetails
instanceKlass org/gradle/api/internal/attributes/DefaultDisambiguationRuleChain
instanceKlass org/gradle/internal/action/InstantiatingAction$ExceptionHandler
instanceKlass org/gradle/api/internal/attributes/DefaultCompatibilityRuleChain
instanceKlass org/gradle/api/attributes/CompatibilityRuleChain
instanceKlass org/gradle/api/attributes/DisambiguationRuleChain
instanceKlass org/gradle/api/internal/attributes/DefaultAttributeMatchingStrategy
instanceKlass org/gradle/api/internal/attributes/CompatibilityCheckResult
instanceKlass org/gradle/api/attributes/CompatibilityCheckDetails
instanceKlass org/gradle/api/internal/attributes/DefaultAttributesSchema$MergedSchema
instanceKlass org/gradle/api/internal/attributes/DefaultAttributesSchema$DefaultAttributeMatcher
instanceKlass org/gradle/api/internal/attributes/AttributeDescriber
instanceKlass org/gradle/api/attributes/AttributeMatchingStrategy
instanceKlass org/gradle/internal/component/model/AttributeSelectionSchema
instanceKlass org/gradle/internal/component/model/AttributeMatcher
instanceKlass org/gradle/internal/component/model/ComponentAttributeMatcher
instanceKlass org/gradle/api/internal/attributes/DefaultAttributesSchema
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/StoreSet
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DependencyResolutionScopeServices$$Lambda$160
instanceKlass org/gradle/api/internal/artifacts/DefaultArtifactRepositoryContainer$$Lambda$159
instanceKlass org/gradle/api/internal/artifacts/DefaultArtifactRepositoryContainer$$Lambda$158
instanceKlass org/gradle/api/internal/collections/AbstractIterationOrderRetainingElementSource$RealizedElementCollectionIterator
instanceKlass org/gradle/api/internal/collections/ListElementSource$1
instanceKlass org/gradle/api/internal/collections/AbstractIterationOrderRetainingElementSource
instanceKlass org/gradle/api/internal/artifacts/DefaultArtifactRepositoryContainer$RepositoryNamer
instanceKlass org/gradle/api/artifacts/repositories/IvyArtifactRepository
instanceKlass org/gradle/api/artifacts/repositories/MavenArtifactRepository
instanceKlass org/gradle/api/artifacts/repositories/MetadataSupplierAware
instanceKlass org/gradle/api/artifacts/repositories/AuthenticationSupported
instanceKlass org/gradle/api/artifacts/repositories/UrlArtifactRepository
instanceKlass org/gradle/api/artifacts/repositories/FlatDirectoryArtifactRepository
instanceKlass org/gradle/api/artifacts/repositories/RepositoryContentDescriptor
instanceKlass org/gradle/api/artifacts/repositories/InclusiveRepositoryContentDescriptor
instanceKlass org/gradle/api/internal/collections/IndexedElementSource
instanceKlass org/gradle/api/internal/artifacts/dsl/RepositoryHandlerInternal
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/GradleModuleMetadataParser
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/MavenVersionSelectorScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/PomParent
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/AbstractModuleDescriptorParser
instanceKlass org/gradle/api/artifacts/repositories/AuthenticationContainer
instanceKlass org/gradle/api/internal/artifacts/repositories/DefaultBaseRepositoryFactory
instanceKlass org/apache/ivy/util/MessageLogger
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultIvyContextManager
instanceKlass org/gradle/internal/resource/local/CompositeLocallyAvailableResourceFinder
instanceKlass org/gradle/internal/resource/local/ivy/PatternBasedLocallyAvailableResourceFinder$1
instanceKlass org/gradle/util/internal/MavenUtil
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/AbstractResourcePattern
instanceKlass org/gradle/internal/resource/local/LocallyAvailableResourceFinderSearchableFileStoreAdapter$$Lambda$157
instanceKlass org/gradle/internal/resource/local/ivy/LocallyAvailableResourceFinderFactory$$Lambda$156
instanceKlass org/gradle/internal/resource/local/LocallyAvailableResourceCandidates
instanceKlass org/gradle/internal/resource/local/AbstractLocallyAvailableResourceFinder
instanceKlass org/gradle/internal/resource/local/ivy/LocallyAvailableResourceFinderFactory$$Lambda$155
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/ResourcePattern
instanceKlass org/gradle/internal/resource/local/ivy/LocallyAvailableResourceFinderFactory
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultLocalMavenRepositoryLocator$CurrentSystemPropertyAccess
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultLocalMavenRepositoryLocator$SystemPropertyAccess
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultLocalMavenRepositoryLocator
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultMavenFileLocations
instanceKlass org/apache/maven/settings/building/SettingsBuildingRequest
instanceKlass org/apache/maven/settings/io/SettingsReader
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/DefaultMavenSettingsProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CapabilitiesConflictHandler$Resolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DependencySubstitutionApplicator
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/ModuleConflictHandler
instanceKlass org/gradle/internal/resolve/resolver/ResolveContextToComponentResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CapabilitiesConflictHandler
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/ConflictHandler
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/DependencyGraphVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/DefaultArtifactDependencyResolver
instanceKlass org/gradle/internal/resolve/caching/ComponentMetadataSupplierRuleExecutor$$Lambda$154
instanceKlass org/gradle/internal/resolve/caching/ComponentMetadataSupplierRuleExecutor$$Lambda$153
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$1
instanceKlass org/gradle/api/artifacts/ComponentMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/simple/DefaultExcludeNothing
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/ExcludeNothing
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/Unions
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/Intersections
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/ModuleSetExclude
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/GroupSetExclude
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/simple/DefaultExcludeFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/DelegatingExcludeFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/CachingExcludeFactory$ConcurrentCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/CachingExcludeFactory$MergeCaches
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/ExcludeSpec
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/ExcludeFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ChangingValueDependencyResolutionListener$1
instanceKlass org/gradle/api/internal/artifacts/configurations/dynamicversion/Expiry
instanceKlass org/gradle/api/artifacts/component/ModuleComponentSelector
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ChangingValueDependencyResolutionListener
instanceKlass org/gradle/api/artifacts/result/ResolvedArtifactResult
instanceKlass org/gradle/api/artifacts/result/ArtifactResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ConnectionFailureRepositoryDisabler
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$2
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/verification/DependencyVerificationOverride$$Lambda$152
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolutionStrategyInternal
instanceKlass org/gradle/api/artifacts/ResolutionStrategy
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$151
instanceKlass org/gradle/api/internal/artifacts/verification/signatures/SignatureVerificationService
instanceKlass org/gradle/security/internal/PublicKeyService
instanceKlass org/gradle/api/internal/artifacts/verification/signatures/DefaultSignatureVerificationServiceFactory
instanceKlass org/gradle/api/internal/changedetection/state/SplitFileHasher
instanceKlass org/gradle/api/internal/artifacts/repositories/transport/RepositoryTransport
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/ExternalResourceCachePolicy
instanceKlass org/gradle/internal/resource/connector/ResourceConnectorSpecification
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$150
instanceKlass org/gradle/internal/resource/local/LocallyAvailableExternalResource
instanceKlass org/gradle/internal/resource/ExternalResource
instanceKlass org/gradle/internal/resource/local/FileResourceConnector
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ResolvedArtifactCaches
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/InMemoryModuleArtifactCache
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$149
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$148
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$147
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$146
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$145
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/DefaultModuleArtifactCache$CachedArtifactSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/DefaultModuleArtifactCache$ArtifactAtRepositoryKeySerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/CachedArtifact
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/CachedArtifacts
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataStore
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataCache$CachedMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/dynamicversions/ModuleVersionsCache$CachedModuleVersionList
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$144
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/StartParameterResolutionOverride$$Lambda$143
instanceKlass org/gradle/api/internal/artifacts/verification/signatures/BuildTreeDefinedKeys
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ModuleComponentRepository
instanceKlass org/gradle/api/internal/file/DefaultFileSystemLocation
instanceKlass org/gradle/api/resources/TextResource
instanceKlass org/gradle/internal/locking/LockFileReaderWriter
instanceKlass org/gradle/api/internal/provider/ValidatingValueCollector
instanceKlass org/gradle/api/internal/provider/AbstractCollectionProperty$EmptySupplier
instanceKlass org/gradle/api/internal/provider/DefaultListProperty$1
instanceKlass org/gradle/api/internal/provider/AbstractCollectionProperty$NoValueSupplier
instanceKlass org/gradle/api/internal/provider/CollectionSupplier
instanceKlass org/gradle/internal/locking/LockEntryFilterFactory$$Lambda$142
instanceKlass org/gradle/internal/locking/LockEntryFilterFactory$$Lambda$141
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/LockEntryFilter
instanceKlass org/gradle/internal/locking/LockEntryFilterFactory
instanceKlass org/gradle/internal/locking/DependencyLockingNotationConverter
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DependencyLockingState
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingProvider
instanceKlass org/gradle/api/internal/artifacts/DefaultComponentSelectorConverter
instanceKlass org/gradle/api/internal/artifacts/component/DefaultComponentIdentifierFactory
instanceKlass org/gradle/api/internal/artifacts/DefaultGlobalDependencyResolutionRules$CompositeDependencySubstitutionRules
instanceKlass org/gradle/api/internal/artifacts/DefaultGlobalDependencyResolutionRules
instanceKlass org/gradle/api/artifacts/ComponentModuleMetadataDetails
instanceKlass org/gradle/api/artifacts/ComponentModuleMetadata
instanceKlass org/gradle/api/internal/artifacts/dsl/ComponentModuleMetadataContainer
instanceKlass org/gradle/api/internal/artifacts/dsl/ModuleReplacementsData
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DependencyResolutionScopeServices$$Lambda$140
instanceKlass com/google/common/collect/MapMakerInternalMap$StrongKeyDummyValueEntry$Helper
instanceKlass org/gradle/api/internal/DefaultNamedDomainObjectCollection$UnfilteredIndex
instanceKlass org/gradle/api/internal/DefaultDomainObjectCollection$1
instanceKlass org/gradle/api/internal/collections/DefaultCollectionEventRegister
instanceKlass org/gradle/api/internal/provider/Collector
instanceKlass org/gradle/api/internal/collections/DefaultPendingSource
instanceKlass org/gradle/api/Namer$Comparator
instanceKlass org/gradle/api/internal/collections/SortedSetElementSource
instanceKlass org/gradle/api/Named$Namer
instanceKlass org/gradle/internal/instantiation/generator/InjectUtil
instanceKlass com/google/common/reflect/Types$WildcardTypeImpl
instanceKlass sun/reflect/generics/tree/ArrayTypeSignature
instanceKlass sun/reflect/generics/tree/IntSignature
instanceKlass java/lang/Class$EnclosingMethodInfo
instanceKlass com/google/common/reflect/Types$ClassOwnership$1LocalClass
instanceKlass com/google/common/reflect/Types$ParameterizedTypeImpl
instanceKlass com/google/common/reflect/Types$1
instanceKlass com/google/common/reflect/Types
instanceKlass com/google/common/reflect/TypeResolver$TypeVariableKey
instanceKlass com/google/common/reflect/TypeResolver$TypeTable
instanceKlass com/google/common/reflect/TypeResolver
instanceKlass com/google/common/reflect/TypeVisitor
instanceKlass org/gradle/api/internal/collections/CollectionFilter
instanceKlass org/gradle/api/reflect/TypeOf
instanceKlass org/gradle/api/NamedDomainObjectCollectionSchema
instanceKlass org/gradle/api/Rule
instanceKlass org/gradle/api/NamedDomainObjectProvider
instanceKlass org/gradle/api/internal/DefaultNamedDomainObjectCollection$Index
instanceKlass org/gradle/api/internal/collections/ElementSource
instanceKlass org/gradle/api/internal/collections/PendingSource
instanceKlass org/gradle/api/internal/collections/CollectionEventRegister
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector$$Lambda$139
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector$CachedConstructor
instanceKlass groovy/lang/Buildable
instanceKlass groovy/lang/Writable
instanceKlass org/gradle/internal/management/DefaultDependencyResolutionManagement$$Lambda$138
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Present
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Missing
instanceKlass org/gradle/api/internal/provider/ValueSupplier$Value
instanceKlass org/gradle/api/internal/provider/Providers
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$4
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$3
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$2
instanceKlass org/gradle/api/internal/provider/ValueSanitizers$1
instanceKlass org/gradle/api/internal/provider/ValueCollector
instanceKlass org/gradle/api/internal/provider/ValueSanitizer
instanceKlass org/gradle/api/internal/provider/ValueSanitizers
instanceKlass org/gradle/api/internal/provider/AbstractProperty$FinalizationState
instanceKlass org/gradle/internal/management/DefaultDependencyResolutionManagement$ComponentMetadataRulesRegistar
instanceKlass org/apache/groovy/util/BeanUtils
instanceKlass groovy/lang/MetaProperty
instanceKlass javax/annotation/meta/TypeQualifier
instanceKlass org/gradle/api/initialization/dsl/VersionCatalogBuilder
instanceKlass org/gradle/api/internal/WithEstimatedSize
instanceKlass org/gradle/internal/metaobject/PropertyMixIn
instanceKlass org/gradle/internal/metaobject/MethodMixIn
instanceKlass org/gradle/api/reflect/HasPublicType
instanceKlass org/gradle/api/artifacts/repositories/ArtifactRepository
instanceKlass org/gradle/api/initialization/resolve/MutableVersionCatalogContainer
instanceKlass org/gradle/internal/management/DefaultDependencyResolutionManagement
instanceKlass org/gradle/api/internal/DefaultCollectionCallbackActionDecorator
instanceKlass org/gradle/api/internal/provider/CredentialsProviderFactory
instanceKlass org/gradle/api/provider/ValueSourceSpec
instanceKlass org/gradle/api/file/FileContents
instanceKlass org/gradle/api/internal/provider/DefaultProviderFactory
instanceKlass org/gradle/api/internal/provider/ValueSourceProviderFactory$Listener
instanceKlass org/gradle/api/provider/ValueSourceParameters$None
instanceKlass org/gradle/api/provider/ValueSourceParameters
instanceKlass org/gradle/api/provider/ValueSource
instanceKlass org/gradle/internal/isolated/IsolationScheme
instanceKlass org/gradle/api/internal/provider/ValueSourceProviderFactory$Listener$ObtainedValue
instanceKlass org/gradle/api/internal/provider/DefaultValueSourceProviderFactory
instanceKlass org/gradle/api/capabilities/CapabilitiesMetadata
instanceKlass org/gradle/internal/component/external/model/AbstractStatelessDerivationStrategy
instanceKlass org/gradle/api/internal/artifacts/dsl/MetadataRuleWrapper
instanceKlass org/gradle/api/internal/notations/ComponentIdentifierParserFactory
instanceKlass org/gradle/api/artifacts/DependencyConstraintMetadata
instanceKlass org/gradle/api/internal/catalog/parser/StrictVersionParser
instanceKlass org/gradle/api/internal/notations/DependencyStringNotationConverter
instanceKlass org/gradle/api/internal/notations/DependencyMetadataNotationParser
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/AbstractDependencyImpl
instanceKlass org/gradle/api/artifacts/DirectDependencyMetadata
instanceKlass org/gradle/api/artifacts/DependencyMetadata
instanceKlass org/gradle/internal/rules/DefaultRuleActionAdapter
instanceKlass org/gradle/api/artifacts/maven/PomModuleDescriptor
instanceKlass org/gradle/api/artifacts/ivy/IvyModuleDescriptor
instanceKlass org/gradle/internal/rules/DefaultRuleActionValidator
instanceKlass org/gradle/api/internal/artifacts/dsl/ComponentMetadataRuleContainer
instanceKlass org/gradle/api/internal/artifacts/MetadataResolutionContext
instanceKlass org/gradle/internal/rules/RuleAction
instanceKlass org/gradle/api/internal/artifacts/dsl/SpecConfigurableRule
instanceKlass org/gradle/internal/action/ConfigurableRule
instanceKlass org/gradle/internal/rules/SpecRuleAction
instanceKlass org/gradle/internal/rules/RuleActionAdapter
instanceKlass org/gradle/internal/rules/RuleActionValidator
instanceKlass org/gradle/api/internal/artifacts/ComponentMetadataProcessor
instanceKlass org/gradle/api/attributes/Category$Impl
instanceKlass org/gradle/api/attributes/Category
instanceKlass org/gradle/internal/resolve/caching/CrossBuildCachingRuleExecutor$AnySerializer
instanceKlass org/gradle/internal/resolve/caching/ComponentMetadataRuleExecutor$$Lambda$137
instanceKlass org/gradle/internal/resolve/caching/ComponentMetadataRuleExecutor$$Lambda$136
instanceKlass org/gradle/internal/resolve/caching/CrossBuildCachingRuleExecutor$EntryValidator
instanceKlass org/gradle/internal/resolve/caching/CrossBuildCachingRuleExecutor$CachedEntry
instanceKlass org/gradle/api/internal/artifacts/configurations/dynamicversion/CachePolicy
instanceKlass org/gradle/api/artifacts/ResolvedModuleVersion
instanceKlass org/gradle/internal/resolve/caching/ImplicitInputRecorder
instanceKlass org/gradle/internal/component/external/model/ModuleDependencyMetadata
instanceKlass org/gradle/internal/component/model/ConfigurationMetadata
instanceKlass org/gradle/internal/component/external/model/CapabilityInternal
instanceKlass org/gradle/internal/component/external/model/AbstractRealisedModuleResolveMetadataSerializationHelper
instanceKlass org/gradle/api/internal/artifacts/ModuleComponentSelectorSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataSerializer
instanceKlass org/gradle/internal/component/external/model/VirtualComponentIdentifier
instanceKlass org/gradle/internal/component/external/model/ModuleComponentResolveMetadata
instanceKlass org/gradle/internal/component/model/ModuleSources
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ModuleDescriptorHashCodec
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MetadataFileSource
instanceKlass org/gradle/internal/component/model/PersistentModuleSource
instanceKlass org/gradle/internal/component/model/ModuleSource
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/DefaultMetadataFileSourceCodec
instanceKlass org/gradle/internal/component/model/PersistentModuleSource$Codec
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$135
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$134
instanceKlass org/gradle/api/internal/filestore/DefaultArtifactIdentifierFileStore$$Lambda$133
instanceKlass org/gradle/api/internal/filestore/DefaultArtifactIdentifierFileStore$1
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$132
instanceKlass org/gradle/internal/resource/cached/CachedExternalResource
instanceKlass org/gradle/internal/resource/metadata/ExternalResourceMetaData
instanceKlass org/gradle/internal/resource/cached/DefaultCachedExternalResourceIndex$CachedExternalResourceSerializer
instanceKlass org/gradle/internal/resource/cached/CachedItem
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCachesProvider$$Lambda$131
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices$$Lambda$130
instanceKlass org/gradle/internal/resource/local/LocallyAvailableResource
instanceKlass org/gradle/internal/resource/local/DefaultPathKeyFileStore
instanceKlass org/gradle/internal/resource/cached/DefaultExternalResourceFileStore$$Lambda$129
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass org/gradle/api/Namer
instanceKlass org/gradle/internal/resource/cached/DefaultExternalResourceFileStore$1
instanceKlass org/gradle/internal/resource/local/GroupedAndNamedUniqueFileStore$Grouper
instanceKlass org/gradle/internal/resource/local/PathKeyFileStore
instanceKlass org/gradle/internal/hash/ChecksumHasher
instanceKlass org/gradle/internal/hash/DefaultChecksumService
instanceKlass org/gradle/internal/component/external/descriptor/Configuration
instanceKlass org/gradle/internal/component/model/IvyArtifactName
instanceKlass org/gradle/internal/component/external/model/ivy/MutableIvyModuleResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/DefaultMavenImmutableAttributesFactory
instanceKlass org/gradle/internal/component/external/model/maven/MutableMavenModuleResolveMetadata
instanceKlass org/gradle/internal/component/external/model/MutableModuleComponentResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MavenImmutableAttributesFactory
instanceKlass org/gradle/internal/component/external/model/PreferJavaRuntimeVariant$PreferJarVariantUsageDisambiguationRule
instanceKlass org/gradle/internal/component/external/model/PreferJavaRuntimeVariant$PreferRuntimeVariantUsageDisambiguationRule
instanceKlass org/gradle/api/attributes/LibraryElements$Impl
instanceKlass org/gradle/api/attributes/Usage$Impl
instanceKlass org/gradle/model/internal/type/ClassTypeWrapper
instanceKlass org/gradle/model/internal/type/TypeWrapper
instanceKlass org/gradle/model/internal/type/ModelType
instanceKlass org/gradle/model/internal/inspect/FormattingValidationProblemCollector
instanceKlass org/gradle/api/attributes/LibraryElements
instanceKlass org/gradle/api/attributes/Usage
instanceKlass org/gradle/api/internal/attributes/EmptySchema$DoNothingDisambiguationRule
instanceKlass org/gradle/api/internal/attributes/EmptySchema$DoNothingCompatibilityRule
instanceKlass org/gradle/api/internal/attributes/DisambiguationRule
instanceKlass org/gradle/api/internal/attributes/CompatibilityRule
instanceKlass org/gradle/internal/snapshot/ValueSnapshot
instanceKlass org/gradle/internal/snapshot/impl/DefaultValueSnapshotter$ValueSnapshotVisitor
instanceKlass org/gradle/api/internal/tasks/properties/PropertyValue
instanceKlass org/gradle/api/internal/tasks/properties/annotations/NestedBeanAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/LocalStatePropertyAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/DestroysPropertyAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/InputPropertyAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/NoOpPropertyAnnotationHandler
instanceKlass org/gradle/api/internal/artifacts/transform/CacheableTransformTypeAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/UntrackedTaskTypeAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/CacheableTaskTypeAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/DisableCachingByDefaultTypeAnnotationHandler
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VcsResolverFactory
instanceKlass org/gradle/vcs/internal/resolver/OncePerBuildInvocationVcsVersionWorkingDirResolver
instanceKlass org/gradle/vcs/internal/resolver/DefaultVcsVersionWorkingDirResolver
instanceKlass org/gradle/vcs/internal/VersionRef
instanceKlass org/gradle/vcs/internal/resolver/PersistentVcsMetadataCache$VersionRefSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/CachingVersionSelectorScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionSelector
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/DefaultVersionSelectorScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/Version
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/StaticVersionComparator
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/DefaultVersionComparator
instanceKlass org/gradle/cache/internal/CleanupActionFactory$BuildOperationCacheCleanupDecorator
instanceKlass org/gradle/internal/resource/local/ModificationTimeFileAccessTimeJournal
instanceKlass org/gradle/vcs/internal/VersionControlRepositoryConnection
instanceKlass org/gradle/vcs/internal/VersionControlSystem
instanceKlass org/gradle/vcs/internal/services/DefaultVersionControlRepositoryFactory
instanceKlass org/gradle/vcs/internal/DefaultVcsMappingsStore
instanceKlass org/gradle/vcs/internal/DefaultVcsMappingFactory
instanceKlass org/gradle/vcs/internal/services/DefaultVersionControlSpecFactory
instanceKlass org/gradle/internal/typeconversion/CharSequenceNotationConverter
instanceKlass org/gradle/api/internal/notations/ModuleIdentifierNotationConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/DefaultLocalComponentRegistry
instanceKlass org/gradle/composite/internal/IncludedBuildDependencyMetadataBuilder
instanceKlass org/gradle/internal/component/local/model/LocalComponentMetadata
instanceKlass org/gradle/internal/component/local/model/BuildableLocalComponentMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/DefaultProjectLocalComponentProvider
instanceKlass org/gradle/api/artifacts/Configuration
instanceKlass org/gradle/api/attributes/HasConfigurableAttributes
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationInternal$VariantVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/DefaultLocalComponentMetadataBuilder
instanceKlass org/gradle/internal/component/local/model/LocalFileDependencyMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultLocalConfigurationMetadataBuilder
instanceKlass org/gradle/util/internal/WrapUtil
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultDependencyDescriptorFactory
instanceKlass org/gradle/internal/component/model/LocalOriginDependencyMetadata
instanceKlass org/gradle/internal/component/model/ForcingDependencyMetadata
instanceKlass org/gradle/internal/component/model/DependencyMetadata
instanceKlass org/gradle/api/artifacts/Dependency
instanceKlass org/gradle/internal/component/model/ExcludeMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultExcludeRuleConverter
instanceKlass org/gradle/internal/model/CalculatedValueContainerFactory$$Lambda$128
instanceKlass org/gradle/api/internal/tasks/NodeExecutionContext
instanceKlass org/gradle/internal/resource/transport/sftp/SftpConnectorFactory
instanceKlass com/jcraft/jsch/HostKeyRepository
instanceKlass com/jcraft/jsch/Logger
instanceKlass org/gradle/internal/resource/transport/sftp/LockableSftpClient
instanceKlass org/gradle/internal/resource/transport/sftp/SftpClientFactory$SftpClientCreator
instanceKlass org/gradle/internal/resource/transport/aws/s3/S3ConnectorFactory
instanceKlass org/gradle/internal/resource/transport/gcp/gcs/GcsConnectorFactory
instanceKlass org/gradle/internal/resource/transport/file/FileConnectorFactory
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceConnector
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceAccessor
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceLister
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceUploader
instanceKlass org/gradle/internal/resource/transport/http/HttpConnectorFactory
instanceKlass org/gradle/internal/resource/transport/http/HttpClientHelper$Factory$$Lambda$127
instanceKlass org/gradle/internal/resource/transport/http/HttpClientHelper
instanceKlass org/gradle/internal/resource/transport/http/HttpSettings
instanceKlass org/gradle/internal/resource/transport/http/DefaultSslContextFactory
instanceKlass org/gradle/composite/internal/CompositeProjectComponentArtifactMetadataSerializer
instanceKlass org/gradle/composite/internal/CompositeProjectComponentArtifactMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ResolvedComponentResultSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectorSerializer$OptimizingAttributeContainerSerializer
instanceKlass org/gradle/api/internal/artifacts/ImmutableVersionConstraint
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionReasonSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionDescriptorSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ResolvedVariantResultSerializer
instanceKlass org/gradle/internal/component/external/model/DefaultModuleComponentIdentifier
instanceKlass org/gradle/api/artifacts/component/ModuleComponentIdentifier
instanceKlass org/gradle/api/internal/artifacts/metadata/ModuleComponentFileArtifactIdentifierSerializer
instanceKlass org/gradle/api/internal/artifacts/metadata/ComponentArtifactIdentifierSerializer
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementValueSnapshotterSerializerRegistry$OpaqueComponentArtifactIdentifierSerializer
instanceKlass org/gradle/api/artifacts/PublishArtifact
instanceKlass org/gradle/api/internal/artifacts/metadata/PublishArtifactLocalArtifactMetadataSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/CapabilitySerializer
instanceKlass org/gradle/api/internal/artifacts/ModuleVersionIdentifierSerializer
instanceKlass org/gradle/internal/resolve/caching/DesugaringAttributeContainerSerializer
instanceKlass org/gradle/api/artifacts/result/ResolvedComponentResult
instanceKlass org/gradle/api/artifacts/result/ComponentResult
instanceKlass org/gradle/api/artifacts/result/ComponentSelectionReason
instanceKlass org/gradle/api/artifacts/result/ResolvedVariantResult
instanceKlass org/gradle/internal/component/local/model/ComponentFileArtifactIdentifier
instanceKlass org/gradle/internal/component/external/model/DefaultModuleComponentArtifactIdentifier
instanceKlass org/gradle/internal/component/external/model/ModuleComponentArtifactIdentifier
instanceKlass org/gradle/internal/component/local/model/OpaqueComponentArtifactIdentifier
instanceKlass org/gradle/internal/component/local/model/PublishArtifactLocalArtifactMetadata
instanceKlass org/gradle/api/artifacts/component/ComponentArtifactIdentifier
instanceKlass org/gradle/internal/component/local/model/LocalComponentArtifactMetadata
instanceKlass org/gradle/api/artifacts/ModuleVersionIdentifier
instanceKlass org/gradle/api/artifacts/result/ComponentSelectionDescriptor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/CachingComponentSelectionDescriptorFactory
instanceKlass org/gradle/internal/resource/UriTextResource$UriResourceLocation
instanceKlass org/gradle/api/internal/initialization/DefaultScriptHandler
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DefaultDependencyResolutionServices
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/DefaultRootComponentMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/RootComponentMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$VariantKey
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationNodeFactory
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory
instanceKlass org/gradle/api/internal/artifacts/dsl/DefaultComponentModuleMetadataHandler
instanceKlass org/gradle/api/internal/artifacts/dsl/DefaultComponentMetadataHandler
instanceKlass org/gradle/api/internal/artifacts/dsl/ComponentMetadataHandlerInternal
instanceKlass org/gradle/api/artifacts/dsl/DependencyHandler
instanceKlass org/gradle/api/artifacts/dsl/ComponentModuleMetadataHandler
instanceKlass org/gradle/api/artifacts/dsl/ComponentMetadataHandler
instanceKlass org/gradle/api/artifacts/dsl/DependencyConstraintHandler
instanceKlass org/gradle/api/artifacts/dsl/ArtifactHandler
instanceKlass org/gradle/api/internal/artifacts/dsl/PublishArtifactNotationParserFactory
instanceKlass org/gradle/api/artifacts/dsl/DependencyLockingHandler
instanceKlass org/gradle/api/internal/artifacts/ComponentModuleMetadataProcessor
instanceKlass org/gradle/api/internal/artifacts/ComponentMetadataProcessorFactory
instanceKlass org/gradle/api/internal/artifacts/transform/TransformedVariantFactory
instanceKlass org/gradle/api/internal/artifacts/RepositoriesSupplier
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationContainerInternal
instanceKlass org/gradle/api/artifacts/ConfigurationContainer
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfigurationFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/DefaultRootComponentMetadataBuilder$Factory
instanceKlass org/gradle/api/artifacts/dsl/RepositoryHandler
instanceKlass org/gradle/api/artifacts/ArtifactRepositoryContainer
instanceKlass org/gradle/api/NamedDomainObjectList
instanceKlass org/gradle/api/internal/artifacts/repositories/DefaultUrlArtifactRepository$Factory
instanceKlass org/gradle/api/internal/artifacts/transform/MutableTransformationWorkspaceServices
instanceKlass org/gradle/internal/file/ReservedFileSystemLocation
instanceKlass org/gradle/api/file/ProjectLayout
instanceKlass org/gradle/api/internal/tasks/TaskResolver
instanceKlass org/gradle/api/internal/artifacts/ArtifactPublicationServices
instanceKlass org/gradle/api/internal/artifacts/query/ArtifactResolutionQueryFactory
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationRegistrationFactory
instanceKlass org/gradle/api/internal/artifacts/transform/TransformerInvocationFactory
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DependencyLockingProvider
instanceKlass org/gradle/internal/component/external/model/VariantDerivationStrategy
instanceKlass org/gradle/api/internal/artifacts/GlobalDependencyResolutionRules
instanceKlass org/gradle/api/internal/artifacts/ConfigurationResolver
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransforms
instanceKlass org/gradle/api/internal/artifacts/BaseRepositoryFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/MetaDataParser
instanceKlass org/gradle/api/internal/artifacts/VariantTransformRegistry
instanceKlass org/gradle/api/internal/artifacts/type/ArtifactTypeRegistry
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$DependencyResolutionScopeServices
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices$ArtifactTransformResolutionGradleUserHomeServices
instanceKlass org/gradle/internal/model/CalculatedModelValue
instanceKlass org/gradle/api/internal/initialization/RootScriptDomainObjectContext
instanceKlass org/gradle/internal/resource/ResourceLocation
instanceKlass org/gradle/internal/resource/UriTextResource
instanceKlass org/gradle/groovy/scripts/TextResourceScriptSource
instanceKlass org/gradle/initialization/BuildOperationSettingsProcessor$2$1
instanceKlass org/gradle/initialization/EvaluateSettingsBuildOperationType$Details
instanceKlass org/gradle/initialization/BuildOperationSettingsProcessor$2
instanceKlass org/gradle/invocation/DefaultGradle$$Lambda$126
instanceKlass org/gradle/initialization/DirectoryInitScriptFinder
instanceKlass org/gradle/initialization/CompositeInitScriptFinder
instanceKlass org/gradle/initialization/InitScriptFinder
instanceKlass org/gradle/initialization/DefaultGradleProperties
instanceKlass org/gradle/configurationcache/extensions/MapExtensionsKt
instanceKlass org/gradle/configurationcache/services/DefaultEnvironment$DefaultProperties
instanceKlass org/gradle/initialization/Environment$Properties
instanceKlass org/gradle/configurationcache/extensions/CastExtensionsKt
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController$Loaded
instanceKlass org/gradle/initialization/DefaultSettingsLoader
instanceKlass org/gradle/initialization/SettingsAttachingSettingsLoader
instanceKlass org/gradle/internal/composite/CommandLineIncludedBuildSettingsLoader
instanceKlass org/gradle/internal/composite/ChildBuildRegisteringSettingsLoader
instanceKlass org/gradle/internal/composite/CompositeBuildSettingsLoader
instanceKlass org/gradle/initialization/InitScriptHandlingSettingsLoader
instanceKlass org/gradle/initialization/GradlePropertiesHandlingSettingsLoader
instanceKlass org/gradle/initialization/BuildOperationFiringSettingsPreparer$LoadBuild$1
instanceKlass org/gradle/initialization/LoadBuildBuildOperationType$Details
instanceKlass org/gradle/initialization/BuildOperationFiringSettingsPreparer$LoadBuild
instanceKlass org/gradle/internal/model/StateTransitionController$$Lambda$125
instanceKlass org/gradle/initialization/VintageBuildModelController$$Lambda$124
instanceKlass org/gradle/internal/model/StateTransitionController$$Lambda$123
instanceKlass org/gradle/internal/model/StateTransitionController$$Lambda$122
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleController$$Lambda$121
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/composite/internal/DefaultBuildControllers$$Lambda$120
instanceKlass org/gradle/composite/internal/BuildController
instanceKlass org/gradle/composite/internal/DefaultBuildControllers
instanceKlass org/gradle/composite/internal/BuildControllers
instanceKlass org/gradle/composite/internal/DefaultIncludedBuildTaskGraph$DefaultBuildTreeWorkGraph
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeLifecycleController$$Lambda$119
instanceKlass org/gradle/internal/buildtree/BuildTreeWorkGraph
instanceKlass org/gradle/internal/model/StateTransitionController$$Lambda$118
instanceKlass org/gradle/internal/model/StateTransitionController$$Lambda$117
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeLifecycleController$$Lambda$116
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeLifecycleController$$Lambda$115
instanceKlass org/gradle/internal/build/ExecutionResult
instanceKlass org/gradle/internal/buildtree/ProblemReportingBuildActionRunner$$Lambda$114
instanceKlass org/gradle/internal/model/StateTransitionController$$Lambda$113
instanceKlass org/gradle/internal/model/StateTransitionController$$Lambda$112
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeLifecycleController$$Lambda$111
instanceKlass org/gradle/launcher/exec/BuildOutcomeReportingBuildActionRunner$$Lambda$110
instanceKlass org/gradle/internal/logging/format/TersePrettyDurationFormatter
instanceKlass org/gradle/internal/buildevents/BuildResultLogger
instanceKlass org/gradle/internal/exceptions/FailureResolutionAware$Context
instanceKlass org/gradle/util/internal/TreeVisitor
instanceKlass org/gradle/internal/buildevents/BuildExceptionReporter
instanceKlass org/gradle/internal/logging/format/DurationFormatter
instanceKlass org/gradle/internal/buildevents/BuildLogger
instanceKlass org/gradle/api/internal/tasks/execution/statistics/TaskExecutionStatisticsEventAdapter
instanceKlass org/gradle/tooling/internal/provider/FileSystemWatchingBuildActionRunner$1
instanceKlass org/gradle/internal/watch/options/FileSystemWatchingSettingsFinalizedProgressDetails
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$Finished
instanceKlass org/gradle/internal/operations/OperationFinishEvent
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$1$1
instanceKlass org/gradle/internal/watch/vfs/BuildStartedFileSystemWatchingBuildOperationType$Result
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$109
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$108
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$107
instanceKlass com/google/common/collect/RangeGwtSerializationDependencies
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$106
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass com/google/common/collect/SortedIterable
instanceKlass com/google/common/collect/ImmutableRangeSet$Builder
instanceKlass com/google/common/collect/AbstractRangeSet
instanceKlass com/google/common/collect/RangeSet
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$105
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$104
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$103
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$102
instanceKlass java/util/stream/Collectors$CollectorImpl
instanceKlass java/util/stream/Collectors
instanceKlass java/util/stream/Collector
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$101
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$100
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$99
instanceKlass com/google/common/collect/CollectCollectors$$Lambda$98
instanceKlass com/google/common/collect/CollectCollectors
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherUpdater$$Lambda$97
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/stream/MatchOps$$Lambda$96
instanceKlass java/util/stream/MatchOps$BooleanTerminalSink
instanceKlass java/util/stream/MatchOps$MatchOp
instanceKlass java/util/stream/MatchOps
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherUpdater$$Lambda$95
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherUpdater$$Lambda$94
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherUpdater$$Lambda$93
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherUpdater$$Lambda$92
instanceKlass java/util/ArrayDeque$DeqSpliterator
instanceKlass org/gradle/internal/watch/registry/impl/WatchableHierarchies$$Lambda$91
instanceKlass java/util/stream/FindOps$$Lambda$90
instanceKlass java/util/stream/FindOps$FindSink
instanceKlass java/util/stream/FindOps$$Lambda$89
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/stream/FindOps$FindOp
instanceKlass java/util/stream/FindOps
instanceKlass org/gradle/internal/watch/registry/impl/WatchableHierarchies$$Lambda$88
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/Spliterator$OfDouble
instanceKlass java/util/Spliterator$OfLong
instanceKlass java/util/Spliterator$OfInt
instanceKlass java/util/Spliterator$OfPrimitive
instanceKlass java/util/Spliterators$EmptySpliterator
instanceKlass java/util/Spliterators
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$87
instanceKlass org/gradle/internal/watch/registry/impl/WatchableHierarchies$$Lambda$86
instanceKlass org/gradle/internal/watch/registry/impl/Combiners$$Lambda$85
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/watch/registry/impl/Combiners
instanceKlass org/gradle/internal/watch/registry/impl/WatchableHierarchies$$Lambda$84
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherProbeRegistry$$Lambda$83
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherProbeRegistry$$Lambda$82
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherProbeRegistry$WatchProbe
instanceKlass org/gradle/internal/watch/registry/impl/HierarchicalFileWatcherUpdater$$Lambda$81
instanceKlass org/gradle/internal/watch/registry/impl/WatchableHierarchies$Invalidator
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherRegistry$$Lambda$80
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherRegistry$$Lambda$79
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherRegistry$MutableFileWatchingStatistics
instanceKlass net/rubygrapefruit/platform/file/FileWatchEvent$Handler
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistry$FileWatchingStatistics
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherRegistry
instanceKlass org/gradle/internal/watch/registry/impl/WindowsFileWatcherRegistryFactory$$Lambda$78
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherUpdater$MovedDirectoryHandler
instanceKlass org/gradle/internal/watch/registry/impl/HierarchicalFileWatcherUpdater$FileSystemLocationToWatchValidator$$Lambda$77
instanceKlass org/gradle/internal/watch/registry/impl/HierarchicalFileWatcherUpdater$FileSystemLocationToWatchValidator
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherUpdater
instanceKlass org/gradle/internal/snapshot/FileSystemSnapshotHierarchyVisitor
instanceKlass org/gradle/internal/file/FileHierarchySet$RootVisitor
instanceKlass org/gradle/internal/watch/registry/impl/WatchableHierarchies
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions$NativeFileWatcher
instanceKlass net/rubygrapefruit/platform/file/FileWatchEvent
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions$NativeFileWatcherCallback
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherRegistryFactory$$Lambda$76
instanceKlass org/gradle/internal/watch/registry/impl/DefaultFileWatcherProbeRegistry
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$3
instanceKlass java/util/stream/Sink$ChainedReference
instanceKlass java/util/stream/ForEachOps$ForEachOp
instanceKlass java/util/stream/ForEachOps
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$1$$Lambda$75
instanceKlass org/gradle/internal/watch/vfs/impl/DefaultWatchableFileSystemDetector$$Lambda$74
instanceKlass org/gradle/internal/watch/vfs/impl/DefaultWatchableFileSystemDetector$$Lambda$73
instanceKlass net/rubygrapefruit/platform/internal/FileSystemList$DefaultCaseSensitivity
instanceKlass net/rubygrapefruit/platform/internal/DefaultFileSystemInfo
instanceKlass net/rubygrapefruit/platform/file/FileSystemInfo
instanceKlass net/rubygrapefruit/platform/internal/jni/PosixFileSystemFunctions
instanceKlass net/rubygrapefruit/platform/file/CaseSensitivity
instanceKlass net/rubygrapefruit/platform/internal/FileSystemList
instanceKlass org/gradle/internal/watch/vfs/BuildStartedFileSystemWatchingBuildOperationType$Details$1
instanceKlass org/gradle/internal/watch/vfs/BuildStartedFileSystemWatchingBuildOperationType$Details
instanceKlass org/gradle/internal/watch/vfs/FileSystemWatchingStatistics
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$1
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$72
instanceKlass org/slf4j/helpers/NamedLoggerBase
instanceKlass org/gradle/configuration/internal/DefaultListenerBuildOperationDecorator
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/OpenOption
instanceKlass org/gradle/api/tasks/util/internal/CachingPatternSpecFactory$CachingSpec
instanceKlass org/gradle/api/internal/file/RelativePathSpec
instanceKlass org/gradle/api/internal/file/pattern/AnythingMatcher
instanceKlass org/gradle/api/internal/file/pattern/FixedPatternStep
instanceKlass org/gradle/api/internal/file/pattern/HasSuffixPatternStep
instanceKlass org/gradle/api/internal/file/pattern/HasPrefixPatternStep
instanceKlass org/gradle/api/internal/file/pattern/HasPrefixAndSuffixPatternStep
instanceKlass org/gradle/api/internal/file/pattern/AnyWildcardPatternStep
instanceKlass org/gradle/api/internal/file/pattern/PatternStep
instanceKlass org/gradle/api/internal/file/pattern/PatternStepFactory
instanceKlass org/gradle/api/internal/file/pattern/FixedStepPathMatcher
instanceKlass org/gradle/api/internal/file/pattern/GreedyPathMatcher
instanceKlass org/gradle/api/internal/file/pattern/EndOfPathMatcher
instanceKlass org/gradle/api/internal/file/pattern/PatternMatcher
instanceKlass org/gradle/api/internal/file/pattern/PathMatcher
instanceKlass org/gradle/api/internal/file/pattern/PatternMatcherFactory
instanceKlass org/gradle/api/tasks/util/internal/CachingPatternSpecFactory$1
instanceKlass org/gradle/api/tasks/util/internal/CachingPatternSpecFactory$SpecKey
instanceKlass org/gradle/cache/internal/BuildScopeCacheDir
instanceKlass org/gradle/launcher/exec/RootBuildLifecycleBuildActionExecutor$$Lambda$71
instanceKlass org/gradle/initialization/buildsrc/BuildSrcDetector
instanceKlass org/gradle/internal/watch/vfs/impl/WatchingVirtualFileSystem$$Lambda$70
instanceKlass java/util/function/UnaryOperator
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeLifecycleController
instanceKlass org/gradle/internal/buildtree/BuildTreeLifecycleController
instanceKlass org/gradle/internal/buildtree/BuildTreeModelController
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeModelCreator
instanceKlass org/gradle/internal/buildtree/BuildTreeModelCreator
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeWorkPreparer
instanceKlass org/gradle/internal/buildtree/BuildTreeWorkPreparer
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeFinishExecutor
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeWorkExecutor
instanceKlass org/gradle/internal/buildtree/BuildOperationFiringBuildTreeWorkExecutor
instanceKlass org/gradle/execution/SelectedTaskExecutionAction
instanceKlass org/gradle/execution/DryRunBuildExecutionAction
instanceKlass org/gradle/execution/BuildOperationFiringBuildWorkerExecutor
instanceKlass org/gradle/internal/build/DefaultBuildWorkPreparer
instanceKlass org/gradle/internal/taskgraph/CalculateTaskGraphBuildOperationType$TaskIdentity
instanceKlass org/gradle/internal/build/BuildOperationFiringBuildWorkPreparer
instanceKlass org/gradle/execution/plan/DefaultNodeValidator
instanceKlass org/gradle/execution/plan/ExecutionPlan
instanceKlass org/gradle/internal/snapshot/EmptyChildMap
instanceKlass org/gradle/internal/collect/PersistentList
instanceKlass org/gradle/internal/snapshot/ChildMap$StoreHandler
instanceKlass org/gradle/internal/snapshot/ChildMap$NodeHandler
instanceKlass org/gradle/execution/plan/ValuedVfsHierarchy
instanceKlass org/gradle/execution/plan/ExecutionNodeAccessHierarchy$AbstractNodeAccessVisitor
instanceKlass org/gradle/execution/plan/ValuedVfsHierarchy$ValueVisitor
instanceKlass org/gradle/execution/plan/ExecutionNodeAccessHierarchy
instanceKlass org/gradle/internal/graph/CachingDirectedGraphWalker$GraphWithEmptyEdges
instanceKlass org/gradle/api/internal/tasks/CachingTaskDependencyResolveContext$TaskGraphImpl
instanceKlass org/gradle/internal/graph/DirectedGraphWithEdgeValues
instanceKlass org/gradle/internal/graph/CachingDirectedGraphWalker
instanceKlass org/gradle/internal/graph/DirectedGraph
instanceKlass org/gradle/api/internal/tasks/AbstractTaskDependencyResolveContext
instanceKlass org/gradle/api/internal/tasks/TaskDependencyResolveContext
instanceKlass org/gradle/execution/plan/TaskNodeFactory$DefaultTypeOriginInspectorFactory
instanceKlass org/gradle/initialization/internal/InternalBuildFinishedListener
instanceKlass org/gradle/BuildResult
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleController
instanceKlass org/gradle/internal/work/DefaultSynchronizer
instanceKlass org/gradle/internal/model/StateTransitionController$CurrentState
instanceKlass org/gradle/internal/model/StateTransitionController
instanceKlass org/gradle/internal/Describables$AbstractDescribable
instanceKlass org/gradle/internal/Describables
instanceKlass org/gradle/api/internal/artifacts/DefaultBuildIdentifier
instanceKlass org/gradle/internal/model/StateTransitionController$State
instanceKlass org/gradle/initialization/VintageBuildModelController
instanceKlass org/gradle/initialization/DefaultTaskExecutionPreparer
instanceKlass org/gradle/execution/BuildExecutionContext
instanceKlass org/gradle/execution/DefaultBuildConfigurationActionExecuter
instanceKlass org/gradle/execution/TaskNameResolvingBuildConfigurationAction
instanceKlass org/gradle/execution/DefaultTasksBuildExecutionAction
instanceKlass org/gradle/execution/BuildConfigurationAction
instanceKlass org/gradle/execution/commandline/CommandLineTaskConfigurer
instanceKlass org/gradle/api/internal/tasks/options/OptionValueNotationParserFactory
instanceKlass org/gradle/initialization/DefaultSettingsPreparer
instanceKlass org/gradle/initialization/BuildOperationFiringSettingsPreparer$1
instanceKlass org/gradle/initialization/LoadBuildBuildOperationType$Result
instanceKlass org/gradle/initialization/BuildOperationFiringSettingsPreparer
instanceKlass org/gradle/internal/id/LongIdGenerator
instanceKlass org/gradle/configuration/DefaultInitScriptProcessor
instanceKlass org/gradle/api/internal/project/ProjectState
instanceKlass org/gradle/internal/model/ModelContainer
instanceKlass org/gradle/initialization/SettingsFactory
instanceKlass org/gradle/initialization/ScriptEvaluatingSettingsProcessor
instanceKlass org/gradle/initialization/SettingsEvaluatedCallbackFiringSettingsProcessor
instanceKlass org/gradle/initialization/RootBuildCacheControllerSettingsProcessor
instanceKlass org/gradle/initialization/BuildOperationSettingsProcessor$1
instanceKlass org/gradle/initialization/EvaluateSettingsBuildOperationType$Result
instanceKlass org/gradle/initialization/BuildOperationSettingsProcessor
instanceKlass org/gradle/internal/resource/TextResource
instanceKlass org/gradle/internal/resource/DefaultTextFileResourceLoader
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/UnknownProjectFinder
instanceKlass org/gradle/api/internal/initialization/ScriptHandlerInternal
instanceKlass org/gradle/api/initialization/dsl/ScriptHandler
instanceKlass org/gradle/api/internal/initialization/DefaultScriptHandlerFactory
instanceKlass org/gradle/api/internal/initialization/DefaultScriptClassPathResolver
instanceKlass org/gradle/configuration/ScriptPluginFactorySelector$1
instanceKlass org/gradle/configuration/ScriptPluginFactorySelector$ProviderInstantiator
instanceKlass org/gradle/configuration/ScriptPluginFactorySelector
instanceKlass org/gradle/groovy/scripts/internal/StatementTransformer
instanceKlass org/gradle/groovy/scripts/internal/CompileOperation
instanceKlass org/gradle/groovy/scripts/Transformer
instanceKlass org/gradle/configuration/project/DefaultCompileOperationFactory
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory$DefaultClassMap$$Lambda$69
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/plugin/management/internal/DefaultPluginResolutionStrategy
instanceKlass org/gradle/plugin/use/internal/PluginDependencyResolutionServices$DefaultPluginRepositoriesProvider
instanceKlass org/gradle/plugin/internal/PluginUsePluginServiceRegistry$BuildScopeServices$1
instanceKlass org/gradle/api/internal/artifacts/Module
instanceKlass org/gradle/internal/service/scopes/BuildScopeServices$DependencyMetaDataProviderImpl
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencyManagementServices
instanceKlass org/gradle/api/internal/file/AbstractFileResolver$2
instanceKlass org/apache/commons/io/FilenameUtils
instanceKlass org/gradle/internal/typeconversion/NotationConverterToNotationParserAdapter$ResultImpl
instanceKlass org/gradle/util/internal/DeferredUtil
instanceKlass org/gradle/plugin/use/resolve/service/internal/ClientInjectedClasspathPluginResolver$$Lambda$68
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/plugins/PluginImplementation
instanceKlass org/gradle/api/internal/plugins/DefaultPluginRegistry
instanceKlass org/gradle/api/internal/plugins/PotentialPlugin
instanceKlass org/gradle/model/internal/inspect/ModelRuleSourceDetector$1
instanceKlass com/google/common/collect/MapMakerInternalMap$StrongKeyWeakValueEntry$Helper
instanceKlass org/gradle/api/internal/initialization/ClassLoaderScopeIdentifier
instanceKlass org/gradle/api/internal/initialization/AbstractClassLoaderScope
instanceKlass org/gradle/api/internal/initialization/loadercache/ClassLoaderId
instanceKlass org/gradle/initialization/ClassLoaderScopeId
instanceKlass org/gradle/initialization/DefaultClassLoaderScopeRegistry
instanceKlass org/gradle/api/internal/initialization/loadercache/DefaultClassLoaderCache$ClassLoaderSpec
instanceKlass org/gradle/api/internal/initialization/loadercache/DefaultClassLoaderCache
instanceKlass org/gradle/composite/internal/plugins/CompositeBuildPluginResolverContributor$CompositeBuildPluginResolver
instanceKlass org/gradle/composite/internal/plugins/CompositeBuildPluginResolverContributor
instanceKlass org/gradle/plugin/management/internal/autoapply/DefaultAutoAppliedPluginHandler
instanceKlass org/gradle/plugin/management/internal/SingletonPluginRequests
instanceKlass java/util/DualPivotQuicksort
instanceKlass org/gradle/plugin/use/internal/DefaultPluginId
instanceKlass org/gradle/plugin/use/PluginId
instanceKlass org/gradle/plugin/management/internal/autoapply/AutoAppliedGradleEnterprisePlugin
instanceKlass org/gradle/plugin/management/internal/DefaultPluginRequest
instanceKlass org/gradle/api/internal/artifacts/DefaultModuleVersionSelector
instanceKlass org/gradle/api/artifacts/ModuleVersionSelector
instanceKlass org/gradle/api/internal/artifacts/DefaultModuleIdentifier
instanceKlass org/gradle/plugin/management/internal/PluginRequestInternal
instanceKlass org/gradle/plugin/management/PluginRequest
instanceKlass org/gradle/plugin/management/internal/autoapply/DefaultAutoAppliedPluginRegistry
instanceKlass org/gradle/workers/internal/DefaultWorkResult
instanceKlass org/gradle/api/tasks/WorkResult
instanceKlass org/gradle/internal/work/ConditionalExecutionQueue
instanceKlass org/gradle/groovy/scripts/internal/BuildScopeInMemoryCachingScriptClassCompiler
instanceKlass org/gradle/groovy/scripts/ScriptCompiler
instanceKlass org/gradle/groovy/scripts/DefaultScriptCompilerFactory
instanceKlass org/gradle/groovy/scripts/ScriptRunner
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptRunnerFactory
instanceKlass org/gradle/groovy/scripts/internal/BuildOperationBackedScriptCompilationHandler$1
instanceKlass org/gradle/internal/scripts/CompileScriptBuildOperationType$Result
instanceKlass org/gradle/groovy/scripts/internal/BuildOperationBackedScriptCompilationHandler
instanceKlass org/gradle/groovy/scripts/ScriptSource
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptCompilationHandler$NoOpGroovyResourceLoader
instanceKlass org/gradle/groovy/scripts/internal/CompiledScript
instanceKlass groovy/lang/GroovyResourceLoader
instanceKlass com/google/common/base/AbstractIterator$1
instanceKlass com/google/common/base/AbstractIterator
instanceKlass com/google/common/base/Splitter$1
instanceKlass com/google/common/base/CharMatcher
instanceKlass com/google/common/base/Splitter$Strategy
instanceKlass com/google/common/base/Splitter
instanceKlass org/gradle/configuration/DefaultImportsReader$2
instanceKlass com/google/common/io/Java8Compatibility
instanceKlass com/google/common/io/LineBuffer
instanceKlass com/google/common/io/LineReader
instanceKlass com/google/common/io/CharStreams
instanceKlass org/gradle/configuration/DefaultImportsReader$1
instanceKlass com/google/common/io/Resources
instanceKlass org/gradle/configuration/DefaultImportsReader
instanceKlass org/gradle/configuration/ScriptPlugin
instanceKlass org/gradle/api/Plugin
instanceKlass org/gradle/configuration/internal/DefaultUserCodeApplicationContext
instanceKlass org/gradle/composite/internal/CompositeBuildClassPathInitializer
instanceKlass org/gradle/api/tasks/TaskContainer
instanceKlass org/gradle/api/PolymorphicDomainObjectContainer
instanceKlass org/gradle/api/tasks/TaskCollection
instanceKlass org/gradle/execution/TaskSelectionResult
instanceKlass org/gradle/execution/TaskNameResolver
instanceKlass org/gradle/execution/ExcludedTaskFilteringProjectsPreparer
instanceKlass org/gradle/execution/DefaultTaskSchedulingPreparer
instanceKlass org/gradle/initialization/TaskSchedulingPreparer
instanceKlass org/gradle/configuration/DefaultProjectsPreparer
instanceKlass org/gradle/configuration/BuildTreePreparingProjectsPreparer
instanceKlass org/gradle/configuration/BuildOperationFiringProjectsPreparer$1
instanceKlass org/gradle/initialization/ConfigureBuildBuildOperationType$Result
instanceKlass org/gradle/configuration/BuildOperationFiringProjectsPreparer
instanceKlass org/gradle/initialization/ModelConfigurationListener
instanceKlass org/gradle/internal/resource/local/FileResourceListener
instanceKlass org/gradle/initialization/InstantiatingBuildLoader
instanceKlass org/gradle/initialization/ProjectPropertySettingBuildLoader
instanceKlass org/gradle/initialization/NotifyingBuildLoader$$Lambda$67
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/initialization/NotifyingBuildLoader$1
instanceKlass org/gradle/initialization/LoadProjectsBuildOperationType$Result$Project
instanceKlass org/gradle/initialization/NotifyProjectsLoadedBuildOperationType$Result
instanceKlass org/gradle/initialization/NotifyingBuildLoader
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController$SharedGradleProperties
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController$NotLoaded
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController$State
instanceKlass org/gradle/initialization/DefaultGradlePropertiesController
instanceKlass org/gradle/initialization/DefaultGradlePropertiesLoader
instanceKlass kotlin/UNINITIALIZED_VALUE
instanceKlass kotlin/SafePublicationLazyImpl$Companion
instanceKlass kotlin/SafePublicationLazyImpl
instanceKlass kotlin/Lazy
instanceKlass kotlin/LazyKt$WhenMappings
instanceKlass kotlin/LazyKt__LazyJVMKt
instanceKlass kotlin/reflect/jvm/internal/pcollections/MapEntry
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val$1
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties
instanceKlass kotlin/jvm/internal/Lambda
instanceKlass kotlin/text/Regex$Companion
instanceKlass kotlin/text/Regex
instanceKlass kotlin/jvm/internal/DefaultConstructorMarker
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KClassifierImpl
instanceKlass kotlin/reflect/jvm/internal/pcollections/ConsPStack
instanceKlass kotlin/reflect/jvm/internal/pcollections/IntTree
instanceKlass kotlin/reflect/jvm/internal/pcollections/IntTreePMap
instanceKlass kotlin/reflect/jvm/internal/pcollections/HashPMap
instanceKlass kotlin/reflect/jvm/internal/KClassCacheKt
instanceKlass kotlin/reflect/jvm/internal/KPropertyImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl
instanceKlass kotlin/reflect/jvm/internal/KTypeParameterOwnerImpl
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl
instanceKlass kotlin/jvm/internal/ClassBasedDeclarationContainer
instanceKlass kotlin/reflect/KMutableProperty1
instanceKlass kotlin/reflect/KMutableProperty2
instanceKlass kotlin/reflect/KProperty1
instanceKlass kotlin/jvm/functions/Function1
instanceKlass kotlin/reflect/KTypeParameter
instanceKlass kotlin/reflect/KType
instanceKlass kotlin/reflect/KProperty2
instanceKlass kotlin/reflect/KMutableProperty0
instanceKlass kotlin/reflect/KMutableProperty
instanceKlass kotlin/jvm/internal/FunctionBase
instanceKlass kotlin/reflect/KClass
instanceKlass kotlin/jvm/internal/ReflectionFactory
instanceKlass kotlin/reflect/KClassifier
instanceKlass kotlin/jvm/internal/Reflection
instanceKlass kotlin/jvm/internal/CallableReference$NoReceiver
instanceKlass kotlin/reflect/KProperty$Getter
instanceKlass kotlin/reflect/KFunction
instanceKlass kotlin/reflect/KProperty$Accessor
instanceKlass kotlin/reflect/KDeclarationContainer
instanceKlass kotlin/jvm/internal/CallableReference
instanceKlass kotlin/reflect/KProperty0
instanceKlass kotlin/jvm/functions/Function0
instanceKlass kotlin/reflect/KProperty
instanceKlass kotlin/reflect/KCallable
instanceKlass kotlin/reflect/KAnnotatedElement
instanceKlass org/gradle/kotlin/dsl/tooling/builders/BuildSrcClassPathModeConfigurationAction
instanceKlass org/gradle/initialization/buildsrc/GroovyBuildSrcProjectConfigurationAction
instanceKlass org/gradle/configuration/project/PluginsProjectConfigureActions
instanceKlass org/gradle/api/internal/InternalAction
instanceKlass org/gradle/configuration/project/ProjectConfigureAction
instanceKlass org/gradle/initialization/buildsrc/BuildSrcProjectConfigurationAction
instanceKlass org/gradle/initialization/buildsrc/BuildSrcBuildListenerFactory
instanceKlass org/gradle/initialization/buildsrc/BuildSourceBuilder$1
instanceKlass org/gradle/initialization/buildsrc/BuildBuildSrcBuildOperationType$Result
instanceKlass org/gradle/util/internal/GUtil$1
instanceKlass org/gradle/internal/build/DefaultPublicBuildPath
instanceKlass org/gradle/api/internal/AbstractMutationGuard
instanceKlass org/gradle/internal/operations/RunnableBuildOperation
instanceKlass org/gradle/api/internal/project/BuildOperationCrossProjectConfigurator
instanceKlass org/gradle/api/internal/WithMutationGuard
instanceKlass org/gradle/internal/concurrent/CompositeStoppable$2
instanceKlass org/gradle/configurationcache/serialization/beans/BeanStateWriterLookup
instanceKlass org/gradle/configurationcache/serialization/beans/BeanStateReaderLookup
instanceKlass org/gradle/configurationcache/ConfigurationCacheClassLoaderScopeRegistryListener
instanceKlass org/gradle/configurationcache/serialization/ScopeLookup
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistryListener
instanceKlass org/gradle/configurationcache/problems/ConfigurationCacheProblems
instanceKlass org/gradle/configurationcache/ConfigurationCacheIO
instanceKlass org/gradle/configurationcache/ConfigurationCacheHost
instanceKlass org/gradle/configurationcache/DefaultConfigurationCache$Host
instanceKlass org/gradle/cache/internal/FileContentCacheFactory$Calculator
instanceKlass org/gradle/language/nativeplatform/internal/incremental/sourceparser/CachingCSourceParser
instanceKlass org/gradle/language/nativeplatform/internal/incremental/sourceparser/CSourceParser
instanceKlass org/gradle/language/nativeplatform/internal/incremental/DefaultCompilationStateCacheFactory
instanceKlass org/gradle/language/nativeplatform/internal/incremental/CompilationStateCacheFactory
instanceKlass org/gradle/internal/scan/config/BuildScanConfig
instanceKlass org/gradle/internal/scan/config/BuildScanConfig$Attributes
instanceKlass org/gradle/internal/enterprise/impl/legacy/LegacyGradleEnterprisePluginCheckInService
instanceKlass org/gradle/internal/scan/eob/BuildScanEndOfBuildNotifier
instanceKlass org/gradle/internal/scan/config/BuildScanConfigProvider
instanceKlass org/gradle/internal/enterprise/impl/legacy/DefaultBuildScanScopeIds
instanceKlass org/gradle/internal/scan/scopeids/BuildScanScopeIds
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginCheckInResult
instanceKlass org/gradle/internal/enterprise/impl/DefautGradleEnterprisePluginCheckInService
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginCheckInService
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginConfig
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginBuildState
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginServiceRef
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginBuildState
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginConfig
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginEndOfBuildListener$BuildResult
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginServiceRef
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginAdapter
instanceKlass org/gradle/internal/enterprise/core/GradleEnterprisePluginAdapter
instanceKlass org/gradle/initialization/DefaultJdkToolsInitializer
instanceKlass org/gradle/api/internal/tasks/compile/incremental/classpath/CachingClassSetAnalyzer
instanceKlass org/gradle/api/internal/tasks/compile/incremental/analyzer/CachingClassDependenciesAnalyzer
instanceKlass org/gradle/api/internal/tasks/compile/incremental/IncrementalCompilerFactory
instanceKlass org/gradle/api/internal/tasks/compile/incremental/classpath/ClassSetAnalyzer
instanceKlass org/gradle/api/internal/tasks/compile/incremental/analyzer/ClassDependenciesAnalyzer
instanceKlass org/gradle/api/internal/tasks/CompileServices$GradleScopeCompileServices
instanceKlass org/gradle/language/java/artifact/JavadocArtifact
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$JavaGradleScopeServices
instanceKlass org/gradle/api/internal/artifacts/DependencyServices$DependencyManagementGradleServices
instanceKlass org/gradle/kotlin/dsl/accessors/PluginAccessorClassPathGenerator
instanceKlass org/gradle/kotlin/dsl/accessors/ProjectAccessorsClassPathGenerator
instanceKlass org/gradle/kotlin/dsl/accessors/GradleScopeServices
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/execution/OutputChangeListener
instanceKlass org/gradle/internal/execution/history/OutputsCleaner
instanceKlass org/gradle/internal/execution/history/OutputFilesRepository
instanceKlass org/gradle/internal/service/scopes/ExecutionGradleServices
instanceKlass org/gradle/caching/internal/controller/BuildCacheController
instanceKlass org/gradle/caching/internal/origin/OriginMetadataFactory
instanceKlass org/gradle/caching/internal/packaging/BuildCacheEntryPacker
instanceKlass org/gradle/caching/internal/packaging/impl/FilePermissionAccess
instanceKlass org/gradle/caching/internal/packaging/impl/TarPackerFileSystemSupport
instanceKlass org/gradle/caching/internal/controller/BuildCacheCommandFactory
instanceKlass org/gradle/caching/internal/BuildCacheServices$3
instanceKlass org/gradle/internal/service/scopes/GradleScopeServices$$Lambda$66
instanceKlass org/gradle/api/execution/TaskExecutionGraphListener
instanceKlass org/gradle/api/services/internal/BuildServiceRegistryInternal
instanceKlass org/gradle/execution/plan/PlanExecutor
instanceKlass org/gradle/api/execution/TaskExecutionListener
instanceKlass org/gradle/api/internal/tasks/options/OptionReader
instanceKlass org/gradle/execution/plan/WorkNodeExecutor
instanceKlass org/gradle/execution/plan/LocalTaskNodeExecutor
instanceKlass org/gradle/execution/plan/NodeExecutor
instanceKlass org/gradle/execution/commandline/CommandLineTaskParser
instanceKlass org/gradle/internal/execution/BuildOutputCleanupRegistry
instanceKlass org/gradle/initialization/TaskExecutionPreparer
instanceKlass org/gradle/execution/BuildConfigurationActionExecuter
instanceKlass org/gradle/execution/BuildWorkExecutor
instanceKlass org/gradle/internal/ImmutableActionSet
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ObjectCreationDetails
instanceKlass com/google/common/collect/Ordering
instanceKlass org/gradle/internal/instantiation/generator/ConstructorComparator
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$InvokeConstructorStrategy
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl$GeneratedConstructorImpl
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$GeneratedConstructor
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$SerializationConstructor
instanceKlass org/objectweb/asm/Handler
instanceKlass org/objectweb/asm/Attribute
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$ReturnTypeEntry
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$65
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$64
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSet$1
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection$WrappedIterator
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$63
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$62
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$61
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$60
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$59
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$58
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$57
instanceKlass org/gradle/internal/reflect/JavaReflectionUtil
instanceKlass org/gradle/model/internal/asm/AsmClassGeneratorUtils
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$56
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$55
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$54
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$MethodCodeBody
instanceKlass org/objectweb/asm/Edge
instanceKlass org/objectweb/asm/Label
instanceKlass org/objectweb/asm/Frame
instanceKlass org/objectweb/asm/ByteVector
instanceKlass org/objectweb/asm/Symbol
instanceKlass org/objectweb/asm/SymbolTable
instanceKlass org/objectweb/asm/FieldVisitor
instanceKlass org/objectweb/asm/MethodVisitor
instanceKlass org/objectweb/asm/AnnotationVisitor
instanceKlass org/objectweb/asm/ModuleVisitor
instanceKlass org/objectweb/asm/RecordComponentVisitor
instanceKlass org/gradle/model/internal/asm/AsmClassGenerator
instanceKlass org/gradle/internal/DisplayName
instanceKlass java/util/concurrent/LinkedBlockingDeque$Node
instanceKlass org/gradle/internal/instantiation/generator/ManagedObjectFactory
instanceKlass java/lang/management/MemoryUsage
instanceKlass org/gradle/util/internal/ConfigureUtil
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionEvent
instanceKlass org/gradle/internal/metaobject/AbstractDynamicObject
instanceKlass org/gradle/api/plugins/Convention
instanceKlass org/gradle/api/plugins/ExtensionContainer
instanceKlass org/gradle/internal/metaobject/DynamicObject
instanceKlass org/gradle/internal/metaobject/PropertyAccess
instanceKlass org/gradle/internal/metaobject/MethodAccess
instanceKlass org/gradle/internal/extensibility/ConventionAwareHelper
instanceKlass org/gradle/api/internal/HasConvention
instanceKlass org/gradle/api/internal/IConventionAware
instanceKlass org/gradle/api/internal/GeneratedSubclass
instanceKlass org/gradle/api/internal/ConventionMapping
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassBuilderImpl
instanceKlass java/lang/Deprecated
instanceKlass javax/annotation/Nullable
instanceKlass org/gradle/api/internal/DynamicObjectAware
instanceKlass org/gradle/internal/extensibility/NoConventionMapping
instanceKlass sun/reflect/generics/tree/Wildcard
instanceKlass sun/reflect/generics/tree/BottomSignature
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$MethodMetadata
instanceKlass org/gradle/internal/reflect/PropertyAccessor
instanceKlass org/gradle/internal/reflect/PropertyMutator
instanceKlass org/gradle/internal/reflect/JavaPropertyReflectionUtil
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$PropertyMetadata
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassMetadata
instanceKlass org/gradle/configuration/ConfigurationTargetIdentifier
instanceKlass org/gradle/api/plugins/PluginContainer
instanceKlass org/gradle/api/plugins/PluginCollection
instanceKlass org/gradle/internal/reflect/MutablePropertyDetails
instanceKlass java/beans/FeatureDescriptor
instanceKlass java/util/EventListener
instanceKlass com/sun/beans/WeakCache
instanceKlass java/beans/Introspector
instanceKlass org/gradle/internal/reflect/MethodSet$MethodKey
instanceKlass org/gradle/api/internal/plugins/DefaultObjectConfigurationAction
instanceKlass org/gradle/api/plugins/ObjectConfigurationAction
instanceKlass org/gradle/api/services/BuildServiceRegistry
instanceKlass org/gradle/execution/taskgraph/TaskExecutionGraphInternal
instanceKlass org/gradle/api/internal/plugins/PluginManagerInternal
instanceKlass org/gradle/api/internal/SettingsInternal
instanceKlass org/gradle/api/initialization/Settings
instanceKlass org/gradle/api/internal/initialization/ClassLoaderScope
instanceKlass org/gradle/util/Path
instanceKlass groovy/lang/GroovyObjectSupport
instanceKlass groovy/lang/GroovyCallable
instanceKlass org/gradle/internal/MutableActionSet
instanceKlass org/gradle/api/execution/TaskExecutionGraph
instanceKlass org/gradle/api/plugins/PluginManager
instanceKlass org/gradle/internal/reflect/PropertyDetails
instanceKlass org/gradle/internal/reflect/MutableClassDetails
instanceKlass org/gradle/internal/reflect/ClassDetails
instanceKlass org/gradle/internal/reflect/ClassInspector
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassGenerationVisitor
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$ClassInspectionVisitorImpl
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$InjectionAnnotationValidator
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$DisabledAnnotationValidator
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassValidator
instanceKlass com/google/common/collect/LinkedHashMultimap$ValueSetLink
instanceKlass org/gradle/internal/reflect/MethodSet
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassGenerationHandler
instanceKlass org/gradle/api/internal/project/AbstractPluginAware
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleControllerFactory$1
instanceKlass org/gradle/internal/featurelifecycle/ScriptUsageLocationReporter
instanceKlass org/gradle/configurationcache/DeprecatedFeaturesListenerManagerAction$DeprecatedFeaturesListener
instanceKlass org/gradle/profile/BuildProfileServices$1$1
instanceKlass org/gradle/api/HasImplicitReceiver
instanceKlass org/gradle/api/NonExtensible
instanceKlass org/gradle/internal/build/AbstractBuildState$$Lambda$53
instanceKlass org/gradle/internal/build/AbstractBuildState$$Lambda$52
instanceKlass org/gradle/internal/build/AbstractBuildState$$Lambda$51
instanceKlass org/gradle/internal/lazy/Lazy$$Lambda$50
instanceKlass org/gradle/internal/lazy/Lazy$Factory
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/lazy/LockingLazy
instanceKlass org/gradle/internal/lazy/Lazy
instanceKlass org/gradle/configurationcache/RelevantProjectsRegistry
instanceKlass org/gradle/profile/ProfileEventAdapter
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransformListener
instanceKlass org/gradle/initialization/BuildCompletionListener
instanceKlass org/gradle/api/artifacts/DependencyResolutionListener
instanceKlass org/gradle/execution/taskgraph/TaskListenerInternal
instanceKlass org/gradle/api/ProjectEvaluationListener
instanceKlass org/gradle/profile/ProfileListener
instanceKlass org/gradle/profile/BuildProfileServices$1
instanceKlass org/gradle/vcs/internal/resolver/VcsDependencyResolver
instanceKlass org/gradle/vcs/internal/resolver/VcsVersionWorkingDirResolver
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VersionControlBuildServices
instanceKlass org/gradle/tooling/provider/model/ToolingModelBuilder
instanceKlass org/gradle/language/cpp/internal/tooling/ToolingNativeServices$ToolingModelRegistration
instanceKlass org/gradle/authentication/aws/AwsImAuthentication
instanceKlass org/gradle/internal/resource/transport/aws/s3/S3ResourcesPluginServiceRegistry$AuthenticationSchemeAction
instanceKlass org/gradle/nativeplatform/toolchain/internal/metadata/CompilerMetaDataProvider
instanceKlass org/gradle/nativeplatform/toolchain/internal/metadata/CompilerMetaDataProviderFactory
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/nativeplatform/internal/resolve/LibraryBinaryLocator
instanceKlass org/gradle/nativeplatform/internal/resolve/NativeDependencyResolver
instanceKlass org/gradle/nativeplatform/internal/resolve/NativeDependencyResolverServices
instanceKlass org/gradle/language/cpp/internal/NativeDependencyCache
instanceKlass org/gradle/ide/xcode/internal/xcodeproj/GidGenerator
instanceKlass org/gradle/ide/xcode/internal/services/XcodeServices$GlobalIdGeneratorServices
instanceKlass org/gradle/plugins/ide/internal/configurer/UniqueProjectNameProvider
instanceKlass org/gradle/plugins/ide/internal/tooling/ToolingModelServices$BuildScopeToolingServices
instanceKlass org/gradle/plugin/use/resolve/internal/PluginResolverContributor
instanceKlass org/gradle/composite/internal/CompositeBuildServices$CompositeBuildBuildScopeServices
instanceKlass org/gradle/caching/http/internal/HttpBuildCacheServiceServices$$Lambda$49
instanceKlass org/apache/http/HttpRequest
instanceKlass org/apache/http/HttpMessage
instanceKlass org/gradle/caching/http/internal/HttpBuildCacheRequestCustomizer
instanceKlass org/gradle/caching/http/internal/DefaultHttpBuildCacheServiceFactory
instanceKlass org/gradle/caching/BuildCacheServiceFactory
instanceKlass org/gradle/caching/configuration/AbstractBuildCache
instanceKlass org/gradle/caching/configuration/BuildCache
instanceKlass org/gradle/caching/configuration/internal/DefaultBuildCacheServiceRegistration
instanceKlass org/gradle/maven/MavenPomArtifact
instanceKlass org/gradle/maven/MavenModule
instanceKlass org/gradle/api/publish/maven/internal/publisher/MavenPublishers
instanceKlass org/gradle/api/publish/maven/internal/dependencies/VersionRangeMapper
instanceKlass org/gradle/api/publish/maven/internal/MavenPublishServices$ComponentRegistrationAction
instanceKlass org/gradle/api/publish/internal/validation/DuplicatePublicationTracker
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/DefaultProjectDependencyPublicationResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectDependencyPublicationResolver
instanceKlass org/gradle/jvm/toolchain/internal/LocationListInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/EnvironmentVariableListInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/AutoDetectingInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/InstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/JavaInstallationRegistry
instanceKlass org/gradle/jvm/toolchain/install/internal/JdkCacheDirectory
instanceKlass org/gradle/language/base/artifact/SourcesArtifact
instanceKlass org/gradle/jvm/JvmLibrary
instanceKlass org/gradle/platform/base/Library
instanceKlass org/gradle/language/jvm/internal/JvmPluginServiceRegistry$ComponentRegistrationAction
instanceKlass org/gradle/ivy/IvyDescriptorArtifact
instanceKlass org/gradle/api/component/Artifact
instanceKlass org/gradle/api/internal/component/DefaultComponentTypeRegistry$DefaultComponentTypeRegistration
instanceKlass org/gradle/ivy/IvyModule
instanceKlass org/gradle/api/component/Component
instanceKlass org/gradle/api/internal/component/ComponentTypeRegistration
instanceKlass org/gradle/api/internal/component/DefaultComponentTypeRegistry
instanceKlass org/gradle/api/publish/ivy/internal/publisher/IvyPublisher
instanceKlass org/gradle/api/publish/ivy/internal/IvyServices$BuildServices
instanceKlass org/gradle/api/internal/resolve/ProjectModelResolver
instanceKlass org/gradle/platform/base/internal/registry/ComponentModelBaseServiceRegistry$BuildScopeServices
instanceKlass org/gradle/plugin/use/tracker/internal/PluginVersionTracker
instanceKlass org/gradle/plugin/use/resolve/internal/PluginResolutionResult
instanceKlass org/gradle/api/internal/plugins/PluginDescriptorLocator
instanceKlass org/gradle/plugin/use/internal/DefaultPluginRequestApplicator
instanceKlass org/gradle/plugin/use/resolve/internal/PluginResolver
instanceKlass org/gradle/plugin/use/internal/PluginResolverFactory
instanceKlass org/gradle/plugin/management/internal/PluginResolutionStrategyInternal
instanceKlass org/gradle/plugin/management/PluginResolutionStrategy
instanceKlass org/gradle/plugin/use/resolve/internal/PluginRepositoriesProvider
instanceKlass org/gradle/plugin/use/internal/PluginDependencyResolutionServices
instanceKlass org/gradle/api/internal/artifacts/DependencyResolutionServices
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/ProjectFinder
instanceKlass org/gradle/plugin/management/internal/autoapply/AutoAppliedPluginRegistry
instanceKlass org/gradle/plugin/use/resolve/service/internal/ClientInjectedClasspathPluginResolver
instanceKlass org/gradle/plugin/internal/PluginUsePluginServiceRegistry$BuildScopeServices
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationNodeDependencyResolver
instanceKlass org/gradle/internal/component/model/ComponentResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectArtifactSetResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectArtifactResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ResolverProviderFactory
instanceKlass org/gradle/internal/component/external/model/ModuleComponentArtifactMetadata
instanceKlass org/gradle/internal/component/model/ComponentArtifactMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionParser
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/ModuleExclusions
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectDependencyResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ComponentResolvers
instanceKlass org/gradle/internal/resolve/resolver/OriginArtifactSelector
instanceKlass org/gradle/internal/resolve/resolver/ArtifactResolver
instanceKlass org/gradle/internal/resolve/resolver/DependencyToComponentIdResolver
instanceKlass org/gradle/internal/resolve/resolver/ComponentMetaDataResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ResolveIvyFactory
instanceKlass org/gradle/api/internal/runtimeshaded/RuntimeShadedJarFactory
instanceKlass org/gradle/internal/resource/local/LocallyAvailableResourceFinder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/verification/DependencyVerificationOverride
instanceKlass org/gradle/internal/resolve/caching/CrossBuildCachingRuleExecutor
instanceKlass org/gradle/internal/resolve/caching/CachingRuleExecutor
instanceKlass org/gradle/internal/resource/local/GroupedAndNamedUniqueFileStore
instanceKlass org/gradle/internal/resource/cached/DefaultExternalResourceFileStore$Factory
instanceKlass org/gradle/internal/management/DependencyResolutionManagementInternal
instanceKlass org/gradle/api/initialization/resolve/DependencyResolutionManagement
instanceKlass org/gradle/internal/resource/cached/AbstractCachedIndex
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleRepositoryCacheProvider
instanceKlass org/gradle/api/internal/filestore/DefaultArtifactIdentifierFileStore$Factory
instanceKlass org/gradle/internal/verifier/HttpRedirectVerifier
instanceKlass org/gradle/api/internal/artifacts/DefaultProjectDependencyFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleSourcesSerializer
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/IvyMutableModuleMetadataFactory
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MavenMutableModuleMetadataFactory
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MutableModuleMetadataFactory
instanceKlass org/gradle/internal/resource/TextUriResourceLoader$Factory
instanceKlass org/gradle/api/internal/artifacts/repositories/transport/RepositoryTransportFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/FileStoreAndIndexProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleRepositoryCaches
instanceKlass org/gradle/util/internal/SimpleMapInterner
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionSelectorScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/RepositoryDisabler
instanceKlass org/gradle/internal/execution/steps/DeferredExecutionAwareStep
instanceKlass org/gradle/internal/execution/steps/Step
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/MavenSettingsProvider
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/MavenFileLocations
instanceKlass org/gradle/api/internal/artifacts/ArtifactDependencyResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectPublicationRegistry
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionComparator
instanceKlass org/gradle/api/internal/artifacts/component/ComponentIdentifierFactory
instanceKlass org/gradle/api/internal/artifacts/verification/signatures/SignatureVerificationServiceFactory
instanceKlass org/gradle/internal/resource/cached/CachedExternalResourceIndex
instanceKlass org/gradle/internal/resource/cached/ExternalResourceFileStore
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/AttributeContainerSerializer
instanceKlass org/gradle/api/internal/artifacts/mvnsettings/LocalMavenRepositoryLocator
instanceKlass org/gradle/api/internal/artifacts/ComponentSelectorConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/dynamicversions/AbstractModuleVersionsCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/dynamicversions/ModuleVersionsCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/ModuleArtifactCache
instanceKlass org/gradle/api/internal/filestore/ArtifactIdentifierFileStore
instanceKlass org/gradle/internal/resource/local/FileStoreSearcher
instanceKlass org/gradle/internal/resource/TextUriResourceLoader
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/ExternalResourceAccessor
instanceKlass org/gradle/internal/resource/local/FileStore
instanceKlass org/gradle/initialization/DependenciesAccessors
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/AbstractModuleMetadataCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/AbstractArtifactsCache
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/ModuleArtifactsCache
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildScopeServices
instanceKlass org/gradle/authentication/http/HttpHeaderAuthentication
instanceKlass org/gradle/authentication/http/DigestAuthentication
instanceKlass org/gradle/internal/authentication/AbstractAuthentication
instanceKlass org/gradle/internal/authentication/AuthenticationInternal
instanceKlass org/gradle/authentication/http/BasicAuthentication
instanceKlass org/gradle/authentication/Authentication
instanceKlass org/gradle/internal/authentication/DefaultAuthenticationSchemeRegistry
instanceKlass org/gradle/internal/resource/transport/http/HttpResourcesPluginServiceRegistry$AuthenticationSchemeAction
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptEvaluator
instanceKlass org/gradle/internal/execution/ExecutionEngine
instanceKlass kotlin/jvm/functions/Function2
instanceKlass kotlin/Function
instanceKlass org/gradle/kotlin/dsl/provider/PluginRequestsHandler
instanceKlass org/gradle/plugin/management/internal/autoapply/AutoAppliedPluginHandler
instanceKlass org/gradle/plugin/use/internal/PluginRequestApplicator
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptClassPathProvider
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DependencyFactory
instanceKlass org/gradle/kotlin/dsl/provider/ClassPathModeExceptionCollector
instanceKlass org/gradle/kotlin/dsl/provider/BuildServices
instanceKlass org/gradle/kotlin/dsl/concurrent/AsyncIOScopeFactory
instanceKlass org/gradle/kotlin/dsl/concurrent/BuildServices
instanceKlass org/gradle/caching/configuration/internal/BuildCacheConfigurationInternal
instanceKlass org/gradle/caching/configuration/BuildCacheConfiguration
instanceKlass org/gradle/caching/configuration/internal/BuildCacheServiceRegistration
instanceKlass org/gradle/caching/local/internal/DirectoryBuildCacheFileStoreFactory
instanceKlass org/gradle/caching/internal/BuildCacheServices$2
instanceKlass org/gradle/configuration/project/ProjectEvaluator
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/LocalComponentRegistry
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/LocalComponentProvider
instanceKlass org/gradle/configurationcache/DefaultBuildModelControllerServices$VintageModelProvider
instanceKlass org/gradle/api/internal/project/CrossProjectModelAccess
instanceKlass org/gradle/configurationcache/DefaultBuildModelControllerServices$VintageIsolatedProjectsProvider
instanceKlass org/gradle/configurationcache/services/DefaultEnvironment
instanceKlass org/gradle/internal/build/BuildModelController
instanceKlass org/gradle/configurationcache/DefaultBuildModelControllerServices$VintageBuildControllerProvider
instanceKlass org/gradle/configurationcache/DeprecatedFeaturesListenerManagerAction
instanceKlass org/gradle/internal/service/scopes/BuildScopeListenerManagerAction
instanceKlass org/gradle/configurationcache/DefaultBuildModelControllerServices$ServicesProvider
instanceKlass org/gradle/internal/composite/DefaultBuildIncluder
instanceKlass org/gradle/internal/build/BuildWorkGraph
instanceKlass org/gradle/internal/build/ExportedTaskNode
instanceKlass org/gradle/internal/build/DefaultBuildWorkGraphController
instanceKlass org/gradle/internal/build/BuildWorkGraphController
instanceKlass org/gradle/api/Task
instanceKlass org/gradle/execution/plan/WorkNodeDependencyResolver
instanceKlass org/gradle/execution/plan/TaskNodeDependencyResolver
instanceKlass org/gradle/execution/plan/DependencyResolver
instanceKlass org/gradle/api/internal/tasks/WorkDependencyResolver
instanceKlass org/gradle/execution/plan/Node
instanceKlass org/gradle/internal/execution/WorkValidationContext
instanceKlass org/gradle/internal/execution/WorkValidationContext$TypeOriginInspector
instanceKlass org/gradle/initialization/layout/ResolvedBuildLayout
instanceKlass org/gradle/internal/build/BuildIncluder
instanceKlass org/gradle/initialization/SettingsLoader
instanceKlass org/gradle/initialization/DefaultSettingsLoaderFactory
instanceKlass org/gradle/api/internal/project/ProjectFactory
instanceKlass org/gradle/api/internal/project/IProjectFactory
instanceKlass org/gradle/execution/TaskPathProjectEvaluator
instanceKlass org/gradle/api/internal/file/DefaultArchiveOperations
instanceKlass org/gradle/api/file/ArchiveOperations
instanceKlass org/gradle/api/internal/file/DefaultFileSystemOperations
instanceKlass org/gradle/api/file/FileSystemOperations
instanceKlass org/gradle/internal/resource/LocalBinaryResource
instanceKlass org/gradle/internal/resource/ReadableContent
instanceKlass org/gradle/api/resources/internal/ReadableResourceInternal
instanceKlass org/gradle/internal/resource/Resource
instanceKlass org/gradle/api/resources/ReadableResource
instanceKlass org/gradle/api/resources/Resource
instanceKlass org/gradle/api/internal/file/delete/DeleteSpecInternal
instanceKlass org/gradle/api/file/DeleteSpec
instanceKlass org/gradle/api/internal/file/DefaultFileOperations
instanceKlass org/gradle/api/internal/file/FileOperations
instanceKlass org/gradle/process/internal/DefaultExecOperations
instanceKlass org/gradle/process/ExecOperations
instanceKlass org/gradle/internal/service/scopes/BuildScopeServices$$Lambda$48
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/project/ProjectInternal
instanceKlass org/gradle/model/internal/registry/ModelRegistryScope
instanceKlass org/gradle/api/internal/DomainObjectContext
instanceKlass org/gradle/api/internal/file/HasScriptServices
instanceKlass org/gradle/api/internal/project/ProjectIdentifier
instanceKlass org/gradle/api/Project
instanceKlass org/gradle/api/plugins/ExtensionAware
instanceKlass org/gradle/api/internal/initialization/ScriptClassPathInitializer
instanceKlass org/gradle/tooling/provider/model/internal/BuildScopeToolingModelBuilderRegistryAction
instanceKlass org/gradle/configuration/DefaultScriptPluginFactory
instanceKlass org/gradle/initialization/InitScriptHandler
instanceKlass org/gradle/execution/plan/TaskDependencyResolver
instanceKlass org/gradle/execution/plan/TaskNodeFactory
instanceKlass org/gradle/api/provider/ProviderFactory
instanceKlass org/gradle/initialization/Environment
instanceKlass org/gradle/api/internal/properties/GradleProperties
instanceKlass org/gradle/api/internal/project/DefaultProjectRegistry
instanceKlass org/gradle/api/internal/tasks/TaskStatistics
instanceKlass org/gradle/api/internal/resources/DefaultResourceHandler$Factory
instanceKlass org/gradle/api/internal/resources/ApiTextResourceAdapter$Factory
instanceKlass org/gradle/execution/plan/ExecutionPlanFactory
instanceKlass org/gradle/initialization/buildsrc/BuildSourceBuilder
instanceKlass org/gradle/initialization/SettingsLoaderFactory
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementServices
instanceKlass org/gradle/api/internal/plugins/PluginInspector
instanceKlass org/gradle/internal/service/scopes/BuildScopeServiceRegistryFactory
instanceKlass org/gradle/internal/service/scopes/ServiceRegistryFactory
instanceKlass org/gradle/execution/ProjectConfigurer
instanceKlass org/gradle/api/internal/GradleInternal
instanceKlass org/gradle/api/internal/plugins/PluginAwareInternal
instanceKlass org/gradle/api/invocation/Gradle
instanceKlass org/gradle/api/plugins/PluginAware
instanceKlass org/gradle/groovy/scripts/internal/FileCacheBackedScriptClassCompiler
instanceKlass org/gradle/groovy/scripts/internal/DefaultScriptCompilationHandler
instanceKlass org/gradle/execution/plan/ExecutionNodeAccessHierarchies
instanceKlass org/gradle/tooling/provider/model/internal/DefaultToolingModelBuilderRegistry
instanceKlass org/gradle/tooling/provider/model/internal/ToolingModelBuilderLookup
instanceKlass org/gradle/tooling/provider/model/ToolingModelBuilderRegistry
instanceKlass org/gradle/initialization/SettingsProcessor
instanceKlass org/gradle/api/internal/project/taskfactory/ITaskFactory
instanceKlass org/gradle/configuration/ScriptPluginFactory
instanceKlass org/gradle/groovy/scripts/ScriptCompilerFactory
instanceKlass org/gradle/groovy/scripts/internal/ScriptClassCompiler
instanceKlass org/gradle/configuration/InitScriptProcessor
instanceKlass org/gradle/execution/plan/NodeValidator
instanceKlass org/gradle/initialization/BuildLoader
instanceKlass org/gradle/internal/actor/ActorFactory
instanceKlass org/gradle/api/internal/provider/ValueSourceProviderFactory
instanceKlass org/gradle/initialization/IGradlePropertiesLoader
instanceKlass org/gradle/initialization/ProjectDescriptorRegistry
instanceKlass org/gradle/api/internal/project/ProjectRegistry
instanceKlass org/gradle/initialization/GradlePropertiesController
instanceKlass org/gradle/api/internal/project/IsolatedAntBuilder
instanceKlass org/gradle/internal/resource/TextFileResourceLoader
instanceKlass org/gradle/internal/build/PublicBuildPath
instanceKlass org/gradle/cache/scopes/BuildScopedCache
instanceKlass org/gradle/internal/build/BuildWorkPreparer
instanceKlass org/gradle/configuration/ProjectsPreparer
instanceKlass org/gradle/groovy/scripts/internal/ScriptRunnerFactory
instanceKlass org/gradle/api/internal/tasks/userinput/BuildScanUserInputHandler
instanceKlass org/gradle/api/internal/plugins/PluginRegistry
instanceKlass org/gradle/api/internal/initialization/ScriptClassPathResolver
instanceKlass org/gradle/initialization/SettingsPreparer
instanceKlass org/gradle/api/internal/component/ComponentTypeRegistry
instanceKlass org/gradle/api/internal/initialization/ScriptHandlerFactory
instanceKlass org/gradle/api/internal/artifacts/configurations/DependencyMetaDataProvider
instanceKlass org/gradle/api/internal/project/ProjectTaskLister
instanceKlass org/gradle/api/invocation/BuildInvocationDetails
instanceKlass org/gradle/configuration/CompileOperationFactory
instanceKlass org/gradle/execution/TaskSelector
instanceKlass org/gradle/internal/operations/logging/BuildOperationLoggerFactory
instanceKlass org/gradle/groovy/scripts/internal/ScriptCompilationHandler
instanceKlass org/gradle/internal/authentication/AuthenticationSchemeRegistry
instanceKlass org/gradle/configurationcache/DefaultBuildModelControllerServices$servicesForBuild$1
instanceKlass org/gradle/internal/build/BuildModelControllerServices$Supplier
instanceKlass org/gradle/internal/composite/IncludedBuildInternal
instanceKlass org/gradle/api/initialization/IncludedBuild
instanceKlass org/gradle/internal/buildtree/BuildTreeFinishExecutor
instanceKlass org/gradle/internal/buildtree/BuildTreeWorkExecutor
instanceKlass org/gradle/api/artifacts/component/ProjectComponentIdentifier
instanceKlass org/gradle/api/artifacts/component/ComponentIdentifier
instanceKlass org/gradle/internal/build/AbstractBuildState
instanceKlass org/gradle/internal/Actions$NullAction
instanceKlass org/gradle/internal/Actions
instanceKlass org/gradle/plugin/management/internal/PluginRequests$EmptyPluginRequests
instanceKlass org/gradle/plugin/management/internal/PluginRequests
instanceKlass org/gradle/api/internal/BuildDefinition
instanceKlass java/util/TimSort
instanceKlass org/gradle/internal/buildtree/ProblemReportingBuildActionRunner$$Lambda$47
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/launcher/exec/ChainingBuildActionRunner
instanceKlass org/gradle/internal/buildtree/ProblemReportingBuildActionRunner
instanceKlass org/gradle/launcher/exec/BuildOutcomeReportingBuildActionRunner
instanceKlass org/gradle/tooling/internal/provider/FileSystemWatchingBuildActionRunner
instanceKlass org/gradle/launcher/exec/BuildCompletionNotifyingBuildActionRunner
instanceKlass org/gradle/launcher/exec/RootBuildLifecycleBuildActionExecutor
instanceKlass org/gradle/initialization/exception/StackTraceSanitizingExceptionAnalyser
instanceKlass org/gradle/initialization/exception/DefaultExceptionAnalyser
instanceKlass org/gradle/internal/scripts/ScriptExecutionListener
instanceKlass org/gradle/initialization/exception/MultipleBuildFailuresExceptionAnalyser
instanceKlass org/gradle/internal/buildtree/DeprecationsReporter
instanceKlass org/gradle/api/artifacts/component/BuildIdentifier
instanceKlass org/gradle/composite/internal/DefaultIncludedBuildRegistry
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DependencySubstitutionsInternal
instanceKlass org/gradle/api/artifacts/DependencySubstitutions
instanceKlass org/gradle/composite/internal/IncludedBuildDependencySubstitutionsBuilder
instanceKlass org/gradle/internal/typeconversion/CompositeNotationConverter
instanceKlass org/gradle/api/capabilities/Capability
instanceKlass org/gradle/api/internal/artifacts/dsl/CapabilityNotationParserFactory
instanceKlass org/gradle/api/internal/attributes/UsageCompatibilityHandler
instanceKlass java/util/Comparator$$Lambda$46
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass org/gradle/api/internal/attributes/DefaultImmutableAttributes$$Lambda$45
instanceKlass org/gradle/api/attributes/Attribute
instanceKlass org/gradle/api/internal/attributes/DefaultImmutableAttributes
instanceKlass org/gradle/api/internal/attributes/AttributeValue
instanceKlass org/gradle/api/internal/attributes/ImmutableAttributes
instanceKlass org/gradle/api/internal/attributes/AttributeContainerInternal
instanceKlass org/gradle/api/attributes/AttributeContainer
instanceKlass org/gradle/api/attributes/HasAttributes
instanceKlass org/gradle/internal/isolation/Isolatable
instanceKlass org/gradle/internal/hash/Hashable
instanceKlass org/gradle/internal/snapshot/impl/DefaultIsolatableFactory$IsolatableVisitor
instanceKlass org/gradle/internal/snapshot/impl/AbstractValueProcessor$ValueVisitor
instanceKlass org/gradle/internal/snapshot/impl/AbstractValueProcessor
instanceKlass com/google/common/cache/LocalCache$StrongValueReference
instanceKlass org/gradle/api/internal/provider/ManagedFactories$ProviderManagedFactory
instanceKlass org/gradle/api/internal/provider/ManagedFactories$PropertyManagedFactory
instanceKlass org/gradle/api/internal/provider/ManagedFactories$MapPropertyManagedFactory
instanceKlass org/gradle/api/internal/provider/ManagedFactories$ListPropertyManagedFactory
instanceKlass org/gradle/api/internal/provider/AbstractMinimalProvider
instanceKlass org/gradle/api/internal/provider/CollectionPropertyInternal
instanceKlass org/gradle/api/internal/provider/CollectionProviderInternal
instanceKlass org/gradle/api/internal/provider/PropertyInternal
instanceKlass org/gradle/internal/state/OwnerAware
instanceKlass org/gradle/api/internal/provider/HasConfigurableValueInternal
instanceKlass org/gradle/api/internal/provider/ProviderInternal
instanceKlass org/gradle/api/internal/provider/ValueSupplier
instanceKlass org/gradle/api/internal/provider/ManagedFactories$SetPropertyManagedFactory
instanceKlass org/gradle/api/internal/file/ManagedFactories$DirectoryPropertyManagedFactory
instanceKlass org/gradle/api/internal/file/ManagedFactories$DirectoryManagedFactory
instanceKlass org/gradle/api/internal/file/ManagedFactories$RegularFilePropertyManagedFactory
instanceKlass org/gradle/api/internal/file/ManagedFactories$RegularFileManagedFactory
instanceKlass org/gradle/api/internal/file/collections/ManagedFactories$ConfigurableFileCollectionManagedFactory
instanceKlass org/gradle/internal/state/DefaultManagedFactoryRegistry
instanceKlass org/gradle/internal/classloader/ConfigurableClassLoaderHierarchyHasher
instanceKlass java/util/Collections$SynchronizedMap
instanceKlass org/gradle/internal/classloader/DefaultClassLoaderFactory
instanceKlass org/gradle/api/internal/initialization/loadercache/DefaultClasspathHasher
instanceKlass javax/annotation/meta/TypeQualifierDefault
instanceKlass javax/annotation/Nonnull
instanceKlass org/gradle/api/NonNullApi
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileAwareClasspathResourceHasher$$Lambda$44
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileAwareClasspathResourceHasher$$Lambda$43
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/fingerprint/impl/EmptyCurrentFileCollectionFingerprint
instanceKlass org/gradle/api/internal/changedetection/state/ZipHasher$$Lambda$42
instanceKlass org/gradle/api/internal/changedetection/state/ZipHasher$HashingExceptionReporter
instanceKlass org/gradle/internal/snapshot/AbstractFileSystemLocationSnapshot
instanceKlass org/gradle/internal/snapshot/FileSystemLeafSnapshot
instanceKlass org/gradle/internal/fingerprint/hashing/ZipEntryContext
instanceKlass org/gradle/api/internal/file/archive/ZipInput
instanceKlass org/gradle/api/internal/changedetection/state/ZipHasher
instanceKlass org/gradle/api/internal/changedetection/state/IgnoringResourceHasher
instanceKlass org/gradle/api/internal/changedetection/state/MetaInfAwareClasspathResourceHasher
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileAwareClasspathResourceHasher$$Lambda$41
instanceKlass java/util/function/BiConsumer
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileAwareClasspathResourceHasher
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingResourceHasher$1
instanceKlass org/gradle/api/internal/changedetection/state/LineEndingNormalizingResourceHasher
instanceKlass org/gradle/internal/snapshot/RelativePathTrackingFileSystemSnapshotHierarchyVisitor
instanceKlass org/gradle/internal/fingerprint/CurrentFileCollectionFingerprint
instanceKlass org/gradle/internal/fingerprint/FileCollectionFingerprint
instanceKlass org/gradle/internal/fingerprint/impl/AbstractFingerprintingStrategy
instanceKlass org/gradle/api/internal/changedetection/state/RuntimeClasspathResourceHasher
instanceKlass org/gradle/api/internal/changedetection/state/PropertiesFileFilter
instanceKlass org/gradle/api/internal/changedetection/state/ResourceEntryFilter$1
instanceKlass org/gradle/api/internal/changedetection/state/ResourceEntryFilter
instanceKlass org/gradle/api/internal/changedetection/state/ResourceFilter$1
instanceKlass org/gradle/api/internal/changedetection/state/ResourceFilter
instanceKlass org/gradle/internal/fingerprint/FingerprintingStrategy
instanceKlass org/gradle/internal/fingerprint/impl/AbstractFileCollectionFingerprinter
instanceKlass org/gradle/internal/execution/fingerprint/FileCollectionSnapshotter$Result
instanceKlass org/gradle/internal/fingerprint/impl/DefaultFileCollectionSnapshotter
instanceKlass org/gradle/internal/fingerprint/impl/DefaultGenericFileTreeSnapshotter
instanceKlass org/gradle/api/internal/changedetection/state/CachingResourceHasher
instanceKlass org/gradle/internal/fingerprint/hashing/ResourceHasher
instanceKlass org/gradle/internal/fingerprint/hashing/ZipEntryContextHasher
instanceKlass org/gradle/internal/fingerprint/hashing/RegularFileSnapshotContextHasher
instanceKlass org/gradle/internal/fingerprint/hashing/ConfigurableNormalizer
instanceKlass org/gradle/api/internal/changedetection/state/DefaultResourceSnapshotterCacheService
instanceKlass org/gradle/internal/typeconversion/NotationParserBuilder$LazyDisplayName
instanceKlass org/gradle/internal/typeconversion/JustReturningParser
instanceKlass org/gradle/api/artifacts/VersionConstraint
instanceKlass org/gradle/internal/typeconversion/TypedNotationConverter
instanceKlass org/gradle/internal/typeconversion/CrossBuildCachingNotationConverter
instanceKlass org/gradle/api/internal/artifacts/DefaultImmutableModuleIdentifierFactory
instanceKlass org/gradle/composite/internal/DefaultBuildableCompositeBuildContext
instanceKlass org/gradle/tooling/internal/provider/serialization/DefaultPayloadClassLoaderRegistry$DetailsToClassLoaderTransformer
instanceKlass org/gradle/tooling/internal/provider/serialization/DefaultPayloadClassLoaderRegistry$ClassLoaderToDetailsTransformer
instanceKlass org/gradle/tooling/internal/provider/serialization/DefaultPayloadClassLoaderRegistry
instanceKlass org/gradle/tooling/internal/provider/serialization/ClassLoaderDetails
instanceKlass org/gradle/tooling/internal/provider/serialization/DeserializeMap
instanceKlass org/gradle/tooling/internal/provider/serialization/SerializeMap
instanceKlass org/gradle/tooling/internal/provider/serialization/WellKnownClassLoaderRegistry
instanceKlass java/io/ObjectInput
instanceKlass java/io/ObjectStreamConstants
instanceKlass java/io/ObjectOutput
instanceKlass org/gradle/tooling/internal/provider/serialization/ModelClassLoaderFactory
instanceKlass org/gradle/tooling/internal/provider/serialization/DaemonSidePayloadClassLoaderFactory
instanceKlass org/gradle/internal/file/impl/SingleDepthFileAccessTracker
instanceKlass org/gradle/cache/internal/SingleDepthFilesFinder
instanceKlass org/gradle/cache/internal/UnusedVersionsCacheCleanup$1
instanceKlass org/gradle/cache/internal/AbstractCacheCleanup
instanceKlass org/gradle/cache/internal/CompositeCleanupAction$Builder
instanceKlass org/gradle/cache/internal/CompositeCleanupAction
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$3
instanceKlass java/util/function/Predicate$$Lambda$40
instanceKlass java/util/stream/ReduceOps$Box
instanceKlass java/util/stream/ReduceOps$AccumulatingSink
instanceKlass java/util/stream/TerminalSink
instanceKlass java/util/stream/Sink
instanceKlass java/util/function/Consumer
instanceKlass java/util/stream/ReduceOps$ReduceOp
instanceKlass java/util/stream/TerminalOp
instanceKlass java/util/stream/ReduceOps
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$$Lambda$39
instanceKlass java/util/function/BinaryOperator
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$$Lambda$38
instanceKlass java/util/stream/StreamOpFlag$MaskBuilder
instanceKlass java/util/stream/PipelineHelper
instanceKlass java/util/stream/Stream
instanceKlass java/util/stream/BaseStream
instanceKlass java/util/stream/StreamSupport
instanceKlass java/util/ArrayList$ArrayListSpliterator
instanceKlass java/util/Spliterator
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$$Lambda$37
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$$Lambda$36
instanceKlass java/util/function/Predicate$$Lambda$35
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$EndMatcher
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes$StartMatcher
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$DefaultExcludes
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$1
instanceKlass java/nio/file/FileVisitor
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter$SymbolicLinkMapping
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotter
instanceKlass com/google/common/util/concurrent/Striped$1
instanceKlass com/google/common/util/concurrent/Striped$6
instanceKlass java/util/concurrent/locks/ReadWriteLock
instanceKlass com/google/common/util/concurrent/Striped$5
instanceKlass com/google/common/util/concurrent/Striped
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess$StripedProducerGuard
instanceKlass java/nio/file/attribute/PosixFilePermissions$1
instanceKlass java/nio/file/attribute/PosixFilePermissions
instanceKlass org/apache/tools/ant/util/FileUtils
instanceKlass org/apache/tools/ant/taskdefs/condition/Os
instanceKlass org/apache/tools/ant/taskdefs/condition/Condition
instanceKlass org/apache/tools/ant/types/resources/Appendable
instanceKlass org/apache/tools/ant/types/resources/FileProvider
instanceKlass org/apache/tools/ant/types/resources/Touchable
instanceKlass org/apache/tools/ant/ProjectComponent
instanceKlass org/apache/tools/ant/types/ResourceCollection
instanceKlass org/apache/tools/ant/DirectoryScanner
instanceKlass org/apache/tools/ant/types/ResourceFactory
instanceKlass org/apache/tools/ant/types/selectors/SelectorScanner
instanceKlass org/apache/tools/ant/FileScanner
instanceKlass org/gradle/internal/snapshot/FileSystemLocationSnapshot
instanceKlass org/gradle/internal/snapshot/FileSystemSnapshot
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotterStatistics
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$$Lambda$34
instanceKlass org/gradle/internal/build/BuildAddedListener
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$$Lambda$33
instanceKlass org/gradle/internal/watch/registry/impl/DaemonDocumentationIndex
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistry$ChangeHandler
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy$NodeDiffListener
instanceKlass org/gradle/internal/snapshot/MetadataSnapshot
instanceKlass org/gradle/internal/vfs/impl/AbstractVirtualFileSystem
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$$Lambda$32
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions$AbstractWatcherBuilder
instanceKlass org/gradle/internal/watch/registry/FileWatcherUpdater
instanceKlass net/rubygrapefruit/platform/file/FileWatcher
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistry
instanceKlass org/gradle/internal/watch/registry/FileWatcherProbeRegistry
instanceKlass org/gradle/internal/watch/registry/impl/AbstractFileWatcherRegistryFactory
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$$Lambda$31
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/snapshot/FileSystemNode
instanceKlass org/gradle/internal/snapshot/ChildMap
instanceKlass org/gradle/internal/vfs/impl/DefaultSnapshotHierarchy$1
instanceKlass org/gradle/internal/snapshot/ReadOnlyFileSystemNode
instanceKlass org/gradle/internal/vfs/impl/DefaultSnapshotHierarchy
instanceKlass org/gradle/internal/snapshot/SnapshotHierarchy
instanceKlass org/apache/commons/io/filefilter/IOFileFilter
instanceKlass java/io/FilenameFilter
instanceKlass org/apache/commons/io/FileUtils
instanceKlass com/google/common/io/CharSource
instanceKlass com/google/common/hash/PrimitiveSink
instanceKlass com/google/common/io/Closer$SuppressingSuppressor
instanceKlass com/google/common/io/Closer$Suppressor
instanceKlass com/google/common/io/Closer
instanceKlass com/google/common/io/CharSink
instanceKlass java/io/File$TempDirectory
instanceKlass org/gradle/api/internal/file/temp/TempFiles
instanceKlass org/gradle/internal/watch/vfs/impl/DefaultWatchableFileSystemDetector
instanceKlass net/rubygrapefruit/platform/internal/PosixFileSystems
instanceKlass org/gradle/internal/file/FilePathUtil
instanceKlass org/gradle/internal/file/FileHierarchySet$Node
instanceKlass org/gradle/internal/file/FileHierarchySet$NodeVisitor
instanceKlass org/gradle/cache/internal/DefaultGlobalCacheLocations
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$3
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices$1
instanceKlass org/gradle/internal/file/FileHierarchySet
instanceKlass org/gradle/internal/hash/DefaultFileHasher
instanceKlass org/gradle/api/internal/changedetection/state/CachingFileHasher
instanceKlass com/google/common/collect/MapMakerInternalMap$WeakKeyDummyValueEntry$Helper
instanceKlass com/google/common/collect/MapMakerInternalMap$InternalEntry
instanceKlass com/google/common/collect/MapMakerInternalMap$1
instanceKlass com/google/common/collect/MapMakerInternalMap$InternalEntryHelper
instanceKlass com/google/common/collect/MapMakerInternalMap$WeakValueReference
instanceKlass com/google/common/collect/Interners$InternerImpl
instanceKlass com/google/common/collect/MapMaker
instanceKlass com/google/common/collect/Interners$InternerBuilder
instanceKlass com/google/common/collect/Interners
instanceKlass org/gradle/internal/hash/HashCode
instanceKlass com/google/common/base/Charsets
instanceKlass org/gradle/internal/hash/Hashing$MessageDigestHasher
instanceKlass org/gradle/internal/hash/Hashing$DefaultHasher
instanceKlass org/gradle/internal/hash/PrimitiveHasher
instanceKlass org/gradle/internal/hash/Hasher
instanceKlass org/gradle/internal/hash/Hashing$MessageDigestHashFunction
instanceKlass org/gradle/internal/hash/HashFunction
instanceKlass org/gradle/internal/hash/Hashing
instanceKlass org/gradle/internal/hash/DefaultStreamHasher
instanceKlass org/gradle/api/internal/changedetection/state/FileTimeStampInspector$$Lambda$30
instanceKlass org/gradle/api/internal/changedetection/state/FileHasherStatistics
instanceKlass org/apache/commons/lang/StringUtils
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches$$Lambda$29
instanceKlass org/gradle/api/internal/artifacts/ivyservice/WritableArtifactCacheLockingManager
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches$LateInitWritableArtifactCacheLockingManager
instanceKlass com/google/common/primitives/IntsMethodsForWeb
instanceKlass org/apache/commons/lang/ArrayUtils
instanceKlass org/gradle/cache/internal/CacheVersion
instanceKlass org/gradle/util/internal/DefaultGradleVersion$Stage
instanceKlass org/gradle/cache/internal/CacheVersionMapping$Builder
instanceKlass org/gradle/cache/internal/CacheVersionMapping$1
instanceKlass org/gradle/cache/internal/CacheVersionMapping
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCacheMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCacheLockingManager
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCacheMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGradleUserHomeScopeServices$1
instanceKlass org/gradle/api/internal/changedetection/state/DefaultFileAccessTimeJournal$$Lambda$28
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$IndexedCacheEntry
instanceKlass org/gradle/cache/internal/locklistener/FileLockPacketPayload
instanceKlass org/gradle/cache/internal/locklistener/DefaultFileLockContentionHandler$ContendedAction
instanceKlass java/net/DatagramPacket$1
instanceKlass org/gradle/cache/internal/locklistener/DefaultFileLockContentionHandler$1
instanceKlass org/gradle/internal/Factories$1
instanceKlass org/gradle/internal/Factories
instanceKlass org/gradle/cache/internal/CrossProcessSynchronizingCache
instanceKlass org/gradle/cache/internal/InMemoryDecoratedCache
instanceKlass org/gradle/cache/internal/InMemoryCacheController
instanceKlass com/google/common/cache/LongAddables$1
instanceKlass com/google/common/cache/Striped64$Cell
instanceKlass com/google/common/cache/Striped64$1
instanceKlass com/google/common/cache/LongAddable
instanceKlass com/google/common/cache/LongAddables
instanceKlass com/google/common/cache/AbstractCache$SimpleStatsCounter
instanceKlass org/gradle/cache/internal/LoggingEvictionListener
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/cache/internal/DefaultInMemoryCacheDecoratorFactory$$Lambda$27
instanceKlass org/gradle/cache/internal/DefaultInMemoryCacheDecoratorFactory$CacheDetails
instanceKlass org/gradle/cache/internal/AsyncCacheAccessDecoratedCache
instanceKlass org/gradle/cache/internal/CacheAccessWorker
instanceKlass org/gradle/cache/internal/DefaultMultiProcessSafePersistentIndexedCache
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$$Lambda$26
instanceKlass org/gradle/cache/internal/btree/BTreePersistentIndexedCache
instanceKlass org/gradle/cache/internal/DefaultInMemoryCacheDecoratorFactory$InMemoryCacheDecorator
instanceKlass org/gradle/cache/PersistentIndexedCacheParameters
instanceKlass org/gradle/api/internal/changedetection/state/DefaultFileAccessTimeJournal
instanceKlass org/gradle/cache/internal/MultiProcessSafeAsyncPersistentIndexedCache
instanceKlass org/gradle/cache/CacheDecorator
instanceKlass org/gradle/cache/internal/DefaultInMemoryCacheDecoratorFactory
instanceKlass org/gradle/cache/internal/DefaultCacheFactory$ReferenceTrackingCache
instanceKlass org/gradle/cache/internal/DefaultCacheFactory$DirCacheReference
instanceKlass org/gradle/cache/internal/cacheops/CacheOperationStack
instanceKlass org/gradle/cache/internal/LockOnDemandCrossProcessCacheAccess$ContendedAction
instanceKlass org/gradle/cache/internal/LockOnDemandCrossProcessCacheAccess$UnlockAction
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$1
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$$Lambda$25
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$$Lambda$24
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/cache/internal/cacheops/CacheAccessOperationsStack
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryStore$Cleanup
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryStore$1
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryStore$2
instanceKlass org/gradle/cache/internal/DefaultCacheAccess$$Lambda$23
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/cache/AsyncCacheAccess
instanceKlass org/gradle/cache/MultiProcessSafePersistentIndexedCache
instanceKlass org/gradle/cache/UnitOfWorkParticipant
instanceKlass org/gradle/cache/PersistentIndexedCache
instanceKlass org/gradle/cache/internal/AbstractCrossProcessCacheAccess
instanceKlass org/gradle/cache/CrossProcessCacheAccess
instanceKlass org/gradle/cache/internal/DefaultCacheAccess
instanceKlass org/gradle/cache/internal/CacheCoordinator
instanceKlass org/gradle/cache/internal/CacheInitializationAction
instanceKlass org/gradle/cache/internal/CacheCleanupAction
instanceKlass org/gradle/cache/internal/DefaultPersistentDirectoryStore
instanceKlass org/gradle/cache/internal/DefaultCacheRepository$PersistentCacheBuilder
instanceKlass org/gradle/cache/internal/scopes/DefaultCacheScopeMapping$1
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$Progress
instanceKlass org/gradle/internal/operations/OperationProgressEvent
instanceKlass org/gradle/initialization/BuildOptionBuildOperationProgressEventsEmitter$1
instanceKlass org/gradle/internal/configurationcache/options/ConfigurationCacheSettingsFinalizedProgressDetails
instanceKlass org/gradle/launcher/exec/BuildTreeLifecycleBuildActionExecutor$$Lambda$22
instanceKlass org/gradle/internal/buildtree/DefaultBuildTreeContext
instanceKlass org/gradle/internal/buildtree/BuildTreeContext
instanceKlass org/gradle/configurationcache/VintageBuildTreeLifecycleControllerFactory
instanceKlass org/gradle/internal/buildtree/BuildTreeLifecycleControllerFactory
instanceKlass org/gradle/configurationcache/initialization/AbstractInjectedClasspathInstrumentationStrategy
instanceKlass org/gradle/plugin/use/resolve/service/internal/InjectedClasspathInstrumentationStrategy
instanceKlass org/gradle/internal/buildtree/BuildInclusionCoordinator
instanceKlass org/gradle/initialization/BuildOptionBuildOperationProgressEventsEmitter
instanceKlass org/gradle/internal/buildtree/BuildTreeLifecycleListener
instanceKlass org/gradle/internal/build/BuildLifecycleController
instanceKlass org/gradle/internal/build/DefaultBuildLifecycleControllerFactory
instanceKlass org/gradle/internal/build/BuildLifecycleControllerFactory
instanceKlass org/gradle/tooling/internal/provider/runner/AbstractClientProvidedBuildActionRunner$ClientAction
instanceKlass org/gradle/tooling/internal/provider/runner/AbstractClientProvidedBuildActionRunner
instanceKlass org/gradle/tooling/internal/provider/runner/TestExecutionRequestActionRunner
instanceKlass org/gradle/internal/buildtree/BuildTreeModelAction
instanceKlass org/gradle/tooling/internal/provider/runner/BuildModelActionRunner
instanceKlass org/gradle/tooling/internal/provider/runner/BuildControllerFactory
instanceKlass kotlin/coroutines/Continuation
instanceKlass org/gradle/configurationcache/fingerprint/ConfigurationCacheFingerprintController
instanceKlass org/gradle/configurationcache/InstrumentedInputAccessListener
instanceKlass org/gradle/internal/classpath/Instrumented$Listener
instanceKlass org/gradle/configurationcache/ConfigurationCacheRepository
instanceKlass org/gradle/configurationcache/DefaultBuildToolingModelControllerFactory
instanceKlass org/gradle/internal/build/BuildToolingModelControllerFactory
instanceKlass org/gradle/configurationcache/DefaultBuildModelControllerServices
instanceKlass org/gradle/internal/build/BuildModelControllerServices
instanceKlass org/gradle/configurationcache/problems/ProblemsListener
instanceKlass org/gradle/configurationcache/initialization/DefaultConfigurationCacheProblemsListener
instanceKlass org/gradle/configurationcache/initialization/ConfigurationCacheProblemsListener
instanceKlass org/gradle/api/internal/BuildScopeListenerRegistrationListener
instanceKlass org/gradle/api/internal/tasks/execution/TaskExecutionAccessListener
instanceKlass org/gradle/configurationcache/problems/ConfigurationCacheReport
instanceKlass org/gradle/configurationcache/initialization/ConfigurationCacheStartParameter
instanceKlass org/gradle/configurationcache/ConfigurationCacheKey
instanceKlass org/gradle/vcs/internal/VcsResolver
instanceKlass org/gradle/vcs/internal/resolver/VcsVersionSelectionCache
instanceKlass org/gradle/vcs/internal/VersionControlSpecFactory
instanceKlass org/gradle/vcs/internal/VcsMappingFactory
instanceKlass org/gradle/vcs/internal/VcsMappingsStore
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VersionControlBuildTreeServices
instanceKlass org/gradle/plugins/ide/internal/IdeArtifactStore
instanceKlass org/gradle/internal/enterprise/impl/legacy/DefaultBuildScanBuildStartedTime
instanceKlass org/gradle/internal/scan/time/BuildScanBuildStartedTime
instanceKlass org/gradle/internal/enterprise/impl/legacy/DefaultBuildScanClock
instanceKlass org/gradle/internal/scan/time/BuildScanClock
instanceKlass org/gradle/internal/enterprise/impl/DefaultGradleEnterprisePluginRequiredServices
instanceKlass org/gradle/internal/enterprise/GradleEnterprisePluginRequiredServices
instanceKlass org/gradle/composite/internal/DefaultIncludedBuildTaskGraph
instanceKlass org/gradle/composite/internal/BuildTreeWorkGraphController
instanceKlass org/gradle/internal/build/IncludedBuildState
instanceKlass org/gradle/composite/internal/DefaultIncludedBuildFactory
instanceKlass org/gradle/internal/buildtree/NestedBuildTree
instanceKlass org/gradle/internal/build/RootBuildState
instanceKlass org/gradle/internal/build/CompositeBuildParticipantBuildState
instanceKlass org/gradle/internal/build/StandAloneNestedBuild
instanceKlass org/gradle/internal/build/BuildActionTarget
instanceKlass org/gradle/internal/build/NestedBuildState
instanceKlass org/gradle/internal/build/IncludedBuildFactory
instanceKlass org/gradle/composite/internal/BuildStateFactory
instanceKlass org/gradle/composite/internal/DefaultLocalComponentInAnotherBuildProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/LocalComponentInAnotherBuildProvider
instanceKlass org/gradle/api/internal/composite/CompositeBuildContext
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DependencySubstitutionRules
instanceKlass org/gradle/composite/internal/CompositeBuildServices$CompositeBuildTreeScopeServices
instanceKlass org/gradle/api/internal/tasks/compile/processing/AnnotationProcessorDetector
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$1
instanceKlass org/gradle/util/internal/BuildCommencedTimeProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/ResolutionResultsStoreFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/StartParameterResolutionOverride
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildTreeScopeServices
instanceKlass org/gradle/caching/internal/controller/RootBuildCacheControllerRef
instanceKlass org/gradle/caching/internal/BuildCacheServices$1
instanceKlass org/gradle/internal/enterprise/core/GradleEnterprisePluginManager
instanceKlass org/gradle/internal/build/BuildStateRegistry
instanceKlass org/gradle/internal/buildtree/BuildTreeActionExecutor
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingBuildTreeScopeServices
instanceKlass org/gradle/api/internal/project/DefaultProjectStateRegistry
instanceKlass org/gradle/api/internal/project/ProjectStateRegistry
instanceKlass org/gradle/problems/buildtree/ProblemReporter
instanceKlass org/gradle/api/internal/provider/ConfigurationTimeBarrier
instanceKlass org/gradle/internal/buildtree/BuildTreeScopeServices
instanceKlass org/gradle/internal/buildtree/BuildTreeState
instanceKlass org/gradle/configurationcache/DefaultBuildTreeModelControllerServices$servicesForBuildTree$1
instanceKlass org/gradle/internal/buildtree/BuildTreeModelControllerServices$Supplier
instanceKlass org/gradle/internal/buildtree/BuildModelParameters
instanceKlass kotlin/text/StringsKt__AppendableKt
instanceKlass org/gradle/internal/buildtree/RunTasksRequirements
instanceKlass org/gradle/initialization/layout/BuildLayoutConfiguration
instanceKlass org/gradle/internal/logging/sink/ProgressLogEventGenerator$Operation
instanceKlass org/gradle/internal/logging/progress/DefaultProgressLoggerFactory$ProgressLoggerImpl
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$Started
instanceKlass org/gradle/internal/operations/OperationStartEvent
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$DefaultBuildOperationContext
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$ListenerAdapter
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationTrackingListener
instanceKlass org/gradle/internal/operations/BuildOperationState
instanceKlass org/gradle/internal/operations/OperationIdentifier
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$2
instanceKlass org/gradle/internal/operations/BuildOperationMetadata$1
instanceKlass org/gradle/internal/operations/BuildOperationDescriptor$Builder
instanceKlass org/gradle/internal/operations/BuildOperationDescriptor
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$CallableBuildOperationWorker
instanceKlass org/gradle/launcher/exec/RunAsBuildOperationBuildActionExecutor$3
instanceKlass org/gradle/internal/operations/notify/BuildOperationStartedNotification
instanceKlass org/gradle/internal/operations/notify/BuildOperationProgressNotification
instanceKlass org/gradle/internal/operations/notify/BuildOperationFinishedNotification
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$Adapter
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$RecordingListener
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$ReplayAndAttachListener
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationListener
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$State
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService$AcquireLocks
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService$2
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService$DefaultResourceLockState
instanceKlass org/gradle/internal/resources/ResourceLockState
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$3
instanceKlass com/google/common/collect/Iterables
instanceKlass com/google/common/util/concurrent/AbstractFuture$Failure
instanceKlass com/google/common/util/concurrent/AbstractFuture$Cancellation
instanceKlass com/google/common/util/concurrent/AbstractFuture$SetFuture
instanceKlass com/google/common/util/concurrent/Uninterruptibles
instanceKlass com/google/common/base/CommonPattern
instanceKlass com/google/common/base/Platform$JdkPatternCompiler
instanceKlass com/google/common/base/PatternCompiler
instanceKlass com/google/common/base/Platform
instanceKlass com/google/common/base/Stopwatch
instanceKlass com/google/common/util/concurrent/AbstractFuture$Waiter
instanceKlass com/google/common/util/concurrent/AbstractFuture$Listener
instanceKlass com/google/common/util/concurrent/AbstractFuture$UnsafeAtomicHelper$1
instanceKlass com/google/common/util/concurrent/AbstractFuture$AtomicHelper
instanceKlass com/google/common/util/concurrent/internal/InternalFutureFailureAccess
instanceKlass com/google/common/util/concurrent/AbstractFuture$Trusted
instanceKlass com/google/common/util/concurrent/ListenableFuture
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry$1
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$WorkerLeaseLockRegistry$1
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry$ThreadLockDetails
instanceKlass org/gradle/launcher/exec/RunAsWorkerThreadBuildActionExecutor$$Lambda$21
instanceKlass org/gradle/internal/buildtree/BuildActionRunner$Result
instanceKlass org/gradle/internal/buildtree/BuildActionModelRequirements
instanceKlass org/gradle/launcher/exec/BuildTreeLifecycleBuildActionExecutor
instanceKlass org/gradle/launcher/exec/RunAsBuildOperationBuildActionExecutor$2
instanceKlass org/gradle/launcher/exec/RunAsBuildOperationBuildActionExecutor$1
instanceKlass org/gradle/launcher/exec/RunBuildBuildOperationType$Result
instanceKlass org/gradle/launcher/exec/RunBuildBuildOperationType$Details
instanceKlass org/gradle/launcher/exec/RunAsBuildOperationBuildActionExecutor
instanceKlass org/gradle/launcher/exec/RunAsWorkerThreadBuildActionExecutor
instanceKlass org/gradle/internal/filewatch/FileWatcherEventListener
instanceKlass org/gradle/execution/CancellableOperationManager
instanceKlass org/gradle/tooling/internal/provider/ContinuousBuildActionExecutor
instanceKlass org/gradle/tooling/internal/provider/SubscribableBuildActionExecutor
instanceKlass org/gradle/internal/scripts/ScriptingLanguages$1
instanceKlass org/gradle/scripts/ScriptingLanguage
instanceKlass org/gradle/internal/scripts/ScriptingLanguages
instanceKlass org/gradle/BuildAdapter
instanceKlass org/gradle/internal/InternalBuildListener
instanceKlass org/gradle/internal/InternalListener
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge$1
instanceKlass org/gradle/BuildListener
instanceKlass org/gradle/deployment/internal/DefaultDeploymentRegistry$PendingChanges
instanceKlass org/gradle/initialization/ContinuousExecutionGate$GateKeeper
instanceKlass org/gradle/initialization/DefaultContinuousExecutionGate
instanceKlass org/gradle/internal/operations/CallableBuildOperation
instanceKlass org/gradle/initialization/ContinuousExecutionGate
instanceKlass org/gradle/api/file/SourceDirectorySet
instanceKlass org/gradle/api/internal/model/DefaultObjectFactory
instanceKlass org/gradle/api/internal/model/NamedObjectInstantiator$$Lambda$20
instanceKlass org/gradle/internal/state/Managed
instanceKlass com/google/common/base/ExtraObjectsMethodsForWeb
instanceKlass org/gradle/model/internal/inspect/ValidationProblemCollector
instanceKlass org/gradle/api/internal/MutationGuards$1
instanceKlass org/gradle/api/internal/MutationGuard
instanceKlass org/gradle/api/internal/MutationGuards
instanceKlass org/gradle/api/internal/CollectionCallbackActionDecorator$1
instanceKlass org/gradle/api/internal/collections/DefaultDomainObjectCollectionFactory
instanceKlass org/gradle/api/file/Directory
instanceKlass org/gradle/api/file/RegularFile
instanceKlass org/gradle/api/file/FileSystemLocation
instanceKlass org/gradle/api/internal/tasks/DefaultTaskDependencyFactory
instanceKlass org/gradle/api/internal/file/FileCollectionInternal$1
instanceKlass org/gradle/api/file/FileVisitor
instanceKlass org/gradle/api/internal/file/FileCollectionStructureVisitor
instanceKlass org/gradle/api/tasks/TaskDependency
instanceKlass org/gradle/api/internal/file/FileCollectionInternal$Source
instanceKlass org/gradle/api/internal/file/AbstractFileCollection
instanceKlass org/gradle/api/internal/file/collections/MinimalFileTree
instanceKlass org/gradle/api/internal/file/collections/MinimalFileCollection
instanceKlass org/gradle/api/internal/file/FileTreeInternal
instanceKlass org/gradle/api/internal/file/DefaultFileCollectionFactory
instanceKlass org/gradle/internal/exceptions/DiagnosticsVisitor
instanceKlass org/gradle/internal/typeconversion/ErrorHandlingNotationParser
instanceKlass org/gradle/internal/typeconversion/NotationConvertResult
instanceKlass org/gradle/internal/typeconversion/NotationConverterToNotationParserAdapter
instanceKlass org/gradle/internal/typeconversion/TypeInfo
instanceKlass org/gradle/internal/typeconversion/NotationParserBuilder
instanceKlass org/gradle/api/internal/file/FileOrUriNotationConverter
instanceKlass org/gradle/api/internal/file/AbstractFileResolver
instanceKlass org/gradle/api/internal/provider/DefaultPropertyFactory
instanceKlass org/gradle/api/internal/provider/PropertyHost$$Lambda$19
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/state/ModelObject
instanceKlass org/gradle/api/internal/file/collections/DefaultDirectoryFileTreeFactory
instanceKlass org/gradle/api/tasks/util/internal/PatternSets$PatternSetFactory
instanceKlass org/gradle/api/tasks/util/internal/PatternSets
instanceKlass com/google/common/cache/LocalCache$AbstractReferenceEntry
instanceKlass org/gradle/cache/internal/HeapProportionalCacheSizer
instanceKlass java/util/EnumMap$1
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiationScheme$DefaultDeserializationInstantiator
instanceKlass org/gradle/internal/instantiation/InstanceFactory
instanceKlass org/gradle/internal/instantiation/generator/DependencyInjectingInstantiator
instanceKlass javax/inject/Inject
instanceKlass org/gradle/internal/instantiation/DeserializationInstantiator
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiationScheme
instanceKlass org/gradle/internal/instantiation/generator/ParamsMatchingConstructorSelector
instanceKlass org/gradle/internal/instantiation/generator/Jsr330ConstructorSelector
instanceKlass com/google/common/collect/Maps$EntryTransformer
instanceKlass com/google/common/base/Converter
instanceKlass com/google/common/collect/SortedMapDifference
instanceKlass com/google/common/collect/MapDifference
instanceKlass com/google/common/collect/Maps
instanceKlass com/google/common/collect/ImmutableMultimap$Builder
instanceKlass com/google/common/collect/Multiset
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$$Lambda$18
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator$GeneratedClass
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory$AbstractCrossBuildInMemoryCache
instanceKlass org/gradle/model/internal/asm/ClassGeneratorSuffixRegistry
instanceKlass org/gradle/api/DomainObjectSet
instanceKlass org/gradle/api/NamedDomainObjectContainer
instanceKlass org/gradle/util/Configurable
instanceKlass org/gradle/api/NamedDomainObjectSet
instanceKlass org/gradle/api/NamedDomainObjectCollection
instanceKlass org/gradle/api/DomainObjectCollection
instanceKlass org/gradle/api/file/DirectoryProperty
instanceKlass org/gradle/api/file/RegularFileProperty
instanceKlass org/gradle/api/file/FileSystemLocationProperty
instanceKlass org/gradle/api/provider/Property
instanceKlass org/gradle/api/provider/MapProperty
instanceKlass org/gradle/api/provider/SetProperty
instanceKlass org/gradle/api/provider/ListProperty
instanceKlass org/gradle/api/provider/HasMultipleValues
instanceKlass org/gradle/api/provider/Provider
instanceKlass org/gradle/api/file/ConfigurableFileTree
instanceKlass org/gradle/api/file/DirectoryTree
instanceKlass org/gradle/api/file/FileTree
instanceKlass org/gradle/api/file/ConfigurableFileCollection
instanceKlass org/gradle/api/provider/HasConfigurableValue
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$InstantiationStrategy
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$ClassInspectionVisitor
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$UnclaimedPropertyHandler
instanceKlass com/google/common/reflect/TypeCapture
instanceKlass com/google/common/collect/AbstractMultimap
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator
instanceKlass org/gradle/internal/instantiation/generator/ClassGenerator
instanceKlass org/gradle/api/internal/tasks/properties/annotations/OutputPropertyRoleAnnotationHandler
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory$ClassGeneratorBackedManagedFactory
instanceKlass org/gradle/internal/instantiation/InstantiationScheme
instanceKlass org/gradle/internal/instantiation/generator/ConstructorSelector
instanceKlass org/gradle/internal/instantiation/generator/DefaultInstantiatorFactory
instanceKlass org/gradle/cache/internal/CrossBuildInMemoryCache
instanceKlass org/gradle/cache/internal/DefaultCrossBuildInMemoryCacheFactory
instanceKlass org/gradle/internal/filewatch/FileSystemChangeWaiter
instanceKlass org/gradle/internal/filewatch/DefaultFileSystemChangeWaiterFactory
instanceKlass org/gradle/internal/filewatch/DefaultFileWatcherFactory
instanceKlass org/gradle/api/execution/internal/TaskInputsListener
instanceKlass org/gradle/api/execution/internal/DefaultTaskInputsListeners
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$$Lambda$17
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListenerFactory
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$$Lambda$16
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$TimeSupplier
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext
instanceKlass org/gradle/internal/operations/BuildOperationContext
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution
instanceKlass org/gradle/internal/operations/BuildOperation
instanceKlass org/gradle/internal/operations/BuildOperationWorker
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner
instanceKlass org/gradle/internal/operations/BuildOperationQueue
instanceKlass org/gradle/internal/operations/DefaultBuildOperationQueueFactory
instanceKlass org/gradle/internal/operations/BuildOperationQueue$QueueWorker
instanceKlass org/gradle/internal/operations/BuildOperationRef
instanceKlass org/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor
instanceKlass org/gradle/internal/resources/AbstractTrackedResourceLock
instanceKlass java/util/concurrent/atomic/AtomicReferenceArray
instanceKlass com/google/common/cache/LocalCache$LoadingValueReference
instanceKlass com/google/common/cache/RemovalListener
instanceKlass com/google/common/cache/Weigher
instanceKlass com/google/common/base/Equivalence
instanceKlass java/util/function/BiPredicate
instanceKlass com/google/common/base/MoreObjects
instanceKlass com/google/common/cache/LocalCache$1
instanceKlass com/google/common/cache/ReferenceEntry
instanceKlass com/google/common/cache/CacheLoader
instanceKlass com/google/common/cache/LocalCache$LocalManualCache
instanceKlass com/google/common/cache/LocalCache$ValueReference
instanceKlass com/google/common/cache/CacheBuilder$2
instanceKlass com/google/common/cache/CacheStats
instanceKlass com/google/common/base/Suppliers$SupplierOfInstance
instanceKlass com/google/common/base/Suppliers
instanceKlass com/google/common/cache/CacheBuilder$1
instanceKlass com/google/common/cache/AbstractCache$StatsCounter
instanceKlass com/google/common/cache/LoadingCache
instanceKlass com/google/common/cache/Cache
instanceKlass com/google/common/base/Ticker
instanceKlass com/google/common/base/Supplier
instanceKlass com/google/common/cache/CacheBuilder
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry$ResourceLockProducer
instanceKlass org/gradle/internal/resources/AbstractResourceLockRegistry
instanceKlass org/gradle/internal/resources/ResourceLockContainer
instanceKlass org/gradle/internal/resources/ResourceLockRegistry
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$ProjectLockStatisticsImpl
instanceKlass org/gradle/internal/resources/ProjectLockStatistics
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$Root
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService
instanceKlass org/gradle/tooling/internal/provider/runner/ToolingApiBuildEventListenerFactory$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager$ProgressShieldingBuildOperationListener
instanceKlass org/gradle/internal/operations/DefaultBuildOperationAncestryTracker
instanceKlass org/gradle/internal/reflect/AnnotationCategory
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$JavaGlobalScopeServices$$Lambda$15
instanceKlass sun/invoke/util/VerifyAccess$1
instanceKlass org/gradle/internal/session/BuildSessionLifecycleListener
instanceKlass org/gradle/internal/session/DefaultBuildSessionContext
instanceKlass org/gradle/internal/session/BuildSessionContext
instanceKlass org/gradle/internal/scopeids/PersistentScopeIdStoreFactory
instanceKlass org/gradle/internal/scopeids/ScopeIdsServices
instanceKlass com/google/common/collect/ListMultimap
instanceKlass org/gradle/internal/work/DefaultAsyncWorkTracker
instanceKlass org/gradle/internal/work/AsyncWorkTracker
instanceKlass org/gradle/internal/exceptions/FailureResolutionAware
instanceKlass org/gradle/internal/model/StateTransitionControllerFactory
instanceKlass org/gradle/internal/model/ValueCalculator
instanceKlass org/gradle/internal/model/CalculatedValueContainerFactory
instanceKlass org/gradle/plugin/use/internal/InjectedPluginClasspath
instanceKlass org/gradle/configurationcache/DefaultBuildTreeModelControllerServices
instanceKlass org/gradle/api/artifacts/ModuleIdentifier
instanceKlass org/gradle/vcs/internal/VcsDirectoryLayout
instanceKlass org/gradle/vcs/internal/resolver/PersistentVcsMetadataCache
instanceKlass org/gradle/vcs/internal/VersionControlRepositoryConnectionFactory
instanceKlass org/gradle/vcs/internal/services/VersionControlServices$VersionControlBuildSessionServices
instanceKlass org/gradle/nativeplatform/toolchain/internal/gcc/metadata/SystemLibraryDiscovery
instanceKlass org/gradle/nativeplatform/toolchain/internal/xcode/AbstractLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/WindowsKitInstall
instanceKlass org/gradle/platform/base/internal/toolchain/SearchResult
instanceKlass org/gradle/platform/base/internal/toolchain/ToolSearchResult
instanceKlass com/google/common/collect/SetMultimap
instanceKlass com/google/common/collect/Multimap
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/AbstractWindowsKitComponentLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/UcrtLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/SystemPathVersionLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/AbstractVisualStudioVersionLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/VisualStudioMetaDataProvider
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/VswhereVersionLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/WindowsSdkLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/VisualCppMetadataProvider
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/VisualStudioLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/WindowsComponentLocator
instanceKlass org/gradle/nativeplatform/toolchain/internal/msvcpp/version/VisualStudioVersionLocator
instanceKlass org/gradle/nativeplatform/internal/services/NativeBinaryServices$BuildSessionScopeServices
instanceKlass org/gradle/composite/internal/CompositeBuildServices$CompositeBuildSessionScopeServices
instanceKlass org/gradle/api/tasks/testing/TestDescriptor
instanceKlass org/gradle/api/internal/tasks/testing/operations/TestListenerBuildOperationAdapter
instanceKlass org/gradle/api/internal/tasks/testing/results/TestListenerInternal
instanceKlass org/gradle/api/internal/tasks/testing/operations/TestExecutionBuildOperationBuildSessionScopeServices
instanceKlass org/gradle/api/internal/catalog/DependenciesAccessorsWorkspaceProvider
instanceKlass org/gradle/internal/execution/workspace/WorkspaceProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementBuildSessionScopeServices
instanceKlass org/gradle/api/internal/file/FileCollectionInternal
instanceKlass org/gradle/api/internal/tasks/TaskDependencyContainer
instanceKlass org/gradle/api/file/FileCollection
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactSetToFileCollectionFactory
instanceKlass org/gradle/workers/internal/WorkerExecutionQueueFactory
instanceKlass org/gradle/internal/work/ConditionalExecutionQueueFactory
instanceKlass org/gradle/process/internal/worker/child/WorkerDirectoryProvider
instanceKlass org/gradle/workers/internal/WorkersServices$BuildSessionScopeServices
instanceKlass org/gradle/internal/fingerprint/impl/FileCollectionFingerprinterRegistrations
instanceKlass org/gradle/internal/vfs/impl/DefaultFileSystemAccess
instanceKlass org/gradle/internal/execution/fingerprint/FileCollectionFingerprinterRegistry
instanceKlass org/gradle/internal/execution/fingerprint/InputFingerprinter
instanceKlass org/gradle/internal/execution/OutputSnapshotter
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$BuildSessionServices
instanceKlass org/gradle/internal/build/BuildLayoutValidator
instanceKlass org/gradle/internal/buildtree/BuildTreeModelControllerServices
instanceKlass org/gradle/internal/session/BuildSessionActionExecutor
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingBuildSessionScopeServices
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CollectionService
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CollectingVisitor
instanceKlass sun/reflect/generics/tree/VoidDescriptor
instanceKlass org/gradle/internal/snapshot/impl/ValueSnapshotterSerializerRegistry
instanceKlass org/gradle/internal/buildevents/BuildStartedTime
instanceKlass org/gradle/api/internal/FeaturePreviews
instanceKlass org/gradle/internal/session/BuildSessionScopeServices$CrossBuildFileHashCacheWrapper
instanceKlass org/gradle/deployment/internal/DefaultDeploymentRegistry
instanceKlass org/gradle/internal/filewatch/PendingChangesListener
instanceKlass org/gradle/deployment/internal/DeploymentRegistryInternal
instanceKlass org/gradle/deployment/internal/DeploymentRegistry
instanceKlass org/gradle/initialization/layout/ProjectCacheDir
instanceKlass org/gradle/api/internal/attributes/DefaultImmutableAttributesFactory
instanceKlass org/gradle/api/internal/attributes/ImmutableAttributesFactory
instanceKlass org/gradle/cache/internal/CleanupActionFactory
instanceKlass org/gradle/internal/scopeids/id/ScopeId
instanceKlass org/gradle/internal/scopeids/PersistentScopeIdLoader
instanceKlass org/gradle/internal/filewatch/PendingChangesManager
instanceKlass org/gradle/initialization/SettingsLocation
instanceKlass org/gradle/internal/hash/ChecksumService
instanceKlass org/gradle/cache/scopes/BuildTreeScopedCache
instanceKlass org/gradle/api/internal/tasks/userinput/UserInputReader
instanceKlass org/gradle/api/internal/tasks/userinput/UserInputHandler
instanceKlass org/gradle/api/internal/project/CrossProjectConfigurator
instanceKlass org/gradle/internal/snapshot/ValueSnapshotter
instanceKlass org/gradle/internal/service/scopes/WorkerSharedBuildSessionScopeServices
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$Services
instanceKlass org/gradle/api/internal/tasks/compile/incremental/cache/UserHomeScopedCompileCaches
instanceKlass org/gradle/api/internal/tasks/compile/incremental/cache/GeneralCompileCaches
instanceKlass org/gradle/api/internal/tasks/CompileServices$UserHomeScopeServices
instanceKlass org/gradle/kotlin/dsl/provider/plugins/DefaultProjectSchemaProvider
instanceKlass org/gradle/kotlin/dsl/accessors/ProjectSchemaProvider
instanceKlass org/gradle/kotlin/dsl/provider/plugins/precompiled/DefaultPrecompiledScriptPluginsSupport
instanceKlass org/gradle/kotlin/dsl/provider/PrecompiledScriptPluginsSupport
instanceKlass org/gradle/kotlin/dsl/provider/plugins/DefaultKotlinScriptBasePluginsApplicator
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptBasePluginsApplicator
instanceKlass org/gradle/kotlin/dsl/provider/plugins/GradleUserHomeServices
instanceKlass org/gradle/api/internal/artifacts/transform/ImmutableTransformationWorkspaceServices
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationWorkspaceServices
instanceKlass org/gradle/internal/execution/history/ExecutionHistoryStore
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ArtifactCachesProvider
instanceKlass org/gradle/internal/execution/history/ExecutionHistoryCacheAccess
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultArtifactCaches$WritableArtifactCacheLockingParameters
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGradleUserHomeScopeServices
instanceKlass org/gradle/workers/internal/WorkerDaemonClientsManager
instanceKlass org/gradle/workers/internal/ClassLoaderStructureProvider
instanceKlass org/gradle/workers/internal/ActionExecutionSpecFactory
instanceKlass org/gradle/workers/internal/WorkersServices$GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/provider/KotlinScriptClassloadingCache
instanceKlass org/gradle/kotlin/dsl/provider/GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/support/EmbeddedKotlinProvider
instanceKlass org/gradle/kotlin/dsl/support/GradleUserHomeServices
instanceKlass org/gradle/kotlin/dsl/cache/KotlinDslWorkspaceProvider
instanceKlass org/gradle/kotlin/dsl/cache/GradleUserHomeServices
instanceKlass org/gradle/internal/build/BuildState
instanceKlass org/gradle/internal/watch/registry/FileWatcherRegistryFactory
instanceKlass org/gradle/internal/vfs/impl/VfsRootReference
instanceKlass java/util/Optional
instanceKlass org/gradle/internal/watch/vfs/impl/LocationsWrittenByCurrentBuild
instanceKlass org/gradle/internal/vfs/FileSystemAccess$WriteListener
instanceKlass org/gradle/api/internal/changedetection/state/CrossBuildFileHashCache
instanceKlass org/gradle/internal/fingerprint/classpath/ClasspathFingerprinter
instanceKlass org/gradle/internal/watch/vfs/BuildLifecycleAwareVirtualFileSystem
instanceKlass org/gradle/internal/vfs/VirtualFileSystem
instanceKlass org/gradle/internal/execution/fingerprint/FileCollectionSnapshotter
instanceKlass org/gradle/internal/fingerprint/GenericFileTreeSnapshotter
instanceKlass org/gradle/internal/hash/FileHasher
instanceKlass org/gradle/internal/execution/fingerprint/FileCollectionFingerprinter
instanceKlass org/gradle/api/internal/changedetection/state/ResourceSnapshotterCacheService
instanceKlass org/gradle/internal/watch/vfs/WatchableFileSystemDetector
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GradleUserHomeServices
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadSerializer
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadClassLoaderRegistry
instanceKlass org/gradle/tooling/internal/provider/serialization/PayloadClassLoaderFactory
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingGradleUserHomeScopeServices
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$InstanceUnpackingVisitor
instanceKlass org/gradle/internal/classpath/ClasspathFileTransformer
instanceKlass org/gradle/internal/classpath/CachedClasspathTransformer$Transform
instanceKlass org/gradle/internal/classpath/DefaultCachedClasspathTransformer
instanceKlass org/gradle/internal/classpath/CachedClasspathTransformer
instanceKlass org/gradle/api/internal/changedetection/state/FileTimeStampInspector
instanceKlass org/gradle/initialization/RootBuildLifecycleListener
instanceKlass org/gradle/internal/file/FileAccessTracker
instanceKlass org/gradle/cache/CleanupAction
instanceKlass org/gradle/cache/internal/FilesFinder
instanceKlass org/gradle/internal/classpath/DefaultClasspathTransformerCacheFactory
instanceKlass org/gradle/internal/classpath/ClasspathTransformerCacheFactory
instanceKlass org/gradle/internal/classpath/ClasspathBuilder$EntryBuilder
instanceKlass org/gradle/internal/classpath/ClasspathBuilder
instanceKlass org/gradle/internal/classpath/ClasspathEntryVisitor$Entry
instanceKlass org/gradle/internal/classpath/ClasspathWalker
instanceKlass org/gradle/cache/internal/DirectoryCleanupAction
instanceKlass org/gradle/cache/CleanupProgressMonitor
instanceKlass org/gradle/cache/internal/GradleUserHomeCleanupService
instanceKlass org/gradle/cache/internal/VersionSpecificCacheDirectoryScanner
instanceKlass org/gradle/cache/internal/UsedGradleVersionsFromGradleUserHomeCaches
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices$$Lambda$14
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/service/scopes/WorkerSharedGlobalScopeServices$$Lambda$13
instanceKlass java/util/function/LongSupplier
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/file/impl/DefaultDeleter
instanceKlass org/gradle/cache/internal/scopes/DefaultCacheScopeMapping
instanceKlass org/gradle/cache/CacheBuilder
instanceKlass org/gradle/cache/internal/DefaultCacheRepository
instanceKlass org/gradle/cache/internal/ReferencablePersistentCache
instanceKlass org/gradle/cache/PersistentCache
instanceKlass org/gradle/cache/CleanableStore
instanceKlass org/gradle/cache/CacheAccess
instanceKlass org/gradle/cache/internal/DefaultCacheFactory
instanceKlass org/gradle/internal/logging/services/ProgressLoggingBridge
instanceKlass org/gradle/internal/logging/progress/ProgressLogger
instanceKlass org/gradle/internal/logging/progress/DefaultProgressLoggerFactory
instanceKlass org/gradle/internal/operations/DefaultBuildOperationIdFactory
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$1$$Lambda$12
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/cache/internal/UsedGradleVersions
instanceKlass org/gradle/cache/internal/GradleUserHomeCleanupServices
instanceKlass org/gradle/cache/internal/scopes/AbstractScopedCache
instanceKlass org/gradle/groovy/scripts/internal/CrossBuildInMemoryCachingScriptClassCache
instanceKlass org/gradle/internal/vfs/FileSystemAccess
instanceKlass org/gradle/initialization/layout/GlobalCacheDir
instanceKlass org/gradle/cache/internal/DefaultGeneratedGradleJarCache
instanceKlass org/gradle/cache/internal/GeneratedGradleJarCache
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistryListenerManager
instanceKlass org/gradle/cache/scopes/GlobalScopedCache
instanceKlass org/gradle/internal/classloader/ClasspathHasher
instanceKlass org/gradle/internal/jvm/JavaModuleDetector
instanceKlass org/gradle/process/internal/worker/child/WorkerProcessClassPathProvider
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry$1
instanceKlass org/gradle/internal/session/BuildSessionState
instanceKlass org/gradle/internal/operations/trace/SerializedOperation
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager$1
instanceKlass org/gradle/internal/operations/DefaultBuildOperationListenerManager
instanceKlass org/gradle/internal/work/Synchronizer
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$LeaseHolder
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry$WorkerLeaseCompletion
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry$WorkerLease
instanceKlass org/gradle/internal/resources/ResourceLock
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationValve
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationBridge
instanceKlass org/gradle/internal/operations/notify/BuildOperationNotificationListenerRegistrar
instanceKlass org/gradle/internal/operations/logging/LoggingBuildOperationProgressBroadcaster
instanceKlass org/gradle/internal/work/WorkerLeaseService
instanceKlass org/gradle/internal/work/WorkerThreadRegistry
instanceKlass org/gradle/internal/resources/ProjectLeaseRegistry
instanceKlass org/gradle/internal/work/WorkerLeaseRegistry
instanceKlass org/gradle/internal/operations/trace/BuildOperationTrace
instanceKlass org/gradle/configuration/internal/ListenerBuildOperationDecorator
instanceKlass org/gradle/internal/operations/BuildOperationExecutor
instanceKlass org/gradle/internal/operations/BuildOperationRunner
instanceKlass org/gradle/internal/operations/BuildOperationQueueFactory
instanceKlass org/gradle/configuration/internal/UserCodeApplicationContext
instanceKlass org/gradle/api/internal/CollectionCallbackActionDecorator
instanceKlass org/gradle/internal/session/CrossBuildSessionState$Services
instanceKlass org/gradle/internal/service/ServiceRegistryBuilder
instanceKlass org/gradle/internal/session/CrossBuildSessionState
instanceKlass org/gradle/tooling/internal/provider/BuildSessionLifecycleBuildActionExecuter$ActionImpl
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$3
instanceKlass org/gradle/internal/logging/sink/ProgressLogEventGenerator
instanceKlass org/gradle/internal/logging/console/BuildLogLevelFilterRenderer
instanceKlass org/gradle/launcher/daemon/server/exec/ExecuteBuild$1
instanceKlass org/gradle/initialization/DefaultBuildRequestMetaData
instanceKlass org/gradle/initialization/DefaultBuildRequestContext
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonConnectionBackedEventConsumer
instanceKlass org/gradle/launcher/daemon/server/exec/WatchForDisconnection$1
instanceKlass org/gradle/internal/featurelifecycle/LoggingIncubatingFeatureHandler
instanceKlass org/gradle/util/internal/IncubationLogger
instanceKlass org/gradle/internal/featurelifecycle/FeatureUsage
instanceKlass org/gradle/internal/featurelifecycle/UsageLocationReporter
instanceKlass org/gradle/internal/featurelifecycle/LoggingDeprecatedFeatureHandler
instanceKlass org/gradle/internal/featurelifecycle/FeatureHandler
instanceKlass org/gradle/internal/deprecation/DeprecationMessageBuilder
instanceKlass org/gradle/internal/deprecation/DeprecationLogger
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput$2
instanceKlass org/gradle/util/internal/StdinSwapper$2
instanceKlass org/gradle/util/internal/StdinSwapper$1
instanceKlass org/gradle/util/internal/Swapper
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput$1
instanceKlass java/math/MathContext
instanceKlass org/gradle/internal/util/NumberUtil
instanceKlass org/gradle/launcher/daemon/server/exec/LogToClient$AsynchronousLogDispatcher$1
instanceKlass java/util/concurrent/CountDownLatch
instanceKlass com/google/common/collect/AbstractIterator$1
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$UnmodifiableEntry
instanceKlass java/util/Collections$UnmodifiableMap$UnmodifiableEntrySet$1
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator$1
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$5
instanceKlass org/gradle/launcher/daemon/server/exec/StartBuildOrRespondWithBusy$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$CommandQueue$1
instanceKlass org/gradle/launcher/daemon/server/exec/HandleCancel$1
instanceKlass com/google/common/collect/Platform
instanceKlass org/gradle/launcher/daemon/server/api/DaemonCommandExecution
instanceKlass org/gradle/launcher/exec/DefaultBuildActionParameters
instanceKlass java/util/AbstractMap$SimpleImmutableEntry
instanceKlass org/gradle/configuration/GradleLauncherMetaData
instanceKlass com/google/common/collect/AbstractMapEntry
instanceKlass com/google/common/collect/ImmutableMap$Builder
instanceKlass com/google/common/collect/BiMap
instanceKlass com/google/common/collect/ImmutableMap
instanceKlass org/gradle/internal/DefaultTaskExecutionRequest
instanceKlass org/gradle/TaskExecutionRequest
instanceKlass org/gradle/internal/buildoption/BuildOption$Value
instanceKlass org/gradle/internal/concurrent/DefaultParallelismConfiguration
instanceKlass org/gradle/internal/logging/DefaultLoggingConfiguration
instanceKlass org/gradle/initialization/BuildLayoutParameters
instanceKlass java/nio/channels/spi/AbstractSelector$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$1
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$ReceiveQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$DisconnectQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection$CommandQueue
instanceKlass org/gradle/launcher/daemon/server/DefaultDaemonConnection
instanceKlass org/gradle/launcher/daemon/server/api/DaemonConnection
instanceKlass org/gradle/launcher/daemon/server/DefaultIncomingConnectionHandler$ConnectionWorker
instanceKlass org/gradle/launcher/daemon/server/SynchronizedDispatchConnection
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter$2
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketInetAddress$Serializer
instanceKlass org/gradle/internal/io/BufferCaster
instanceKlass sun/nio/ch/WindowsSelectorImpl$MapEntry
instanceKlass sun/nio/ch/OptionKey
instanceKlass sun/nio/ch/SocketOptionRegistry$LazyInitialization
instanceKlass sun/nio/ch/SocketOptionRegistry$RegistryKey
instanceKlass sun/nio/ch/SocketOptionRegistry
instanceKlass sun/nio/ch/ExtendedSocketOption$1
instanceKlass sun/nio/ch/ExtendedSocketOption
instanceKlass sun/nio/ch/SocketChannelImpl$DefaultOptionsHolder
instanceKlass sun/nio/ch/IOStatus
instanceKlass sun/nio/ch/Util$BufferCache
instanceKlass sun/nio/ch/PipeImpl$Initializer$LoopbackConnector
instanceKlass sun/nio/ch/PipeImpl$Initializer
instanceKlass java/nio/channels/Pipe
instanceKlass sun/nio/ch/NativeObject
instanceKlass sun/nio/ch/PollArrayWrapper
instanceKlass sun/nio/ch/WindowsSelectorImpl$FinishLock
instanceKlass sun/nio/ch/WindowsSelectorImpl$StartLock
instanceKlass sun/nio/ch/WindowsSelectorImpl$SubSelector
instanceKlass java/nio/channels/SelectionKey
instanceKlass sun/nio/ch/Util$3
instanceKlass sun/nio/ch/Util$2
instanceKlass sun/nio/ch/Util
instanceKlass java/nio/channels/Selector
instanceKlass java/nio/DirectByteBuffer$Deallocator
instanceKlass org/gradle/internal/remote/internal/KryoBackedMessageSerializer
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnection
instanceKlass org/gradle/internal/serialize/ObjectReader
instanceKlass org/gradle/internal/serialize/ObjectWriter
instanceKlass org/gradle/internal/serialize/Serializers$StatefulSerializerAdapter
instanceKlass org/gradle/internal/serialize/StatefulSerializer
instanceKlass org/gradle/internal/serialize/Serializers
instanceKlass org/gradle/internal/remote/internal/RemoteConnection
instanceKlass org/gradle/internal/remote/internal/Connection
instanceKlass org/gradle/internal/dispatch/Receive
instanceKlass org/gradle/internal/remote/internal/MessageSerializer
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnectCompletion
instanceKlass org/gradle/internal/remote/internal/ConnectCompletion
instanceKlass java/net/Socket
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator$2
instanceKlass org/gradle/launcher/daemon/server/Daemon$DefaultDaemonExpirationListener
instanceKlass org/gradle/launcher/daemon/server/Daemon$DaemonExpirationPeriodicCheck
instanceKlass org/gradle/launcher/daemon/server/DaemonRegistryUnavailableExpirationStrategy
instanceKlass org/gradle/internal/event/DefaultListenerManager$ListenerDetails
instanceKlass org/gradle/launcher/daemon/server/health/LowMemoryDaemonExpirationStrategy
instanceKlass org/gradle/process/internal/health/memory/OsMemoryStatusListener
instanceKlass org/gradle/launcher/daemon/server/NotMostRecentlyUsedDaemonExpirationStrategy
instanceKlass com/google/common/base/Functions$ConstantFunction
instanceKlass com/google/common/base/Functions
instanceKlass org/gradle/launcher/daemon/server/DaemonIdleTimeoutExpirationStrategy
instanceKlass org/gradle/launcher/daemon/context/DaemonCompatibilitySpec
instanceKlass org/gradle/api/internal/specs/ExplainingSpec
instanceKlass org/gradle/launcher/daemon/server/CompatibleDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/expiry/AllDaemonExpirationStrategy
instanceKlass org/gradle/internal/stream/EncodedStream
instanceKlass org/gradle/launcher/daemon/bootstrap/DaemonStartupCommunication
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$3
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$2
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$1
instanceKlass org/gradle/internal/remote/internal/inet/SocketInetAddress
instanceKlass org/gradle/internal/serialize/AbstractEncoder
instanceKlass org/gradle/internal/serialize/FlushableEncoder
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent$$Lambda$11
instanceKlass org/gradle/launcher/daemon/registry/DaemonInfo$Serializer
instanceKlass org/gradle/cache/internal/filelock/LockInfo
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$$Lambda$10
instanceKlass org/gradle/cache/internal/filelock/DefaultLockStateSerializer$SequenceNumberLockState
instanceKlass org/gradle/internal/io/IOQuery$Result
instanceKlass org/gradle/cache/internal/filelock/FileLockOutcome
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$DefaultFileLock$4
instanceKlass org/gradle/internal/io/ExponentialBackoff
instanceKlass org/gradle/cache/internal/DefaultFileLockManager$AwaitableFileLockReleasedSignal
instanceKlass org/gradle/cache/FileLockReleasedSignal
instanceKlass org/gradle/cache/internal/filelock/LockInfoSerializer
instanceKlass org/gradle/cache/internal/filelock/LockInfoAccess
instanceKlass org/gradle/cache/internal/filelock/LockStateAccess
instanceKlass org/gradle/cache/internal/filelock/LockFileAccess
instanceKlass org/gradle/cache/internal/filelock/LockState
instanceKlass org/gradle/cache/internal/filelock/DefaultLockStateSerializer
instanceKlass org/gradle/internal/io/IOQuery
instanceKlass org/gradle/cache/FileLock$State
instanceKlass org/gradle/cache/internal/filelock/LockStateSerializer
instanceKlass sun/net/ResourceManager
instanceKlass java/net/DatagramPacket
instanceKlass java/net/DatagramSocket$1
instanceKlass java/net/AbstractPlainDatagramSocketImpl$1
instanceKlass java/net/DatagramSocketImpl
instanceKlass java/net/DefaultDatagramSocketImplFactory$1
instanceKlass java/net/DefaultDatagramSocketImplFactory
instanceKlass java/net/DatagramSocket
instanceKlass org/gradle/cache/internal/locklistener/FileLockCommunicator
instanceKlass org/gradle/cache/internal/filelock/LockOptionsBuilder
instanceKlass org/gradle/cache/internal/SimpleStateCache$1Updater
instanceKlass org/gradle/cache/internal/FileIntegrityViolationSuppressingPersistentStateCacheDecorator$1
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry$8
instanceKlass org/gradle/launcher/daemon/registry/DaemonInfo
instanceKlass org/gradle/launcher/daemon/context/DaemonConnectDetails
instanceKlass sun/util/locale/provider/TimeZoneNameUtility$TimeZoneNameGetter
instanceKlass sun/util/locale/provider/TimeZoneNameUtility
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector$1
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector$Receiver
instanceKlass org/gradle/internal/remote/internal/inet/MultiChoiceAddress
instanceKlass org/gradle/internal/remote/internal/inet/InetEndpoint
instanceKlass java/util/UUID$Holder
instanceKlass java/util/UUID
instanceKlass sun/net/NetHooks
instanceKlass java/net/Inet4AddressImpl
instanceKlass java/net/NetworkInterface$1checkedAddresses
instanceKlass org/gradle/internal/remote/internal/inet/InetAddresses
instanceKlass sun/misc/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass java/net/PlainSocketImpl$1
instanceKlass java/net/StandardSocketOptions$StdSocketOption
instanceKlass java/net/StandardSocketOptions
instanceKlass java/net/AbstractPlainSocketImpl$1
instanceKlass java/net/SocketImpl
instanceKlass java/net/SocketOptions
instanceKlass java/net/SocksConsts
instanceKlass java/net/ServerSocket
instanceKlass sun/nio/ch/Net$3
instanceKlass sun/nio/ch/Net$4
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions$1
instanceKlass jdk/net/ExtendedSocketOptions$PlatformSocketOptions
instanceKlass jdk/net/SocketFlow
instanceKlass jdk/net/ExtendedSocketOptions$ExtSocketOption
instanceKlass java/net/SocketOption
instanceKlass jdk/net/ExtendedSocketOptions
instanceKlass sun/net/ExtendedSocketOptions
instanceKlass sun/nio/ch/Net$1
instanceKlass java/net/ProtocolFamily
instanceKlass sun/nio/ch/Net
instanceKlass java/net/InetSocketAddress$InetSocketAddressHolder
instanceKlass sun/nio/ch/SelChImpl
instanceKlass sun/nio/ch/DefaultSelectorProvider
instanceKlass java/nio/channels/spi/SelectorProvider$1
instanceKlass java/nio/channels/spi/SelectorProvider
instanceKlass java/nio/channels/NetworkChannel
instanceKlass org/gradle/launcher/daemon/server/DaemonTcpServerConnector$1
instanceKlass org/gradle/launcher/daemon/server/Daemon$5
instanceKlass org/gradle/launcher/daemon/server/DefaultIncomingConnectionHandler
instanceKlass org/gradle/initialization/DefaultBuildCancellationToken
instanceKlass java/util/concurrent/SynchronousQueue$TransferStack$SNode
instanceKlass java/util/concurrent/SynchronousQueue$Transferer
instanceKlass org/gradle/initialization/BuildCancellationToken
instanceKlass org/gradle/launcher/daemon/server/DaemonStateCoordinator
instanceKlass org/gradle/launcher/daemon/server/Daemon$4
instanceKlass org/gradle/launcher/daemon/server/Daemon$3
instanceKlass org/gradle/launcher/daemon/server/Daemon$2
instanceKlass org/gradle/launcher/daemon/server/Daemon$1
instanceKlass org/gradle/launcher/daemon/server/DaemonRegistryUpdater
instanceKlass sun/nio/fs/BasicFileAttributesHolder
instanceKlass sun/nio/fs/WindowsDirectoryStream$WindowsDirectoryIterator
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsNativeDispatcher$BackupResult
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass sun/nio/fs/WindowsNativeDispatcher$1
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/WindowsDirectoryStream
instanceKlass java/nio/file/DirectoryStream
instanceKlass java/nio/file/Files$AcceptAllFilter
instanceKlass java/nio/file/DirectoryStream$Filter
instanceKlass java/nio/file/Files
instanceKlass sun/nio/fs/AbstractPath
instanceKlass sun/nio/fs/Util
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/net/NetworkInterface$2
instanceKlass java/net/DefaultInterface
instanceKlass java/net/InterfaceAddress
instanceKlass java/net/NetworkInterface$1
instanceKlass java/net/NetworkInterface
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass sun/security/provider/SeedGenerator$1
instanceKlass sun/security/provider/SeedGenerator
instanceKlass sun/security/provider/SecureRandom$SeederHolder
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/MessageDigestSpi
instanceKlass java/security/SecureRandomSpi
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/jca/ProviderConfig$2
instanceKlass sun/security/jca/ProviderList$2
instanceKlass sun/misc/FDBigInteger
instanceKlass sun/misc/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass sun/misc/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass sun/misc/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass sun/misc/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass sun/misc/FloatingDecimal$BinaryToASCIIConverter
instanceKlass sun/misc/FloatingDecimal
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass com/google/common/base/Joiner
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonCommandExecuter
instanceKlass org/gradle/internal/remote/internal/inet/MultiChoiceAddressSerializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent$Serializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryContent
instanceKlass org/gradle/cache/LockOptions
instanceKlass org/gradle/cache/internal/AbstractFileAccess
instanceKlass org/gradle/internal/serialize/Encoder
instanceKlass org/gradle/cache/internal/SimpleStateCache
instanceKlass org/gradle/cache/internal/FileIntegrityViolationSuppressingPersistentStateCacheDecorator
instanceKlass org/gradle/cache/PersistentStateCache$UpdateAction
instanceKlass org/gradle/cache/PersistentStateCache
instanceKlass org/gradle/launcher/daemon/registry/PersistentDaemonRegistry
instanceKlass org/gradle/cache/internal/CacheAccessSerializer$$Lambda$9
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/cache/Cache$$Lambda$8
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryServices$$Lambda$7
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/FallbackStat
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/EmptyChmod
instanceKlass org/gradle/internal/nativeintegration/filesystem/jdk7/Jdk7Symlink
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass net/rubygrapefruit/platform/file/PosixFileInfo
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$BrokenService
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/UnavailablePosixFiles
instanceKlass net/rubygrapefruit/platform/terminal/Terminals
instanceKlass org/gradle/api/internal/file/temp/GradleUserHomeTemporaryFileProvider$1
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$2
instanceKlass net/rubygrapefruit/platform/file/WindowsFileInfo
instanceKlass net/rubygrapefruit/platform/file/FileInfo
instanceKlass net/rubygrapefruit/platform/internal/DirList
instanceKlass net/rubygrapefruit/platform/internal/AbstractFiles
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/NativePlatformBackedFileMetadataAccessor
instanceKlass java/util/Random
instanceKlass org/gradle/internal/id/RandomLongIdGenerator
instanceKlass org/gradle/cache/internal/DefaultProcessMetaDataProvider
instanceKlass org/gradle/internal/io/ExponentialBackoff$Signal
instanceKlass org/gradle/cache/FileLock
instanceKlass org/gradle/cache/FileAccess
instanceKlass org/gradle/cache/internal/DefaultFileLockManager
instanceKlass org/gradle/internal/remote/ConnectionAcceptor
instanceKlass org/gradle/internal/remote/Address
instanceKlass java/net/SocketAddress
instanceKlass org/gradle/internal/remote/internal/inet/TcpIncomingConnector
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$OutputMessageSerializer
instanceKlass org/gradle/internal/logging/serializer/LogLevelChangeEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ProgressEventSerializer
instanceKlass org/gradle/internal/logging/serializer/ProgressCompleteEventSerializer
instanceKlass org/gradle/internal/operations/BuildOperationMetadata
instanceKlass org/gradle/internal/logging/serializer/ProgressStartEventSerializer
instanceKlass org/gradle/internal/logging/serializer/SpanSerializer
instanceKlass org/gradle/internal/logging/serializer/StyledTextOutputEventSerializer
instanceKlass org/gradle/internal/logging/serializer/UserInputResumeEventSerializer
instanceKlass org/gradle/internal/logging/serializer/PromptOutputEventSerializer
instanceKlass org/gradle/internal/logging/serializer/UserInputRequestEventSerializer
instanceKlass org/gradle/internal/logging/serializer/LogEventSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$CloseInputSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$ForwardInputSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildEventSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$FinishedSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$SuccessSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$FailureSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildStartedSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$DaemonUnavailableSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$CancelSerializer
instanceKlass org/gradle/launcher/exec/BuildActionParameters
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildActionParametersSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer$BuildSerializer
instanceKlass org/gradle/launcher/daemon/protocol/DaemonMessageSerializer
instanceKlass org/gradle/launcher/daemon/server/DaemonTcpServerConnector
instanceKlass org/gradle/launcher/daemon/server/IncomingConnectionHandler
instanceKlass org/gradle/launcher/daemon/server/api/DaemonStateControl
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$TypeInfo
instanceKlass java/util/TreeMap$PrivateEntryIterator
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$TestExecutionRequestActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ClientProvidedPhasedActionSerializer
instanceKlass org/gradle/tooling/internal/provider/serialization/SerializedPayloadSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ClientProvidedBuildActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$BuildEventSubscriptionsSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$BuildModelActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/SubscribableBuildAction
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ValueSerializer
instanceKlass org/gradle/internal/serialize/AbstractSerializer
instanceKlass org/gradle/internal/serialize/BaseSerializerFactory
instanceKlass org/gradle/internal/serialize/AbstractCollectionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$NullableFileSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$StartParameterSerializer
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer$ExecuteBuildActionSerializer
instanceKlass org/gradle/tooling/internal/provider/action/ExecuteBuildAction
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$HierarchySerializerMatcher
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$StrictSerializerMatcher
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$SerializerClassMatcherStrategy
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry$1
instanceKlass org/gradle/internal/serialize/DefaultSerializerRegistry
instanceKlass org/gradle/internal/serialize/SerializerRegistry
instanceKlass org/gradle/tooling/internal/provider/action/BuildActionSerializer
instanceKlass org/gradle/initialization/BuildRequestContext
instanceKlass org/gradle/launcher/daemon/server/exec/WatchForDisconnection
instanceKlass org/gradle/launcher/daemon/server/exec/ResetDeprecationLogger
instanceKlass org/gradle/launcher/daemon/server/exec/RequestStopIfSingleUsedDaemon
instanceKlass org/gradle/launcher/daemon/server/api/StdinHandler
instanceKlass org/gradle/launcher/daemon/server/exec/ForwardClientInput
instanceKlass org/gradle/launcher/daemon/server/health/HealthLogger
instanceKlass org/gradle/launcher/daemon/server/exec/LogAndCheckHealth
instanceKlass org/gradle/launcher/daemon/server/exec/BuildCommandOnly
instanceKlass org/gradle/launcher/daemon/server/exec/ReturnResult
instanceKlass org/gradle/launcher/daemon/server/api/HandleReportStatus
instanceKlass org/gradle/launcher/daemon/server/exec/HandleCancel
instanceKlass org/gradle/launcher/daemon/server/api/HandleInvalidateVirtualFileSystem
instanceKlass org/gradle/launcher/daemon/protocol/Message
instanceKlass org/gradle/launcher/daemon/server/api/HandleStop
instanceKlass org/gradle/launcher/daemon/diagnostics/DaemonDiagnostics
instanceKlass org/gradle/tooling/internal/provider/BuildSessionLifecycleBuildActionExecuter
instanceKlass org/gradle/tooling/internal/provider/StartParamsValidatingActionExecuter
instanceKlass org/gradle/initialization/BuildRequestMetaData
instanceKlass org/gradle/initialization/exception/ExceptionAnalyser
instanceKlass org/gradle/initialization/exception/ExceptionCollector
instanceKlass org/gradle/tooling/internal/provider/SessionFailureReportingActionExecuter
instanceKlass org/gradle/StartParameter
instanceKlass org/gradle/concurrent/ParallelismConfiguration
instanceKlass org/gradle/tooling/internal/provider/SetupLoggingActionExecuter
instanceKlass org/gradle/internal/file/FileAccessTimeJournal
instanceKlass org/gradle/cache/GlobalCacheLocations
instanceKlass org/gradle/initialization/ClassLoaderScopeRegistry
instanceKlass org/gradle/process/internal/worker/WorkerProcessFactory
instanceKlass org/gradle/internal/classloader/HashingClassLoaderFactory
instanceKlass org/gradle/internal/execution/timeout/TimeoutHandler
instanceKlass org/gradle/cache/internal/FileContentCacheFactory
instanceKlass org/gradle/cache/scopes/ScopedCache
instanceKlass org/gradle/groovy/scripts/internal/ScriptSourceHasher
instanceKlass org/gradle/cache/CacheRepository
instanceKlass org/gradle/cache/internal/CacheScopeMapping
instanceKlass org/gradle/internal/hash/ClassLoaderHierarchyHasher
instanceKlass org/gradle/api/internal/initialization/loadercache/ClassLoaderCache
instanceKlass org/gradle/internal/isolation/IsolatableFactory
instanceKlass org/gradle/internal/service/scopes/WorkerSharedUserHomeScopeServices
instanceKlass org/gradle/internal/service/scopes/DefaultGradleUserHomeScopeServiceRegistry
instanceKlass org/gradle/internal/logging/text/AbstractStyledTextOutputFactory
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationResult
instanceKlass org/gradle/internal/event/DefaultListenerManager$EventBroadcast
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationListener
instanceKlass com/google/common/collect/ObjectArrays
instanceKlass org/gradle/launcher/daemon/server/health/LowNonHeapDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/health/LowHeapSpaceDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/health/GcThrashingDaemonExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/expiry/AnyDaemonExpirationStrategy
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$RegistrationWrapper
instanceKlass org/gradle/internal/service/scopes/Scopes$Build
instanceKlass org/gradle/internal/service/scopes/EventScope
instanceKlass java/lang/FunctionalInterface
instanceKlass kotlin/annotation/Target
instanceKlass kotlin/annotation/Retention
instanceKlass kotlin/Metadata
instanceKlass java/lang/annotation/Documented
instanceKlass org/gradle/api/Incubating
instanceKlass sun/reflect/ClassDefiner$1
instanceKlass sun/reflect/ClassDefiner
instanceKlass sun/reflect/MethodAccessorGenerator$1
instanceKlass sun/reflect/Label$PatchInfo
instanceKlass sun/reflect/Label
instanceKlass sun/reflect/UTF8
instanceKlass sun/reflect/ClassFileAssembler
instanceKlass sun/reflect/ByteVectorImpl
instanceKlass sun/reflect/ByteVector
instanceKlass sun/reflect/ByteVectorFactory
instanceKlass sun/reflect/AccessorGenerator
instanceKlass sun/reflect/ClassFileConstants
instanceKlass org/gradle/internal/service/scopes/Scopes$BuildTree
instanceKlass org/gradle/internal/service/scopes/Scopes$BuildSession
instanceKlass org/gradle/internal/service/scopes/Scopes$UserHome
instanceKlass java/lang/annotation/Target
instanceKlass sun/reflect/annotation/AnnotationInvocationHandler
instanceKlass sun/reflect/annotation/AnnotationParser$1
instanceKlass sun/reflect/annotation/ExceptionProxy
instanceKlass java/lang/annotation/Inherited
instanceKlass java/lang/annotation/Retention
instanceKlass sun/reflect/annotation/AnnotationType$1
instanceKlass java/lang/reflect/GenericArrayType
instanceKlass org/gradle/internal/service/scopes/ServiceScope
instanceKlass sun/reflect/annotation/AnnotationParser
instanceKlass org/gradle/internal/service/scopes/StatefulListener
instanceKlass org/gradle/internal/service/scopes/Scope$Global
instanceKlass org/gradle/internal/service/scopes/Scope
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/Executors$RunnableAdapter
instanceKlass java/util/concurrent/Executors
instanceKlass java/util/concurrent/FutureTask$WaitNode
instanceKlass java/util/concurrent/FutureTask
instanceKlass org/gradle/internal/concurrent/ManagedExecutorImpl$1
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionCheck
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor$1
instanceKlass java/util/concurrent/BlockingDeque
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultSlidingWindow
instanceKlass org/gradle/launcher/daemon/server/health/gc/SlidingWindow
instanceKlass org/gradle/launcher/daemon/server/health/gc/DefaultGarbageCollectionMonitor
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionInfo
instanceKlass org/gradle/internal/concurrent/ExecutorPolicy$CatchAndRecordFailures
instanceKlass java/util/concurrent/RunnableScheduledFuture
instanceKlass java/util/concurrent/ScheduledFuture
instanceKlass java/util/concurrent/Delayed
instanceKlass java/util/concurrent/RunnableFuture
instanceKlass java/util/concurrent/Future
instanceKlass org/gradle/internal/concurrent/ThreadFactoryImpl
instanceKlass java/util/concurrent/ThreadPoolExecutor$AbortPolicy
instanceKlass java/util/concurrent/RejectedExecutionHandler
instanceKlass java/util/concurrent/Callable
instanceKlass java/util/concurrent/AbstractExecutorService
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass org/gradle/internal/concurrent/ManagedScheduledExecutor
instanceKlass java/util/concurrent/ScheduledExecutorService
instanceKlass org/gradle/internal/concurrent/ManagedExecutor
instanceKlass java/util/concurrent/ExecutorService
instanceKlass java/util/concurrent/Executor
instanceKlass org/gradle/internal/concurrent/AsyncStoppable
instanceKlass org/gradle/internal/concurrent/ExecutorPolicy
instanceKlass org/gradle/internal/concurrent/DefaultExecutorFactory
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$3
instanceKlass sun/management/Sensor
instanceKlass sun/management/MemoryPoolImpl
instanceKlass java/lang/management/MemoryPoolMXBean
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$2
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectorMonitoringStrategy$1
instanceKlass com/sun/management/GarbageCollectorMXBean
instanceKlass java/lang/management/GarbageCollectorMXBean
instanceKlass sun/management/ManagementFactory
instanceKlass java/lang/management/MemoryManagerMXBean
instanceKlass sun/management/NotificationEmitterSupport
instanceKlass javax/management/NotificationEmitter
instanceKlass javax/management/NotificationBroadcaster
instanceKlass java/lang/management/MemoryMXBean
instanceKlass java/lang/management/PlatformManagedObject
instanceKlass sun/management/VMManagementImpl
instanceKlass sun/management/VMManagement
instanceKlass sun/management/ManagementFactoryHelper$4
instanceKlass sun/management/ManagementFactoryHelper
instanceKlass java/lang/management/ManagementFactory
instanceKlass org/gradle/launcher/daemon/server/health/gc/GarbageCollectionMonitor
instanceKlass org/gradle/internal/time/DefaultTimer
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$StateContext
instanceKlass java/text/DontCareFieldPosition$1
instanceKlass java/text/Format$FieldDelegate
instanceKlass java/util/Date
instanceKlass java/text/DigitList
instanceKlass java/text/FieldPosition
instanceKlass java/text/DateFormatSymbols
instanceKlass sun/util/calendar/CalendarUtils
instanceKlass sun/util/calendar/CalendarDate
instanceKlass sun/util/locale/provider/CalendarDataUtility$CalendarWeekParameterGetter
instanceKlass sun/util/locale/provider/CalendarDataUtility
instanceKlass java/util/Calendar$Builder
instanceKlass java/util/Calendar
instanceKlass java/util/TimeZone$1
instanceKlass sun/util/calendar/ZoneInfoFile$ZoneOffsetTransitionRule
instanceKlass sun/util/calendar/ZoneInfoFile$1
instanceKlass sun/util/calendar/ZoneInfoFile
instanceKlass sun/util/calendar/CalendarSystem
instanceKlass java/util/TimeZone
instanceKlass java/text/AttributedCharacterIterator$Attribute
instanceKlass java/text/Format
instanceKlass org/gradle/internal/logging/sink/LogEventDispatcher
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$SeenFromEol
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$4
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$3
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$2
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$1
instanceKlass org/gradle/internal/logging/text/AbstractLineChoppingStyledTextOutput$State
instanceKlass org/gradle/internal/logging/text/StreamBackedStandardOutputListener
instanceKlass org/gradle/internal/logging/text/AbstractStyledTextOutput
instanceKlass org/gradle/internal/logging/console/StyledTextOutputBackedRenderer
instanceKlass org/slf4j/helpers/FormattingTuple
instanceKlass org/slf4j/helpers/MessageFormatter
instanceKlass net/rubygrapefruit/platform/internal/FunctionResult
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$PrintStreamDestination
instanceKlass java/util/logging/ErrorManager
instanceKlass org/gradle/internal/logging/source/JavaUtilLoggingSystem$SnapshotImpl
instanceKlass org/gradle/internal/logging/config/LoggingSystemAdapter$SnapshotImpl
instanceKlass org/gradle/internal/dispatch/MethodInvocation
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$SnapshotImpl
instanceKlass org/gradle/process/internal/shutdown/ShutdownHooks
instanceKlass org/gradle/launcher/daemon/bootstrap/DaemonMain$1
instanceKlass com/google/common/io/Files$2
instanceKlass com/google/common/io/LineProcessor
instanceKlass com/google/common/io/ByteSink
instanceKlass com/google/common/io/ByteSource
instanceKlass com/google/common/base/Predicate
instanceKlass java/util/function/Predicate
instanceKlass com/google/common/graph/SuccessorsFunction
instanceKlass com/google/common/io/Files
instanceKlass org/gradle/util/internal/GFileUtils
instanceKlass java/util/regex/Pattern$CharPropertyNames$CharPropertyFactory
instanceKlass java/util/regex/Pattern$CharPropertyNames
instanceKlass org/gradle/util/GradleVersion
instanceKlass org/gradle/launcher/daemon/context/DefaultDaemonContext$Serializer
instanceKlass org/gradle/launcher/daemon/context/DefaultDaemonContext
instanceKlass net/rubygrapefruit/platform/internal/jni/PosixProcessFunctions
instanceKlass org/gradle/internal/FileUtils$1
instanceKlass org/gradle/internal/FileUtils
instanceKlass org/gradle/internal/nativeintegration/ReflectiveEnvironment
instanceKlass org/gradle/internal/nativeintegration/processenvironment/AbstractProcessEnvironment
instanceKlass net/rubygrapefruit/platform/internal/DefaultProcess
instanceKlass net/rubygrapefruit/platform/internal/WrapperProcess
instanceKlass net/rubygrapefruit/platform/file/WindowsFiles
instanceKlass org/gradle/launcher/daemon/context/DaemonContextBuilder
instanceKlass org/gradle/internal/id/UUIDGenerator
instanceKlass org/gradle/internal/remote/internal/OutgoingConnector
instanceKlass org/gradle/internal/remote/MessagingServer
instanceKlass org/gradle/internal/remote/internal/IncomingConnector
instanceKlass org/gradle/internal/remote/MessagingClient
instanceKlass org/gradle/internal/id/IdGenerator
instanceKlass org/gradle/internal/remote/services/MessagingServices
instanceKlass org/gradle/api/internal/file/DefaultFileLookup
instanceKlass org/gradle/internal/scripts/DefaultScriptFileResolver
instanceKlass org/gradle/internal/scripts/ScriptFileResolver
instanceKlass org/gradle/initialization/layout/BuildLayoutFactory
instanceKlass org/gradle/tooling/internal/provider/runner/OperationDependencyLookup
instanceKlass org/gradle/tooling/internal/provider/runner/ToolingApiBuildEventListenerFactory
instanceKlass org/gradle/configurationcache/serialization/beans/BeanConstructors
instanceKlass org/gradle/nativeplatform/NativeBinarySpec
instanceKlass org/gradle/platform/base/BinarySpec
instanceKlass org/gradle/platform/base/Binary
instanceKlass org/gradle/api/CheckableComponentSpec
instanceKlass org/gradle/api/BuildableComponentSpec
instanceKlass org/gradle/platform/base/ComponentSpec
instanceKlass org/gradle/model/ModelElement
instanceKlass org/gradle/api/Buildable
instanceKlass org/gradle/internal/resource/transport/sftp/SftpClientFactory
instanceKlass org/gradle/internal/resource/transport/sftp/SftpResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/aws/s3/S3ResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/gcp/gcs/GcsResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/nativeplatform/TargetMachineBuilder
instanceKlass org/gradle/nativeplatform/TargetMachine
instanceKlass org/gradle/nativeplatform/internal/DefaultTargetMachineFactory
instanceKlass org/gradle/nativeplatform/TargetMachineFactory
instanceKlass org/gradle/nativeplatform/internal/NativePlatformResolver
instanceKlass org/gradle/platform/base/internal/PlatformResolver
instanceKlass org/gradle/nativeplatform/platform/internal/NativePlatformInternal
instanceKlass org/gradle/nativeplatform/platform/NativePlatform
instanceKlass org/gradle/platform/base/Platform
instanceKlass org/gradle/nativeplatform/platform/internal/OperatingSystemInternal
instanceKlass org/gradle/nativeplatform/platform/OperatingSystem
instanceKlass org/gradle/api/Named
instanceKlass org/gradle/nativeplatform/platform/internal/NativePlatforms
instanceKlass org/gradle/internal/logging/text/DiagnosticsVisitor
instanceKlass org/gradle/buildinit/plugins/internal/action/InitBuiltInCommand
instanceKlass org/gradle/api/component/SoftwareComponentFactory
instanceKlass org/gradle/api/plugins/internal/PluginAuthorServices$GlobalScopeServices
instanceKlass org/gradle/internal/build/event/OperationResultPostProcessorFactory
instanceKlass org/gradle/internal/build/event/BuildEventSubscriptions
instanceKlass org/gradle/language/java/internal/JavaLanguagePluginServiceRegistry$JavaGlobalScopeServices
instanceKlass org/gradle/platform/base/internal/registry/ComponentModelBaseServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/reporting/ReportRenderer
instanceKlass org/gradle/api/reporting/components/internal/DiagnosticsServices$1
instanceKlass org/gradle/api/plugins/internal/HelpBuiltInCommand
instanceKlass org/gradle/configuration/project/BuiltInCommand
instanceKlass org/gradle/api/internal/artifacts/configurations/MarkConfigurationObservedListener
instanceKlass org/gradle/api/internal/artifacts/configurations/ProjectDependencyObservedListener
instanceKlass org/gradle/api/artifacts/component/ComponentSelector
instanceKlass org/gradle/internal/resource/ExternalResourceName
instanceKlass org/gradle/api/Describable
instanceKlass org/gradle/api/internal/attributes/EmptySchema
instanceKlass org/gradle/api/internal/attributes/AttributesSchemaInternal
instanceKlass org/gradle/api/internal/attributes/DescribableAttributesSchema
instanceKlass org/gradle/api/attributes/AttributesSchema
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/PlatformSupport
instanceKlass org/gradle/internal/typeconversion/NotationParser
instanceKlass org/gradle/cache/internal/ProducerGuard
instanceKlass org/gradle/api/internal/tasks/properties/annotations/AbstractInputFilePropertyAnnotationHandler
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransformActionScheme
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactTransformParameterScheme
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/AbstractIvyDependencyDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/IvyDependencyDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/ExcludeRuleConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/IvyContextManager
instanceKlass org/gradle/api/internal/artifacts/ImmutableModuleIdentifierFactory
instanceKlass org/gradle/internal/resource/local/FileResourceRepository
instanceKlass org/gradle/internal/resource/ExternalResourceRepository
instanceKlass org/gradle/internal/typeconversion/NotationConverter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/LocalConfigurationMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/LocalComponentMetadataBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DependencyDescriptorFactory
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementGlobalScopeServices
instanceKlass org/gradle/internal/resource/transport/http/HttpClientHelper$Factory
instanceKlass org/gradle/internal/resource/connector/ResourceConnectorFactory
instanceKlass org/gradle/internal/resource/transport/http/SslContextFactory
instanceKlass org/gradle/internal/resource/transport/http/HttpResourcesPluginServiceRegistry$GlobalScopeServices
instanceKlass org/gradle/kotlin/dsl/support/ImplicitImports
instanceKlass org/gradle/kotlin/dsl/support/GlobalServices
instanceKlass org/gradle/internal/build/event/BuildEventServices$1
instanceKlass org/gradle/internal/build/event/BuildEventListenerFactory
instanceKlass org/gradle/internal/operations/BuildOperationListener
instanceKlass org/gradle/initialization/BuildEventConsumer
instanceKlass org/gradle/internal/build/event/DefaultBuildEventsListenerRegistry
instanceKlass org/gradle/internal/build/event/BuildEventListenerRegistryInternal
instanceKlass org/gradle/build/event/BuildEventsListenerRegistry
instanceKlass org/gradle/internal/snapshot/impl/DirectorySnapshotterStatistics$Collector
instanceKlass org/gradle/api/internal/changedetection/state/FileHasherStatistics$Collector
instanceKlass org/gradle/internal/service/scopes/VirtualFileSystemServices$GlobalScopeServices
instanceKlass org/gradle/api/internal/tasks/properties/PropertyWalker
instanceKlass org/gradle/internal/operations/BuildOperationAncestryTracker
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$AnnotationHandlerRegistration
instanceKlass org/gradle/api/internal/tasks/properties/TaskScheme
instanceKlass org/gradle/api/internal/tasks/properties/TypeScheme
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices$AnnotationHandlerRegistar
instanceKlass org/gradle/api/internal/tasks/properties/InspectionSchemeFactory
instanceKlass org/gradle/api/model/ReplacedBy
instanceKlass org/gradle/api/tasks/Internal
instanceKlass org/gradle/api/tasks/OutputFiles
instanceKlass org/gradle/api/tasks/OutputFile
instanceKlass org/gradle/api/tasks/OutputDirectory
instanceKlass org/gradle/api/tasks/OutputDirectories
instanceKlass org/gradle/api/tasks/options/OptionValues
instanceKlass org/gradle/api/tasks/Nested
instanceKlass org/gradle/api/tasks/LocalState
instanceKlass org/gradle/api/tasks/InputFiles
instanceKlass org/gradle/api/tasks/InputFile
instanceKlass org/gradle/api/tasks/InputDirectory
instanceKlass org/gradle/api/artifacts/transform/InputArtifactDependencies
instanceKlass org/gradle/api/artifacts/transform/InputArtifact
instanceKlass org/gradle/api/tasks/Input
instanceKlass org/gradle/api/tasks/Destroys
instanceKlass org/gradle/api/tasks/Console
instanceKlass org/gradle/api/internal/project/taskfactory/TaskClassInfoStore
instanceKlass org/gradle/internal/reflect/annotations/TypeAnnotationMetadataStore
instanceKlass org/gradle/internal/execution/TaskExecutionTracker
instanceKlass org/gradle/api/internal/tasks/properties/annotations/TypeAnnotationHandler
instanceKlass org/gradle/internal/service/scopes/ExecutionGlobalServices
instanceKlass org/gradle/tooling/internal/provider/ExecuteBuildActionRunner
instanceKlass org/gradle/internal/buildtree/BuildActionRunner
instanceKlass org/gradle/tooling/internal/provider/serialization/ClassLoaderCache
instanceKlass org/gradle/internal/filewatch/FileSystemChangeWaiterFactory
instanceKlass org/gradle/tooling/internal/provider/LauncherServices$ToolingGlobalScopeServices
instanceKlass org/gradle/internal/service/DefaultServiceLocator$ServiceFactory
instanceKlass org/gradle/internal/service/scopes/AbstractPluginServiceRegistry
instanceKlass org/gradle/internal/service/scopes/PluginServiceRegistry
instanceKlass java/nio/file/attribute/FileTime
instanceKlass com/google/common/collect/CollectPreconditions
instanceKlass com/google/common/collect/Lists
instanceKlass org/gradle/util/internal/CollectionUtils
instanceKlass org/gradle/api/specs/NotSpec
instanceKlass org/gradle/api/internal/DynamicModulesClassPathProvider$1
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass org/gradle/internal/IoActions
instanceKlass org/gradle/api/Transformer
instanceKlass org/gradle/util/internal/GUtil
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda$6
instanceKlass java/util/NavigableSet
instanceKlass java/util/SortedSet
instanceKlass com/google/common/collect/Sets
instanceKlass groovy/lang/MetaClass
instanceKlass groovy/lang/MetaObjectProtocol
instanceKlass groovy/lang/GroovySystem
instanceKlass groovy/lang/MetaClassRegistry
instanceKlass groovy/lang/GroovyObject
instanceKlass org/objectweb/asm/ClassVisitor
instanceKlass java/util/ComparableTimSort
instanceKlass java/util/Arrays$LegacyMergeSort
instanceKlass org/gradle/internal/util/Trie$Builder
instanceKlass org/gradle/internal/util/Trie
instanceKlass org/gradle/internal/classloader/FilteringClassLoader$TrieSet
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass java/lang/Package$1
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ReflectionPackagesFetcher
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ReflectionClassDefiner
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ClassLoaderPackagesFetcher
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils$ClassDefiner
instanceKlass org/gradle/internal/classloader/ClassLoaderUtils
instanceKlass org/gradle/initialization/GradleApiSpecAggregator$DefaultSpec
instanceKlass kotlin/jvm/internal/Intrinsics
instanceKlass kotlin/collections/SetsKt__SetsJVMKt
instanceKlass com/google/common/collect/PeekingIterator
instanceKlass com/google/common/collect/UnmodifiableIterator
instanceKlass com/google/common/collect/Iterators
instanceKlass com/google/common/collect/Hashing
instanceKlass com/google/common/base/Java8Usage$$Lambda$5
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass com/google/common/base/Java8Usage
instanceKlass com/google/common/base/Preconditions
instanceKlass org/apache/groovy/json/DefaultFastStringServiceFactory
instanceKlass org/apache/groovy/json/FastStringServiceFactory
instanceKlass org/gradle/internal/reflect/ReflectionCache$CacheEntry
instanceKlass com/google/common/math/IntMath$1
instanceKlass com/google/common/math/MathPreconditions
instanceKlass com/google/common/math/IntMath
instanceKlass com/google/common/collect/ImmutableCollection$Builder
instanceKlass com/google/common/collect/ImmutableSet$SetBuilderImpl
instanceKlass org/gradle/kotlin/dsl/provider/KotlinGradleApiSpecProvider
instanceKlass org/gradle/initialization/GradleApiSpecProvider$SpecAdapter
instanceKlass org/gradle/initialization/GradleApiSpecProvider
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/initialization/GradleApiSpecProvider$Spec
instanceKlass org/gradle/initialization/GradleApiSpecAggregator
instanceKlass com/google/common/base/Function
instanceKlass org/gradle/internal/reflect/CachedInvokable
instanceKlass org/gradle/internal/reflect/ReflectionCache
instanceKlass org/gradle/internal/reflect/DirectInstantiator
instanceKlass org/gradle/initialization/DefaultClassLoaderRegistry
instanceKlass org/gradle/internal/installation/GradleRuntimeShadedJarDetector
instanceKlass sun/net/www/protocol/jar/JarFileFactory
instanceKlass sun/net/www/protocol/jar/URLJarFile$URLJarFileCloseController
instanceKlass org/objectweb/asm/Type
instanceKlass org/gradle/initialization/DefaultLegacyTypesSupport
instanceKlass org/gradle/api/internal/DynamicModulesClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/DefaultPluginModuleRegistry
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda$4
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/api/tasks/util/PatternSet
instanceKlass org/gradle/api/tasks/util/PatternFilterable
instanceKlass org/gradle/api/tasks/AntBuilderAware
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspectExtractionStrategy
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractionStrategy
instanceKlass org/gradle/api/internal/tasks/properties/annotations/AbstractOutputPropertyAnnotationHandler
instanceKlass org/gradle/api/internal/tasks/properties/annotations/PropertyAnnotationHandler
instanceKlass org/gradle/internal/instantiation/InjectAnnotationHandler
instanceKlass org/gradle/model/internal/inspect/MethodModelRuleExtractor
instanceKlass org/gradle/internal/event/DefaultListenerManager
instanceKlass org/gradle/api/internal/DocumentationRegistry
instanceKlass org/gradle/api/internal/file/FileLookup
instanceKlass org/gradle/cache/internal/locklistener/DefaultFileLockContentionHandler
instanceKlass org/gradle/cache/internal/locklistener/FileLockContentionHandler
instanceKlass org/gradle/internal/remote/internal/inet/InetAddressFactory
instanceKlass org/gradle/api/internal/file/DefaultFilePropertyFactory
instanceKlass org/gradle/api/internal/file/FileResolver
instanceKlass org/gradle/internal/file/PathToFileResolver
instanceKlass org/gradle/internal/file/RelativeFilePathResolver
instanceKlass org/gradle/api/internal/provider/PropertyHost
instanceKlass org/gradle/internal/state/ManagedFactoryRegistry
instanceKlass org/gradle/api/internal/file/FileFactory
instanceKlass org/gradle/api/internal/tasks/TaskDependencyFactory
instanceKlass org/gradle/internal/operations/BuildOperationProgressEventEmitter
instanceKlass org/gradle/execution/DefaultWorkValidationWarningRecorder
instanceKlass org/gradle/execution/WorkValidationWarningReporter
instanceKlass org/gradle/internal/execution/steps/ValidateStep$ValidationWarningRecorder
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass org/gradle/api/internal/model/NamedObjectInstantiator
instanceKlass org/gradle/internal/state/ManagedFactory
instanceKlass org/gradle/api/internal/file/FilePropertyFactory
instanceKlass org/gradle/api/internal/cache/StringInterner
instanceKlass com/google/common/collect/Interner
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/cache/GlobalCache
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass org/gradle/model/internal/inspect/ModelRuleSourceDetector
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspectExtractor
instanceKlass org/gradle/model/internal/inspect/ModelRuleExtractor
instanceKlass org/gradle/model/internal/manage/instance/ManagedProxyFactory
instanceKlass org/gradle/internal/operations/CurrentBuildOperationRef
instanceKlass org/gradle/internal/instantiation/InstanceGenerator
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/service/scopes/GlobalScopeServices$$Lambda$3
instanceKlass org/gradle/internal/environment/GradleBuildEnvironment
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/operations/BuildOperationListenerManager
instanceKlass org/gradle/internal/service/scopes/GradleUserHomeScopeServiceRegistry
instanceKlass org/gradle/internal/execution/history/changes/ExecutionStateChangeDetector
instanceKlass org/gradle/api/internal/collections/DomainObjectCollectionFactory
instanceKlass org/gradle/internal/resources/ResourceLockCoordinationService
instanceKlass org/gradle/internal/execution/history/OverlappingOutputDetector
instanceKlass org/gradle/api/internal/classpath/PluginModuleRegistry
instanceKlass org/gradle/api/execution/internal/TaskInputsListeners
instanceKlass org/gradle/model/internal/manage/binding/StructBindingsStore
instanceKlass org/gradle/process/internal/health/memory/OsMemoryInfo
instanceKlass org/gradle/api/tasks/util/internal/PatternSpecFactory
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/cache/internal/InMemoryCacheDecoratorFactory
instanceKlass org/gradle/api/model/ObjectFactory
instanceKlass org/gradle/internal/reflect/Instantiator
instanceKlass org/gradle/initialization/ClassLoaderRegistry
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/process/internal/health/memory/JvmMemoryInfo
instanceKlass org/gradle/model/internal/manage/schema/ModelSchemaStore
instanceKlass org/gradle/internal/filewatch/FileWatcherFactory
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractor
instanceKlass org/gradle/internal/instantiation/InstantiatorFactory
instanceKlass org/gradle/internal/instantiation/PropertyRoleAnnotationHandler
instanceKlass org/gradle/configuration/ImportsReader
instanceKlass org/gradle/initialization/JdkToolsInitializer
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/process/internal/health/memory/MemoryManager
instanceKlass org/gradle/cache/internal/CrossBuildInMemoryCacheFactory
instanceKlass org/gradle/api/internal/provider/PropertyFactory
instanceKlass org/gradle/internal/hash/StreamHasher
instanceKlass org/gradle/internal/logging/progress/ProgressLoggerFactory
instanceKlass org/gradle/internal/logging/progress/ProgressListener
instanceKlass org/gradle/initialization/LegacyTypesSupport
instanceKlass org/gradle/cache/internal/CacheFactory
instanceKlass org/gradle/internal/operations/BuildOperationIdFactory
instanceKlass org/gradle/internal/file/Deleter
instanceKlass org/gradle/process/internal/ExecFactory
instanceKlass org/gradle/api/internal/ProcessOperations
instanceKlass org/gradle/process/internal/JavaForkOptionsFactory
instanceKlass org/gradle/process/internal/JavaExecHandleFactory
instanceKlass org/gradle/process/internal/ExecHandleFactory
instanceKlass org/gradle/process/internal/ExecActionFactory
instanceKlass org/gradle/internal/jvm/inspection/JvmVersionDetector
instanceKlass org/gradle/internal/jvm/inspection/JvmMetadataDetector
instanceKlass org/gradle/cache/internal/ProcessMetaDataProvider
instanceKlass org/gradle/api/internal/file/FileCollectionFactory
instanceKlass org/gradle/api/internal/file/collections/DirectoryFileTreeFactory
instanceKlass org/gradle/internal/service/scopes/BasicGlobalScopeServices
instanceKlass org/gradle/cache/FileLockManager
instanceKlass org/gradle/launcher/daemon/registry/DaemonDir
instanceKlass org/gradle/internal/concurrent/Synchronizer
instanceKlass org/gradle/cache/internal/CacheSupport
instanceKlass org/gradle/cache/internal/CacheAccessSerializer
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistry
instanceKlass org/gradle/cache/Cache
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistryServices
instanceKlass org/gradle/internal/invocation/BuildAction
instanceKlass sun/reflect/generics/tree/TypeVariableSignature
instanceKlass org/gradle/launcher/daemon/server/api/DaemonCommandAction
instanceKlass sun/reflect/generics/tree/MethodTypeSignature
instanceKlass org/gradle/launcher/daemon/server/MasterExpirationStrategy
instanceKlass org/gradle/internal/concurrent/ExecutorFactory
instanceKlass org/gradle/launcher/daemon/server/health/HealthExpirationStrategy
instanceKlass org/gradle/launcher/daemon/server/health/DaemonMemoryStatus
instanceKlass org/gradle/launcher/exec/BuildExecuter
instanceKlass org/gradle/launcher/daemon/server/health/DaemonHealthCheck
instanceKlass org/gradle/launcher/daemon/server/health/DaemonHealthStats
instanceKlass org/gradle/launcher/daemon/context/DaemonContext
instanceKlass org/gradle/internal/event/ListenerManager
instanceKlass org/gradle/launcher/daemon/server/stats/DaemonRunningStats
instanceKlass org/gradle/launcher/daemon/server/Daemon
instanceKlass org/gradle/internal/serialize/Serializer
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$CompositeServiceProvider
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ParentServices
instanceKlass org/gradle/api/specs/Spec
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/launcher/daemon/server/expiry/DaemonExpirationStrategy
instanceKlass org/gradle/launcher/exec/BuildActionExecuter
instanceKlass org/gradle/launcher/daemon/server/scaninfo/DaemonScanInfo
instanceKlass org/gradle/launcher/daemon/server/DaemonServerConnector
instanceKlass java/lang/reflect/WildcardType
instanceKlass sun/reflect/generics/reflectiveObjects/LazyReflectiveObjectGenerator
instanceKlass java/lang/reflect/TypeVariable
instanceKlass sun/reflect/generics/reflectiveObjects/ParameterizedTypeImpl
instanceKlass sun/reflect/generics/visitor/Reifier
instanceKlass sun/reflect/generics/visitor/TypeTreeVisitor
instanceKlass sun/reflect/generics/tree/ClassSignature
instanceKlass sun/reflect/generics/tree/Signature
instanceKlass sun/reflect/generics/tree/ClassTypeSignature
instanceKlass sun/reflect/generics/tree/SimpleClassTypeSignature
instanceKlass sun/reflect/generics/tree/FieldTypeSignature
instanceKlass sun/reflect/generics/tree/BaseType
instanceKlass sun/reflect/generics/tree/TypeSignature
instanceKlass sun/reflect/generics/tree/ReturnType
instanceKlass sun/reflect/generics/tree/TypeArgument
instanceKlass sun/reflect/generics/tree/FormalTypeParameter
instanceKlass sun/reflect/generics/tree/TypeTree
instanceKlass sun/reflect/generics/tree/Tree
instanceKlass sun/reflect/generics/parser/SignatureParser
instanceKlass sun/reflect/generics/factory/CoreReflectionFactory
instanceKlass sun/reflect/generics/factory/GenericsFactory
instanceKlass sun/reflect/generics/scope/AbstractScope
instanceKlass sun/reflect/generics/scope/Scope
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager$StartableLoggingSystem
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager$StartableLoggingRouter
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManager
instanceKlass org/gradle/internal/logging/source/JavaUtilLoggingSystem
instanceKlass org/gradle/internal/logging/slf4j/Slf4jLoggingConfigurer
instanceKlass org/gradle/internal/logging/config/LoggingSystemAdapter
instanceKlass org/gradle/internal/logging/LoggingManagerInternal
instanceKlass org/gradle/internal/logging/StandardOutputCapture
instanceKlass org/gradle/api/logging/LoggingManager
instanceKlass org/gradle/internal/logging/source/StdErrLoggingSystem
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$SnapshotImpl
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$OutputEventDestination
instanceKlass org/gradle/internal/SystemProperties
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem$1
instanceKlass org/gradle/internal/service/AnnotatedServiceLifecycleHandler
instanceKlass org/gradle/internal/logging/events/operations/StyledTextBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/StyledTextBuildOperationProgressDetails
instanceKlass org/gradle/internal/io/TextStream
instanceKlass org/gradle/internal/logging/source/PrintStreamLoggingSystem
instanceKlass org/gradle/internal/logging/source/StdOutLoggingSystem
instanceKlass java/lang/reflect/ParameterizedType
instanceKlass org/gradle/internal/logging/sink/OutputEventListenerManager$1
instanceKlass org/gradle/internal/logging/services/TextStreamOutputEventListener
instanceKlass org/gradle/internal/logging/services/DefaultLoggingManagerFactory
instanceKlass org/gradle/internal/logging/sink/OutputEventListenerManager
instanceKlass org/gradle/internal/logging/services/LoggingServiceRegistry$1
instanceKlass org/gradle/internal/logging/config/LoggingConfigurer
instanceKlass org/gradle/internal/logging/config/LoggingSourceSystem
instanceKlass org/gradle/launcher/daemon/configuration/DefaultDaemonServerConfiguration
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiStorage
instanceKlass org/fusesource/jansi/Ansi
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibrary
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibraryFactory$1
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions
instanceKlass java/util/logging/LogManager$5
instanceKlass java/util/logging/LoggingProxyImpl
instanceKlass sun/util/logging/LoggingProxy
instanceKlass sun/util/logging/LoggingSupport$1
instanceKlass sun/util/logging/LoggingSupport
instanceKlass sun/util/logging/PlatformLogger$LoggerProxy
instanceKlass sun/util/logging/PlatformLogger$1
instanceKlass sun/util/logging/PlatformLogger
instanceKlass java/util/logging/LogManager$LoggerContext$1
instanceKlass java/util/logging/LogManager$3
instanceKlass java/util/logging/LogManager$2
instanceKlass java/lang/Shutdown$Lock
instanceKlass java/lang/Shutdown
instanceKlass java/lang/ApplicationShutdownHooks$1
instanceKlass java/lang/ApplicationShutdownHooks
instanceKlass java/util/logging/LogManager$LogNode
instanceKlass java/util/logging/LogManager$LoggerContext
instanceKlass java/util/logging/LogManager$1
instanceKlass java/util/logging/LogManager
instanceKlass java/util/logging/Logger$LoggerBundle
instanceKlass java/util/logging/Level$KnownLevel
instanceKlass java/util/logging/Level
instanceKlass java/util/logging/Handler
instanceKlass java/util/logging/Logger
instanceKlass net/rubygrapefruit/platform/internal/jni/NativeLogger
instanceKlass net/rubygrapefruit/platform/file/FileEvents
instanceKlass java/util/RegularEnumSet$EnumSetIterator
instanceKlass net/rubygrapefruit/platform/internal/jni/NativeLibraryFunctions
instanceKlass java/io/RandomAccessFile$1
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel$1
instanceKlass sun/nio/ch/Interruptible
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/ch/NativeThread
instanceKlass java/nio/channels/FileLock
instanceKlass sun/nio/ch/FileDispatcherImpl$1
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass java/net/Inet6Address$Inet6AddressHolder
instanceKlass java/net/InetAddress$2
instanceKlass sun/net/spi/nameservice/NameService
instanceKlass java/net/Inet6AddressImpl
instanceKlass java/net/InetAddressImpl
instanceKlass java/net/InetAddressImplFactory
instanceKlass java/net/InetAddress$Cache
instanceKlass java/net/InetAddress$InetAddressHolder
instanceKlass java/net/InetAddress$1
instanceKlass java/net/InetAddress
instanceKlass sun/nio/ch/IOUtil$1
instanceKlass sun/nio/ch/IOUtil
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass java/io/RandomAccessFile
instanceKlass java/util/Formattable
instanceKlass java/util/Formatter$Conversion
instanceKlass java/util/Formatter$Flags
instanceKlass java/util/Formatter$FormatSpecifier
instanceKlass java/util/Formatter$FixedString
instanceKlass java/util/Formatter$FormatString
instanceKlass java/util/Currency$CurrencyNameGetter
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool$LocalizedObjectGetter
instanceKlass sun/util/locale/provider/SPILocaleProviderAdapter$1
instanceKlass sun/util/locale/provider/LocaleServiceProviderPool
instanceKlass java/io/DataInput
instanceKlass java/util/Currency$1
instanceKlass java/util/Currency
instanceKlass java/util/concurrent/atomic/AtomicMarkableReference$Pair
instanceKlass java/util/concurrent/atomic/AtomicMarkableReference
instanceKlass java/util/ResourceBundle$CacheKeyReference
instanceKlass java/util/ResourceBundle$CacheKey
instanceKlass java/util/ResourceBundle$RBClassLoader$1
instanceKlass java/util/ServiceLoader$1
instanceKlass java/util/ServiceLoader$LazyIterator
instanceKlass java/util/ServiceLoader
instanceKlass java/util/spi/ResourceBundleControlProvider
instanceKlass java/util/ResourceBundle
instanceKlass java/util/ResourceBundle$Control
instanceKlass sun/util/resources/LocaleData$1
instanceKlass sun/util/resources/LocaleData
instanceKlass sun/util/locale/provider/LocaleResources
instanceKlass sun/util/locale/LanguageTag
instanceKlass sun/util/locale/provider/JRELocaleProviderAdapter$1
instanceKlass sun/util/locale/provider/LocaleDataMetaInfo
instanceKlass sun/util/locale/provider/AvailableLanguageTags
instanceKlass sun/util/locale/provider/LocaleProviderAdapter$1
instanceKlass sun/util/locale/provider/ResourceBundleBasedAdapter
instanceKlass sun/util/locale/provider/LocaleProviderAdapter
instanceKlass java/util/spi/LocaleServiceProvider
instanceKlass java/text/DecimalFormatSymbols
instanceKlass java/util/Locale$1
instanceKlass java/util/Formatter
instanceKlass net/rubygrapefruit/platform/internal/LibraryDef
instanceKlass net/rubygrapefruit/platform/internal/NativeLibraryLocator
instanceKlass net/rubygrapefruit/platform/internal/NativeLibraryLoader
instanceKlass net/rubygrapefruit/platform/Process
instanceKlass net/rubygrapefruit/platform/internal/Platform
instanceKlass net/rubygrapefruit/platform/Native
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/gradle/api/internal/file/temp/DefaultTemporaryFileProvider
instanceKlass org/gradle/internal/nativeintegration/services/NativeServices$1
instanceKlass org/gradle/internal/file/StatStatistics
instanceKlass org/gradle/internal/file/StatStatistics$Collector
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/GenericFileSystem
instanceKlass org/gradle/internal/service/InjectUtil
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/MethodHandleImpl$ArrayAccessor
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$1
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/GenericFileSystem$Factory
instanceKlass org/gradle/api/internal/file/temp/TemporaryFileProvider
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileCanonicalizer
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileSystem
instanceKlass org/gradle/internal/file/Stat
instanceKlass org/gradle/internal/file/Chmod
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileModeMutator
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileModeAccessor
instanceKlass org/gradle/internal/nativeintegration/filesystem/Symlink
instanceKlass org/gradle/internal/nativeintegration/filesystem/services/FileSystemServices
instanceKlass org/gradle/internal/nativeintegration/jansi/DefaultJansiRuntimeResolver
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiRuntimeResolver
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiLibraryFactory
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiStorageLocator
instanceKlass org/gradle/internal/nativeintegration/jansi/JansiBootPathConfigurer
instanceKlass java/lang/Class$4
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ClassInspector$ClassDetails
instanceKlass java/util/concurrent/ConcurrentLinkedQueue$Node
instanceKlass org/gradle/internal/reflect/JavaMethod
instanceKlass org/gradle/internal/service/AbstractServiceMethod
instanceKlass java/util/LinkedList$ListItr
instanceKlass net/rubygrapefruit/platform/SystemInfo
instanceKlass net/rubygrapefruit/platform/file/PosixFiles
instanceKlass net/rubygrapefruit/platform/file/Files
instanceKlass org/gradle/internal/jvm/Jvm
instanceKlass org/gradle/internal/jvm/JavaInfo
instanceKlass net/rubygrapefruit/platform/memory/Memory
instanceKlass net/rubygrapefruit/platform/file/FileSystems
instanceKlass net/rubygrapefruit/platform/WindowsRegistry
instanceKlass org/gradle/internal/os/OperatingSystem
instanceKlass org/gradle/internal/service/RelevantMethodsBuilder
instanceKlass org/gradle/internal/Cast
instanceKlass org/gradle/internal/service/ServiceMethod
instanceKlass org/gradle/internal/service/MethodHandleBasedServiceMethodFactory
instanceKlass org/gradle/internal/service/DefaultServiceMethodFactory
instanceKlass org/gradle/internal/service/ServiceMethodFactory
instanceKlass org/gradle/internal/service/RelevantMethods
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ClassInspector
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ThisAsService
instanceKlass org/gradle/internal/concurrent/CompositeStoppable$1
instanceKlass org/gradle/internal/concurrent/CompositeStoppable
instanceKlass org/gradle/internal/service/AnnotatedServiceLifecycleHandler$Registration
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$OwnServices
instanceKlass org/gradle/internal/nativeintegration/ProcessEnvironment
instanceKlass org/gradle/initialization/GradleUserHomeDirProvider
instanceKlass net/rubygrapefruit/platform/ProcessLauncher
instanceKlass net/rubygrapefruit/platform/NativeIntegration
instanceKlass org/gradle/internal/nativeintegration/filesystem/FileMetadataAccessor
instanceKlass org/gradle/internal/nativeintegration/NativeCapabilities
instanceKlass org/gradle/internal/nativeintegration/network/HostnameLookup
instanceKlass org/gradle/internal/nativeintegration/console/ConsoleDetector
instanceKlass org/gradle/internal/service/ServiceRegistration
instanceKlass org/gradle/internal/service/ServiceProvider$Visitor
instanceKlass org/gradle/internal/service/DefaultServiceRegistry$ManagedObjectServiceProvider
instanceKlass org/gradle/internal/service/Service
instanceKlass org/gradle/internal/service/ServiceProvider
instanceKlass org/gradle/internal/concurrent/Stoppable
instanceKlass org/gradle/internal/service/DefaultServiceRegistry
instanceKlass org/gradle/internal/service/ContainsServices
instanceKlass org/gradle/internal/serialize/AbstractDecoder
instanceKlass org/gradle/internal/serialize/Decoder
instanceKlass org/gradle/launcher/bootstrap/EntryPoint$RecordingExecutionListener
instanceKlass org/gradle/internal/logging/events/operations/LogEventBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/LogEventBuildOperationProgressDetails
instanceKlass org/gradle/internal/logging/slf4j/BuildOperationAwareLogger
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$2
instanceKlass org/gradle/internal/dispatch/ReflectionDispatch
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$1
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer$LazyListener
instanceKlass org/gradle/internal/logging/events/OutputEventListener$1
instanceKlass java/lang/reflect/WeakCache$Value
instanceKlass sun/misc/ProxyGenerator$ExceptionTableEntry
instanceKlass sun/misc/ProxyGenerator$PrimitiveTypeInfo
instanceKlass sun/misc/ProxyGenerator$FieldInfo
instanceKlass java/io/DataOutput
instanceKlass sun/misc/ProxyGenerator$ConstantPool$Entry
instanceKlass sun/misc/ProxyGenerator$MethodInfo
instanceKlass sun/misc/ProxyGenerator$ProxyMethod
instanceKlass sun/misc/ProxyGenerator$ConstantPool
instanceKlass sun/misc/ProxyGenerator
instanceKlass java/lang/reflect/WeakCache$Factory
instanceKlass java/util/function/Supplier
instanceKlass java/lang/reflect/Proxy$ProxyClassFactory
instanceKlass java/lang/reflect/Proxy$KeyFactory
instanceKlass java/util/function/BiFunction
instanceKlass java/lang/reflect/WeakCache
instanceKlass java/lang/reflect/Proxy
instanceKlass org/gradle/internal/dispatch/ProxyDispatchAdapter$DispatchingInvocationHandler
instanceKlass java/lang/reflect/InvocationHandler
instanceKlass org/gradle/internal/dispatch/ProxyDispatchAdapter
instanceKlass org/gradle/internal/logging/events/operations/ProgressStartBuildOperationProgressDetails
instanceKlass org/gradle/internal/operations/logging/ProgressStartBuildOperationProgressDetails
instanceKlass org/gradle/internal/logging/sink/OutputEventTransformer
instanceKlass org/gradle/internal/exceptions/MultiCauseException
instanceKlass org/gradle/internal/event/AbstractBroadcastDispatch
instanceKlass org/gradle/internal/event/ListenerBroadcast
instanceKlass org/gradle/internal/dispatch/Dispatch
instanceKlass org/gradle/internal/Factory
instanceKlass org/gradle/internal/logging/format/LogHeaderFormatter
instanceKlass org/gradle/internal/nativeintegration/console/ConsoleMetaData
instanceKlass org/gradle/internal/logging/console/ColorMap
instanceKlass org/gradle/internal/logging/config/LoggingSystem$Snapshot
instanceKlass org/gradle/internal/logging/text/StyledTextOutput
instanceKlass org/gradle/api/logging/StandardOutputListener
instanceKlass org/gradle/internal/logging/events/OutputEvent
instanceKlass org/gradle/internal/logging/sink/OutputEventRenderer
instanceKlass org/gradle/internal/logging/config/LoggingRouter
instanceKlass org/gradle/internal/logging/LoggingOutputInternal
instanceKlass org/gradle/api/logging/LoggingOutput
instanceKlass org/gradle/internal/logging/config/LoggingSystem
instanceKlass org/gradle/internal/logging/slf4j/OutputEventListenerBackedLoggerContext$NoOpLogger
instanceKlass org/gradle/api/logging/Logger
instanceKlass java/util/concurrent/atomic/AtomicReference
instanceKlass org/gradle/internal/time/TimeSource$1
instanceKlass org/gradle/internal/time/TimeSource
instanceKlass org/gradle/internal/time/MonotonicClock
instanceKlass org/gradle/internal/time/CountdownTimer
instanceKlass org/gradle/internal/time/Timer
instanceKlass org/gradle/internal/time/Clock
instanceKlass org/gradle/internal/time/Time
instanceKlass org/gradle/internal/logging/events/OutputEventListener
instanceKlass org/gradle/internal/logging/slf4j/OutputEventListenerBackedLoggerContext
instanceKlass org/slf4j/impl/StaticLoggerBinder
instanceKlass org/slf4j/spi/LoggerFactoryBinder
instanceKlass java/net/URLClassLoader$3$1
instanceKlass sun/misc/CompoundEnumeration
instanceKlass java/net/URLClassLoader$3
instanceKlass sun/misc/URLClassPath$1
instanceKlass java/lang/ClassLoader$2
instanceKlass sun/misc/URLClassPath$2
instanceKlass org/slf4j/helpers/Util
instanceKlass org/slf4j/helpers/NOPLoggerFactory
instanceKlass java/util/concurrent/LinkedBlockingQueue$Node
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$ConditionObject
instanceKlass java/util/concurrent/locks/Condition
instanceKlass java/util/concurrent/BlockingQueue
instanceKlass org/slf4j/Logger
instanceKlass org/slf4j/helpers/SubstituteLoggerFactory
instanceKlass org/slf4j/ILoggerFactory
instanceKlass org/slf4j/event/LoggingEvent
instanceKlass org/slf4j/LoggerFactory
instanceKlass java/util/concurrent/locks/AbstractQueuedSynchronizer$Node
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass org/slf4j/helpers/BasicMarker
instanceKlass org/slf4j/Marker
instanceKlass org/slf4j/helpers/BasicMarkerFactory
instanceKlass org/slf4j/IMarkerFactory
instanceKlass org/slf4j/MarkerFactory
instanceKlass org/gradle/api/logging/Logging
instanceKlass org/gradle/launcher/daemon/configuration/DaemonServerConfiguration
instanceKlass org/gradle/internal/service/ServiceRegistry
instanceKlass org/gradle/internal/service/ServiceLookup
instanceKlass org/gradle/launcher/bootstrap/ExecutionCompleter
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/logging/text/StyledTextOutputFactory
instanceKlass org/gradle/api/logging/configuration/LoggingConfiguration
instanceKlass org/gradle/initialization/BuildClientMetaData
instanceKlass org/gradle/launcher/bootstrap/ExecutionListener
instanceKlass org/gradle/launcher/bootstrap/EntryPoint
instanceKlass java/util/LinkedList$Node
instanceKlass java/net/URLClassLoader$2
instanceKlass sun/misc/Launcher$BootClassPathHolder$1
instanceKlass sun/misc/Launcher$BootClassPathHolder
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/IoActions
instanceKlass org/gradle/api/Transformer
instanceKlass org/gradle/util/internal/GUtil
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda$2
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass java/util/Collections$1
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/internal/classloader/DefaultClassLoaderFactory
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/Cast
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass java/util/Collections$EmptyIterator
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass java/io/FileFilter
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass java/net/URI$Parser
instanceKlass java/net/URI
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/InnerClassLambdaMetafactory$1
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$$Lambda$1
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass sun/security/action/GetBooleanAction
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/security/AccessController$1
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass sun/reflect/UnsafeFieldAccessorFactory
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/lang/invoke/BoundMethodHandle$Factory$1
instanceKlass java/lang/invoke/BoundMethodHandle$SpeciesData$1
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/MethodHandleImpl$Lazy
instanceKlass java/lang/invoke/LambdaForm$BMH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass org/gradle/api/specs/Spec
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/SubList$1
instanceKlass java/util/ArrayList$Itr
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/LambdaForm$MH
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$CpPatch
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/lang/invoke/LambdaForm$DMH
instanceKlass java/util/HashMap$HashIterator
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$2
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass jdk/internal/org/objectweb/asm/Item
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass java/lang/invoke/DirectMethodHandle$Lazy
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass java/lang/invoke/BoundMethodHandle$Factory
instanceKlass java/lang/invoke/BoundMethodHandle$SpeciesData
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass sun/invoke/util/ValueConversions
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/lang/Long$LongCache
instanceKlass java/lang/Character$CharacterCache
instanceKlass java/lang/Short$ShortCache
instanceKlass java/lang/Byte$ByteCache
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodHandles
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/cache/GlobalCache
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/launcher/bootstrap/ProcessBootstrap
instanceKlass java/lang/Void
instanceKlass java/lang/Class$MethodArray
instanceKlass sun/launcher/LauncherHelper$FXHelper
instanceKlass org/gradle/launcher/daemon/bootstrap/GradleDaemon
instanceKlass java/io/FilePermission$1
instanceKlass sun/net/www/MessageHeader
instanceKlass java/net/URLConnection
instanceKlass java/security/PermissionCollection
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass sun/security/util/DisabledAlgorithmConstraints$1
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraint
instanceKlass java/util/AbstractList$Itr
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass sun/security/util/DisabledAlgorithmConstraints$Constraints
instanceKlass java/util/ArrayList$SubList$1
instanceKlass java/util/ListIterator
instanceKlass java/util/Properties$LineReader
instanceKlass java/security/Security$1
instanceKlass java/security/Security
instanceKlass sun/security/util/AbstractAlgorithmConstraints$1
instanceKlass java/util/regex/ASCII
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass sun/security/util/AlgorithmDecomposer
instanceKlass sun/security/util/AbstractAlgorithmConstraints
instanceKlass java/security/AlgorithmConstraints
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass sun/misc/Resource
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass java/util/jar/JarVerifier$3
instanceKlass java/security/CodeSigner
instanceKlass java/util/jar/JarVerifier
instanceKlass sun/misc/IOUtils
instanceKlass java/util/zip/ZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass sun/misc/ExtensionDependency
instanceKlass sun/misc/JarIndex
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass sun/misc/PerfCounter$CoreCounters
instanceKlass sun/misc/Perf
instanceKlass sun/misc/Perf$GetPerfAction
instanceKlass sun/misc/PerfCounter
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass java/nio/charset/StandardCharsets
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass sun/misc/JavaUtilJarAccess
instanceKlass sun/misc/FileURLMapper
instanceKlass sun/misc/URLClassPath$JarLoader$1
instanceKlass sun/nio/cs/ThreadLocalCoders$Cache
instanceKlass sun/nio/cs/ThreadLocalCoders
instanceKlass java/util/zip/ZipFile$1
instanceKlass sun/misc/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass sun/misc/URLClassPath$Loader
instanceKlass sun/misc/URLClassPath$3
instanceKlass sun/net/util/URLUtil
instanceKlass java/net/URLClassLoader$1
instanceKlass java/io/FileOutputStream$1
instanceKlass sun/usagetracker/UsageTrackerClient$3
instanceKlass sun/usagetracker/UsageTrackerClient$2
instanceKlass sun/usagetracker/UsageTrackerClient$4
instanceKlass sun/usagetracker/UsageTrackerClient$1
instanceKlass java/util/concurrent/atomic/AtomicBoolean
instanceKlass sun/usagetracker/UsageTrackerClient
instanceKlass sun/misc/PostVMInitHook$1
instanceKlass jdk/internal/util/EnvUtils
instanceKlass sun/misc/PostVMInitHook$2
instanceKlass sun/misc/PostVMInitHook
instanceKlass java/lang/invoke/MethodHandleStatics$1
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/MethodHandleImpl$3
instanceKlass java/lang/invoke/MethodHandleImpl$2
instanceKlass java/util/function/Function
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass java/lang/SystemClassLoaderAction
instanceKlass sun/misc/Launcher$AppClassLoader$1
instanceKlass sun/misc/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$2
instanceKlass sun/misc/JavaSecurityProtectionDomainAccess
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass sun/misc/JavaSecurityAccess
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass java/net/Parts
instanceKlass java/util/BitSet
instanceKlass sun/net/www/ParseUtil
instanceKlass java/io/FileInputStream$1
instanceKlass java/lang/CharacterData
instanceKlass sun/util/locale/LocaleUtils
instanceKlass java/util/Locale$LocaleKey
instanceKlass sun/util/locale/BaseLocale$Key
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass sun/util/locale/LocaleObjectCache
instanceKlass java/util/Locale
instanceKlass java/lang/reflect/Array
instanceKlass java/io/Reader
instanceKlass sun/misc/MetaIndex
instanceKlass java/util/StringTokenizer
instanceKlass sun/misc/Launcher$ExtClassLoader$1
instanceKlass java/net/URLClassLoader$7
instanceKlass sun/misc/JavaNetAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass sun/security/util/Debug
instanceKlass sun/misc/Launcher$Factory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass java/lang/Compiler$1
instanceKlass java/lang/Compiler
instanceKlass java/lang/System$2
instanceKlass sun/misc/JavaLangAccess
instanceKlass sun/io/Win32ErrorMode
instanceKlass sun/misc/OSEnvironment
instanceKlass java/lang/Integer$IntegerCache
instanceKlass sun/misc/NativeSignalHandler
instanceKlass sun/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass sun/misc/SignalHandler
instanceKlass java/lang/Terminator
instanceKlass java/lang/ClassLoader$NativeLibrary
instanceKlass java/io/ExpiringCache$Entry
instanceKlass java/lang/ClassLoader$3
instanceKlass java/nio/charset/CoderResult$Cache
instanceKlass java/nio/charset/CoderResult
instanceKlass java/lang/Readable
instanceKlass java/lang/StringCoding$StringEncoder
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/lang/Enum
instanceKlass java/io/ExpiringCache
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/nio/Bits$1
instanceKlass sun/misc/JavaNioAccess
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Bits
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/io/Writer
instanceKlass sun/reflect/misc/ReflectUtil
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass sun/misc/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass sun/misc/Version
instanceKlass java/lang/Runtime
instanceKlass java/util/Hashtable$Enumerator
instanceKlass java/util/Iterator
instanceKlass java/util/Enumeration
instanceKlass java/util/Objects
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass sun/nio/cs/ext/DelegatableDecoder
instanceKlass sun/nio/cs/ext/DoubleByte
instanceKlass java/lang/StringCoding$StringDecoder
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass java/lang/StringCoding
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass java/util/TreeMap$Entry
instanceKlass sun/misc/ASCIICaseInsensitiveComparator
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass sun/reflect/ReflectionFactory$1
instanceKlass java/lang/Class$1
instanceKlass java/nio/charset/Charset$ExtendedProviderHolder$1
instanceKlass java/nio/charset/Charset$ExtendedProviderHolder
instanceKlass java/util/Arrays
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass sun/reflect/LangReflectAccess
instanceKlass java/lang/reflect/Modifier
instanceKlass sun/reflect/annotation/AnnotationType
instanceKlass java/lang/Class$AnnotationData
instanceKlass sun/reflect/generics/repository/AbstractRepository
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass java/lang/Class$3
instanceKlass java/lang/ThreadLocal
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/lang/Math
instanceKlass java/util/Hashtable$Entry
instanceKlass sun/misc/VM
instanceKlass java/util/HashMap$Node
instanceKlass java/util/Map$Entry
instanceKlass sun/reflect/Reflection
instanceKlass sun/misc/SharedSecrets
instanceKlass java/lang/ref/Reference$1
instanceKlass sun/misc/JavaLangRefAccess
instanceKlass java/lang/ref/ReferenceQueue$Lock
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/AbstractMap
instanceKlass java/util/Set
instanceKlass java/util/Collections
instanceKlass java/lang/ref/Reference$Lock
instanceKlass sun/reflect/ReflectionFactory
instanceKlass java/util/AbstractCollection
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/security/cert/Certificate
instanceKlass sun/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass java/security/AccessController
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/security/CodeSource
instanceKlass sun/misc/Launcher
instanceKlass java/util/jar/Manifest
instanceKlass java/net/URL
instanceKlass java/io/File
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass sun/misc/Unsafe
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/MethodHandle
instanceKlass sun/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass sun/reflect/FieldAccessor
instanceKlass sun/reflect/ConstantPool
instanceKlass sun/reflect/ConstructorAccessor
instanceKlass sun/reflect/MethodAccessor
instanceKlass sun/reflect/MagicAccessorImpl
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/util/Dictionary
instanceKlass java/util/Map
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass java/lang/ref/Reference
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 78 3 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 100 7 7 7 7 7 1 1 1 12 12 12 12 12 12 12 12 12 12 10 10 10 10 10 10 10 10 10 10 1
ciInstanceKlass java/io/Serializable 1 0 7 1 1 1 100 100 1
ciInstanceKlass java/lang/String 1 1 540 3 3 3 3 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 7 7 100 100 100 7 7 100 100 7 100 100 100 7 100 100 7 100 7 7 100 7 100 100 7 100 7 100 100 7 7 7 7 100 7 7 100 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 1 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/lang/Class 1 1 1190 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 5 0 8 8 8 8 8 7 7 7 100 100 100 7 7 100 7 100 7 7 7 7 7 7 7 100 7 100 100 100 7 100 100 100 100 100 100 100 7 7 7 100 100 100 7 7 7 100 100 7 7 100 100 7 7 7 100 100 7 7 100 100 100 7 7 7 100 100 7 100 7 7 100 7 7 7 7 100 100 7 7 7 7 100 7 100 7 7 100 7 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 1 1 1 1 1 1 1 1
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/Cloneable 1 0 7 1 1 1 100 100 1
instanceKlass org/gradle/internal/classloader/CachingClassLoader
instanceKlass org/gradle/internal/classloader/MultiParentClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader$RetrieveSystemPackagesClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass java/util/ResourceBundle$RBClassLoader
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass sun/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 825 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 100 7 7 7 7 100 100 100 7 7 100 7 7 7 7 100 7 100 7 100 100 7 7 100 100 7 100 7 7 100 100 100 100 7 100 100 7 7 100 7 7 7 100 7 7 7 7 7 7 7 7 7 7 7 7 7 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
ciInstanceKlass java/lang/System 1 1 369 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 7 7 7 100 7 100 100 100 100 100 100 7 7 100 100 7 100 100 7 7 7 7 100 100 100 7 100 100 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/PipedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; org/gradle/internal/io/LinePerThreadBufferingOutputStream
staticfield java/lang/System err Ljava/io/PrintStream; org/gradle/internal/io/LinePerThreadBufferingOutputStream
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 353 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 100 100 100 7 100 100 100 100 100 100 7 100 7 100 100 100 100 100 100 100 100 100 7 7 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$UnmodifiableRandomAccessList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass kotlin/jvm/KotlinReflectionNotSupportedError
instanceKlass java/util/ServiceConfigurationError
instanceKlass com/google/common/util/concurrent/ExecutionError
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
instanceKlass java/lang/ThreadDeath
ciInstanceKlass java/lang/Error 1 1 30 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 1 1 12 12 12 12 12 10 10 10 10 10 1
ciInstanceKlass java/lang/ThreadDeath 0 0 18 1 1 1 1 1 1 1 1 1 1 5 0 100 100 12 10 1
instanceKlass org/apache/maven/settings/building/SettingsBuildingException
instanceKlass com/jcraft/jsch/JSchException
instanceKlass javax/xml/xpath/XPathException
instanceKlass org/xml/sax/SAXException
instanceKlass javax/xml/parsers/ParserConfigurationException
instanceKlass org/gradle/api/internal/attributes/AttributeMergingException
instanceKlass java/lang/CloneNotSupportedException
instanceKlass java/security/GeneralSecurityException
instanceKlass java/util/concurrent/TimeoutException
instanceKlass java/util/concurrent/ExecutionException
instanceKlass java/text/ParseException
instanceKlass java/security/PrivilegedActionException
instanceKlass java/net/URISyntaxException
instanceKlass java/io/IOException
instanceKlass java/lang/InterruptedException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 30 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 1 1 12 12 12 12 12 10 10 10 10 10 1
instanceKlass groovy/lang/StringWriterIOException
instanceKlass java/lang/reflect/MalformedParameterizedTypeException
instanceKlass org/gradle/internal/execution/fingerprint/InputFingerprinter$InputFileFingerprintingException
instanceKlass org/gradle/internal/execution/OutputSnapshotter$OutputFileSnapshottingException
instanceKlass org/gradle/api/internal/attributes/AttributeMatchException
instanceKlass org/gradle/internal/locking/MissingLockStateException
instanceKlass org/gradle/internal/locking/InvalidLockFileException
instanceKlass org/gradle/api/internal/provider/AbstractProperty$PropertyQueryException
instanceKlass org/gradle/cli/CommandLineArgumentException
instanceKlass groovy/lang/GroovyRuntimeException
instanceKlass kotlin/NoWhenBranchMatchedException
instanceKlass java/util/ConcurrentModificationException
instanceKlass org/gradle/internal/reflect/NoSuchPropertyException
instanceKlass org/gradle/internal/snapshot/impl/IsolationException
instanceKlass org/gradle/internal/snapshot/ValueSnapshottingException
instanceKlass org/apache/tools/ant/BuildException
instanceKlass java/io/UncheckedIOException
instanceKlass org/gradle/tooling/internal/protocol/InternalBuildActionFailureException
instanceKlass org/gradle/tooling/internal/protocol/test/InternalTestExecutionException
instanceKlass org/gradle/internal/typeconversion/TypeConversionException
instanceKlass com/google/common/util/concurrent/UncheckedExecutionException
instanceKlass com/google/common/cache/CacheLoader$InvalidCacheLoadException
instanceKlass org/gradle/internal/work/NoAvailableWorkerLeaseException
instanceKlass org/gradle/launcher/daemon/server/BadlyFormedRequestException
instanceKlass org/gradle/internal/remote/internal/MessageIOException
instanceKlass org/gradle/cache/InsufficientLockModeException
instanceKlass org/gradle/cache/LockTimeoutException
instanceKlass org/gradle/cache/internal/locklistener/GracefullyStoppedException
instanceKlass org/gradle/launcher/daemon/registry/DaemonRegistry$EmptyRegistryException
instanceKlass org/gradle/cache/FileIntegrityViolationException
instanceKlass org/gradle/internal/file/FileException
instanceKlass org/gradle/launcher/daemon/server/api/DaemonStoppedException
instanceKlass org/gradle/launcher/daemon/server/api/DaemonUnavailableException
instanceKlass java/lang/TypeNotPresentException
instanceKlass java/util/MissingResourceException
instanceKlass org/gradle/util/internal/GFileUtils$TailReadingException
instanceKlass org/gradle/internal/jvm/JavaHomeException
instanceKlass kotlin/UninitializedPropertyAccessException
instanceKlass java/util/NoSuchElementException
instanceKlass org/gradle/api/reflect/ObjectInstantiationException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass org/gradle/internal/nativeintegration/NativeIntegrationException
instanceKlass org/gradle/internal/reflect/NoSuchMethodException
instanceKlass net/rubygrapefruit/platform/NativeException
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass com/esotericsoftware/kryo/KryoException
instanceKlass java/lang/reflect/UndeclaredThrowableException
instanceKlass org/gradle/internal/operations/BuildOperationInvocationException
instanceKlass org/gradle/internal/UncheckedException
instanceKlass org/gradle/api/GradleException
instanceKlass java/lang/UnsupportedOperationException
instanceKlass java/lang/SecurityException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/gradle/api/GradleException
instanceKlass java/lang/invoke/WrongMethodTypeException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass java/lang/IllegalStateException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 30 1 1 1 1 1 1 1 1 1 1 1 1 5 0 7 100 1 1 12 12 12 12 12 10 10 10 10 10 1
instanceKlass org/codehaus/groovy/reflection/ReflectionUtils$ClassContextHelper
ciInstanceKlass java/lang/SecurityManager 1 1 375 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 100 7 100 100 100 100 100 100 7 7 7 100 100 100 100 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
staticfield java/lang/SecurityManager packageAccessLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/SecurityManager packageDefinitionLock Ljava/lang/Object; java/lang/Object
ciInstanceKlass java/security/ProtectionDomain 1 1 272 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 7 100 100 100 100 100 100 100 100 100 7 7 100 7 7 100 7 7 7 100 100 100 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 1 1
staticfield java/security/ProtectionDomain debug Lsun/security/util/Debug; null
ciInstanceKlass java/security/AccessControlContext 1 1 305 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 7 100 100 7 100 100 7 100 100 7 100 100 7 100 7 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 1
instanceKlass java/net/URLClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 130 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 100 100 100 7 100 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
staticfield java/security/SecureClassLoader debug Lsun/security/util/Debug; null
instanceKlass java/lang/reflect/InvocationTargetException
instanceKlass java/lang/IllegalAccessException
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/InstantiationException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 27 1 1 1 1 1 1 1 1 1 1 1 1 5 0 7 100 1 12 12 12 12 10 10 10 10 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 32 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 100 1 1 1 12 12 12 9 10 10 1
instanceKlass java/lang/ClassFormatError
instanceKlass java/lang/UnsatisfiedLinkError
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 24 1 1 1 1 1 1 1 1 1 1 1 5 0 7 100 1 12 12 12 10 10 10 1
ciInstanceKlass java/lang/NoClassDefFoundError 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 7 100 12 12 10 10 1
instanceKlass org/codehaus/groovy/runtime/typehandling/GroovyCastException
ciInstanceKlass java/lang/ClassCastException 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 12 12 10 10 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 12 12 10 10 1
instanceKlass java/lang/InternalError
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
ciInstanceKlass java/lang/VirtualMachineError 1 1 27 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 1 12 12 12 12 10 10 10 10 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 12 12 10 10 1
ciInstanceKlass java/lang/StackOverflowError 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 12 12 10 10 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 12 12 10 10 1
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 134 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 100 7 7 100 7 7 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1 1
instanceKlass org/codehaus/groovy/util/ReferenceType$SoftRef
instanceKlass sun/util/locale/provider/LocaleResources$ResourceReference
instanceKlass java/util/ResourceBundle$BundleReference
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
instanceKlass sun/util/locale/LocaleObjectCache$CacheEntry
ciInstanceKlass java/lang/ref/SoftReference 1 1 35 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 7 1 1 1 1 12 12 12 12 12 9 9 10 10 10 1
instanceKlass org/codehaus/groovy/util/ReferenceType$WeakRef
instanceKlass com/google/common/cache/LocalCache$WeakValueReference
instanceKlass java/util/logging/LogManager$LoggerWeakRef
instanceKlass sun/nio/ch/SharedFileLockTable$FileLockReference
instanceKlass java/util/ResourceBundle$LoaderReference
instanceKlass java/lang/reflect/WeakCache$CacheValue
instanceKlass java/lang/reflect/Proxy$Key1
instanceKlass java/lang/reflect/WeakCache$CacheKey
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/util/WeakHashMap$Entry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 20 1 1 1 1 1 1 1 1 7 100 1 1 1 1 12 12 10 10 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 16 1 1 1 1 1 1 1 100 7 1 1 1 12 10 1
instanceKlass sun/misc/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 19 1 1 1 1 1 1 1 1 1 1 100 7 1 1 1 12 10 1
ciInstanceKlass sun/misc/Cleaner 1 1 74 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 7 100 7 100 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 10 10 10 10 10 10 10 11 1
staticfield sun/misc/Cleaner dummyQueue Ljava/lang/ref/ReferenceQueue; java/lang/ref/ReferenceQueue
ciInstanceKlass java/lang/ref/Finalizer 1 1 150 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 7 7 100 7 7 100 100 100 7 7 100 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
instanceKlass java/lang/ref/ReferenceQueue$Null
ciInstanceKlass java/lang/ref/ReferenceQueue 1 1 130 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 100 100 7 100 100 7 7 100 7 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 11 1 1
staticfield java/lang/ref/ReferenceQueue $assertionsDisabled Z 1
instanceKlass net/rubygrapefruit/platform/internal/jni/AbstractFileEventFunctions$NativeFileWatcher$1
instanceKlass org/gradle/launcher/daemon/server/exec/DaemonConnectionBackedEventConsumer$ForwardEvents
instanceKlass org/gradle/launcher/daemon/server/exec/LogToClient$AsynchronousLogDispatcher
instanceKlass java/util/logging/LogManager$Cleaner
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
ciInstanceKlass java/lang/Thread 1 1 539 3 3 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 100 100 100 100 100 100 100 100 100 100 100 100 7 7 7 100 7 100 7 7 100 100 100 100 100 100 7 100 100 100 100 100 100 100 7 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 1 1 1 1 1
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Thread SUBCLASS_IMPLEMENTATION_PERMISSION Ljava/lang/RuntimePermission; java/lang/RuntimePermission
ciInstanceKlass java/lang/ThreadGroup 1 1 268 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 100 100 100 100 7 100 100 7 7 100 100 7 100 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1 1
ciInstanceKlass java/util/Map 1 1 132 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 7 7 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 11 1
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 31 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 1 1 1 1 1 1 12 10 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 416 3 3 4 4 8 8 8 8 8 8 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 5 0 100 100 100 100 100 100 100 100 100 100 7 100 100 7 100 7 100 100 100 7 100 7 7 100 7 7 7 100 100 7 7 7 100 7 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 1 1 1 1 1
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 263 3 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 100 100 100 100 100 100 7 100 100 100 100 7 7 100 100 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 1 1 1
staticfield java/util/Properties hexDigit [C 16
instanceKlass com/google/common/reflect/Element
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 144 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 7 100 100 7 7 100 7 100 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1 1
staticfield java/lang/reflect/AccessibleObject ACCESS_PERMISSION Ljava/security/Permission; java/lang/reflect/ReflectPermission
staticfield java/lang/reflect/AccessibleObject reflectionFactory Lsun/reflect/ReflectionFactory; sun/reflect/ReflectionFactory
ciInstanceKlass java/lang/reflect/Field 1 1 362 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 100 100 100 100 7 7 100 100 100 7 7 7 7 7 7 7 7 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 210 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 1
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 378 3 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 7 100 100 100 100 100 7 7 7 100 100 100 7 100 100 100 7 7 7 7 7 100 100 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 346 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 7 100 100 7 7 100 100 7 100 7 100 100 100 7 7 7 7 7 7 7 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 330 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 7 100 100 100 100 100 100 7 7 100 100 7 100 100 7 7 7 100 100 100 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 1 1
instanceKlass sun/reflect/FieldAccessorImpl
instanceKlass sun/reflect/ConstructorAccessorImpl
instanceKlass sun/reflect/MethodAccessorImpl
ciInstanceKlass sun/reflect/MagicAccessorImpl 1 1 13 1 1 1 1 1 1 1 7 100 12 10 1
instanceKlass sun/reflect/GeneratedMethodAccessor2
instanceKlass sun/reflect/GeneratedMethodAccessor1
instanceKlass sun/reflect/DelegatingMethodAccessorImpl
instanceKlass sun/reflect/NativeMethodAccessorImpl
ciInstanceKlass sun/reflect/MethodAccessorImpl 1 1 22 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 100 100 12 10 1
instanceKlass sun/reflect/GeneratedConstructorAccessor5
instanceKlass sun/reflect/GeneratedConstructorAccessor4
instanceKlass sun/reflect/GeneratedConstructorAccessor3
instanceKlass sun/reflect/GeneratedConstructorAccessor2
instanceKlass sun/reflect/BootstrapConstructorAccessorImpl
instanceKlass sun/reflect/GeneratedConstructorAccessor1
instanceKlass sun/reflect/DelegatingConstructorAccessorImpl
instanceKlass sun/reflect/NativeConstructorAccessorImpl
ciInstanceKlass sun/reflect/ConstructorAccessorImpl 1 1 24 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 7 12 10 1
ciInstanceKlass sun/reflect/DelegatingClassLoader 1 1 13 1 1 1 1 1 1 1 7 100 1 12 10
ciInstanceKlass sun/reflect/ConstantPool 1 1 106 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
instanceKlass sun/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass sun/reflect/FieldAccessorImpl 1 1 56 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 12 10 1
instanceKlass sun/reflect/UnsafeQualifiedFieldAccessorImpl
instanceKlass sun/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass sun/reflect/UnsafeFieldAccessorImpl 1 1 229 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 7 100 100 100 100 100 100 7 100 100 100 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
staticfield sun/reflect/UnsafeFieldAccessorImpl unsafe Lsun/misc/Unsafe; sun/misc/Unsafe
instanceKlass sun/reflect/UnsafeQualifiedStaticFieldAccessorImpl
instanceKlass sun/reflect/UnsafeStaticObjectFieldAccessorImpl
ciInstanceKlass sun/reflect/UnsafeStaticFieldAccessorImpl 1 1 38 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 7 7 1 1 1 1 12 12 12 12 12 9 9 10 10 10 1
ciInstanceKlass sun/reflect/CallerSensitive 0 0 17 1 1 1 1 1 1 1 1 100 100 100 1 1 1 1 1
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 438 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 100 100 100 100 7 100 100 100 7 100 7 7 7 7 100 7 7 7 7 100 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 701 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 7 7 7 7 7 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 7 7 100 7 100 7 7 100 7 7 100 7 7 7 7 7 7 100 7 7 100 7 7 100 7 7 7 100 100 100 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 642 3 3 3 3 3 3 3 3 3 3 3 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 3 3 3 3 7 7 100 100 100 7 7 100 100 100 100 100 100 100 100 100 7 100 7 7 7 7 7 100 7 7 100 100 100 100 7 100 100 100 7 7 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 427 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 100 100 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 7 100 100 100 100 100 100 100 100 100 100 100 100 100 7 100 7 100 100 100 7 7 7 7 7 7 100 7 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1 1
staticfield java/lang/invoke/MethodHandleNatives COUNT_GWT Z 1
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 967 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 8 100 100 100 100 7 7 100 100 100 7 100 100 100 100 100 100 100 100 7 7 7 100 7 7 100 100 100 7 100 7 100 100 7 7 7 7 7 100 100 7 7 7 7 100 100 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identityForm [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zeroForm [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 588 8 8 8 8 8 8 8 8 8 8 8 8 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 5 0 7 100 100 100 100 7 100 100 7 100 7 100 100 100 100 100 7 7 7 7 100 7 7 7 7 7 7 7 7 7 7 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType rtypeOffset J 12
staticfield java/lang/invoke/MethodType ptypesOffset J 16
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 38 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 1 1 12 12 12 12 12 10 10 10 10 10 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 311 8 8 8 8 8 8 8 8 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 100 100 100 100 7 100 100 100 100 100 100 7 100 7 100 7 7 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1
staticfield java/lang/invoke/CallSite GET_TARGET Ljava/lang/invoke/MethodHandle; java/lang/invoke/DirectMethodHandle
staticfield java/lang/invoke/CallSite THROW_UCS Ljava/lang/invoke/MethodHandle; java/lang/invoke/MethodHandleImpl$AsVarargsCollector
staticfield java/lang/invoke/CallSite TARGET_OFFSET J 12
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 42 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 7 7 1 1 12 12 12 12 12 12 9 9 10 10 10 10 10 1
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 57 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 33 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 12 12 12 12 12 12 10 10 10 10 10 10 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 318 3 3 3 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 7 7 100 7 100 100 100 7 7 7 100 7 100 100 100 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 1
ciInstanceKlass java/lang/StringBuffer 1 1 371 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 7 100 7 7 100 100 7 7 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1 1 1
staticfield java/lang/StringBuffer serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/lang/StringBuilder 1 1 326 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 7 100 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1
ciInstanceKlass sun/misc/Unsafe 1 1 389 8 8 7 7 7 7 7 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 100 7 100 100 7 7 7 100 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
staticfield sun/misc/Unsafe theUnsafe Lsun/misc/Unsafe; sun/misc/Unsafe
staticfield sun/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield sun/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield sun/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield sun/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield sun/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield sun/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield sun/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield sun/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield sun/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield sun/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield sun/misc/Unsafe ADDRESS_SIZE I 8
instanceKlass com/google/common/io/BaseEncoding$StandardBaseEncoding$2
instanceKlass java/io/ObjectInputStream
instanceKlass org/apache/tools/ant/util/FileUtils$1
instanceKlass org/gradle/util/internal/BulkReadInputStream
instanceKlass java/io/PipedInputStream
instanceKlass org/gradle/internal/remote/internal/inet/SocketConnection$SocketInputStream
instanceKlass org/gradle/internal/io/RandomAccessFileInputStream
instanceKlass com/esotericsoftware/kryo/io/Input
instanceKlass org/gradle/internal/serialize/kryo/KryoBackedDecoder$1
instanceKlass org/gradle/internal/serialize/AbstractDecoder$DecoderStream
instanceKlass org/gradle/internal/stream/EncodedStream$EncodedInput
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 61 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 5 0 100 100 7 7 100 100 100 7 12 12 12 12 12 10 10 10 10 10 10 10 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 62 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 7 100 7 100 7 1 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 1
ciInstanceKlass java/io/File 1 1 578 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 7 100 7 100 7 7 7 7 100 100 100 100 100 100 100 7 100 100 100 100 100 7 100 100 100 100 7 7 7 100 100 7 100 100 7 7 100 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 1 1 1 1
staticfield java/io/File fs Ljava/io/FileSystem; java/io/WinNTFileSystem
staticfield java/io/File separatorChar C 92
staticfield java/io/File separator Ljava/lang/String; "\"
staticfield java/io/File pathSeparatorChar C 59
staticfield java/io/File pathSeparator Ljava/lang/String; ";"
staticfield java/io/File PATH_OFFSET J 16
staticfield java/io/File PREFIX_LENGTH_OFFSET J 12
staticfield java/io/File UNSAFE Lsun/misc/Unsafe; sun/misc/Unsafe
staticfield java/io/File $assertionsDisabled Z 1
instanceKlass groovy/lang/GroovyClassLoader
instanceKlass org/gradle/internal/classloader/VisitableURLClassLoader
instanceKlass org/gradle/internal/classloader/VisitableURLClassLoader
instanceKlass sun/misc/Launcher$ExtClassLoader
instanceKlass sun/misc/Launcher$AppClassLoader
ciInstanceKlass java/net/URLClassLoader 1 1 527 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 7 7 100 100 7 7 7 7 100 7 100 100 100 7 100 7 100 7 100 7 7 7 7 7 100 100 100 7 7 100 100 100 7 7 7 7 100 7 100 100 100 7 7 7 100 7 7 7 7 7 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11
ciInstanceKlass java/net/URL 1 1 573 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 100 7 100 7 7 100 100 100 100 100 7 7 100 7 7 100 100 100 100 7 100 100 7 100 7 7 7 100 100 7 7 7 100 7 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/util/jar/Manifest 1 1 256 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 7 7 100 7 100 100 100 7 100 7 100 100 7 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 1 1
ciInstanceKlass sun/misc/Launcher 1 1 218 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 100 100 100 100 100 100 100 100 7 100 7 100 7 7 100 7 7 100 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1 1 1
ciInstanceKlass sun/misc/Launcher$AppClassLoader 1 1 201 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 7 7 100 7 100 7 7 100 100 7 100 7 100 7 100 7 7 7 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1
staticfield sun/misc/Launcher$AppClassLoader $assertionsDisabled Z 1
ciInstanceKlass sun/misc/Launcher$ExtClassLoader 1 1 243 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 7 100 7 100 7 7 100 100 100 7 7 100 100 100 7 100 100 100 7 7 7 7 7 100 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 1
ciInstanceKlass java/security/CodeSource 1 1 324 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 100 100 100 100 7 100 100 100 7 100 7 100 100 100 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 1
ciInstanceKlass java/lang/StackTraceElement 0 0 98 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 100 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 1
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/CharBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 103 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 100 100 7 100 7 100 100 100 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 1
ciInstanceKlass java/lang/Boolean 1 1 110 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 7 100 100 100 7 100 7 7 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 10 10 10 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Character 1 1 459 3 3 3 3 3 3 3 3 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 5 0 5 0 100 100 7 7 100 100 100 7 100 7 100 100 100 100 7 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
instanceKlass com/google/common/cache/Striped64
instanceKlass java/math/BigInteger
instanceKlass java/math/BigDecimal
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 34 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 7 12 12 10 10 1
ciInstanceKlass java/lang/Float 1 1 169 3 3 3 4 4 4 4 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 4 4 5 0 7 100 100 7 100 7 100 100 7 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Double 1 1 223 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 5 0 5 0 5 0 5 0 5 0 6 0 6 0 6 0 6 0 6 0 6 0 6 0 7 100 7 100 100 7 100 100 100 7 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 153 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 5 0 5 0 7 7 7 100 100 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 159 3 3 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 5 0 5 0 7 100 100 7 100 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer 1 1 309 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 5 0 5 0 5 0 100 7 7 100 100 7 7 100 7 100 7 100 100 100 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [C 100
staticfield java/lang/Integer DigitOnes [C 100
staticfield java/lang/Integer sizeTable [I 10
ciInstanceKlass java/lang/Long 1 1 356 3 3 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 100 7 7 100 100 7 7 7 7 100 7 100 100 100 7 100 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
instanceKlass kotlin/KotlinNullPointerException
ciInstanceKlass java/lang/NullPointerException 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 12 12 10 10 1
ciInstanceKlass java/lang/ArithmeticException 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 12 12 10 10 1
ciInstanceKlass java/util/Comparator 1 1 262 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 7 100 100 100 100 100 100 100 7 100 100 100 100 7 100 100 100 7 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 15 15 15 15 15 15 15 16 18 18 18 18 18 18 1 1 1 1
ciInstanceKlass java/security/AccessController 1 1 187 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 7 100 7 7 100 100 100 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
instanceKlass org/gradle/api/internal/DefaultDomainObjectCollection
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$WrappedCollection
instanceKlass java/util/TreeMap$Values
instanceKlass java/util/LinkedHashMap$LinkedValues
instanceKlass com/google/common/collect/ImmutableCollection
instanceKlass java/util/AbstractQueue
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 143 3 3 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 7 100 7 100 100 7 7 100 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 1
instanceKlass groovy/lang/EmptyRange
instanceKlass groovy/lang/ObjectRange
instanceKlass groovy/lang/IntRange
instanceKlass groovy/lang/Tuple
instanceKlass sun/security/jca/ProviderList$3
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList
instanceKlass java/util/Collections$SingletonList
instanceKlass java/util/AbstractSequentialList
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList
instanceKlass java/util/SubList
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/ArrayList$SubList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
instanceKlass java/util/Vector
ciInstanceKlass java/util/AbstractList 1 1 167 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 100 100 7 7 100 7 7 100 100 7 7 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 1 1
instanceKlass org/apache/tools/ant/util/VectorSet
instanceKlass java/util/Stack
ciInstanceKlass java/util/Vector 1 1 379 3 3 8 8 8 8 8 8 8 8 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 100 100 100 100 100 100 100 100 100 7 100 100 7 100 7 7 100 100 100 100 100 100 100 100 7 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 1 1 1 1 1 1
ciInstanceKlass java/util/Stack 1 1 56 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 7 1 1 1 1 12 12 12 12 12 12 12 10 10 10 10 10 10 10 10 1
ciInstanceKlass java/util/ArrayList 1 1 356 3 3 8 8 8 8 8 8 8 8 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 5 0 100 100 100 100 100 100 100 100 100 100 7 7 100 100 7 100 7 7 7 7 7 7 100 7 100 100 7 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
instanceKlass com/google/common/collect/MapMakerInternalMap
instanceKlass java/util/EnumMap
instanceKlass com/google/common/cache/LocalCache
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/concurrent/ConcurrentHashMap
instanceKlass java/util/TreeMap
instanceKlass java/util/WeakHashMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/Collections$EmptyMap
ciInstanceKlass java/util/AbstractMap 1 1 152 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 100 7 100 100 7 100 100 100 100 100 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 1 1 1
ciInstanceKlass sun/misc/SharedSecrets 1 1 186 100 100 100 100 100 100 100 100 100 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 1
staticfield sun/misc/SharedSecrets unsafe Lsun/misc/Unsafe; sun/misc/Unsafe
instanceKlass groovy/lang/SpreadMap
instanceKlass sun/nio/ch/WindowsSelectorImpl$FdMap
instanceKlass java/lang/ProcessEnvironment
instanceKlass java/util/LinkedHashMap
ciInstanceKlass java/util/HashMap 1 1 482 3 3 4 4 4 4 4 8 8 8 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 5 0 100 7 100 100 100 100 100 100 100 100 100 7 100 100 100 100 7 100 100 100 7 100 100 7 100 7 100 100 100 100 7 100 7 7 100 100 7 7 7 7 7 100 100 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 11 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/LinkedHashMap$Entry
ciInstanceKlass java/util/HashMap$Node 1 1 85 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 100 7 100 100 100 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 11 11 1
ciInstanceKlass java/lang/Math 1 1 281 3 3 3 3 3 3 4 4 4 4 4 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 5 0 6 0 6 0 6 0 6 0 6 0 6 0 6 0 6 0 6 0 100 100 7 7 7 100 100 100 100 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1
staticfield java/lang/Math $assertionsDisabled Z 1
ciInstanceKlass java/util/Arrays 1 1 800 3 8 8 8 8 8 8 8 8 100 100 100 100 100 100 7 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 100 100 100 7 7 100 100 100 7 7 100 100 7 100 100 100 7 100 100 100 100 100 7 7 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 7 100 7 100 100 100 100 100 100 100 7 7 100 100 100 100 100 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 15 15 15 15 15 16 18 18 18 18 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/Arrays $assertionsDisabled Z 1
ciInstanceKlass sun/misc/ASCIICaseInsensitiveComparator 1 1 67 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 7 7 100 1 1 1 1 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 1
staticfield sun/misc/ASCIICaseInsensitiveComparator CASE_INSENSITIVE_ORDER Ljava/util/Comparator; sun/misc/ASCIICaseInsensitiveComparator
staticfield sun/misc/ASCIICaseInsensitiveComparator $assertionsDisabled Z 1
ciInstanceKlass java/security/PrivilegedExceptionAction 1 0 15 1 1 1 1 1 1 1 1 1 100 100 100 1 1
instanceKlass org/gradle/internal/io/LinePerThreadBufferingOutputStream
ciInstanceKlass java/io/PrintStream 1 1 282 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 100 100 100 7 7 100 7 7 7 100 100 7 100 100 100 7 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 1
instanceKlass java/io/ExpiringCache$1
ciInstanceKlass java/util/LinkedHashMap 1 1 230 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 100 7 7 100 7 7 100 7 100 100 7 100 7 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ExpiringCache$1 1 1 45 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 100 7 100 100 1 1 1 1 1 1 12 12 12 12 12 9 10 10 10 1
instanceKlass java/util/HashMap$TreeNode
ciInstanceKlass java/util/LinkedHashMap$Entry 1 1 27 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 100 100 1 1 1 1 1 12 10 1
ciInstanceKlass java/util/StringTokenizer 1 1 119 3 3 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 7 7 100 100 7 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
ciInstanceKlass sun/misc/MetaIndex 1 1 151 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 7 7 100 100 7 100 7 7 100 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 1
staticfield sun/misc/MetaIndex $assertionsDisabled Z 1
ciInstanceKlass java/util/Locale 1 1 891 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 3 3 3 3 3 5 0 8 8 8 8 100 100 100 100 100 100 100 100 7 100 100 100 7 100 100 100 100 7 100 100 7 100 7 100 100 7 100 100 100 100 100 7 7 7 100 7 7 100 100 7 100 100 100 100 7 7 100 7 100 100 100 7 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/Locale LOCALECACHE Ljava/util/Locale$Cache; java/util/Locale$Cache
staticfield java/util/Locale ENGLISH Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale FRENCH Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale GERMAN Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale ITALIAN Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale JAPANESE Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale KOREAN Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale CHINESE Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale SIMPLIFIED_CHINESE Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale TRADITIONAL_CHINESE Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale FRANCE Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale GERMANY Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale ITALY Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale JAPAN Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale KOREA Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale CHINA Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale PRC Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale TAIWAN Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale UK Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale US Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale CANADA Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale CANADA_FRENCH Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale ROOT Ljava/util/Locale; java/util/Locale
staticfield java/util/Locale serialPersistentFields [Ljava/io/ObjectStreamField; 6 [Ljava/io/ObjectStreamField;
staticfield java/util/Locale $assertionsDisabled Z 1
ciInstanceKlass java/util/HashMap$TreeNode 0 0 177 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1
instanceKlass sun/net/www/protocol/jar/Handler
instanceKlass sun/net/www/protocol/file/Handler
ciInstanceKlass java/net/URLStreamHandler 1 1 238 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 7 100 7 7 7 100 100 7 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
ciInstanceKlass sun/net/www/protocol/file/Handler 1 1 131 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 100 7 100 100 7 100 7 7 7 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
ciInstanceKlass sun/misc/URLClassPath 1 1 515 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 8 100 7 100 100 7 100 100 7 100 100 7 100 7 100 100 100 7 100 7 100 7 100 7 7 100 7 100 100 100 7 100 7 7 7 7 100 100 7 7 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 1 1 1 1
staticfield sun/misc/URLClassPath JAVA_VERSION Ljava/lang/String; "1.8.0_261"
staticfield sun/misc/URLClassPath DEBUG Z 0
staticfield sun/misc/URLClassPath DEBUG_LOOKUP_CACHE Z 0
staticfield sun/misc/URLClassPath DISABLE_JAR_CHECKING Z 0
staticfield sun/misc/URLClassPath DISABLE_ACC_CHECKING Z 0
staticfield sun/misc/URLClassPath DISABLE_CP_URL_CHECK Z 1
staticfield sun/misc/URLClassPath DEBUG_CP_URL_CHECK Z 0
ciInstanceKlass sun/net/util/URLUtil 1 1 113 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 7 7 100 100 100 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1
ciInstanceKlass sun/misc/URLClassPath$3 1 1 95 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 7 7 100 7 7 100 7 100 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 10 10 10 10 10 10 10 10 10 10 10 1 1 1 1
instanceKlass sun/misc/URLClassPath$JarLoader
ciInstanceKlass sun/misc/URLClassPath$Loader 1 1 151 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 7 100 100 100 100 100 100 100 100 100 100 7 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1 1
ciInstanceKlass sun/misc/URLClassPath$JarLoader 1 1 531 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 7 7 100 100 100 7 7 7 100 100 100 7 100 100 100 100 7 7 100 100 100 7 100 100 7 7 7 100 7 7 100 7 7 100 7 7 100 7 7 7 7 7 7 7 100 7 100 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 1 1 1
staticfield sun/misc/URLClassPath$JarLoader zipAccess Lsun/misc/JavaUtilZipFileAccess; java/util/zip/ZipFile$1
ciInstanceKlass sun/misc/URLClassPath$JarLoader$1 1 1 146 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 7 100 100 100 100 7 100 7 7 7 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 1 1
instanceKlass sun/net/www/protocol/jar/URLJarFile
ciInstanceKlass java/util/jar/JarFile 1 1 477 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 7 100 7 7 7 100 7 7 7 100 7 7 100 100 100 7 100 7 100 7 100 100 100 100 7 7 100 7 7 100 7 7 7 7 7 100 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 1 1 1 1
staticfield java/util/jar/JarFile isInitializing Ljava/lang/ThreadLocal; java/lang/ThreadLocal
staticfield java/util/jar/JarFile CLASSPATH_CHARS [C 10
staticfield java/util/jar/JarFile CLASSPATH_LASTOCC [I 128
staticfield java/util/jar/JarFile CLASSPATH_OPTOSFT [I 10
ciInstanceKlass sun/misc/JavaUtilJarAccess 1 0 34 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/jar/JavaUtilJarAccessImpl 1 1 74 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 7 7 100 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 10 10 10 10 10 10 10 10 10 10 10 1
ciInstanceKlass sun/misc/JarIndex 1 1 292 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 7 100 100 100 100 7 100 7 7 100 7 100 7 100 7 100 100 100 7 100 7 100 100 7 7 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 1
staticfield sun/misc/JarIndex metaInfFilenames Z 0
ciInstanceKlass sun/misc/ExtensionDependency 1 1 388 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 100 7 100 100 100 100 100 100 100 100 100 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 1 1
ciInstanceKlass java/util/jar/Attributes 1 1 251 8 8 8 8 8 8 8 8 8 8 7 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 100 100 100 100 100 7 7 100 100 7 7 100 7 100 100 7 100 7 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 11 1 1
ciInstanceKlass java/util/jar/Attributes$Name 1 1 172 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 7 7 7 100 7 7 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 12 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 9 10 10 10 10 10 10 10 10 10 10 10 10 11 1
staticfield java/util/jar/Attributes$Name MANIFEST_VERSION Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name SIGNATURE_VERSION Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name CONTENT_TYPE Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name CLASS_PATH Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name MAIN_CLASS Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name SEALED Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name EXTENSION_LIST Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name EXTENSION_NAME Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name EXTENSION_INSTALLATION Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name IMPLEMENTATION_TITLE Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name IMPLEMENTATION_VERSION Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name IMPLEMENTATION_VENDOR Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name IMPLEMENTATION_VENDOR_ID Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name IMPLEMENTATION_URL Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name SPECIFICATION_TITLE Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name SPECIFICATION_VERSION Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
staticfield java/util/jar/Attributes$Name SPECIFICATION_VENDOR Ljava/util/jar/Attributes$Name; java/util/jar/Attributes$Name
instanceKlass java/io/UnsupportedEncodingException
instanceKlass com/google/common/io/BaseEncoding$DecodingException
instanceKlass java/io/InterruptedIOException
instanceKlass java/nio/file/FileSystemException
instanceKlass org/apache/commons/io/FileExistsException
instanceKlass java/nio/channels/ClosedChannelException
instanceKlass java/net/SocketException
instanceKlass java/io/ObjectStreamException
instanceKlass java/net/UnknownHostException
instanceKlass java/io/EOFException
instanceKlass java/io/FileNotFoundException
instanceKlass java/util/zip/ZipException
instanceKlass java/net/MalformedURLException
ciInstanceKlass java/io/IOException 1 1 27 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 1 12 12 12 12 10 10 10 10 1
instanceKlass org/codehaus/groovy/runtime/powerassert/PowerAssertionError
instanceKlass org/codehaus/groovy/GroovyBugError
ciInstanceKlass java/lang/AssertionError 0 0 65 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 100 100 100 100 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 10 10 10 10 10 10 10 10 10 10 10 10 1
instanceKlass org/objectweb/asm/MethodTooLargeException
instanceKlass org/objectweb/asm/ClassTooLargeException
instanceKlass java/lang/ArrayIndexOutOfBoundsException
ciInstanceKlass java/lang/IndexOutOfBoundsException 1 1 21 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 12 12 10 10 1
ciInstanceKlass java/io/FileNotFoundException 1 1 42 8 8 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 100 100 1 12 12 12 12 10 10 10 10 10 1
ciInstanceKlass java/security/PrivilegedActionException 1 1 59 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 100 100 100 100 7 1 1 1 1 1 1 12 12 12 12 12 12 12 9 10 10 10 10 10 10 10 1
instanceKlass java/security/AccessControlException
ciInstanceKlass java/lang/SecurityException 1 1 27 1 1 1 1 1 1 1 1 1 1 1 1 5 0 7 100 1 12 12 12 12 10 10 10 10 1
ciInstanceKlass java/lang/ArrayIndexOutOfBoundsException 1 1 38 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 100 7 100 1 1 12 12 12 12 12 10 10 10 10 10 10 1
ciInstanceKlass sun/nio/ch/WindowsSelectorImpl$FdMap 1 1 80 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 5 0 7 7 7 7 100 100 7 7 1 1 1 1 1 1 1 1 1 1 12 12 12 12 12 12 12 12 12 12 12 12 9 9 10 10 10 10 10 10 10 10 10 10 11 1 1 1
ciMethod java/lang/Object <init> ()V 4097 1 263882 0 0
ciMethod java/lang/Object getClass ()Ljava/lang/Class; 3073 1 384 0 -1
ciMethod java/lang/Object hashCode ()I 2049 1 256 0 -1
ciMethod java/lang/Object equals (Ljava/lang/Object;)Z 3113 1 14559 0 -1
ciMethod java/lang/String <init> ([CII)V 2849 1 5609 0 -1
ciMethod java/lang/String length ()I 4097 1 174027 0 64
ciMethod java/lang/String charAt (I)C 4097 1 860386 0 160
ciMethod java/lang/String codePointAt (I)I 105 1 5236 0 -1
ciMethod java/lang/String getChars (II[CI)V 2401 1 5510 0 -1
ciMethod java/lang/String equals (Ljava/lang/Object;)Z 2209 10761 2591 0 -1
ciMethod java/lang/String hashCode ()I 2785 32769 1490 0 320
ciMethod java/lang/String toLowerCase (Ljava/util/Locale;)Ljava/lang/String; 977 28633 1493 0 -1
ciMethod java/lang/String toLowerCase ()Ljava/lang/String; 2065 1 2071 0 0
ciMethod java/lang/System arraycopy (Ljava/lang/Object;ILjava/lang/Object;II)V 7169 1 896 0 -1
ciMethod java/util/Map get (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/AbstractStringBuilder <init> (I)V 1217 1 27447 0 0
ciMethod java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 2857 1 105723 0 640
ciMethod java/lang/AbstractStringBuilder newCapacity (I)I 857 1 5239 0 160
ciMethod java/lang/AbstractStringBuilder hugeCapacity (I)I 0 0 1 0 -1
ciMethod java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 2193 1 5481 0 -1
ciMethod java/lang/AbstractStringBuilder appendNull ()Ljava/lang/AbstractStringBuilder; 0 0 1 0 -1
ciMethod java/lang/AbstractStringBuilder append (I)Ljava/lang/AbstractStringBuilder; 1 1 1067 0 0
ciMethod java/lang/StringBuilder <init> ()V 889 1 16960 0 224
ciMethod java/lang/StringBuilder append (Ljava/lang/Object;)Ljava/lang/StringBuilder; 153 1 506 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 1953 1 32641 0 1024
ciMethod java/lang/StringBuilder append (I)Ljava/lang/StringBuilder; 1 1 1067 0 0
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 977 1 18827 0 864
ciMethod sun/misc/Unsafe ensureClassInitialized (Ljava/lang/Class;)V 2577 1 320 0 -1
ciMethod java/net/URL <init> (Ljava/net/URL;Ljava/lang/String;)V 2049 1 1162 0 -1
ciMethod java/net/URL getPort ()I 1025 1 128 0 0
ciMethod java/net/URL getDefaultPort ()I 2057 1 490 0 0
ciMethod java/net/URL getProtocol ()Ljava/lang/String; 1057 1 132 0 0
ciMethod java/net/URL getHost ()Ljava/lang/String; 1073 1 134 0 0
ciMethod java/net/URL getFile ()Ljava/lang/String; 1041 1 130 0 0
ciMethod java/util/jar/Manifest getMainAttributes ()Ljava/util/jar/Attributes; 1025 1 128 0 0
ciMethod java/lang/Character charCount (I)I 241 1 7764 0 -1
ciMethod java/lang/Character toChars (I[CI)I 0 0 1 0 -1
ciMethod java/lang/Character toChars (I)[C 0 0 1 0 -1
ciMethod java/lang/Character toLowerCase (C)C 2113 1 14602 0 -1
ciMethod java/lang/Character toLowerCase (I)I 4097 1 116097 0 -1
ciMethod java/lang/Integer getChars (II[C)V 3097 185 1098 0 0
ciMethod java/lang/Integer stringSize (I)I 3097 185 1098 0 0
ciMethod java/util/Comparator compare (Ljava/lang/Object;Ljava/lang/Object;)I 0 0 1 0 -1
ciMethod java/security/AccessController doPrivileged (Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;)Ljava/lang/Object; 2065 1 258 4 -1
ciMethod java/util/Vector size ()I 1025 1 128 0 0
ciMethod java/util/Vector elementAt (I)Ljava/lang/Object; 329 1 567 0 0
ciMethod java/util/Vector removeElementAt (I)V 161 1 325 0 0
ciMethod java/util/Vector elementData (I)Ljava/lang/Object; 2049 1 567 0 -1
ciMethod java/util/Stack push (Ljava/lang/Object;)Ljava/lang/Object; 2585 1 324 0 -1
ciMethod java/util/Stack pop ()Ljava/lang/Object; 161 1 325 0 0
ciMethod java/util/Stack peek ()Ljava/lang/Object; 169 1 330 0 0
ciMethod java/util/Stack empty ()Z 273 1 558 0 0
ciMethod java/util/ArrayList calculateCapacity ([Ljava/lang/Object;I)I 777 1 13516 0 0
ciMethod java/util/ArrayList ensureCapacityInternal (I)V 777 1 13516 0 0
ciMethod java/util/ArrayList ensureExplicitCapacity (I)V 777 1 13516 0 0
ciMethod java/util/ArrayList grow (I)V 121 1 3871 0 0
ciMethod java/util/ArrayList hugeCapacity (I)I 0 0 1 0 -1
ciMethod java/util/ArrayList size ()I 1049 1 131 0 0
ciMethod java/util/ArrayList elementData (I)Ljava/lang/Object; 2001 1 8890 0 0
ciMethod java/util/ArrayList get (I)Ljava/lang/Object; 2001 1 8808 0 128
ciMethod java/util/ArrayList add (Ljava/lang/Object;)Z 777 1 13310 0 768
ciMethod java/util/ArrayList rangeCheck (I)V 2001 1 8890 0 0
ciMethod sun/misc/SharedSecrets javaUtilJarAccess ()Lsun/misc/JavaUtilJarAccess; 2057 1 8820 0 0
ciMethod java/util/HashMap hash (Ljava/lang/Object;)I 2129 1 34573 0 0
ciMethod java/util/HashMap get (Ljava/lang/Object;)Ljava/lang/Object; 3073 1 18841 0 704
ciMethod java/util/HashMap getNode (ILjava/lang/Object;)Ljava/util/HashMap$Node; 2073 9 11528 0 544
ciMethod java/util/HashMap containsKey (Ljava/lang/Object;)Z 153 1 3299 0 0
ciMethod java/util/HashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 753 1 7024 0 0
ciMethod java/util/HashMap putVal (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; 785 17 6712 0 0
ciMethod java/util/HashMap resize ()[Ljava/util/HashMap$Node; 649 521 1116 0 -1
ciMethod java/util/HashMap treeifyBin ([Ljava/util/HashMap$Node;I)V 0 0 1 0 0
ciMethod java/util/HashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 2249 1 5402 0 224
ciMethod java/util/HashMap replacementTreeNode (Ljava/util/HashMap$Node;Ljava/util/HashMap$Node;)Ljava/util/HashMap$TreeNode; 0 0 1 0 -1
ciMethod java/util/HashMap afterNodeAccess (Ljava/util/HashMap$Node;)V 3225 1 333 0 0
ciMethod java/util/HashMap afterNodeInsertion (Z)V 3185 1 1425 0 0
ciMethod java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 2073 1 11065 0 0
ciMethod java/lang/Math max (II)I 729 1 101851 0 -1
ciMethod java/lang/Math min (II)I 3961 1 65923 0 -1
ciMethod java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 2113 1 5232 0 0
ciMethod java/util/Arrays copyOf ([Ljava/lang/Object;ILjava/lang/Class;)[Ljava/lang/Object; 3073 1 5571 0 -1
ciMethod java/util/Arrays copyOf ([CI)[C 1105 1 18009 0 480
ciMethod sun/misc/ASCIICaseInsensitiveComparator compare (Ljava/lang/String;Ljava/lang/String;)I 4097 6513 12930 0 864
ciMethod sun/misc/ASCIICaseInsensitiveComparator lowerCaseHashCode (Ljava/lang/String;)I 577 10041 1163 0 640
ciMethod sun/misc/ASCIICaseInsensitiveComparator isUpper (I)Z 4097 1 43748 0 64
ciMethod sun/misc/ASCIICaseInsensitiveComparator toLower (I)I 4097 1 43748 0 64
ciMethod sun/misc/ASCIICaseInsensitiveComparator compare (Ljava/lang/Object;Ljava/lang/Object;)I 4097 1 14273 0 960
ciMethodData java/lang/String charAt (I)C 2 860386 orig 264 128 100 200 94 0 0 0 0 104 61 60 229 109 2 0 0 120 1 0 0 32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 17 247 104 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 80 0 0 0 255 255 255 255 7 0 1 0 0 0 0 0 data 10 0x10007 0x0 0x40 0xd1ee2 0xa0007 0xd1ee2 0x30 0x0 0x120002 0x0 oops 0
ciMethodData java/lang/String length ()I 2 174027 orig 264 128 100 200 94 0 0 0 0 24 60 60 229 109 2 0 0 32 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 89 46 21 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 0 0 0 0 data 0 oops 0
ciMethodData sun/misc/ASCIICaseInsensitiveComparator toLower (I)I 2 43748 orig 264 128 100 200 94 0 0 0 0 168 41 79 229 109 2 0 0 144 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 33 71 5 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 72 0 0 0 255 255 255 255 2 0 1 0 0 0 0 0 data 9 0x10002 0xa8e4 0x40007 0x8988 0x38 0x1f5c 0xb0003 0x1f5c 0x18 oops 0
ciMethodData sun/misc/ASCIICaseInsensitiveComparator isUpper (I)Z 2 43748 orig 264 128 100 200 94 0 0 0 0 8 41 79 229 109 2 0 0 88 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 33 71 5 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 56 0 0 0 255 255 255 255 7 0 9 0 0 0 0 0 data 7 0x90007 0x8988 0x38 0x1f5c 0xd0003 0x1f5c 0x18 oops 0
ciMethodData sun/misc/ASCIICaseInsensitiveComparator compare (Ljava/lang/Object;Ljava/lang/Object;)I 2 14273 orig 264 128 100 200 94 0 0 0 0 232 42 79 229 109 2 0 0 232 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 9 174 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 144 0 0 0 255 255 255 255 4 0 2 0 0 0 0 0 data 18 0x20004 0x0 0x26de5eae840 0x3082 0x0 0x0 0x60004 0x0 0x26de5eae840 0x3082 0x0 0x0 0x90005 0x0 0x26deb7b4a00 0x35c1 0x0 0x0 oops 3 2 java/lang/String 8 java/lang/String 14 sun/misc/ASCIICaseInsensitiveComparator
ciMethodData sun/misc/ASCIICaseInsensitiveComparator compare (Ljava/lang/String;Ljava/lang/String;)I 2 31296 orig 264 128 100 200 94 0 0 0 0 16 39 79 229 109 2 0 0 88 3 0 0 128 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 7 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 46 3 0 0 17 132 1 0 145 184 3 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 255 255 255 255 5 0 1 0 0 0 0 0 data 64 0x10005 0x3082 0x0 0x0 0x0 0x0 0x60005 0x3082 0x0 0x0 0x0 0x0 0xe0007 0x1d9b 0x38 0x12e7 0x120003 0x12e7 0x18 0x200007 0x87 0x168 0xa70b 0x260005 0xa70b 0x0 0x0 0x0 0x0 0x2e0005 0xa70b 0x0 0x0 0x0 0x0 0x360007 0xa70b 0x70 0x0 0x3d0007 0x0 0x40 0x0 0x440007 0x0 0x30 0x0 0x4b0002 0x0 0x530007 0x7384 0x60 0x3387 0x580002 0x3387 0x600002 0x3387 0x6a0007 0x38c 0x20 0x2ffb 0x760003 0x7710 0xfffffffffffffeb0 oops 0
ciMethodData java/lang/Object <init> ()V 2 263882 orig 264 128 100 200 94 0 0 0 0 128 4 60 229 109 2 0 0 32 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 81 38 32 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 1 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 0 0 0 0 data 0 oops 0
ciMethod java/io/PrintStream println (Ljava/lang/String;)V 9 1 1 0 -1
ciMethod java/util/StringTokenizer <init> (Ljava/lang/String;)V 65 1 8 0 -1
ciMethod java/util/StringTokenizer hasMoreTokens ()Z 2209 1 352 0 -1
ciMethod java/util/StringTokenizer nextToken ()Ljava/lang/String; 2241 1 354 0 -1
ciMethod java/util/StringTokenizer countTokens ()I 25 113 3 0 -1
ciMethod java/util/Locale getDefault ()Ljava/util/Locale; 2065 1 2081 0 0
ciMethod java/util/Locale getLanguage ()Ljava/lang/String; 401 1 1316 0 -1
ciMethod java/util/HashMap$TreeNode getTreeNode (ILjava/lang/Object;)Ljava/util/HashMap$TreeNode; 0 0 1 0 -1
ciMethod java/util/HashMap$TreeNode treeify ([Ljava/util/HashMap$Node;)V 0 0 1 0 -1
ciMethod java/util/HashMap$TreeNode putTreeVal (Ljava/util/HashMap;[Ljava/util/HashMap$Node;ILjava/lang/Object;Ljava/lang/Object;)Ljava/util/HashMap$TreeNode; 0 0 1 0 -1
ciMethod java/util/HashMap$TreeNode split (Ljava/util/HashMap;[Ljava/util/HashMap$Node;II)V 0 0 1 0 -1
ciMethod java/net/URLStreamHandler getDefaultPort ()I 1025 1 8179 0 0
ciMethodData java/lang/String hashCode ()I 2 20463 orig 264 128 100 200 94 0 0 0 0 184 79 60 229 109 2 0 0 152 1 0 0 32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 16 0 0 177 35 0 0 121 255 1 0 0 0 0 0 0 0 0 0 2 0 0 0 1 0 11 0 2 0 0 0 120 0 0 0 255 255 255 255 7 0 6 0 0 0 0 0 data 15 0x60007 0x277 0x78 0x1ff 0xe0007 0x5 0x58 0x1fa 0x1e0007 0x1fa 0x38 0x3fca 0x2d0003 0x3fca 0xffffffffffffffe0 oops 0
ciMethod sun/misc/URLClassPath disableAllLookupCaches ()V 25 1 3 0 0
ciMethod sun/misc/URLClassPath validateLookupCache (ILjava/lang/String;)V 153 1 311 0 0
ciMethod sun/misc/URLClassPath getNextLoader ([II)Lsun/misc/URLClassPath$Loader; 2113 1 8340 0 -1
ciMethod sun/misc/URLClassPath getLoader (I)Lsun/misc/URLClassPath$Loader; 2113 153 8356 0 0
ciMethod sun/misc/URLClassPath getLoader (Ljava/net/URL;)Lsun/misc/URLClassPath$Loader; 153 1 313 0 0
ciMethod sun/misc/URLClassPath push ([Ljava/net/URL;)V 25 153 8 0 0
ciMethod sun/misc/URLClassPath access$1500 ()Z 97 1 12 0 -1
ciMethod sun/misc/URLClassPath access$1600 ()Z 0 0 1 0 -1
ciMethod sun/net/util/URLUtil urlNoFragString (Ljava/net/URL;)Ljava/lang/String; 153 1 490 0 0
ciMethod sun/misc/URLClassPath$3 <init> (Lsun/misc/URLClassPath;Ljava/net/URL;)V 153 1 313 0 0
ciMethod sun/misc/URLClassPath$JarLoader ensureOpen ()V 689 1 5285 2 256
ciMethod sun/misc/URLClassPath$JarLoader getClassPath ()[Ljava/net/URL; 2049 1 311 0 0
ciMethod sun/misc/URLClassPath$JarLoader parseExtensionsDependencies ()V 1633 1 258 0 0
ciMethod sun/misc/URLClassPath$JarLoader parseClassPath (Ljava/net/URL;Ljava/lang/String;)[Ljava/net/URL; 17 97 2 0 0
ciMethod sun/misc/URLClassPath$JarLoader tryResolve (Ljava/net/URL;Ljava/lang/String;)Ljava/net/URL; 0 0 1 0 -1
ciMethod sun/misc/URLClassPath$Loader getClassPath ()[Ljava/net/URL; 0 0 1 0 -1
ciMethodData java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 2 105723 orig 264 128 100 200 94 0 0 0 0 56 74 67 229 109 2 0 0 144 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 101 1 0 0 177 220 12 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 11 0 2 0 0 0 64 0 0 0 255 255 255 255 7 0 7 0 0 0 0 0 data 8 0x70007 0x184e4 0x40 0x16b2 0x110002 0x16b2 0x140002 0x16b2 oops 0
ciMethodData java/lang/AbstractStringBuilder newCapacity (I)I 2 5239 orig 264 128 100 200 94 0 0 0 0 248 74 67 229 109 2 0 0 176 1 0 0 64 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 107 0 0 0 97 160 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 18 0 2 0 0 0 136 0 0 0 255 255 255 255 7 0 13 0 0 0 0 0 data 17 0xd0007 0xa9c 0x20 0x970 0x130007 0x0 0x40 0x140c 0x1a0007 0x140c 0x48 0x0 0x1f0002 0x0 0x220003 0x0 0x18 oops 0
ciMethodData java/util/Arrays copyOf ([CI)[C 2 18009 orig 264 128 100 200 94 0 0 0 0 120 158 77 229 109 2 0 0 112 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 138 0 0 0 121 46 2 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 7 0 2 0 0 0 32 0 0 0 255 255 255 255 2 0 11 0 0 0 0 0 data 4 0xb0002 0x45cf 0xe0002 0x45cf oops 0
ciMethodData java/lang/String getChars (II[CI)V 2 5510 orig 264 128 100 200 94 0 0 0 0 192 65 60 229 109 2 0 0 8 2 0 0 128 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 44 1 0 0 209 162 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 9 0 2 0 0 0 160 0 0 0 255 255 255 255 7 0 1 0 0 0 0 0 data 20 0x10007 0x145a 0x30 0x0 0x90002 0x0 0x130007 0x145a 0x30 0x0 0x1b0002 0x0 0x210007 0x145a 0x30 0x0 0x2b0002 0x0 0x3a0002 0x145a oops 0
ciMethodData java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 2 5481 orig 264 128 100 200 94 0 0 0 0 144 83 67 229 109 2 0 0 240 1 0 0 32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 18 1 0 0 185 162 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 14 0 2 0 0 0 160 0 0 0 255 255 255 255 7 0 1 0 0 0 0 0 data 20 0x10007 0x1457 0x30 0x0 0x50002 0x0 0xa0005 0x9 0x26de5eae840 0x144e 0x0 0x0 0x150002 0x1457 0x230005 0x9 0x26de5eae840 0x144e 0x0 0x0 oops 2 8 java/lang/String 16 java/lang/String
ciMethodData java/lang/AbstractStringBuilder appendNull ()Ljava/lang/AbstractStringBuilder; 1 0 orig 264 128 100 200 94 0 0 0 0 192 86 67 229 109 2 0 0 48 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 16 0 0 0 255 255 255 255 2 0 9 0 0 0 0 0 data 2 0x90002 0x0 oops 0
ciMethod sun/misc/URLClassPath$JarLoader$1 <init> (Lsun/misc/URLClassPath$JarLoader;)V 369 1 141 0 -1
ciMethod java/util/jar/JarFile getManifest ()Ljava/util/jar/Manifest; 705 1 4417 0 -1
ciMethod sun/misc/JavaUtilJarAccess jarFileHasClassPathAttribute (Ljava/util/jar/JarFile;)Z 0 0 1 0 -1
ciMethodData java/lang/StringBuilder <init> ()V 2 16979 orig 264 128 100 200 94 0 0 0 0 136 132 67 229 109 2 0 0 48 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 111 0 0 0 33 15 2 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 16 0 0 0 255 255 255 255 2 0 3 0 0 0 0 0 data 2 0x30002 0x41e4 oops 0
ciMethodData java/lang/AbstractStringBuilder <init> (I)V 2 27470 orig 264 128 100 200 94 0 0 0 0 184 71 67 229 109 2 0 0 56 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 152 0 0 0 177 85 3 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 16 0 0 0 255 255 255 255 2 0 1 0 0 0 0 0 data 2 0x10002 0x6ab6 oops 0
ciMethodData java/lang/StringBuilder append (I)Ljava/lang/StringBuilder; 2 1072 orig 264 128 100 200 94 0 0 0 0 96 140 67 229 109 2 0 0 56 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 129 33 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 16 0 0 0 255 255 255 255 2 0 2 0 0 0 0 0 data 2 0x20002 0x430 oops 0
ciMethodData java/lang/AbstractStringBuilder append (I)Ljava/lang/AbstractStringBuilder; 2 1072 orig 264 128 100 200 94 0 0 0 0 72 92 67 229 109 2 0 0 24 2 0 0 152 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 129 33 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 2 0 24 0 2 0 0 0 200 0 0 0 255 255 255 255 7 0 3 0 0 0 0 0 data 25 0x30007 0x430 0x50 0x0 0x90005 0x0 0x0 0x0 0x0 0x0 0x100007 0x430 0x48 0x0 0x150002 0x0 0x1a0003 0x0 0x28 0x1e0002 0x430 0x2b0002 0x430 0x340002 0x430 oops 0
ciMethodData java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 2 32675 orig 264 128 100 200 94 0 0 0 0 160 135 67 229 109 2 0 0 56 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 244 0 0 0 121 245 3 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 16 0 0 0 255 255 255 255 2 0 2 0 0 0 0 0 data 2 0x20002 0x7eaf oops 0
ciMethodData java/lang/StringBuilder toString ()Ljava/lang/String; 2 18854 orig 264 128 100 200 94 0 0 0 0 80 155 67 229 109 2 0 0 48 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 29 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 122 0 0 0 97 73 2 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 16 0 0 0 255 255 255 255 2 0 13 0 0 0 0 0 data 2 0xd0002 0x492c oops 0
ciMethod sun/misc/ExtensionDependency <init> ()V 0 0 1 0 0
ciMethod sun/misc/ExtensionDependency checkExtensionsDependencies (Ljava/util/jar/JarFile;)Z 1633 1 258 0 0
ciMethod sun/misc/ExtensionDependency checkExtensions (Ljava/util/jar/JarFile;)Z 0 0 1 0 0
ciMethod java/util/jar/Attributes get (Ljava/lang/Object;)Ljava/lang/Object; 2057 1 8238 0 0
ciMethod java/util/jar/Attributes getValue (Ljava/util/jar/Attributes$Name;)Ljava/lang/String; 2057 1 8102 0 0
ciMethod java/util/jar/Attributes$Name equals (Ljava/lang/Object;)Z 2057 1 1600 0 0
ciMethod java/util/jar/Attributes$Name hashCode ()I 2049 1 5407 0 704
ciMethodData java/util/HashMap hash (Ljava/lang/Object;)I 2 34573 orig 264 128 100 200 94 0 0 0 0 192 142 74 229 109 2 0 0 176 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 10 1 0 0 25 48 4 0 1 0 0 0 181 93 0 0 0 0 0 0 2 0 0 0 0 0 6 0 2 0 0 0 104 0 0 0 255 255 255 255 7 224 1 0 0 0 0 0 data 13 0x1e007 0x853b 0x38 0xc9 0x50003 0xc9 0x48 0x90005 0x4630 0x26de5e56720 0x526 0x26de5eae840 0x39e5 oops 2 9 java/security/CodeSource 11 java/lang/String
ciMethodData sun/misc/URLClassPath getLoader (I)Lsun/misc/URLClassPath$Loader; 2 8356 orig 264 128 100 200 94 0 0 0 0 96 200 90 229 109 2 0 0 16 7 0 0 48 5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 8 1 0 0 225 252 0 0 129 4 0 0 0 0 0 0 0 0 0 0 2 0 0 0 1 0 72 0 2 0 0 0 192 5 0 0 255 255 255 255 7 0 4 0 0 0 0 0 data 184 0x40007 0x1f9c 0x20 0x0 0xd0005 0x0 0x26de944e340 0x202c 0x0 0x0 0x130007 0x1e20 0x420 0x20c 0x210005 0x0 0x26de94528e0 0x20c 0x0 0x0 0x240007 0x90 0x20 0x17c 0x2f0005 0x0 0x26de94528e0 0x90 0x0 0x0 0x320004 0x0 0x26de5e56450 0x90 0x0 0x0 0x380003 0x90 0x18 0x430002 0x90 0x4c0005 0x0 0x26debc0bca0 0x90 0x0 0x0 0x4f0007 0x8a 0x38 0x6 0x520003 0x6 0xfffffffffffffe88 0x570002 0x8a 0x5e0005 0x0 0x26debc0f3c0 0x88 0x0 0x0 0x650007 0x87 0x30 0x1 0x6b0002 0x1 0x6e0003 0x88 0x198 0x730003 0x2 0xfffffffffffffde8 0x7b0007 0x0 0x150 0x0 0x850002 0x0 0x8a0005 0x0 0x0 0x0 0x0 0x0 0x8e0005 0x0 0x0 0x0 0x0 0x0 0x930005 0x0 0x0 0x0 0x0 0x0 0x980005 0x0 0x0 0x0 0x0 0x0 0x9b0005 0x0 0x0 0x0 0x0 0x0 0x9e0005 0x0 0x0 0x0 0x0 0x0 0xa10003 0x0 0xfffffffffffffc80 0xa90005 0x0 0x26de944e340 0x88 0x0 0x0 0xad0002 0x88 0xb60005 0x0 0x26de944e340 0x88 0x0 0x0 0xc10005 0x0 0x26debc0bca0 0x88 0x0 0x0 0xc50003 0x88 0xfffffffffffffbc8 0xcb0007 0x1e20 0xf0 0x0 0xd50002 0x0 0xda0005 0x0 0x0 0x0 0x0 0x0 0xde0005 0x0 0x0 0x0 0x0 0x0 0xe10005 0x0 0x0 0x0 0x0 0x0 0xe40005 0x0 0x0 0x0 0x0 0x0 0xec0005 0x0 0x26de944e340 0x1e20 0x0 0x0 0xef0004 0x0 0x26debc0f3c0 0x1e20 0x0 0x0 oops 11 6 java/util/ArrayList 16 java/util/Stack 26 java/util/Stack 32 java/net/URL 43 java/util/HashMap 58 sun/misc/URLClassPath$JarLoader 121 java/util/ArrayList 129 java/util/ArrayList 135 java/util/HashMap 174 java/util/ArrayList 180 sun/misc/URLClassPath$JarLoader
ciMethodData java/util/Stack empty ()Z 1 558 orig 264 128 100 200 94 0 0 0 0 8 161 72 229 109 2 0 0 176 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 34 0 0 0 97 16 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 7 0 2 0 0 0 104 0 0 0 255 255 255 255 5 0 1 0 0 0 0 0 data 13 0x10005 0x0 0x26de94528e0 0x20c 0x0 0x0 0x40007 0x90 0x38 0x17c 0x80003 0x17c 0x18 oops 1 2 java/util/Stack
ciMethodData java/util/Stack pop ()Ljava/lang/Object; 1 325 orig 264 128 100 200 94 0 0 0 0 184 159 72 229 109 2 0 0 216 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 20 0 0 0 137 9 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 9 0 2 0 0 0 144 0 0 0 255 255 255 255 5 0 1 0 0 0 0 0 data 18 0x10005 0x0 0x26de94528e0 0x131 0x0 0x0 0x60005 0x0 0x26de94528e0 0x131 0x0 0x0 0xe0005 0x0 0x26de94528e0 0x131 0x0 0x0 oops 3 2 java/util/Stack 8 java/util/Stack 14 java/util/Stack
ciMethodData java/util/Stack peek ()Ljava/lang/Object; 1 330 orig 264 128 100 200 94 0 0 0 0 104 160 72 229 109 2 0 0 216 1 0 0 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 21 0 0 0 169 9 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 6 0 2 0 0 0 144 0 0 0 255 255 255 255 5 0 1 0 0 0 0 0 data 18 0x10005 0x0 0x26de94528e0 0x135 0x0 0x0 0x60007 0x135 0x30 0x0 0xd0002 0x0 0x150005 0x0 0x26de94528e0 0x135 0x0 0x0 oops 2 2 java/util/Stack 14 java/util/Stack
ciMethodData java/util/Vector elementAt (I)Ljava/lang/Object; 1 567 orig 264 128 100 200 94 0 0 0 0 104 64 72 229 109 2 0 0 128 2 0 0 240 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 41 0 0 0 113 16 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 5 0 2 0 0 0 48 1 0 0 255 255 255 255 7 0 5 0 0 0 0 0 data 38 0x50007 0x20e 0x100 0x0 0x100002 0x0 0x140005 0x0 0x0 0x0 0x0 0x0 0x190005 0x0 0x0 0x0 0x0 0x0 0x200005 0x0 0x0 0x0 0x0 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x260002 0x0 0x2c0005 0x0 0x26de91590f0 0xd9 0x26de94528e0 0x135 oops 2 34 java/util/Vector 36 java/util/Stack
ciMethodData java/util/Vector removeElementAt (I)V 1 325 orig 264 128 100 200 94 0 0 0 0 152 67 72 229 109 2 0 0 224 2 0 0 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 20 0 0 0 137 9 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 10 0 2 0 0 0 144 1 0 0 255 255 255 255 7 0 15 0 0 0 0 0 data 50 0xf0007 0x131 0x100 0x0 0x1a0002 0x0 0x1e0005 0x0 0x0 0x0 0x0 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x2a0005 0x0 0x0 0x0 0x0 0x0 0x2d0005 0x0 0x0 0x0 0x0 0x0 0x300002 0x0 0x350007 0x131 0x30 0x0 0x3d0002 0x0 0x4b0007 0x131 0x30 0x0 0x5b0002 0x0 0x710104 0x0 0x0 0x0 0x0 0x0 oops 0
ciMethodData sun/net/util/URLUtil urlNoFragString (Ljava/net/URL;)Ljava/lang/String; 1 490 orig 264 128 100 200 94 0 0 0 0 232 56 93 229 109 2 0 0 152 4 0 0 240 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 19 0 0 0 185 14 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 17 0 2 0 0 0 80 3 0 0 255 255 255 255 2 0 4 0 0 0 0 0 data 106 0x40002 0x1d7 0x90005 0x102 0x26de5e56450 0xd5 0x0 0x0 0xe0007 0x0 0xb0 0x1d7 0x120005 0x102 0x26de5eae840 0xd5 0x0 0x0 0x180005 0x102 0x26de5e560d0 0xd5 0x0 0x0 0x1f0005 0x102 0x26de5e560d0 0xd5 0x0 0x0 0x240005 0x102 0x26de5e56450 0xd5 0x0 0x0 0x290007 0x0 0x180 0x1d7 0x2d0005 0x102 0x26de5eae840 0xd5 0x0 0x0 0x330005 0x102 0x26de5e560d0 0xd5 0x0 0x0 0x380005 0x102 0x26de5e56450 0xd5 0x0 0x0 0x400007 0x0 0x50 0x1d7 0x440005 0x102 0x26de5e56450 0xd5 0x0 0x0 0x4c0007 0x1d7 0x80 0x0 0x520005 0x0 0x0 0x0 0x0 0x0 0x570005 0x0 0x0 0x0 0x0 0x0 0x5c0005 0x102 0x26de5e56450 0xd5 0x0 0x0 0x630007 0x0 0x50 0x1d7 0x690005 0x102 0x26de5e560d0 0xd5 0x0 0x0 0x6e0005 0x102 0x26de5e560d0 0xd5 0x0 0x0 oops 12 4 java/net/URL 14 java/lang/String 20 java/lang/StringBuilder 26 java/lang/StringBuilder 32 java/net/URL 42 java/lang/String 48 java/lang/StringBuilder 54 java/net/URL 64 java/net/URL 86 java/net/URL 96 java/lang/StringBuilder 102 java/lang/StringBuilder
ciMethodData java/util/HashMap containsKey (Ljava/lang/Object;)Z 2 3299 orig 264 128 100 200 94 0 0 0 0 16 153 74 229 109 2 0 0 200 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 19 0 0 0 129 102 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 10 0 2 0 0 0 120 0 0 0 255 255 255 255 2 0 2 0 0 0 0 0 data 15 0x20002 0xcd0 0x60005 0x73 0x26debc0bca0 0xbd7 0x26dede23030 0x86 0x90007 0x9db 0x38 0x2f5 0xd0003 0x2f5 0x18 oops 2 4 java/util/HashMap 6 java/util/LinkedHashMap
ciMethodData java/util/HashMap getNode (ILjava/lang/Object;)Ljava/util/HashMap$Node; 2 11528 orig 264 128 100 200 94 0 0 0 0 112 152 74 229 109 2 0 0 8 4 0 0 112 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 3 1 0 0 41 96 1 0 145 20 0 0 125 20 0 0 51 1 0 0 2 0 0 0 1 0 37 0 2 0 0 0 176 2 0 0 255 255 255 255 7 0 6 0 0 0 0 0 data 86 0x60007 0x494 0x2b0 0x2771 0xe0007 0x0 0x290 0x2771 0x1c0007 0xef9 0x270 0x1878 0x250007 0x910 0xb0 0xf68 0x310007 0x5e0 0x90 0x988 0x350007 0x0 0x70 0x988 0x3b0005 0x6c7 0x26dedd91cf0 0x96 0x26de5e56720 0x22b 0x3e0007 0x71 0x20 0x917 0x4c0007 0x527 0x1a0 0x45a 0x510004 0xfffffffffffffba6 0x26dede227b0 0x2e 0x0 0x0 0x540007 0x45a 0x80 0x0 0x590004 0x0 0x0 0x0 0x0 0x0 0x5e0005 0x0 0x0 0x0 0x0 0x0 0x680007 0x287 0xb0 0x2cb 0x740007 0x98 0x90 0x233 0x780007 0x0 0x70 0x233 0x7e0005 0xfd 0x26de5eae840 0x135 0x26de5e56330 0x1 0x81e007 0x10 0x20 0x225 0x8f0007 0xf8 0xffffffffffffff50 0x19f oops 5 26 java/util/jar/Attributes$Name 28 java/security/CodeSource 40 java/util/HashMap$Node 74 java/lang/String 76 java/io/File
ciMethodData sun/misc/URLClassPath getLoader (Ljava/net/URL;)Lsun/misc/URLClassPath$Loader; 1 313 orig 264 128 100 200 94 0 0 0 0 32 201 90 229 109 2 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 19 0 0 0 49 9 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 5 0 2 0 0 0 176 0 0 0 255 255 255 255 2 0 6 0 0 0 0 0 data 22 0x60002 0x126 0xd0002 0x126 0x100004 0x0 0x26debc0f3c0 0x124 0x0 0x0 0x160005 0x0 0x26dedd95f50 0x2 0x0 0x0 0x190004 0x0 0x26dedd96d80 0x2 0x0 0x0 oops 3 6 sun/misc/URLClassPath$JarLoader 12 java/security/PrivilegedActionException 18 java/io/FileNotFoundException
ciMethodData sun/misc/URLClassPath$3 <init> (Lsun/misc/URLClassPath;Ljava/net/URL;)V 1 313 orig 264 128 100 200 94 0 0 0 0 96 67 93 229 109 2 0 0 64 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 19 0 0 0 49 9 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 16 0 0 0 255 255 255 255 2 0 11 0 0 0 0 0 data 2 0xb0002 0x126 oops 0
ciMethodData sun/misc/URLClassPath push ([Ljava/net/URL;)V 1 318 orig 264 128 100 200 94 0 0 0 0 248 201 90 229 109 2 0 0 208 1 0 0 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 19 0 0 0 41 0 0 0 89 9 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 128 0 0 0 255 255 255 255 7 0 13 0 0 0 0 0 data 16 0xd0007 0x5 0x68 0x12b 0x170005 0x0 0x26de94528e0 0x12b 0x0 0x0 0x1e0003 0x12b 0xffffffffffffffb0 0x230003 0x5 0x18 oops 1 6 java/util/Stack
ciMethodData sun/misc/URLClassPath validateLookupCache (ILjava/lang/String;)V 1 311 orig 264 128 100 200 94 0 0 0 0 152 197 90 229 109 2 0 0 56 3 0 0 208 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 5 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 19 0 0 0 33 9 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 15 0 2 0 0 0 224 1 0 0 255 255 255 255 7 0 4 0 0 0 0 0 data 60 0x40007 0x124 0x1e0 0x0 0xa0007 0x0 0x1c0 0x0 0x130007 0x0 0x80 0x0 0x1d0002 0x0 0x200005 0x0 0x0 0x0 0x0 0x0 0x230007 0x0 0x20 0x0 0x2a0007 0x0 0x40 0x0 0x300007 0x0 0xf0 0x0 0x3a0002 0x0 0x3f0005 0x0 0x0 0x0 0x0 0x0 0x430005 0x0 0x0 0x0 0x0 0x0 0x460005 0x0 0x0 0x0 0x0 0x0 0x490005 0x0 0x0 0x0 0x0 0x0 0x4c0002 0x0 oops 0
ciMethodData java/util/ArrayList add (Ljava/lang/Object;)Z 2 13310 orig 264 128 100 200 94 0 0 0 0 168 10 73 229 109 2 0 0 144 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 97 0 0 0 233 156 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 15 0 2 0 0 0 64 0 0 0 255 255 255 255 2 0 7 0 0 0 0 0 data 8 0x70002 0x339d 0x1a0104 0x0 0x26de5eae8d0 0x89 0x26deb756a30 0x2 oops 2 4 java/lang/Class 6 [Ljava/lang/invoke/MemberName;
ciMethodData java/util/ArrayList ensureCapacityInternal (I)V 2 13516 orig 264 128 100 200 94 0 0 0 0 32 0 73 229 109 2 0 0 112 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 97 0 0 0 89 163 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 14 0 2 0 0 0 32 0 0 0 255 255 255 255 2 0 6 0 0 0 0 0 data 4 0x60002 0x346b 0x90002 0x346b oops 0
ciMethodData java/util/ArrayList calculateCapacity ([Ljava/lang/Object;I)I 2 13516 orig 264 128 100 200 94 0 0 0 0 128 255 72 229 109 2 0 0 128 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 97 0 0 0 89 163 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 48 0 0 0 255 255 255 255 7 0 4 0 0 0 0 0 data 6 0x40007 0x2726 0x30 0xd45 0xa0002 0xd45 oops 0
ciMethodData java/util/ArrayList ensureExplicitCapacity (I)V 2 13516 orig 264 128 100 200 94 0 0 0 0 208 0 73 229 109 2 0 0 88 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 97 0 0 0 89 163 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 48 0 0 0 255 255 255 255 7 0 17 0 0 0 0 0 data 6 0x110007 0x255b 0x30 0xf10 0x160002 0xf10 oops 0
ciMethodData java/util/ArrayList grow (I)V 2 3871 orig 264 128 100 200 94 0 0 0 0 152 1 73 229 109 2 0 0 176 1 0 0 64 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 15 0 0 0 129 120 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 15 0 2 0 0 0 96 0 0 0 255 255 255 255 7 0 15 0 0 0 0 0 data 12 0xf0007 0x1cb 0x20 0xd45 0x180007 0xf10 0x30 0x0 0x1c0002 0x0 0x260002 0xf10 oops 0
ciMethodData java/util/HashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 2 7025 orig 264 128 100 200 94 0 0 0 0 176 153 74 229 109 2 0 0 152 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 94 0 0 0 153 216 0 0 1 0 0 0 203 25 0 0 0 0 0 0 2 0 0 0 0 0 7 0 2 0 0 0 64 0 0 0 255 255 255 255 2 0 2 0 0 0 0 0 data 8 0x20002 0x1b13 0x90005 0x2aa 0x26dede23030 0x1182 0x26debc0bca0 0x6e7 oops 2 4 java/util/LinkedHashMap 6 java/util/HashMap
ciMethodData java/util/HashMap putVal (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; 2 6712 orig 264 128 100 200 94 0 0 0 0 144 155 74 229 109 2 0 0 176 6 0 0 232 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 4 0 0 8 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 98 0 0 0 177 206 0 0 233 10 0 0 142 24 0 0 77 1 0 0 2 0 0 0 1 0 50 0 2 0 0 0 64 5 0 0 255 255 255 255 7 0 7 0 0 0 0 0 data 168 0x70007 0x208 0x40 0x17ce 0x100007 0x17ce 0x50 0x0 0x140005 0x47 0x26debc0bca0 0xbc 0x26dede23030 0x105 0x2c0007 0x9b0 0x98 0x1026 0x387005 0xa 0x26debc0bca0 0x47d 0x26dede23030 0xba0 0x3b0004 0x0 0x26dede227b0 0x47e 0x26ded0960a0 0xba9 0x3c0003 0x1027 0x3d0 0x450007 0x715 0xc8 0x29b 0x510007 0x163 0x90 0x138 0x550007 0x0 0x88 0x138 0x5bf005 0x8 0x26de5e56330 0xbf 0x26de5eae840 0x77 0x5e0007 0x8 0x38 0x136 0x650003 0x299 0x278 0x6a0004 0xfffffffffffff8e3 0x26dede227b0 0x31 0x26ded0960a0 0x3 0x6d0007 0x71d 0x98 0x0 0x720004 0x0 0x0 0x0 0x0 0x0 0x7b0005 0x0 0x0 0x0 0x0 0x0 0x800003 0x0 0x1b0 0x8e0007 0x1d0 0xb8 0x6aa 0x980005 0x54d 0x26debc0bca0 0x159 0x26ded096150 0x4 0xa2e007 0x6aa 0x148 0x1 0xa90005 0x1 0x0 0x0 0x0 0x0 0xac0003 0x1 0xf8 0xb50007 0x158 0xc8 0x78 0xc10007 0x45 0xc0 0x33 0xc50007 0x0 0x88 0x33 0xcbf005 0x5 0x26de5e56330 0xf 0x26de5eae840 0x22 0xce0007 0x5 0x38 0x31 0xd10003 0x31 0x30 0xdb0003 0x15d 0xfffffffffffffe80 0xe00007 0x6ab 0x90 0x30f 0xec0007 0x30f 0x40 0x0 0xf10007 0x0 0x20 0x0 0xfdf005 0x94 0x26dede23030 0x26b 0x26debc0bca0 0x12 0x11c0007 0x158b 0x50 0x147 0x1200005 0xf 0x26debc0bca0 0x9d 0x26dede23030 0x9b 0x1270005 0x10ef 0x26debc0bca0 0x5d6 0x26ded096150 0xd oops 20 10 java/util/HashMap 12 java/util/LinkedHashMap 20 java/util/HashMap 22 java/util/LinkedHashMap 26 java/util/HashMap$Node 28 java/util/LinkedHashMap$Entry 47 java/io/File 49 java/lang/String 60 java/util/HashMap$Node 62 java/util/LinkedHashMap$Entry 89 java/util/HashMap 91 java/io/ExpiringCache$1 120 java/io/File 122 java/lang/String 148 java/util/LinkedHashMap 150 java/util/HashMap 158 java/util/HashMap 160 java/util/LinkedHashMap 164 java/util/HashMap 166 java/io/ExpiringCache$1
ciMethodData java/util/ArrayList get (I)Ljava/lang/Object; 2 8808 orig 264 128 100 200 94 0 0 0 0 80 9 73 229 109 2 0 0 144 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 250 0 0 0 113 11 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 6 0 2 0 0 0 64 0 0 0 255 255 255 255 2 0 2 0 0 0 0 0 data 8 0x20002 0x216e 0x70005 0x0 0x26de944e340 0x216e 0x0 0x0 oops 1 4 java/util/ArrayList
ciMethodData java/util/ArrayList rangeCheck (I)V 2 8890 orig 264 128 100 200 94 0 0 0 0 48 18 73 229 109 2 0 0 104 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 250 0 0 0 1 14 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 5 0 2 0 0 0 64 0 0 0 255 255 255 255 7 0 5 0 0 0 0 0 data 8 0x50007 0x21c0 0x40 0x0 0xe0002 0x0 0x110002 0x0 oops 0
ciMethodData java/util/ArrayList elementData (I)Ljava/lang/Object; 2 8890 orig 264 128 100 200 94 0 0 0 0 176 8 73 229 109 2 0 0 40 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 250 0 0 0 1 14 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 1 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 0 0 0 0 data 0 oops 0
ciMethodData sun/misc/URLClassPath getNextLoader ([II)Lsun/misc/URLClassPath$Loader; 2 8344 orig 264 128 100 200 94 0 0 0 0 144 198 90 229 109 2 0 0 168 3 0 0 16 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 8 1 0 0 121 252 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 14 0 2 0 0 0 80 2 0 0 255 255 255 255 7 0 4 0 0 0 0 0 data 74 0x40007 0x1f8f 0x20 0x0 0xa0007 0x1f8f 0x220 0x0 0x100007 0x0 0x200 0x0 0x1a0005 0x0 0x0 0x0 0x0 0x0 0x1d0004 0x0 0x0 0x0 0x0 0x0 0x240007 0x0 0x180 0x0 0x2e0002 0x0 0x330005 0x0 0x0 0x0 0x0 0x0 0x390005 0x0 0x0 0x0 0x0 0x0 0x3e0005 0x0 0x0 0x0 0x0 0x0 0x420005 0x0 0x0 0x0 0x0 0x0 0x450005 0x0 0x0 0x0 0x0 0x0 0x480005 0x0 0x0 0x0 0x0 0x0 0x4b0005 0x0 0x0 0x0 0x0 0x0 0x540002 0x1f8f oops 0
ciMethodData sun/misc/URLClassPath$JarLoader ensureOpen ()V 2 5285 orig 264 128 100 200 94 0 0 0 0 208 95 93 229 109 2 0 0 0 2 0 0 64 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 86 0 0 0 121 162 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 8 0 2 0 0 0 184 0 0 0 255 255 255 255 7 0 4 0 0 0 0 0 data 23 0x40007 0x13c9 0xb8 0x86 0xc0002 0x86 0x130002 0x86 0x170003 0x84 0x78 0x1c0005 0x0 0x26dedd95f50 0x2 0x0 0x0 0x1f0004 0x0 0x26dedd96d80 0x2 0x0 0x0 oops 2 13 java/security/PrivilegedActionException 19 java/io/FileNotFoundException
ciMethodData java/lang/String toLowerCase (Ljava/util/Locale;)Ljava/lang/String; 2 103797 orig 264 128 100 200 94 0 0 0 0 208 104 60 229 109 2 0 0 112 6 0 0 248 4 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 251 13 0 0 217 42 0 0 209 59 12 0 120 2 0 0 210 62 1 0 2 0 0 0 3 0 69 0 2 0 0 0 32 5 0 0 255 255 255 255 7 0 1 0 0 0 0 0 data 164 0x10007 0x55b 0x30 0x0 0x80002 0x0 0x160007 0x25d 0x160 0x947 0x250007 0x947 0xe0 0x0 0x2c0007 0x0 0xc0 0x0 0x310005 0x0 0x0 0x0 0x0 0x0 0x3a0002 0x0 0x3d0007 0x0 0x38 0x0 0x400003 0x0 0xa0 0x460002 0x0 0x4b0003 0x0 0x60 0x520002 0x947 0x55e007 0x649 0x38 0x2ff 0x580003 0x2ff 0x30 0x5e0003 0x649 0xfffffffffffffeb8 0x740002 0x2ff 0x780005 0x6 0x26decf69660 0x2f9 0x0 0x0 0x810007 0x0 0x60 0x2ff 0x880007 0x0 0x40 0x2ff 0x8f0007 0x2ff 0x38 0x0 0x930003 0x0 0x18 0x9f0007 0x2fe 0x2c8 0x1812d 0xb00007 0x1812d 0x98 0x0 0xb80007 0x0 0x78 0x0 0xbe0005 0x0 0x0 0x0 0x0 0x0 0xc50002 0x0 0xca0003 0x0 0x18 0xd20007 0x0 0x60 0x1812d 0xda0007 0x0 0x40 0x1812d 0xe20007 0x1812d 0x48 0x0 0xe90002 0x0 0xee0003 0x0 0x28 0xf30002 0x1812d 0xfb0007 0x0 0x40 0x1812d 0x1020007 0x1812d 0x140 0x0 0x1080007 0x0 0x48 0x0 0x10f0002 0x0 0x1140003 0x0 0x70 0x11a0007 0x0 0x48 0x0 0x1280002 0x0 0x1310003 0x0 0xa8 0x1360002 0x0 0x1440007 0x0 0x30 0x0 0x15f0002 0x0 0x16d0007 0x0 0x38 0x0 0x1830003 0x0 0xffffffffffffffe0 0x1900003 0x0 0x18 0x1a50003 0x1812d 0xfffffffffffffd50 0x1b30002 0x2fe oops 1 54 java/util/Locale
ciMethodData java/util/HashMap resize ()[Ljava/util/HashMap$Node; 2 14170 orig 264 128 100 200 94 0 0 0 0 192 157 74 229 109 2 0 0 56 6 0 0 176 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 81 0 0 0 89 32 0 0 201 184 1 0 0 0 0 0 0 0 0 0 2 0 0 0 2 0 56 0 2 0 0 0 240 4 0 0 255 255 255 255 7 0 6 0 0 0 0 0 data 158 0x60007 0x169 0x38 0x2a2 0xa0003 0x2a2 0x18 0x190007 0x2a2 0x98 0x169 0x1f0007 0x169 0x20 0x0 0x320007 0x0 0x90 0x169 0x380007 0x65 0x70 0x104 0x400003 0x104 0x50 0x440007 0xb7 0x38 0x1eb 0x4a0003 0x1eb 0x18 0x570007 0x1bb 0x78 0x250 0x680007 0x0 0x58 0x250 0x700007 0x0 0x38 0x250 0x760003 0x250 0x18 0x880004 0x0 0x26dede29530 0xed 0x0 0x0 0x940007 0x2a2 0x340 0x169 0x9d0007 0x169 0x320 0x283a 0xa70007 0x1199 0x2e8 0x16a1 0xae0104 0x0 0x0 0x0 0x0 0x0 0xb40007 0x709 0x68 0xf98 0xc50004 0x0 0x26dede227b0 0x594 0x26ded0960a0 0xa04 0xc60003 0xf98 0x248 0xcb0004 0xfffffffffffff8f7 0x26dede227b0 0x48 0x26ded0960a0 0x5e 0xce0007 0x709 0x98 0x0 0xd30004 0x0 0x0 0x0 0x0 0x0 0xdc0005 0x0 0x0 0x0 0x0 0x0 0xdf0003 0x0 0x180 0xfc0007 0x700 0x70 0x885 0x1010007 0x2e7 0x38 0x59e 0x1080003 0x59e 0x18 0x1160003 0x885 0x50 0x11b0007 0x200 0x38 0x500 0x1220003 0x500 0x18 0x1350007 0x87c 0xffffffffffffff58 0x709 0x13a0007 0x16b 0x50 0x59e 0x1490004 0x0 0x26dede227b0 0x26e 0x26ded0960a0 0x330 0x14c0007 0x209 0x50 0x500 0x15d0004 0x0 0x26dede227b0 0x22f 0x26ded0960a0 0x2d1 0x1610003 0x283a 0xfffffffffffffcf8 oops 9 50 [Ljava/util/HashMap$Node; 78 java/util/HashMap$Node 80 java/util/LinkedHashMap$Entry 87 java/util/HashMap$Node 89 java/util/LinkedHashMap$Entry 141 java/util/HashMap$Node 143 java/util/LinkedHashMap$Entry 151 java/util/HashMap$Node 153 java/util/LinkedHashMap$Entry
ciMethodData java/util/HashMap treeifyBin ([Ljava/util/HashMap$Node;I)V 1 1 orig 264 128 100 200 94 0 0 0 0 208 158 74 229 109 2 0 0 8 3 0 0 136 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 9 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 176 1 0 0 255 255 255 255 7 0 1 0 0 0 0 0 data 54 0x10007 0x0 0x40 0x1 0xa0007 0x0 0x68 0x1 0xe0005 0x1 0x0 0x0 0x0 0x0 0x120003 0x1 0x140 0x220007 0x0 0x128 0x0 0x2f0005 0x0 0x0 0x0 0x0 0x0 0x360007 0x0 0x38 0x0 0x3d0003 0x0 0x18 0x5a0007 0x0 0xffffffffffffff98 0x0 0x630004 0x0 0x0 0x0 0x0 0x0 0x640007 0x0 0x50 0x0 0x6a0005 0x0 0x0 0x0 0x0 0x0 oops 0
ciMethodData java/util/HashMap get (Ljava/lang/Object;)Ljava/lang/Object; 2 18841 orig 264 128 100 200 94 0 0 0 0 64 151 74 229 109 2 0 0 200 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 128 1 0 0 201 64 2 0 1 0 0 0 24 44 0 0 0 0 0 0 2 0 0 0 0 0 10 0 2 0 0 0 120 0 0 0 255 255 255 255 2 0 2 0 0 0 0 0 data 15 0x20002 0x4819 0x60005 0x142 0x26debc0bca0 0x46d5 0x26deb793240 0x3 0xb0007 0x2912 0x38 0x1f09 0xf0003 0x1f09 0x18 oops 2 4 java/util/HashMap 6 sun/nio/ch/WindowsSelectorImpl$FdMap
ciMethodData java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 2 11065 orig 264 128 100 200 94 0 0 0 0 192 221 74 229 109 2 0 0 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 3 1 0 0 177 81 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 16 0 0 0 255 255 255 255 2 0 1 0 0 0 0 0 data 2 0x10002 0x2a36 oops 0
ciMethodData java/util/HashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 2 5402 orig 264 128 100 200 94 0 0 0 0 40 183 74 229 109 2 0 0 80 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 25 1 0 0 9 160 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 16 0 0 0 255 255 255 255 2 0 9 0 0 0 0 0 data 2 0x90002 0x1401 oops 0
ciMethodData java/util/HashMap afterNodeInsertion (Z)V 2 1425 orig 264 128 100 200 94 0 0 0 0 240 186 74 229 109 2 0 0 40 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 142 1 0 0 25 32 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 1 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 0 0 0 0 data 0 oops 0
ciMethod java/security/PrivilegedActionException getException ()Ljava/lang/Exception; 33 1 4 0 0
ciMethodData java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 2 5232 orig 264 128 100 200 94 0 0 0 0 104 154 77 229 109 2 0 0 192 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 8 1 0 0 65 155 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 112 0 0 0 255 255 255 255 5 0 3 0 0 0 0 0 data 14 0x30005 0x443 0x26decf63810 0xee6 0x26dec2ccd80 0x3f 0x60002 0x1368 0x90004 0x0 0x0 0x0 0x0 0x0 oops 2 2 [Ljava/lang/Object; 4 [Ljava/lang/reflect/Method;
ciMethodData java/lang/String toLowerCase ()Ljava/lang/String; 2 2071 orig 264 128 100 200 94 0 0 0 0 104 105 60 229 109 2 0 0 136 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 1 0 0 169 56 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 64 0 0 0 255 255 255 255 2 0 1 0 0 0 0 0 data 8 0x10002 0x715 0x40005 0x0 0x26de5eae840 0x715 0x0 0x0 oops 1 4 java/lang/String
ciMethodData java/util/Locale getDefault ()Ljava/util/Locale; 2 2081 orig 264 128 100 200 94 0 0 0 0 120 63 87 229 109 2 0 0 24 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 2 1 0 0 249 56 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 1 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 0 0 0 0 data 0 oops 0
ciMethodData java/util/jar/Attributes$Name hashCode ()I 2 5407 orig 264 128 100 200 94 0 0 0 0 80 168 97 229 109 2 0 0 120 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 249 160 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 6 0 2 0 0 0 48 0 0 0 255 255 255 255 7 0 5 0 0 0 0 0 data 6 0x50007 0xde3 0x30 0x63c 0xd0002 0x63c oops 0
ciMethodData sun/misc/ASCIICaseInsensitiveComparator lowerCaseHashCode (Ljava/lang/String;)I 2 18693 orig 264 128 100 200 94 0 0 0 0 200 39 79 229 109 2 0 0 240 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 231 4 0 0 25 34 0 0 241 32 2 0 0 0 0 0 0 0 0 0 2 0 0 0 1 0 20 0 2 0 0 0 168 0 0 0 255 255 255 255 5 0 3 0 0 0 0 0 data 21 0x30005 0x4f 0x26de5eae840 0x3f4 0x0 0x0 0xb0007 0x443 0x78 0x441e 0x140005 0x50a 0x26de5eae840 0x3f14 0x0 0x0 0x170002 0x441e 0x1f0003 0x441e 0xffffffffffffffa0 oops 2 2 java/lang/String 12 java/lang/String
ciMethodData sun/misc/SharedSecrets javaUtilJarAccess ()Lsun/misc/JavaUtilJarAccess; 2 8822 orig 264 128 100 200 94 0 0 0 0 216 60 74 229 109 2 0 0 144 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 169 11 1 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 6 0 2 0 0 0 80 0 0 0 255 255 255 255 7 0 3 0 0 0 0 0 data 10 0x30007 0x2175 0x50 0x0 0xb0005 0x0 0x0 0x0 0x0 0x0 oops 0
ciMethodData java/util/jar/Attributes get (Ljava/lang/Object;)Ljava/lang/Object; 2 8238 orig 264 128 100 200 94 0 0 0 0 176 115 97 229 109 2 0 0 128 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 105 249 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 48 0 0 0 255 255 255 255 5 0 5 0 0 0 0 0 data 6 0x50005 0x0 0x26debc0bca0 0x1f2d 0x0 0x0 oops 1 2 java/util/HashMap
ciMethodData java/util/jar/Attributes getValue (Ljava/util/jar/Attributes$Name;)Ljava/lang/String; 2 8104 orig 264 128 100 200 94 0 0 0 0 232 116 97 229 109 2 0 0 176 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 57 245 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 96 0 0 0 255 255 255 255 5 0 2 0 0 0 0 0 data 12 0x20005 0x0 0x26deb950ec0 0x1ea7 0x0 0x0 0x50104 0x0 0x26de5eae840 0x600 0x0 0x0 oops 2 2 java/util/jar/Attributes 8 java/lang/String
ciMethod java/lang/ArrayIndexOutOfBoundsException <init> (Ljava/lang/String;)V 113 1 14 0 -1
ciMethodData java/net/URLStreamHandler getDefaultPort ()I 2 8179 orig 264 128 100 200 94 0 0 0 0 248 69 90 229 109 2 0 0 32 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 128 0 0 0 153 251 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 0 0 0 0 data 0 oops 0
ciMethodData java/util/jar/Attributes$Name equals (Ljava/lang/Object;)Z 2 1600 orig 264 128 100 200 94 0 0 0 0 168 167 97 229 109 2 0 0 56 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 17 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 249 41 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 8 0 2 0 0 0 232 0 0 0 255 255 255 255 4 0 1 0 0 0 0 0 data 29 0x10004 0x0 0x26dedd91cf0 0x53f 0x0 0x0 0x40007 0x0 0xb8 0x53f 0x110004 0x0 0x26dedd91cf0 0x53f 0x0 0x0 0x170005 0x0 0x26deb7b4a00 0x53f 0x0 0x0 0x1c0007 0x0 0x38 0x53f 0x200003 0x53f 0x18 oops 3 2 java/util/jar/Attributes$Name 12 java/util/jar/Attributes$Name 18 sun/misc/ASCIICaseInsensitiveComparator
ciMethodData java/util/Vector elementData (I)Ljava/lang/Object; 2 567 orig 264 128 100 200 94 0 0 0 0 144 73 72 229 109 2 0 0 40 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 185 9 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 1 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 0 0 0 0 data 0 oops 0
ciMethodData java/net/URL getDefaultPort ()I 1 490 orig 264 128 100 200 94 0 0 0 0 104 198 68 229 109 2 0 0 120 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 1 0 0 73 7 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 3 0 2 0 0 0 48 0 0 0 255 255 255 255 5 0 4 0 0 0 0 0 data 6 0x40005 0x0 0x26deb899060 0xe9 0x0 0x0 oops 1 2 sun/net/www/protocol/file/Handler
ciMethodData sun/misc/URLClassPath$JarLoader getClassPath ()[Ljava/net/URL; 1 311 orig 264 128 100 200 94 0 0 0 0 24 105 93 229 109 2 0 0 8 3 0 0 32 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0 0 185 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 20 0 2 0 0 0 192 1 0 0 255 255 255 255 7 0 4 0 0 0 0 0 data 56 0x40007 0x36 0x20 0x1 0xd0007 0x36 0x20 0x0 0x130002 0x36 0x170002 0x36 0x1a0002 0x36 0x210005 0x0 0x26deb951d80 0x36 0x0 0x0 0x260007 0x36 0x120 0x0 0x2d0005 0x0 0x0 0x0 0x0 0x0 0x320007 0x0 0xd0 0x0 0x360005 0x0 0x0 0x0 0x0 0x0 0x3b0007 0x0 0x80 0x0 0x420005 0x0 0x0 0x0 0x0 0x0 0x470007 0x0 0x30 0x0 0x500002 0x0 oops 1 16 java/util/jar/JavaUtilJarAccessImpl
ciMethodData sun/misc/URLClassPath$JarLoader parseExtensionsDependencies ()V 1 258 orig 264 128 100 200 94 0 0 0 0 184 105 93 229 109 2 0 0 88 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 204 0 0 0 177 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 16 0 0 0 255 255 255 255 2 0 4 0 0 0 0 0 data 2 0x40002 0x36 oops 0
ciMethodData sun/misc/ExtensionDependency checkExtensionsDependencies (Ljava/util/jar/JarFile;)Z 1 258 orig 264 128 100 200 94 0 0 0 0 200 80 96 229 109 2 0 0 232 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 204 0 0 0 177 1 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 160 0 0 0 255 255 255 255 7 0 3 0 0 0 0 0 data 20 0x30007 0x0 0x20 0x36 0xc0002 0x0 0x120005 0x0 0x0 0x0 0x0 0x0 0x180005 0x0 0x0 0x0 0x0 0x0 0x1b0002 0x0 oops 0
ciMethodData sun/misc/URLClassPath$JarLoader parseClassPath (Ljava/net/URL;Ljava/lang/String;)[Ljava/net/URL; 1 12 orig 264 128 100 200 94 0 0 0 0 248 106 93 229 109 2 0 0 248 4 0 0 40 3 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 12 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 160 3 0 0 255 255 255 255 2 0 5 0 0 0 0 0 data 116 0x50002 0x0 0xa0005 0x0 0x0 0x0 0x0 0x0 0x160005 0x0 0x0 0x0 0x0 0x0 0x190007 0x0 0x298 0x0 0x1d0005 0x0 0x0 0x0 0x0 0x0 0x220002 0x0 0x250007 0x0 0x48 0x0 0x2f0002 0x0 0x320003 0x0 0x28 0x380002 0x0 0x3f0007 0x0 0x68 0x0 0x480004 0x0 0x0 0x0 0x0 0x0 0x4c0003 0x0 0x178 0x4f0002 0x0 0x520007 0x0 0x150 0x0 0x5c0002 0x0 0x610005 0x0 0x0 0x0 0x0 0x0 0x660005 0x0 0x0 0x0 0x0 0x0 0x6b0005 0x0 0x0 0x0 0x0 0x0 0x6f0005 0x0 0x0 0x0 0x0 0x0 0x720005 0x0 0x0 0x0 0x0 0x0 0x750005 0x0 0x0 0x0 0x0 0x0 0x780003 0x0 0xfffffffffffffd50 0x7d0007 0x0 0x38 0x0 0x830003 0x0 0x78 0x8b0007 0x0 0x60 0x0 0x920002 0x0 0x950004 0x0 0x0 0x0 0x0 0x0 oops 0
ciMethodData sun/misc/URLClassPath disableAllLookupCaches ()V 1 3 orig 264 128 100 200 94 0 0 0 0 88 192 90 229 109 2 0 0 24 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 3 0 0 0 1 0 0 0 1 0 0 0 0 0 0 0 0 0 0 0 2 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 255 255 255 255 0 0 0 0 0 0 0 0 data 0 oops 0
ciMethodData java/lang/Integer stringSize (I)I 1 1098 orig 264 128 100 200 94 0 0 0 0 24 82 70 229 109 2 0 0 88 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 131 1 0 0 57 22 0 0 65 14 0 0 0 0 0 0 0 0 0 0 2 0 0 0 1 0 6 0 2 0 0 0 56 0 0 0 255 255 255 255 7 0 8 0 0 0 0 0 data 7 0x80007 0x1c8 0x20 0x2c7 0x120003 0x1c8 0xffffffffffffffe0 oops 0
ciMethodData java/lang/Integer getChars (II[C)V 1 1182 orig 264 128 100 200 94 0 0 0 0 112 81 70 229 109 2 0 0 232 1 0 0 120 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 77 68 79 32 101 120 116 114 97 32 100 97 116 97 32 108 111 99 107 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 131 1 0 0 57 22 0 0 57 36 0 0 0 0 0 0 0 0 0 0 2 0 0 0 2 0 15 0 2 0 0 0 176 0 0 0 255 255 255 255 7 0 7 0 0 0 0 0 data 22 0x70007 0x2c7 0x20 0x0 0x140007 0x2c7 0x38 0x8 0x480003 0x8 0xffffffffffffffe0 0x6e0007 0x1b8 0x0 0x2c7 0x710003 0x2c7 0x18 0x760007 0x2c7 0x20 0x0 oops 0
compile sun/misc/URLClassPath getNextLoader ([II)Lsun/misc/URLClassPath$Loader; -1 4 inline 97 0 -1 sun/misc/URLClassPath getNextLoader ([II)Lsun/misc/URLClassPath$Loader; 1 84 sun/misc/URLClassPath getLoader (I)Lsun/misc/URLClassPath$Loader; 2 13 java/util/ArrayList size ()I 2 33 java/util/Stack empty ()Z 3 1 java/util/Vector size ()I 2 47 java/util/Stack pop ()Ljava/lang/Object; 3 1 java/util/Vector size ()I 3 6 java/util/Stack peek ()Ljava/lang/Object; 4 1 java/util/Vector size ()I 2 67 sun/net/util/URLUtil urlNoFragString (Ljava/net/URL;)Ljava/lang/String; 3 4 java/lang/StringBuilder <init> ()V 4 3 java/lang/AbstractStringBuilder <init> (I)V 5 1 java/lang/Object <init> ()V 3 9 java/net/URL getProtocol ()Ljava/lang/String; 3 18 java/lang/String toLowerCase ()Ljava/lang/String; 4 1 java/util/Locale getDefault ()Ljava/util/Locale; 3 36 java/net/URL getHost ()Ljava/lang/String; 3 45 java/lang/String toLowerCase ()Ljava/lang/String; 4 1 java/util/Locale getDefault ()Ljava/util/Locale; 3 56 java/net/URL getPort ()I 3 68 java/net/URL getDefaultPort ()I 4 4 java/net/URLStreamHandler getDefaultPort ()I 3 87 java/lang/StringBuilder append (I)Ljava/lang/StringBuilder; 4 2 java/lang/AbstractStringBuilder append (I)Ljava/lang/AbstractStringBuilder; 5 30 java/lang/Integer stringSize (I)I 5 43 java/lang/AbstractStringBuilder ensureCapacityInternal (I)V 6 17 java/lang/AbstractStringBuilder newCapacity (I)I 6 20 java/util/Arrays copyOf ([CI)[C 5 52 java/lang/Integer getChars (II[C)V 3 92 java/net/URL getFile ()Ljava/lang/String; 2 76 java/util/HashMap containsKey (Ljava/lang/Object;)Z 3 2 java/util/HashMap hash (Ljava/lang/Object;)I 4 9 java/lang/String hashCode ()I 3 6 java/util/HashMap getNode (ILjava/lang/Object;)Ljava/util/HashMap$Node; 2 87 sun/misc/URLClassPath getLoader (Ljava/net/URL;)Lsun/misc/URLClassPath$Loader; 3 6 sun/misc/URLClassPath$3 <init> (Lsun/misc/URLClassPath;Ljava/net/URL;)V 4 11 java/lang/Object <init> ()V 2 94 sun/misc/URLClassPath$JarLoader getClassPath ()[Ljava/net/URL; 3 23 sun/misc/URLClassPath$JarLoader parseExtensionsDependencies ()V 4 4 sun/misc/ExtensionDependency checkExtensionsDependencies (Ljava/util/jar/JarFile;)Z 5 12 sun/misc/ExtensionDependency <init> ()V 6 1 java/lang/Object <init> ()V 3 26 sun/misc/SharedSecrets javaUtilJarAccess ()Lsun/misc/JavaUtilJarAccess; 3 54 java/util/jar/Manifest getMainAttributes ()Ljava/util/jar/Attributes; 3 66 java/util/jar/Attributes getValue (Ljava/util/jar/Attributes$Name;)Ljava/lang/String; 4 2 java/util/jar/Attributes get (Ljava/lang/Object;)Ljava/lang/Object; 5 5 java/util/HashMap get (Ljava/lang/Object;)Ljava/lang/Object; 6 2 java/util/HashMap hash (Ljava/lang/Object;)I 7 9 java/util/jar/Attributes$Name hashCode ()I 8 13 sun/misc/ASCIICaseInsensitiveComparator lowerCaseHashCode (Ljava/lang/String;)I 9 3 java/lang/String length ()I 9 20 java/lang/String charAt (I)C 9 23 sun/misc/ASCIICaseInsensitiveComparator toLower (I)I 10 1 sun/misc/ASCIICaseInsensitiveComparator isUpper (I)Z 6 6 java/util/HashMap getNode (ILjava/lang/Object;)Ljava/util/HashMap$Node; 7 59 java/util/jar/Attributes$Name equals (Ljava/lang/Object;)Z 8 23 sun/misc/ASCIICaseInsensitiveComparator compare (Ljava/lang/Object;Ljava/lang/Object;)I 9 9 sun/misc/ASCIICaseInsensitiveComparator compare (Ljava/lang/String;Ljava/lang/String;)I 10 1 java/lang/String length ()I 10 6 java/lang/String length ()I 10 38 java/lang/String charAt (I)C 10 46 java/lang/String charAt (I)C 10 88 sun/misc/ASCIICaseInsensitiveComparator toLower (I)I 10 96 sun/misc/ASCIICaseInsensitiveComparator toLower (I)I 7 126 java/util/jar/Attributes$Name equals (Ljava/lang/Object;)Z 8 23 sun/misc/ASCIICaseInsensitiveComparator compare (Ljava/lang/Object;Ljava/lang/Object;)I 9 9 sun/misc/ASCIICaseInsensitiveComparator compare (Ljava/lang/String;Ljava/lang/String;)I 10 1 java/lang/String length ()I 10 6 java/lang/String length ()I 10 38 java/lang/String charAt (I)C 10 46 java/lang/String charAt (I)C 10 88 sun/misc/ASCIICaseInsensitiveComparator toLower (I)I 10 96 sun/misc/ASCIICaseInsensitiveComparator toLower (I)I 2 169 java/util/ArrayList size ()I 2 173 sun/misc/URLClassPath validateLookupCache (ILjava/lang/String;)V 3 76 sun/misc/URLClassPath disableAllLookupCaches ()V 2 182 java/util/ArrayList add (Ljava/lang/Object;)Z 3 7 java/util/ArrayList ensureCapacityInternal (I)V 4 6 java/util/ArrayList calculateCapacity ([Ljava/lang/Object;I)I 4 9 java/util/ArrayList ensureExplicitCapacity (I)V 5 22 java/util/ArrayList grow (I)V 6 38 java/util/Arrays copyOf ([Ljava/lang/Object;I)[Ljava/lang/Object; 2 193 java/util/HashMap put (Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object; 3 2 java/util/HashMap hash (Ljava/lang/Object;)I 4 9 java/lang/String hashCode ()I 3 9 java/util/HashMap putVal (ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; 4 56 java/util/HashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 5 9 java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 6 1 java/lang/Object <init> ()V 4 152 java/util/HashMap newNode (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)Ljava/util/HashMap$Node; 5 9 java/util/HashMap$Node <init> (ILjava/lang/Object;Ljava/lang/Object;Ljava/util/HashMap$Node;)V 6 1 java/lang/Object <init> ()V 4 253 java/util/HashMap afterNodeAccess (Ljava/util/HashMap$Node;)V 4 295 java/util/HashMap afterNodeInsertion (Z)V 2 236 java/util/ArrayList get (I)Ljava/lang/Object; 3 2 java/util/ArrayList rangeCheck (I)V 3 7 java/util/ArrayList elementData (I)Ljava/lang/Object;
