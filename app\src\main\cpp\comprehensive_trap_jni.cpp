#include <jni.h>
#include <android/log.h>
#include "comprehensive_trap_system.h"
#include "memory_trap_sdk.h"
#include "DpMemoryTrap.h"
#include "AdvancedMemoryTrap.h"

#define TAG "ComprehensiveTrapJNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

// 全局回调引用
static JavaVM* g_jvm = nullptr;
static jobject g_callback_obj = nullptr;
static jmethodID g_callback_method = nullptr;

// JNI回调函数
void DetectionCallback(const MemoryTrapSDK::DetectionEvent& event) {
    LOGI("检测到内存访问事件: 地址=%p, 描述=%s", event.fault_address, event.description.c_str());
    
    if (!g_jvm || !g_callback_obj || !g_callback_method) {
        LOGE("回调环境未正确设置");
        return;
    }
    
    JNIEnv* env = nullptr;
    bool need_detach = false;
    
    // 获取JNIEnv
    int status = g_jvm->GetEnv((void**)&env, JNI_VERSION_1_6);
    if (status == JNI_EDETACHED) {
        status = g_jvm->AttachCurrentThread(&env, nullptr);
        if (status < 0) {
            LOGE("无法附加到当前线程");
            return;
        }
        need_detach = true;
    } else if (status != JNI_OK) {
        LOGE("无法获取JNIEnv");
        return;
    }
    
    // 准备参数
    jlong address = reinterpret_cast<jlong>(event.fault_address);
    jstring description = env->NewStringUTF(event.description.c_str());
    jboolean is_modifier_scan = event.analysis.is_modifier_scan ? JNI_TRUE : JNI_FALSE;
    jint access_frequency = static_cast<jint>(event.analysis.access_frequency);
    jfloat step_ratio = static_cast<jfloat>(event.analysis.step_ratio);
    
    // 调用Java回调
    env->CallVoidMethod(g_callback_obj, g_callback_method, 
                        address, description, is_modifier_scan, 
                        access_frequency, step_ratio);
    
    // 清理局部引用
    env->DeleteLocalRef(description);
    
    // 检查并清除异常
    if (env->ExceptionCheck()) {
        env->ExceptionClear();
        LOGE("调用Java回调时发生异常");
    }
    
    // 如果需要，分离线程
    if (need_detach) {
        g_jvm->DetachCurrentThread();
    }
}

extern "C" {

JNIEXPORT jboolean JNICALL
Java_com_sy_newfwg_ComprehensiveMemoryTrapManager_nativeInitializeTrapSystem(JNIEnv* env, jclass clazz) {
    bool result = ComprehensiveTrap::init_comprehensive_trap_system();
    LOGI("初始化综合陷阱系统: %s", result ? "成功" : "失败");
    return result ? JNI_TRUE : JNI_FALSE;
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_ComprehensiveMemoryTrapManager_nativeDeployTraps(JNIEnv* env, jclass clazz) {
    ComprehensiveTrap::deploy_comprehensive_traps();
    LOGI("部署综合陷阱完成");
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_ComprehensiveMemoryTrapManager_nativeStartDetection(JNIEnv* env, jclass clazz) {
    ComprehensiveTrap::start_comprehensive_detection();
    LOGI("开始内存陷阱检测");
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_ComprehensiveMemoryTrapManager_nativeLogCaCbStatus(JNIEnv* env, jclass clazz) {
    LOGI("🔍 [调试] 检查Ca/Cb区域状态...");

    // 获取DpMemoryTrap系统的统计信息
    auto& dpSystem = DpTrap::DpMemoryTrapSystem::GetInstance();
    size_t caSize = dpSystem.GetCaMemorySize();
    size_t cbSize = dpSystem.GetCbMemorySize();
    size_t caCount = dpSystem.GetCaTrapCount();
    size_t cbCount = dpSystem.GetCbTrapCount();

    LOGI("📊 [Ca区域] 陷阱数量: %zu, 内存大小: %zu 字节 (%.2f KB)",
         caCount, caSize, (float)caSize / 1024.0f);
    LOGI("📊 [Cb区域] 陷阱数量: %zu, 内存大小: %zu 字节 (%.2f KB)",
         cbCount, cbSize, (float)cbSize / 1024.0f);

    if (caSize == 0) {
        LOGE("❌ Ca区域大小为0！DpMemoryTrap系统可能未正确部署Ca陷阱");
    }
    if (cbSize == 0) {
        LOGE("❌ Cb区域大小为0！DpMemoryTrap系统可能未正确部署Cb陷阱");
    }

    // 调用DpMemoryTrap的dump方法获取更详细信息
    dp_dump_memory_info();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_ComprehensiveMemoryTrapManager_nativeStopDetection(JNIEnv* env, jclass clazz) {
    // ComprehensiveTrap::stop_comprehensive_detection();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_ComprehensiveMemoryTrapManager_nativeCleanupTrapSystem(JNIEnv* env, jclass clazz) {
    // ComprehensiveTrap::cleanup_comprehensive_trap_system();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_ComprehensiveMemoryTrapManager_nativeSetDetectionCallback(JNIEnv* env, jclass clazz, jobject callback) {
    // 删除旧的全局引用
    if (g_callback_obj) {
        env->DeleteGlobalRef(g_callback_obj);
        g_callback_obj = nullptr;
    }
    
    // 创建新的全局引用
    if (callback) {
        g_callback_obj = env->NewGlobalRef(callback);
        
        // 获取方法ID
        jclass callback_class = env->GetObjectClass(callback);
        g_callback_method = env->GetMethodID(callback_class, "onDetection", 
                                           "(JLjava/lang/String;ZIF)V");
        
        if (!g_callback_method) {
            LOGE("找不到回调方法 onDetection");
            env->DeleteGlobalRef(g_callback_obj);
            g_callback_obj = nullptr;
            return;
        }
        
        // 设置C++回调
        // ComprehensiveTrap::set_comprehensive_detection_callback(DetectionCallback);
        LOGI("设置检测回调成功");
    } else {
        // 清除回调
        // ComprehensiveTrap::set_comprehensive_detection_callback(nullptr);
        LOGI("清除检测回调");
    }
}

/**
 * 输出详细的内存地址范围信息
 * 用于帮助分析为什么腾讯内存分析工具显示Ca/Cb区域为0B
 */
JNIEXPORT void JNICALL
Java_com_sy_newfwg_ComprehensiveMemoryTrapManager_nativeLogMemoryRanges(JNIEnv* env, jclass clazz) {
    LOGI("🗺️ [内存地址分析] 开始输出详细的内存地址范围信息...");

    // 获取DpMemoryTrap系统的内存地址范围
    auto& dpSystem = DpTrap::DpMemoryTrapSystem::GetInstance();

    LOGI("📍 [Ca区域地址范围] 开始分析Ca区域内存分布...");
    // 这里我们需要调用DpMemoryTrap的方法来获取地址范围
    // 由于我们没有直接的API，我们可以通过其他方式获取信息

    LOGI("📍 [Cb区域地址范围] 开始分析Cb区域内存分布...");

    // 输出进程内存映射信息
    LOGI("📋 [进程内存映射] 读取/proc/self/maps信息...");

    FILE* maps_file = fopen("/proc/self/maps", "r");
    if (maps_file != nullptr) {
        char line[1024];
        int count = 0;
        while (fgets(line, sizeof(line), maps_file) && count < 20) { // 只输出前20行避免日志过多
            // 移除换行符
            line[strcspn(line, "\n")] = 0;

            // 检查是否包含我们关心的内存区域
            if (strstr(line, "heap") || strstr(line, "anon") || strstr(line, "libmemorytrap.so")) {
                LOGI("🗺️ [内存映射] %s", line);
                count++;
            }
        }
        fclose(maps_file);
    } else {
        LOGE("❌ 无法读取/proc/self/maps文件");
    }

    // 输出一些关键的内存地址信息
    LOGI("🎯 [关键地址] 分析关键内存地址...");

    // 获取堆的起始地址
    void* heap_start = sbrk(0);
    LOGI("📍 [堆地址] 当前堆顶地址: %p", heap_start);

    // 分配一个小块内存来查看malloc的地址范围
    void* test_malloc = malloc(1024);
    if (test_malloc) {
        LOGI("📍 [malloc测试] 测试malloc地址: %p", test_malloc);
        free(test_malloc);
    }

    LOGI("✅ [内存地址分析] 内存地址范围信息输出完成");
}

} // extern "C"