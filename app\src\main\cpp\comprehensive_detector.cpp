#include "memory_trap_sdk.h"
#include <android/log.h>
#include <sys/mman.h>
#include <unistd.h>
#include <cstring>
#include <chrono>
#include <vector>
#include <mutex>
#include <thread>
#include <cstdio>
#include <dirent.h>

#define TAG "ComprehensiveDetector"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

namespace MemoryTrapSDK {

// 扩展的访问记录
struct ExtendedAccessRecord {
    void* addr;
    std::chrono::steady_clock::time_point time;
    pid_t pid;
    uid_t uid;
};

// 全局变量
static std::vector<ExtendedAccessRecord> g_access_history;
static std::mutex g_history_mutex;
static std::vector<TrapInfo> g_traps;
static std::mutex g_traps_mutex;
static DetectionCallback g_callback = nullptr;
static std::thread g_monitor_thread;
static std::atomic<bool> g_monitoring{false};

// 配置选项
static const bool ENABLE_BACKGROUND_SCAN = true;  // 临时启用来调试

// 新增：内存访问监控
static std::atomic<int> g_memory_scan_count{0};
static std::chrono::steady_clock::time_point g_last_scan_time;
static std::mutex g_scan_mutex;

// 新增：检测内存扫描行为
bool detectMemoryScanningBehavior() {
    LOGI("🔍 检测内存扫描行为...");

    // 检查/proc/self/fd目录，寻找对/proc/pid/mem的访问
    DIR* fd_dir = opendir("/proc/self/fd");
    if (!fd_dir) {
        LOGW("⚠️ 无法打开/proc/self/fd");
        return false;
    }

    bool found_suspicious = false;
    int mem_fd_count = 0;

    struct dirent* entry;
    while ((entry = readdir(fd_dir)) != nullptr) {
        if (!isdigit(entry->d_name[0])) continue;

        char link_path[256];
        char target[512];
        snprintf(link_path, sizeof(link_path), "/proc/self/fd/%s", entry->d_name);

        ssize_t len = readlink(link_path, target, sizeof(target) - 1);
        if (len > 0) {
            target[len] = '\0';

            // 检查是否指向/proc/pid/mem文件
            if (strstr(target, "/proc/") && strstr(target, "/mem")) {
                mem_fd_count++;
                LOGW("🚨 发现内存访问文件描述符！");
                LOGW("   FD: %s -> %s", entry->d_name, target);
                found_suspicious = true;
            }
        }
    }

    closedir(fd_dir);

    if (found_suspicious) {
        LOGW("🚨 检测到内存扫描行为！");
        LOGW("   内存FD数量: %d", mem_fd_count);
        LOGW("   检测规则: /proc/pid/mem文件访问");
        LOGW("   风险等级: 🔴 高危");
    }

    return found_suspicious;
}

// 检测方式1：进程名检测（增强版）
bool detectSuspiciousProcesses() {
    const char* suspicious_names[] = {
        "guardian", "gg", "cheat", "hack", "mod", "trainer",
        "gameguardian", "修改器", "作弊", "外挂", "dv.gameguardian",
        "lua", "script", "xposed", "frida", "substrate",
        // 更多修改器关键词
        "lucky", "patcher", "freedom", "creehack", "sbgameha",
        "game", "killer", "八门", "烧饼", "葫芦侠", "修改大师",
        "变速", "加速", "speedhack", "memory", "editor", "ce",
        "root", "su", "magisk", "supersu", "kingroot",
        // 发现的可疑包名
        "odacdyhxws", "hpp"
    };

    LOGI("📋 开始扫描所有进程...");
    LOGI("🔍 实时进程监控模式 - 显示所有应用进程");

    // 首先测试能否访问/proc目录
    LOGI("🧪 测试/proc目录访问权限...");

    DIR* proc_dir = opendir("/proc");
    if (!proc_dir) {
        LOGE("❌ 无法打开/proc目录 - 权限不足");
        LOGE("❌ 这可能是Android安全限制导致的");
        return false;
    }

    LOGI("✅ 成功打开/proc目录");

    bool found_suspicious = false;
    int total_processes = 0;
    int scanned_processes = 0;

    struct dirent* entry;
    while ((entry = readdir(proc_dir)) != nullptr) {
        // 只处理数字目录（进程ID）
        if (!isdigit(entry->d_name[0])) continue;

        total_processes++;

        // 特别检查目标进程
        if (strcmp(entry->d_name, "29705") == 0) {
            LOGI("🎯 找到目标PID: 29705");
        }

        char cmdline_path[256];
        char comm_path[256];
        snprintf(cmdline_path, sizeof(cmdline_path), "/proc/%s/cmdline", entry->d_name);
        snprintf(comm_path, sizeof(comm_path), "/proc/%s/comm", entry->d_name);

        // 读取cmdline
        char cmdline[512] = {0};
        FILE* f = fopen(cmdline_path, "r");
        if (f) {
            fread(cmdline, 1, sizeof(cmdline) - 1, f);
            fclose(f);
            scanned_processes++;

            // 特别检查目标进程的cmdline
            if (strstr(cmdline, "odacdyhxws")) {
                LOGI("🎯 在cmdline中找到目标字符串: %s", cmdline);
            }
        } else {
            LOGW("⚠️ 无法读取 %s", cmdline_path);
        }

        // 读取comm（进程名）
        char comm[256] = {0};
        f = fopen(comm_path, "r");
        if (f) {
            fgets(comm, sizeof(comm) - 1, f);
            fclose(f);
            // 移除换行符
            comm[strcspn(comm, "\n")] = 0;

            // 特别检查目标进程的comm
            if (strstr(comm, "hpp") || strstr(comm, "odacdyhxws")) {
                LOGI("🎯 在comm中找到目标字符串: %s", comm);
            }
        } else {
            LOGW("⚠️ 无法读取 %s", comm_path);
        }

        // 打印所有应用进程用于调试
        bool should_print = false;
        if (strlen(cmdline) > 0) {
            // 打印所有包含包名的应用进程
            if (strstr(cmdline, "com.") || strstr(cmdline, "org.") ||
                strstr(cmdline, "net.") || strstr(cmdline, "cn.")) {
                should_print = true;
            }
        }

        // 也打印一些系统进程和可疑进程名
        if (strlen(comm) > 0) {
            if (strstr(comm, "gg") || strstr(comm, "guardian") ||
                strstr(comm, "cheat") || strstr(comm, "hack") ||
                strstr(comm, "lucky") || strstr(comm, "su") ||
                strstr(comm, "root") || strstr(comm, "magisk")) {
                should_print = true;
            }
        }

        if (should_print) {
            LOGI("📱 进程[%s]: comm='%s', cmdline='%s'",
                 entry->d_name, comm, cmdline);
        }

        // 创建小写版本用于比较
        char cmdline_lower[512] = {0};
        char comm_lower[256] = {0};

        for (int i = 0; cmdline[i] && i < 511; i++) {
            cmdline_lower[i] = tolower(cmdline[i]);
        }
        for (int i = 0; comm[i] && i < 255; i++) {
            comm_lower[i] = tolower(comm[i]);
        }

        // 检查可疑关键词
        for (const char* name : suspicious_names) {
            if ((strlen(cmdline_lower) > 0 && strstr(cmdline_lower, name)) ||
                (strlen(comm_lower) > 0 && strstr(comm_lower, name))) {
                LOGW("🚨 检测到可疑进程！");
                LOGW("   PID: %s", entry->d_name);
                LOGW("   进程名: %s", comm);
                LOGW("   命令行: %s", cmdline);
                LOGW("   匹配关键词: %s", name);
                LOGW("   检测规则: 进程名/命令行关键词匹配");
                LOGW("   风险等级: 🔴 高危");
                found_suspicious = true;
            }
        }
    }

    closedir(proc_dir);

    LOGI("📋 进程扫描完成: 总进程%d个, 成功扫描%d个", total_processes, scanned_processes);
    LOGI("📊 检测规则统计:");
    LOGI("   - 关键词数量: %d个", (int)(sizeof(suspicious_names)/sizeof(suspicious_names[0])));
    LOGI("   - 扫描成功率: %.1f%%", (float)scanned_processes/total_processes*100);

    if (found_suspicious) {
        LOGW("🚨 发现可疑进程！");
        LOGW("   检测方法: 进程名/命令行关键词匹配");
    } else {
        LOGI("✅ 未发现可疑进程");
        LOGI("   所有进程均通过安全检查");
    }

    return found_suspicious;
}

// 检测方式2：内存映射检测（增强版）
bool detectSuspiciousMemoryMaps() {
    LOGI("🗺️ 开始扫描内存映射...");

    FILE* f = fopen("/proc/self/maps", "r");
    if (!f) {
        LOGE("❌ 无法打开/proc/self/maps");
        return false;
    }

    const char* suspicious_keywords[] = {
        "gameguardian", "guardian", "cheat", "hack",
        "frida-agent", "xposed", "substrate"
        // 移除了太宽泛的关键词如"mod", "lua", "script"
    };

    bool found_suspicious = false;
    int total_maps = 0;
    char line[512];

    while (fgets(line, sizeof(line), f)) {
        total_maps++;

        // 转换为小写进行检查
        char line_lower[512];
        for (int i = 0; line[i] && i < 511; i++) {
            line_lower[i] = tolower(line[i]);
        }
        line_lower[511] = 0;

        // 检查可疑关键词
        for (const char* keyword : suspicious_keywords) {
            if (strstr(line_lower, keyword)) {
                LOGW("🚨 检测到可疑内存映射！");
                LOGW("   原始内容: %s", line);
                LOGW("   小写内容: %s", line_lower);
                LOGW("   匹配关键词: %s", keyword);
                LOGW("   检测规则: 内存映射路径关键词匹配");
                LOGW("   风险等级: 🟡 可疑");
                found_suspicious = true;
            }
        }
    }

    fclose(f);

    LOGI("🗺️ 内存映射扫描完成: 总计%d个映射", total_maps);

    if (found_suspicious) {
        LOGW("🚨 发现可疑内存映射！");
    } else {
        LOGI("✅ 未发现可疑内存映射");
    }

    return found_suspicious;
}

// 检测方式3：调试器检测（增强版）
bool detectDebugger() {
    LOGI("🔍 检查调试器状态...");

    FILE* f = fopen("/proc/self/status", "r");
    if (!f) {
        LOGE("❌ 无法打开/proc/self/status");
        return false;
    }

    char line[256];
    int tracer_pid = -1;
    char process_name[256] = {0};
    int process_pid = -1;

    while (fgets(line, sizeof(line), f)) {
        // 打印关键状态信息
        if (strstr(line, "Name:")) {
            sscanf(line, "Name:\t%s", process_name);
            LOGI("📱 进程名: %s", process_name);
        } else if (strstr(line, "Pid:")) {
            sscanf(line, "Pid:\t%d", &process_pid);
            LOGI("📱 进程PID: %d", process_pid);
        } else if (strstr(line, "TracerPid:")) {
            sscanf(line, "TracerPid:\t%d", &tracer_pid);
            LOGI("🔍 TracerPid: %d", tracer_pid);
        } else if (strstr(line, "State:")) {
            LOGI("📊 进程状态: %s", line);
        }
    }

    fclose(f);

    if (tracer_pid > 0) {
        LOGW("🚨 检测到调试器附加！");
        LOGW("   调试器PID: %d", tracer_pid);
        LOGW("   被调试进程: %s (PID: %d)", process_name, process_pid);
        LOGW("   检测规则: TracerPid字段检查");
        LOGW("   风险等级: 🔴 高危");

        // 尝试获取调试器进程信息
        char tracer_comm_path[256];
        snprintf(tracer_comm_path, sizeof(tracer_comm_path), "/proc/%d/comm", tracer_pid);
        FILE* tracer_f = fopen(tracer_comm_path, "r");
        if (tracer_f) {
            char tracer_name[256] = {0};
            fgets(tracer_name, sizeof(tracer_name) - 1, tracer_f);
            tracer_name[strcspn(tracer_name, "\n")] = 0;
            LOGW("   调试器名称: %s", tracer_name);
            fclose(tracer_f);
        }

        return true;
    } else {
        LOGI("✅ 未检测到调试器附加 (TracerPid: %d)", tracer_pid);
        return false;
    }
}

// 检测方式4：访问模式分析
bool analyzeAccessPattern(const std::vector<ExtendedAccessRecord>& history) {
    if (history.size() < 2) return false;
    
    // 检查连续访问
    int sequential_count = 0;
    for (size_t i = 1; i < history.size(); i++) {
        uintptr_t prev_addr = (uintptr_t)history[i-1].addr;
        uintptr_t curr_addr = (uintptr_t)history[i].addr;
        uintptr_t diff = abs((int64_t)(curr_addr - prev_addr));
        
        // 检查是否为典型的扫描步长
        if (diff == 4 || diff == 8 || diff == 1) {
            sequential_count++;
        }
    }
    
    float sequential_ratio = (float)sequential_count / (history.size() - 1);
    LOGI("📊 连续访问分析:");
    LOGI("   - 总访问次数: %zu", history.size());
    LOGI("   - 连续访问次数: %d", sequential_count);
    LOGI("   - 连续访问比例: %.1f%%", sequential_ratio * 100);
    LOGI("   - 阈值: 50.0%%");

    if (sequential_ratio > 0.5f) {
        LOGW("🚨 检测到连续访问模式: 比例 %.1f%%", sequential_ratio * 100);
        LOGW("   检测规则: 连续内存访问模式分析");
        LOGW("   风险等级: 🟡 可疑");
        return true;
    }
    
    return false;
}

// 检测方式5：频率检测
bool detectHighFrequencyAccess(const std::vector<ExtendedAccessRecord>& history) {
    if (history.size() < 3) return false;
    
    auto first_time = history.front().time;
    auto last_time = history.back().time;
    int duration_ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        last_time - first_time).count();
    
    if (duration_ms == 0) duration_ms = 1;
    int frequency = (history.size() * 1000) / duration_ms;

    LOGI("📊 访问频率分析:");
    LOGI("   - 访问次数: %zu", history.size());
    LOGI("   - 时间跨度: %d ms", duration_ms);
    LOGI("   - 访问频率: %d次/秒", frequency);
    LOGI("   - 阈值: 10次/秒");

    if (frequency > 10) { // 每秒超过10次
        LOGW("🚨 检测到高频访问: %d次/秒", frequency);
        LOGW("   检测规则: 高频内存访问检测");
        LOGW("   风险等级: 🔴 高危");
        return true;
    }
    
    return false;
}

// 后台监控线程（优化版）
void backgroundMonitor() {
    LOGI("🔍 后台监控线程启动");

    // 启动后先等待10秒，避免应用启动时立即扫描
    LOGI("⏰ 等待10秒后开始后台扫描...");
    std::this_thread::sleep_for(std::chrono::seconds(10));

    int scan_count = 0;
    while (g_monitoring.load()) {
        scan_count++;
        LOGI("🔍 开始第%d次后台扫描", scan_count);

        bool detected = false;
        std::string detection_reason;

        // 方式1：进程检测
        if (detectSuspiciousProcesses()) {
            detected = true;
            detection_reason += "可疑进程; ";
        }

        // 方式2：内存映射检测
        if (detectSuspiciousMemoryMaps()) {
            detected = true;
            detection_reason += "可疑内存映射; ";
        }

        // 方式3：调试器检测
        if (detectDebugger()) {
            detected = true;
            detection_reason += "调试器附加; ";
        }

        if (detected) {
            LOGW("🚨🚨🚨 后台检测到修改器！🚨🚨🚨");
            LOGW("检测原因: %s", detection_reason.c_str());

            // 触发回调
            if (g_callback && !g_traps.empty()) {
                DetectionEvent event;
                event.fault_address = nullptr;
                event.trap_info = &g_traps[0]; // 使用第一个陷阱
                event.timestamp = std::chrono::steady_clock::now();
                event.description = "后台检测到修改器: " + detection_reason;
                event.analysis.is_modifier_scan = true;
                event.analysis.reason = detection_reason;

                g_callback(event);
            }
        } else {
            LOGI("✅ 第%d次后台扫描完成，未发现异常", scan_count);
        }

        // 每30秒检查一次（降低频率）
        LOGI("⏰ 下次扫描将在30秒后进行");
        std::this_thread::sleep_for(std::chrono::seconds(30));
    }

    LOGI("🔍 后台监控线程结束");
}

// 综合信号处理器
void comprehensiveSignalHandler(int signum, siginfo_t* info, void* context) {
    if (!info) return;
    
    void* fault_addr = info->si_addr;
    LOGW("=== 综合检测器触发 ===");
    LOGW("地址: %p, 信号: %d", fault_addr, signum);
    
    // 获取当前进程信息
    pid_t current_pid = getpid();
    uid_t current_uid = getuid();
    
    // 1. 查找匹配的陷阱
    bool is_our_trap = false;
    TrapInfo* matched_trap = nullptr;
    
    {
        std::lock_guard<std::mutex> lock(g_traps_mutex);
        for (auto& trap : g_traps) {
            if (fault_addr >= trap.address &&
                fault_addr < (char*)trap.address + trap.size) {
                
                is_our_trap = true;
                matched_trap = &trap;
                LOGW("匹配到陷阱: %p, 区域: %d", trap.address, (int)trap.region);
                
                // 解除保护
                if (trap.is_protected) {
                    if (mprotect(trap.address, trap.size, PROT_READ | PROT_WRITE) == 0) {
                        trap.is_protected = false;
                        LOGW("解除陷阱保护成功");
                    }
                }
                break;
            }
        }
    }
    
    if (!is_our_trap) {
        LOGI("不是我们的陷阱，忽略");
        return;
    }
    
    // 2. 记录访问并进行综合分析
    auto now = std::chrono::steady_clock::now();
    bool detected = false;
    std::string detection_reasons;
    
    {
        std::lock_guard<std::mutex> lock(g_history_mutex);
        
        // 清理旧记录（保留最近5秒）
        auto cutoff = now - std::chrono::seconds(5);
        g_access_history.erase(
            std::remove_if(g_access_history.begin(), g_access_history.end(),
                          [cutoff](const ExtendedAccessRecord& record) {
                              return record.time < cutoff;
                          }),
            g_access_history.end()
        );
        
        // 添加当前访问
        g_access_history.push_back({fault_addr, now, current_pid, current_uid});
        
        size_t count = g_access_history.size();
        LOGW("最近5秒内访问次数: %zu", count);
        
        // 检测方式1：简单计数
        if (count >= 2) {
            detected = true;
            detection_reasons += "频繁访问(" + std::to_string(count) + "次); ";
        }
        
        // 检测方式2：访问模式分析
        if (analyzeAccessPattern(g_access_history)) {
            detected = true;
            detection_reasons += "连续访问模式; ";
        }
        
        // 检测方式3：高频检测
        if (detectHighFrequencyAccess(g_access_history)) {
            detected = true;
            detection_reasons += "高频访问; ";
        }
    }
    
    // 3. 如果检测到，触发回调
    if (detected) {
        LOGW("🚨🚨🚨 综合检测到修改器扫描！🚨🚨🚨");
        LOGW("触发地址: %p", fault_addr);
        LOGW("检测原因: %s", detection_reasons.c_str());
        
        if (g_callback && matched_trap) {
            DetectionEvent event;
            event.fault_address = fault_addr;
            event.trap_info = matched_trap;
            event.timestamp = now;
            event.description = "综合检测到修改器扫描";
            event.analysis.is_modifier_scan = true;
            event.analysis.total_accesses = g_access_history.size();
            event.analysis.reason = detection_reasons;
            
            g_callback(event);
        }
        
        // 清空记录避免重复报告
        std::lock_guard<std::mutex> lock(g_history_mutex);
        g_access_history.clear();
    }
}

// 设置陷阱信息
void setTrapsComprehensive(const std::vector<TrapInfo>& traps) {
    std::lock_guard<std::mutex> lock(g_traps_mutex);
    g_traps = traps;
    LOGI("设置了 %zu 个陷阱", traps.size());
}

// 设置回调
void setCallbackComprehensive(DetectionCallback callback) {
    g_callback = callback;
}

// 启动综合检测
bool startComprehensiveDetection() {
    // 安装信号处理器
    struct sigaction sa;
    memset(&sa, 0, sizeof(sa));
    sa.sa_sigaction = comprehensiveSignalHandler;
    sa.sa_flags = SA_SIGINFO | SA_RESTART;
    
    if (sigaction(SIGSEGV, &sa, nullptr) == -1) {
        LOGE("安装SIGSEGV处理器失败");
        return false;
    }
    
    // 根据配置决定是否启动后台监控线程
    if (ENABLE_BACKGROUND_SCAN) {
        g_monitoring.store(true);
        g_monitor_thread = std::thread(backgroundMonitor);
        LOGI("🚀 综合检测系统启动成功（包含后台扫描）");
    } else {
        LOGI("🚀 综合检测系统启动成功（仅信号检测）");
    }

    return true;
}

// 停止综合检测
void stopComprehensiveDetection() {
    if (ENABLE_BACKGROUND_SCAN) {
        g_monitoring.store(false);
        if (g_monitor_thread.joinable()) {
            g_monitor_thread.join();
        }
    }
    LOGI("🛑 综合检测系统已停止");
}

} // namespace MemoryTrapSDK
