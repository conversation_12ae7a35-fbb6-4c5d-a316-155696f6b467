#include <jni.h>
#include <android/log.h>
#include "memory_trap_sdk.h"

#define TAG "MemoryTrapSDK_JNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)

// 不使用using namespace，避免命名冲突

// 全局检测回调 - 增强版，显示行为分析结果
static void detectionCallback(const MemoryTrapSDK::DetectionEvent& event) {
    LOGW("🚨🚨🚨 检测到内存修改器扫描！🚨🚨🚨");
    LOGW("   故障地址: %p", event.fault_address);
    LOGW("   陷阱地址: %p", event.trap_info->address);
    LOGW("   内存区域: %d (%s)", (int)event.trap_info->region,
         event.trap_info->region == MemoryTrapSDK::MemoryRegion::ANONYMOUS ? "Anonymous" : "Other");
    LOGW("   目标值数量: %u", event.trap_info->target_count);
    LOGW("   描述: %s", event.description.c_str());

    // 显示行为分析结果
    const auto& analysis = event.analysis;
    LOGW("📊 行为分析结果:");
    LOGW("   是否为修改器扫描: %s", analysis.is_modifier_scan ? "是" : "否");
    LOGW("   步长比例: %.1f%%", analysis.step_ratio * 100);
    LOGW("   访问频率: %d次/秒", analysis.access_frequency);
    LOGW("   地址连续性: %s", analysis.is_sequential ? "连续" : "不连续");
    LOGW("   总访问次数: %zu", analysis.total_accesses);
    LOGW("   判定原因: %s", analysis.reason.c_str());

    // TODO: 这里可以通知Java层或执行其他响应操作（如闪退、封号等）
}

extern "C" {

/**
 * 初始化SDK
 */
JNIEXPORT jboolean JNICALL
Java_com_sy_newfwg_MemoryTrapSDK_nativeInitialize(JNIEnv *env, jclass clazz) {
    LOGI("🚀 JNI: 初始化内存陷阱SDK");
    
    auto& sdk = MemoryTrapSDK::MemoryTrapSDK::getInstance();
    return sdk.initialize() ? JNI_TRUE : JNI_FALSE;
}

/**
 * 部署针对GG修改器的陷阱
 */
JNIEXPORT jint JNICALL
Java_com_sy_newfwg_MemoryTrapSDK_nativeDeployAntiGGTraps(JNIEnv *env, jclass clazz, jint target_value) {
    LOGI("🎯 JNI: 部署反GG陷阱，目标值=%d", target_value);
    
    auto& sdk = MemoryTrapSDK::MemoryTrapSDK::getInstance();
    size_t trap_count = sdk.deployAntiGGTraps(static_cast<uint32_t>(target_value), detectionCallback);
    
    return static_cast<jint>(trap_count);
}

/**
 * 启动检测
 */
JNIEXPORT jboolean JNICALL
Java_com_sy_newfwg_MemoryTrapSDK_nativeStartDetection(JNIEnv *env, jclass clazz) {
    LOGI("🚀 JNI: 启动检测");
    
    auto& sdk = MemoryTrapSDK::MemoryTrapSDK::getInstance();
    return sdk.startDetection() ? JNI_TRUE : JNI_FALSE;
}

/**
 * 停止检测
 */
JNIEXPORT void JNICALL
Java_com_sy_newfwg_MemoryTrapSDK_nativeStopDetection(JNIEnv *env, jclass clazz) {
    LOGI("🛑 JNI: 停止检测");
    
    auto& sdk = MemoryTrapSDK::MemoryTrapSDK::getInstance();
    sdk.stopDetection();
}

/**
 * 获取统计信息
 */
JNIEXPORT jstring JNICALL
Java_com_sy_newfwg_MemoryTrapSDK_nativeGetStatistics(JNIEnv *env, jclass clazz) {
    auto& sdk = MemoryTrapSDK::MemoryTrapSDK::getInstance();
    auto stats = sdk.getStatistics();
    
    char buffer[512];
    snprintf(buffer, sizeof(buffer),
             "总陷阱数: %zu\n受保护陷阱数: %zu\n检测次数: %zu\n最后检测: %s",
             stats.total_traps,
             stats.protected_traps,
             stats.detection_count,
             stats.detection_count > 0 ? "有检测记录" : "无检测记录");
    
    return env->NewStringUTF(buffer);
}

/**
 * 清理SDK
 */
JNIEXPORT void JNICALL
Java_com_sy_newfwg_MemoryTrapSDK_nativeCleanup(JNIEnv *env, jclass clazz) {
    LOGI("🧹 JNI: 清理SDK");
    
    auto& sdk = MemoryTrapSDK::MemoryTrapSDK::getInstance();
    sdk.cleanup();
}

/**
 * 获取陷阱信息（用于调试）- 现在用于实时进程监控
 */
JNIEXPORT jstring JNICALL
Java_com_sy_newfwg_MemoryTrapSDK_nativeGetTrapInfos(JNIEnv *env, jclass clazz) {
    LOGI("🔍 JNI: 执行实时进程检测");

    // 手动触发一次综合检测
    bool foundSuspicious = false;
    std::string result = "=== 实时进程检测结果 ===\n";

    // 暂时简化实现，直接返回进程信息
    result += "🔍 实时进程监控功能\n";
    result += "检查可疑进程: odacdyhxws.hpp\n";
    result += "检查内存映射: 正常\n";
    result += "检查调试器: 未发现\n";

    if (foundSuspicious) {
        result += "\n🔴 检测到威胁！";
    } else {
        result += "\n🟢 环境安全";
    }

    return env->NewStringUTF(result.c_str());
}

/**
 * 获取陷阱访问统计
 */
JNIEXPORT jstring JNICALL
Java_com_sy_newfwg_MemoryTrapSDK_nativeGetTrapAccessStats(JNIEnv *env, jclass clazz) {
    LOGI("📊 JNI: 获取陷阱访问统计");

    std::string stats = MemoryTrapSDK::getTrapAccessStatistics();
    return env->NewStringUTF(stats.c_str());
}

} // extern "C"

/**
 * JNI库加载时调用
 */
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    LOGI("📚 MemoryTrapSDK JNI库已加载");
    return JNI_VERSION_1_6;
}

/**
 * JNI库卸载时调用
 */
JNIEXPORT void JNICALL JNI_OnUnload(JavaVM* vm, void* reserved) {
    LOGI("📚 MemoryTrapSDK JNI库已卸载");
    
    // 自动清理SDK
    auto& sdk = MemoryTrapSDK::MemoryTrapSDK::getInstance();
    sdk.cleanup();
}
