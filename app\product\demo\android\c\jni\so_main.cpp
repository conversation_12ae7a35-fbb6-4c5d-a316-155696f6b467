#include <stdio.h>
#include <jni.h>

#include "game.h"


//jni函数注册信息表
static JNINativeMethod g_jni_methods[] =
{
    { "run", "()I", (void *)run_game },
    { "onPause", "()I", (void *)game_pause },
    { "onResume", "()I", (void *)game_resume },
};

static int regist_natives(JNIEnv *env)
{
    //查找要绑定JNI方法的类
    jclass clazz = env->FindClass("com/tencent/tpcpp/Game");
    if (clazz == NULL)
    {
        return -1;
    }
    //绑定JNI方法
    int jni_method_num = sizeof(g_jni_methods)/sizeof(g_jni_methods[0]);
    if (env->RegisterNatives(clazz, g_jni_methods, jni_method_num) < 0)
    {
        return -1;
    }
    return 0;
}

/**
 * 此方法会在本so被加载的第一时刻被调用, 用于注册jni方法
 */
JNIEXPORT jint JNI_OnLoad(JavaVM* vm, void* reserved)
{
    JNIEnv* env;

    if (vm->GetEnv((void **)&env, JNI_VERSION_1_4) != JNI_OK)
    {
        return -1;
    }
    if (env == NULL)
    {
        return -1;
    }
    if (regist_natives(env) != 0)
    {
        return -1;
    }
    return JNI_VERSION_1_4;
}
