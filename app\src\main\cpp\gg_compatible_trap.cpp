#include <jni.h>
#include <android/log.h>
#include <unistd.h>
#include <sys/mman.h>
#include <malloc.h>
#include <cstring>
#include <vector>
#include <thread>
#include <chrono>
#include <fcntl.h>
#include <stdlib.h>
#include <new>
#include <algorithm>
#include <dlfcn.h>
#include <link.h>
#include <cstdlib>  // for aligned_alloc, valloc

#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, "GG-Trap", __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, "GG-Trap", __VA_ARGS__)

// 🚀 基于腾讯SDK成功案例的GG兼容内存陷阱系统
class GGCompatibleTrapSystem {
private:
    static bool initialized_;
    static std::vector<void*> ca_heap_blocks_;
    static std::vector<void*> cb_bss_blocks_;
    static std::vector<void*> tencent_blocks_; // 腾讯SDK模拟分配
    static std::thread keep_alive_thread_;
    static bool keep_running_;
    
    // 🎯 专门为Ca区域设计的内存分配（ELF段操作版）
    static void* allocateCaMemory(size_t size) {
        // 🚀 革命性方法：直接模拟动态链接器的C++ alloc行为

        // 方法1: 模拟libstdc++的operator new（关键！）
        // GG可能通过检查调用栈来识别C++ alloc
        static void* (*real_malloc)(size_t) = nullptr;
        if (!real_malloc) {
            real_malloc = (void*(*)(size_t))dlsym(RTLD_NEXT, "malloc");
        }

        if (real_malloc) {
            void* ptr1 = real_malloc(size + 16); // 额外空间用于C++对象头
            if (ptr1) {
                // 模拟C++对象的内存布局
                *((size_t*)ptr1) = size; // 对象大小
                *((void**)((char*)ptr1 + 8)) = (void*)0x12345678; // 虚函数表指针模拟
                void* obj_ptr = (char*)ptr1 + 16;
                memset(obj_ptr, 0xCA, size);
                return ptr1; // 返回包含头部的完整指针
            }
        }

        // 方法2: 直接调用libc的__libc_malloc（重要！）
        // 这可能被GG识别为真正的C++ alloc
        void* ptr2 = dlsym(RTLD_DEFAULT, "__libc_malloc");
        if (ptr2) {
            void* (*libc_malloc)(size_t) = (void*(*)(size_t))ptr2;
            void* result = libc_malloc(size);
            if (result) {
                memset(result, 0xAA, size);
                return result;
            }
        }

        // 方法3: 使用new[]操作符（关键！）
        // 数组new可能有不同的识别特征
        try {
            char* ptr3 = new char[size];
            if (ptr3) {
                memset(ptr3, 0xCA, size);
                return ptr3;
            }
        } catch (...) {
            // 继续尝试其他方法
        }

        return nullptr;
    }
    
    // 🎯 专门为Cb区域设计的.bss段内存分配（ELF段操作版）
    static void* allocateCbMemory(size_t size) {
        // 🚀 革命性方法：直接操作ELF段来创建真正的.bss特征

        // 方法1: 使用编译器的.bss段属性（关键！）
        // 这是真正的.bss段，应该被GG正确识别
        static char __attribute__((section(".bss"))) real_bss_array[8 * 1024 * 1024]; // 8MB真实.bss段
        static size_t bss_used = 0;

        if (bss_used + size <= sizeof(real_bss_array)) {
            void* ptr = &real_bss_array[bss_used];
            bss_used += size;
            // .bss段的关键特征：不要初始化，保持编译器的零初始化
            return ptr;
        }

        // 方法2: 模拟动态链接器的.bss扩展（重要！）
        // 使用dlopen来动态创建.bss段
        static void* bss_handle = nullptr;
        if (!bss_handle) {
            // 尝试获取当前进程的句柄
            bss_handle = dlopen(nullptr, RTLD_LAZY);
        }

        if (bss_handle) {
            // 查找.bss段的符号
            void* bss_start = dlsym(bss_handle, "__bss_start");
            void* bss_end = dlsym(bss_handle, "_end");

            if (bss_start && bss_end) {
                // 在.bss段范围内分配内存
                static size_t bss_offset = 0;
                size_t bss_size = (char*)bss_end - (char*)bss_start;
                if (bss_offset + size <= bss_size / 2) { // 只使用一半，避免冲突
                    void* ptr = (char*)bss_start + bss_offset;
                    bss_offset += size;
                    return ptr;
                }
            }
        }

        // 方法3: 使用全局未初始化变量（关键！）
        // 这些变量会被放在.bss段中
        static char global_bss_buffer[2 * 1024 * 1024]; // 2MB全局未初始化数组
        static size_t global_offset = 0;

        if (global_offset + size <= sizeof(global_bss_buffer)) {
            void* ptr = &global_bss_buffer[global_offset];
            global_offset += size;
            return ptr;
        }

        return nullptr;
    }
    
    // 🎯 保持内存活跃的线程
    static void keepMemoryAlive() {
        while (keep_running_) {
            // 定期访问Ca区域内存
            for (void* ptr : ca_heap_blocks_) {
                if (ptr) {
                    volatile char* access = (volatile char*)ptr;
                    *access = *access; // 读写操作保持活跃
                }
            }
            
            // 定期访问Cb区域内存
            for (void* ptr : cb_bss_blocks_) {
                if (ptr) {
                    volatile char* access = (volatile char*)ptr;
                    *access = *access; // 读写操作保持活跃
                }
            }
            
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

public:
    static bool initialize() {
        if (initialized_) {
            return true;
        }
        
        LOGI("🚀 [GG-Init] 初始化TP2SDK精确复制版...");
        LOGI("🔥 [GG-Init] TP2SDK复制版 v4.0 - 精确模拟腾讯成功模式");
        LOGI("🎯 [GG-Init] 模拟TP2Sdk.initEx(20616, \"f38294be7e0a824dd5629e148b21fcac\")");

        // 🎯 创建Ca区域（C++ heap）- 使用腾讯的成功模式
        createCaRegion();

        // 🎯 创建Cb区域（C++ .bss）- 使用腾讯的成功模式
        createCbRegion();

        // 启动内存保活线程
        keep_running_ = true;
        keep_alive_thread_ = std::thread(keepMemoryAlive);
        keep_alive_thread_.detach();

        initialized_ = true;
        LOGI("✅ [GG-Init] 腾讯风格内存陷阱系统初始化完成");
        return true;
    }
    
    static void createCaRegion() {
        LOGI("🎯 [GG-Ca] 最小化测试：只分配4.10kB模拟腾讯");
        LOGI("🔍 [GG-Ca] 关闭大量分配，专注腾讯模式...");

        // 🎯 最小化策略：只做腾讯4.10kB分配
        // 不做大量分配，避免干扰GG的识别算法

        LOGI("✅ [GG-Ca] 跳过大量分配，只保留腾讯4.10kB模拟");
        LOGI("✅ [GG-Ca] 创建完成: 0个大块, 专注腾讯模拟");
    }
    
    static void createCbRegion() {
        LOGI("🔥 [GG-Cb] 革命性.bss段陷阱创建开始！");
        LOGI("🎯 [GG-Cb] 基于Ca成功经验，尝试创建Cb区域");

        // 🚀 策略1：创建全局静态变量（.bss段特征）
        createGlobalStaticVariables();

        // 🚀 策略2：使用mmap创建匿名内存映射
        createAnonymousMapping();

        // 🚀 策略3：创建大型静态数组
        createStaticArrays();

        // 🚀 策略4：模拟.bss段特征的内存
        createBssLikeMemory();

        LOGI("🔥 [GG-Cb] 完成所有.bss段陷阱创建策略");
        LOGI("🎯 [GG-Cb] 如果成功，应该能看到Cb区域有内容");
    }

    // 🚀 策略1：创建全局静态变量
    static void createGlobalStaticVariables() {
        LOGI("🎯 [策略1] 创建全局静态变量...");

        // 创建大型静态变量
        static char static_buffer_1[4198];  // 4.10kB
        static char static_buffer_2[4198];  // 4.10kB
        static int static_array[1024];      // 4KB
        static double static_doubles[512];  // 4KB

        // 填充数据以确保被使用
        memset(static_buffer_1, 0xCB, sizeof(static_buffer_1));
        memset(static_buffer_2, 0xCB, sizeof(static_buffer_2));

        for (int i = 0; i < 1024; i++) {
            static_array[i] = 0xCBCBCBCB;
        }

        for (int i = 0; i < 512; i++) {
            static_doubles[i] = 3.14159 * i;
        }

        // 保存地址以防被优化
        cb_bss_blocks_.push_back(static_buffer_1);
        cb_bss_blocks_.push_back(static_buffer_2);
        cb_bss_blocks_.push_back(static_array);
        cb_bss_blocks_.push_back(static_doubles);

        LOGI("✅ [策略1] 创建了4个静态变量，总计约16KB");
    }

    // 🚀 策略2：使用mmap创建匿名内存映射
    static void createAnonymousMapping() {
        LOGI("🎯 [策略2] 创建匿名内存映射...");

        size_t map_size = 8192; // 8KB
        void* mapped_mem = mmap(nullptr, map_size,
                               PROT_READ | PROT_WRITE,
                               MAP_PRIVATE | MAP_ANONYMOUS,
                               -1, 0);

        if (mapped_mem != MAP_FAILED) {
            // 填充特征数据
            memset(mapped_mem, 0xCB, map_size);
            cb_bss_blocks_.push_back(mapped_mem);
            LOGI("✅ [策略2] mmap分配成功: %zu字节", map_size);
        } else {
            LOGI("❌ [策略2] mmap分配失败");
        }
    }

    // 🚀 策略3：创建大型静态数组
    static void createStaticArrays() {
        LOGI("🎯 [策略3] 创建大型静态数组...");

        // 使用thread_local创建线程局部存储
        thread_local static char tls_buffer[8192];
        memset(tls_buffer, 0xCB, sizeof(tls_buffer));
        cb_bss_blocks_.push_back(tls_buffer);

        LOGI("✅ [策略3] 创建thread_local静态数组: 8KB");
    }

    // 🚀 策略4：模拟.bss段特征的内存
    static void createBssLikeMemory() {
        LOGI("🎯 [策略4] 模拟.bss段特征内存...");

        // 使用特殊的分配方式模拟.bss段
        size_t bss_size = 4198; // 匹配Ca的大小

        // 方法1：使用posix_memalign模拟段对齐
        void* aligned_mem = nullptr;
        if (posix_memalign(&aligned_mem, 4096, (bss_size + 4095) & ~4095) == 0) {
            memset(aligned_mem, 0x00, bss_size); // .bss段特征：初始为0
            memset(aligned_mem, 0xCB, 64);       // 然后填充少量标识
            cb_bss_blocks_.push_back(aligned_mem);
            LOGI("✅ [策略4] posix_memalign分配成功: %zu字节", bss_size);
        }

        // 方法2：使用memalign（内存对齐分配）
        void* page_mem = memalign(4096, bss_size);
        if (page_mem) {
            memset(page_mem, 0x00, bss_size);
            memset(page_mem, 0xCB, 64);
            cb_bss_blocks_.push_back(page_mem);
            LOGI("✅ [策略4] memalign分配成功: %zu字节", bss_size);
        }

        LOGI("🔥 [策略4] 完成.bss段特征内存模拟");
    }

    // 🎯 模拟腾讯SDK的4.10kB精确分配模式
    static void simulateTencentAllocation() {
        LOGI("🔥 [革命性] 标准malloc失败，尝试不同的分配API");
        LOGI("🔍 [革命性] 腾讯可能使用了特殊的内存分配方式");

        size_t target_size = 4198; // 4.10kB

        // 🚀 方法1：使用C++ new操作符（可能被GG识别为C++ alloc）
        LOGI("🎯 [方法1] 尝试C++ new操作符...");
        try {
            char* cpp_ptr = new char[target_size];
            if (cpp_ptr) {
                memset(cpp_ptr, 0xAC, target_size);
                tencent_blocks_.push_back(cpp_ptr);
                LOGI("✅ [方法1] C++ new分配成功: %zu字节", target_size);
            }
        } catch (...) {
            LOGI("❌ [方法1] C++ new分配失败");
        }

        // 🚀 方法2：使用calloc（零初始化分配）
        LOGI("🎯 [方法2] 尝试calloc零初始化分配...");
        void* calloc_ptr = calloc(1, target_size);
        if (calloc_ptr) {
            memset(calloc_ptr, 0xAC, target_size);
            tencent_blocks_.push_back(calloc_ptr);
            LOGI("✅ [方法2] calloc分配成功: %zu字节", target_size);
        } else {
            LOGI("❌ [方法2] calloc分配失败");
        }

        // 🚀 方法3：使用realloc（重新分配）
        LOGI("🎯 [方法3] 尝试realloc重新分配...");
        void* realloc_ptr = realloc(nullptr, target_size);
        if (realloc_ptr) {
            memset(realloc_ptr, 0xAC, target_size);
            tencent_blocks_.push_back(realloc_ptr);
            LOGI("✅ [方法3] realloc分配成功: %zu字节", target_size);
        } else {
            LOGI("❌ [方法3] realloc分配失败");
        }

        // 🚀 方法4：使用posix_memalign（POSIX对齐分配）
        LOGI("🎯 [方法4] 尝试posix_memalign对齐分配...");
        void* aligned_ptr = nullptr;
        if (posix_memalign(&aligned_ptr, 64, target_size) == 0 && aligned_ptr) {
            memset(aligned_ptr, 0xAC, target_size);
            tencent_blocks_.push_back(aligned_ptr);
            LOGI("✅ [方法4] posix_memalign分配成功: %zu字节", target_size);
        } else {
            LOGI("❌ [方法4] posix_memalign分配失败");
        }

        LOGI("🔥 [革命性] 完成多种分配API测试，总计分配: %zu个块", tencent_blocks_.size());
        LOGI("🎯 [革命性] 如果仍然失败，说明问题在于GG的识别算法");

        // 🔍 深度内存分析
        analyzeMemoryLayout();
    }

    // 🔍 深度分析内存布局和特征
    static void analyzeMemoryLayout() {
        LOGI("🔬 [内存分析] 开始深度分析内存布局...");

        // 分析每个分配块的详细信息
        for (size_t i = 0; i < tencent_blocks_.size(); i++) {
            void* ptr = tencent_blocks_[i];

            // 获取内存地址信息
            uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);

            LOGI("🔬 [块%zu] 地址: 0x%lx", i, addr);
            LOGI("🔬 [块%zu] 地址范围: %s", i, getAddressRange(addr).c_str());
            LOGI("🔬 [块%zu] 对齐: %s", i, getAlignmentInfo(addr).c_str());

            // 分析内存内容特征
            analyzeMemoryContent(ptr, 64); // 分析前64字节
        }

        // 分析整体内存分布
        analyzeMemoryDistribution();
    }

    // 获取地址范围信息
    static std::string getAddressRange(uintptr_t addr) {
        if (addr >= 0x10000000 && addr < 0x20000000) {
            return "低地址区域 (可能是.text段)";
        } else if (addr >= 0x20000000 && addr < 0x40000000) {
            return "中低地址区域";
        } else if (addr >= 0x40000000 && addr < 0x80000000) {
            return "中地址区域 (可能是堆区)";
        } else if (addr >= 0x80000000 && addr < 0xC0000000) {
            return "中高地址区域";
        } else if (addr >= 0xC0000000) {
            return "高地址区域 (可能是栈区)";
        }
        return "未知区域";
    }

    // 获取对齐信息
    static std::string getAlignmentInfo(uintptr_t addr) {
        std::string info = "";
        if (addr % 8 == 0) info += "8字节对齐 ";
        if (addr % 16 == 0) info += "16字节对齐 ";
        if (addr % 32 == 0) info += "32字节对齐 ";
        if (addr % 64 == 0) info += "64字节对齐 ";
        if (addr % 4096 == 0) info += "页面对齐 ";
        return info.empty() ? "无特殊对齐" : info;
    }

    // 分析内存内容
    static void analyzeMemoryContent(void* ptr, size_t size) {
        unsigned char* bytes = static_cast<unsigned char*>(ptr);

        // 检查内容模式
        bool all_zero = true;
        bool all_same = true;
        bool has_pattern = false;

        unsigned char first_byte = bytes[0];
        for (size_t i = 0; i < size; i++) {
            if (bytes[i] != 0) all_zero = false;
            if (bytes[i] != first_byte) all_same = false;
        }

        // 检查是否有重复模式
        if (size >= 4) {
            uint32_t pattern = *reinterpret_cast<uint32_t*>(bytes);
            has_pattern = true;
            for (size_t i = 4; i < size - 3; i += 4) {
                if (*reinterpret_cast<uint32_t*>(bytes + i) != pattern) {
                    has_pattern = false;
                    break;
                }
            }
        }

        LOGI("🔬 [内容] 全零: %s, 全相同: %s, 有模式: %s",
             all_zero ? "是" : "否",
             all_same ? "是" : "否",
             has_pattern ? "是" : "否");

        if (has_pattern) {
            LOGI("🔬 [内容] 模式值: 0x%02X%02X%02X%02X",
                 bytes[0], bytes[1], bytes[2], bytes[3]);
        }
    }

    // 分析内存分布
    static void analyzeMemoryDistribution() {
        if (tencent_blocks_.empty()) return;

        LOGI("🔬 [分布] 分析%zu个内存块的分布特征...", tencent_blocks_.size());

        // 计算地址范围
        uintptr_t min_addr = UINTPTR_MAX;
        uintptr_t max_addr = 0;

        for (void* ptr : tencent_blocks_) {
            uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);
            min_addr = std::min(min_addr, addr);
            max_addr = std::max(max_addr, addr);
        }

        LOGI("🔬 [分布] 地址范围: 0x%lx - 0x%lx", min_addr, max_addr);
        LOGI("🔬 [分布] 跨度: %lu字节 (%.2fKB)",
             max_addr - min_addr, (max_addr - min_addr) / 1024.0);

        // 检查地址连续性
        std::vector<uintptr_t> addrs;
        for (void* ptr : tencent_blocks_) {
            addrs.push_back(reinterpret_cast<uintptr_t>(ptr));
        }
        std::sort(addrs.begin(), addrs.end());

        bool is_continuous = true;
        for (size_t i = 1; i < addrs.size(); i++) {
            if (addrs[i] - addrs[i-1] > 8192) { // 8KB间隔认为不连续
                is_continuous = false;
                break;
            }
        }

        LOGI("🔬 [分布] 地址连续性: %s", is_continuous ? "连续" : "分散");
    }

    static void logMemoryStatus() {
        size_t ca_total = ca_heap_blocks_.size() * 1024 * 1024; // 估算
        size_t cb_total = cb_bss_blocks_.size() * 512 * 1024;   // 估算

        LOGI("📊 [GG-Status] ===== TP2SDK复制版内存状态 =====");
        LOGI("🎯 [GG-Ca] 陷阱数量: %zu个, 估算大小: %.2f MB",
             ca_heap_blocks_.size(), ca_total / (1024.0 * 1024.0));
        LOGI("🎯 [GG-Cb] 陷阱数量: %zu个, 估算大小: %.2f MB",
             cb_bss_blocks_.size(), cb_total / (1024.0 * 1024.0));

        // 🔍 关键调试：输出内存地址范围
        if (!ca_heap_blocks_.empty()) {
            LOGI("🔍 [GG-Debug] Ca区域地址范围:");
            for (size_t i = 0; i < std::min(size_t(5), ca_heap_blocks_.size()); i++) {
                LOGI("🔍 [GG-Debug] Ca[%zu]: 地址=0x%p", i, ca_heap_blocks_[i]);
            }
        }

            // 🎯 腾讯SDK分析：模拟4.10kB精确分配
        LOGI("🎯 [腾讯分析] 腾讯SDK成功创建4.10kB Ca区域");
        LOGI("🎯 [腾讯分析] 开始模拟腾讯的精确分配模式...");
        GGCompatibleTrapSystem::simulateTencentAllocation();

        if (!cb_bss_blocks_.empty()) {
            LOGI("🔍 [GG-Debug] Cb区域地址范围:");
            for (size_t i = 0; i < std::min(size_t(5), cb_bss_blocks_.size()); i++) {
                LOGI("🔍 [GG-Debug] Cb[%zu]: 地址=0x%p", i, cb_bss_blocks_[i]);
            }
        }

        LOGI("📊 [GG-Status] ===== 状态结束 =====");

        // 🎯 启动腾讯ACE SDK逆向分析
        analyzeTencentACEPattern();
    }

    // 🎯 腾讯ACE SDK逆向分析
    static void analyzeTencentACEPattern() {
        LOGI("🎯 [腾讯逆向] 开始分析腾讯ACE SDK内存模式...");

        // 模拟腾讯ACE SDK可能的内存分配策略
        LOGI("🎯 [腾讯逆向] 策略1: 模拟ACE引擎初始化");
        simulateACEEngineInit();

        LOGI("🎯 [腾讯逆向] 策略2: 模拟反作弊模块加载");
        simulateAntiCheatModuleLoad();

        LOGI("🎯 [腾讯逆向] 策略3: 模拟内存保护机制");
        simulateMemoryProtection();

        LOGI("🎯 [腾讯逆向] 策略4: 模拟SDK特征标记");
        simulateSDKSignature();

        LOGI("🎯 [腾讯逆向] 完成所有模拟策略，总计新增: %d个特征块", 4);
    }

    // 模拟ACE引擎初始化
    static void simulateACEEngineInit() {
        LOGI("🔧 [ACE引擎] 模拟引擎初始化内存分配...");

        // ACE引擎可能分配的典型结构
        struct ACEEngineContext {
            uint32_t magic;           // 魔数标识
            uint32_t version;         // 版本号
            uint64_t timestamp;       // 时间戳
            char signature[32];       // 签名
            void* callback_table[16]; // 回调函数表
            uint8_t reserved[128];    // 保留空间
        };

        ACEEngineContext* ctx = new ACEEngineContext();
        ctx->magic = 0x41434521;  // "ACE!"
        ctx->version = 0x20240801; // 版本
        ctx->timestamp = time(nullptr);
        strcpy(ctx->signature, "TencentACEAntiCheat2024");

        tencent_blocks_.push_back(ctx);
        LOGI("🔧 [ACE引擎] 分配引擎上下文: %zu字节", sizeof(ACEEngineContext));
    }

    // 模拟反作弊模块加载
    static void simulateAntiCheatModuleLoad() {
        LOGI("🛡️ [反作弊] 模拟反作弊模块内存分配...");

        // 反作弊模块可能的内存结构
        struct AntiCheatModule {
            uint32_t module_id;
            uint32_t flags;
            uint64_t base_address;
            uint32_t size;
            uint8_t hash[32];
            uint8_t encrypted_data[256];
        };

        AntiCheatModule* module = new AntiCheatModule();
        module->module_id = 0x54503200; // "TP2\0"
        module->flags = 0x12345678;
        module->base_address = reinterpret_cast<uint64_t>(module);
        module->size = sizeof(AntiCheatModule);

        // 填充加密数据
        for (int i = 0; i < 256; i++) {
            module->encrypted_data[i] = (i * 0x41) ^ 0xAC;
        }

        tencent_blocks_.push_back(module);
        LOGI("🛡️ [反作弊] 分配反作弊模块: %zu字节", sizeof(AntiCheatModule));
    }

    // 模拟内存保护机制
    static void simulateMemoryProtection() {
        LOGI("🔒 [内存保护] 模拟内存保护机制...");

        // 分配受保护的内存区域
        size_t protect_size = 1024; // 1KB保护区域
        void* protected_mem = malloc(protect_size);

        if (protected_mem) {
            // 填充保护模式
            uint32_t* data = static_cast<uint32_t*>(protected_mem);
            for (size_t i = 0; i < protect_size / 4; i++) {
                data[i] = 0xDEADBEEF ^ (i * 0x12345678);
            }

            tencent_blocks_.push_back(protected_mem);
            LOGI("🔒 [内存保护] 分配保护内存: %zu字节", protect_size);
        }
    }

    // 模拟SDK特征标记
    static void simulateSDKSignature() {
        LOGI("📝 [SDK签名] 模拟SDK特征标记...");

        // 创建SDK特征数据
        struct SDKSignature {
            char vendor[16];      // 厂商标识
            char product[16];     // 产品标识
            uint32_t build_time;  // 构建时间
            uint32_t checksum;    // 校验和
            uint8_t fingerprint[64]; // 指纹数据
        };

        SDKSignature* sig = new SDKSignature();
        strcpy(sig->vendor, "Tencent");
        strcpy(sig->product, "TP2SDK");
        sig->build_time = 0x20240801;
        sig->checksum = 0xACEACEAC;

        // 生成指纹
        for (int i = 0; i < 64; i++) {
            sig->fingerprint[i] = (i * 0xAC) ^ 0x21;
        }

        tencent_blocks_.push_back(sig);
        LOGI("📝 [SDK签名] 分配SDK签名: %zu字节", sizeof(SDKSignature));
    }
    
    static void cleanup() {
        keep_running_ = false;
        
        // 清理Ca区域内存
        for (void* ptr : ca_heap_blocks_) {
            if (ptr) {
                free(ptr);
            }
        }
        ca_heap_blocks_.clear();
        
        // 清理Cb区域内存
        for (void* ptr : cb_bss_blocks_) {
            if (ptr && ptr != MAP_FAILED) {
                munmap(ptr, 1024 * 1024); // 使用最大可能的大小
            }
        }
        cb_bss_blocks_.clear();
        
        initialized_ = false;
        LOGI("✅ [GG兼容] 清理完成");
    }
};

// 静态成员定义
bool GGCompatibleTrapSystem::initialized_ = false;
std::vector<void*> GGCompatibleTrapSystem::ca_heap_blocks_;
std::vector<void*> GGCompatibleTrapSystem::cb_bss_blocks_;
std::vector<void*> GGCompatibleTrapSystem::tencent_blocks_;
std::thread GGCompatibleTrapSystem::keep_alive_thread_;
bool GGCompatibleTrapSystem::keep_running_ = false;

// JNI接口
extern "C" {

JNIEXPORT jboolean JNICALL
Java_com_sy_newfwg_GGCompatibleMemoryTrapManager_nativeInitialize(JNIEnv *env, jclass clazz) {
    return GGCompatibleTrapSystem::initialize();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_GGCompatibleMemoryTrapManager_nativeLogStatus(JNIEnv *env, jclass clazz) {
    GGCompatibleTrapSystem::logMemoryStatus();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_GGCompatibleMemoryTrapManager_nativeCleanup(JNIEnv *env, jclass clazz) {
    GGCompatibleTrapSystem::cleanup();
}

} // extern "C"
