#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1683104 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=20496, tid=27232
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-574fff826d0515716894785faac1f2e4-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:25:26 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 4.535779 seconds (0d 0h 0m 4s)

---------------  T H R E A D  ---------------

Current thread (0x0000023fd49df2a0):  JavaThread "C2 CompilerThread2" daemon [_thread_in_native, id=27232, stack(0x000000baf3b00000,0x000000baf3c00000) (1024K)]


Current CompileTask:
C2:4535 4139       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)

Stack: [0x000000baf3b00000,0x000000baf3c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x1e0029]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000023fd644af50, length=25, elements={
0x0000023ff1e793a0, 0x0000023ff95e5dc0, 0x0000023ff95e6c80, 0x0000023ffa4151b0,
0x0000023ffa415ec0, 0x0000023ffa416bd0, 0x0000023ffa418630, 0x0000023ffa419460,
0x0000023ffa451560, 0x0000023ffa515480, 0x0000023fd40763d0, 0x0000023fd4833400,
0x0000023fd4627400, 0x0000023fd48f6530, 0x0000023fd48f7400, 0x0000023fd4a45fb0,
0x0000023fd4bdb590, 0x0000023fd4f9c6b0, 0x0000023fd45fef00, 0x0000023fd49dec00,
0x0000023fd49df2a0, 0x0000023fd4bfa2f0, 0x0000023fd4bfbd30, 0x0000023fd4bfb010,
0x0000023fd4bf88b0
}

Java Threads: ( => current thread )
  0x0000023ff1e793a0 JavaThread "main"                              [_thread_blocked, id=26672, stack(0x000000baf1d00000,0x000000baf1e00000) (1024K)]
  0x0000023ff95e5dc0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=41352, stack(0x000000baf2100000,0x000000baf2200000) (1024K)]
  0x0000023ff95e6c80 JavaThread "Finalizer"                  daemon [_thread_blocked, id=29808, stack(0x000000baf2200000,0x000000baf2300000) (1024K)]
  0x0000023ffa4151b0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=46568, stack(0x000000baf2300000,0x000000baf2400000) (1024K)]
  0x0000023ffa415ec0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=38456, stack(0x000000baf2400000,0x000000baf2500000) (1024K)]
  0x0000023ffa416bd0 JavaThread "Service Thread"             daemon [_thread_blocked, id=26688, stack(0x000000baf2500000,0x000000baf2600000) (1024K)]
  0x0000023ffa418630 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=46124, stack(0x000000baf2600000,0x000000baf2700000) (1024K)]
  0x0000023ffa419460 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=45160, stack(0x000000baf2700000,0x000000baf2800000) (1024K)]
  0x0000023ffa451560 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=35568, stack(0x000000baf2800000,0x000000baf2900000) (1024K)]
  0x0000023ffa515480 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=44548, stack(0x000000baf2900000,0x000000baf2a00000) (1024K)]
  0x0000023fd40763d0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=6448, stack(0x000000baf2b00000,0x000000baf2c00000) (1024K)]
  0x0000023fd4833400 JavaThread "Active Thread: Equinox Container: 9b1eecd6-6edb-44e1-a5b9-aee7b8a40c43"        [_thread_blocked, id=14268, stack(0x000000baf3300000,0x000000baf3400000) (1024K)]
  0x0000023fd4627400 JavaThread "Refresh Thread: Equinox Container: 9b1eecd6-6edb-44e1-a5b9-aee7b8a40c43" daemon [_thread_blocked, id=42336, stack(0x000000baf3500000,0x000000baf3600000) (1024K)]
  0x0000023fd48f6530 JavaThread "Framework Event Dispatcher: Equinox Container: 9b1eecd6-6edb-44e1-a5b9-aee7b8a40c43" daemon [_thread_blocked, id=44228, stack(0x000000baf3600000,0x000000baf3700000) (1024K)]
  0x0000023fd48f7400 JavaThread "Start Level: Equinox Container: 9b1eecd6-6edb-44e1-a5b9-aee7b8a40c43" daemon [_thread_in_native, id=43332, stack(0x000000baf3700000,0x000000baf3800000) (1024K)]
  0x0000023fd4a45fb0 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=41468, stack(0x000000baf3800000,0x000000baf3900000) (1024K)]
  0x0000023fd4bdb590 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=31708, stack(0x000000baf3900000,0x000000baf3a00000) (1024K)]
  0x0000023fd4f9c6b0 JavaThread "SCR Component Registry"     daemon [_thread_blocked, id=4580, stack(0x000000baf3a00000,0x000000baf3b00000) (1024K)]
  0x0000023fd45fef00 JavaThread "Worker-JM"                         [_thread_blocked, id=38644, stack(0x000000baf2c00000,0x000000baf2d00000) (1024K)]
  0x0000023fd49dec00 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=41280, stack(0x000000baf2a00000,0x000000baf2b00000) (1024K)]
=>0x0000023fd49df2a0 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=27232, stack(0x000000baf3b00000,0x000000baf3c00000) (1024K)]
  0x0000023fd4bfa2f0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=38956, stack(0x000000baf3c00000,0x000000baf3d00000) (1024K)]
  0x0000023fd4bfbd30 JavaThread "Worker-0"                          [_thread_blocked, id=18204, stack(0x000000baf3d00000,0x000000baf3e00000) (1024K)]
  0x0000023fd4bfb010 JavaThread "Worker-1"                          [_thread_blocked, id=43816, stack(0x000000baf3e00000,0x000000baf3f00000) (1024K)]
  0x0000023fd4bf88b0 JavaThread "Java indexing"              daemon [_thread_new, id=25388, stack(0x0000000000000000,0x0000000000000000) (0B)]
Total: 25

Other Threads:
  0x0000023ffa412b80 VMThread "VM Thread"                           [id=29708, stack(0x000000baf2000000,0x000000baf2100000) (1024K)]
  0x0000023ff950cb50 WatcherThread "VM Periodic Task Thread"        [id=34020, stack(0x000000baf1f00000,0x000000baf2000000) (1024K)]
  0x0000023ff1e9a090 WorkerThread "GC Thread#0"                     [id=16412, stack(0x000000baf1e00000,0x000000baf1f00000) (1024K)]
  0x0000023fd429ba10 WorkerThread "GC Thread#1"                     [id=19516, stack(0x000000baf2d00000,0x000000baf2e00000) (1024K)]
  0x0000023fd4039d10 WorkerThread "GC Thread#2"                     [id=19968, stack(0x000000baf2e00000,0x000000baf2f00000) (1024K)]
  0x0000023fd41a9ce0 WorkerThread "GC Thread#3"                     [id=13480, stack(0x000000baf2f00000,0x000000baf3000000) (1024K)]
  0x0000023fd41aa080 WorkerThread "GC Thread#4"                     [id=4492, stack(0x000000baf3000000,0x000000baf3100000) (1024K)]
  0x0000023fd41924b0 WorkerThread "GC Thread#5"                     [id=45608, stack(0x000000baf3100000,0x000000baf3200000) (1024K)]
  0x0000023fd4192850 WorkerThread "GC Thread#6"                     [id=42592, stack(0x000000baf3200000,0x000000baf3300000) (1024K)]
  0x0000023fd4ab0ad0 WorkerThread "GC Thread#7"                     [id=39340, stack(0x000000baf3400000,0x000000baf3500000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  4571 4261       4       java.util.Properties::loadConvert (540 bytes)
C2 CompilerThread2  4571 4139       4       org.lombokweb.asm.ClassReader::readMethod (1070 bytes)
Total: 2

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000023f8f000000-0x0000023f8fba0000-0x0000023f8fba0000), size 12189696, SharedBaseAddress: 0x0000023f8f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000023f90000000-0x0000023fd0000000, reserved size: 1073741824
Narrow klass base: 0x0000023f8f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 25600K, used 19654K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 23552K, 77% used [0x00000000d5580000,0x00000000d6739640,0x00000000d6c80000)
  from space 2048K, 73% used [0x00000000d7000000,0x00000000d7178300,0x00000000d7200000)
  to   space 2048K, 0% used [0x00000000d7200000,0x00000000d7200000,0x00000000d7400000)
 ParOldGen       total 68608K, used 14155K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 20% used [0x0000000080000000,0x0000000080dd2cf8,0x0000000084300000)
 Metaspace       used 27591K, committed 28416K, reserved 1114112K
  class space    used 2617K, committed 3008K, reserved 1048576K

Card table byte_map: [0x0000023ff16b0000,0x0000023ff1ac0000] _byte_map_base: 0x0000023ff12b0000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x0000023ff4ff0000, 0x0000023ff6ff0000)
 End Bits:   [0x0000023ff6ff0000, 0x0000023ff8ff0000)

Polling page: 0x0000023ff13a0000

Metaspace:

Usage:
  Non-class:     24.39 MB used.
      Class:      2.56 MB used.
       Both:     26.95 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      24.81 MB ( 39%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       2.94 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      27.75 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  7.08 MB
       Class:  13.01 MB
        Both:  20.08 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 600.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 444.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 17.
num_chunks_taken_from_freelist: 1671.
num_chunk_merges: 9.
num_chunk_splits: 1101.
num_chunks_enlarged: 724.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=2034Kb max_used=2034Kb free=117965Kb
 bounds [0x0000023f87ad0000, 0x0000023f87d40000, 0x0000023f8f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=8941Kb max_used=8941Kb free=111059Kb
 bounds [0x0000023f80000000, 0x0000023f808c0000, 0x0000023f87530000]
CodeHeap 'non-nmethods': size=5760Kb used=1360Kb max_used=1360Kb free=4399Kb
 bounds [0x0000023f87530000, 0x0000023f877a0000, 0x0000023f87ad0000]
 total_blobs=4905 nmethods=4279 adapters=530
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 4.496 Thread 0x0000023ffa451560 4241       3       java.lang.invoke.MethodHandleImpl::getConstantHandle (21 bytes)
Event: 4.497 Thread 0x0000023ffa451560 nmethod 4241 0x0000023f808a0210 code [0x0000023f808a0400, 0x0000023f808a08f8]
Event: 4.497 Thread 0x0000023fd49dec00 4242       4       sun.invoke.util.VerifyAccess::isSamePackage (37 bytes)
Event: 4.498 Thread 0x0000023fd49dec00 nmethod 4242 0x0000023f87cc9410 code [0x0000023f87cc95c0, 0x0000023f87cc96e8]
Event: 4.499 Thread 0x0000023fd49dec00 4243       4       sun.invoke.util.VerifyType::isNullConversion (157 bytes)
Event: 4.502 Thread 0x0000023fd49dec00 nmethod 4243 0x0000023f87cc9810 code [0x0000023f87cc9a40, 0x0000023f87cc9ed0]
Event: 4.503 Thread 0x0000023ffa451560 4244       3       java.lang.invoke.LambdaForm$BasicType::basicType (187 bytes)
Event: 4.503 Thread 0x0000023ffa419460 4245       4       java.util.ImmutableCollections$List12::get (35 bytes)
Event: 4.503 Thread 0x0000023ffa451560 nmethod 4244 0x0000023f808a0a90 code [0x0000023f808a0d40, 0x0000023f808a1788]
Event: 4.503 Thread 0x0000023fd49dec00 4246       4       java.lang.invoke.BoundMethodHandle::<init> (32 bytes)
Event: 4.503 Thread 0x0000023ffa419460 nmethod 4245 0x0000023f87cca390 code [0x0000023f87cca520, 0x0000023f87cca600]
Event: 4.505 Thread 0x0000023fd49dec00 nmethod 4246 0x0000023f87cca690 code [0x0000023f87cca840, 0x0000023f87ccaa08]
Event: 4.505 Thread 0x0000023ffa419460 4247       4       java.lang.invoke.MethodType::methodType (69 bytes)
Event: 4.509 Thread 0x0000023ffa419460 nmethod 4247 0x0000023f87ccac10 code [0x0000023f87ccae00, 0x0000023f87ccb238]
Event: 4.515 Thread 0x0000023ffa451560 4248   !   3       org.eclipse.core.internal.resources.Workspace::getResourceInfo (109 bytes)
Event: 4.516 Thread 0x0000023ffa451560 nmethod 4248 0x0000023f808a1a10 code [0x0000023f808a1ca0, 0x0000023f808a27e8]
Event: 4.519 Thread 0x0000023ffa451560 4249       3       com.sun.org.apache.xerces.internal.util.XMLResourceIdentifierImpl::setValues (28 bytes)
Event: 4.519 Thread 0x0000023ffa451560 nmethod 4249 0x0000023f808a2d90 code [0x0000023f808a2f20, 0x0000023f808a3090]
Event: 4.522 Thread 0x0000023ffa451560 4250       3       com.sun.org.apache.xerces.internal.impl.XMLEntityManager::setScannerVersion (115 bytes)
Event: 4.522 Thread 0x0000023ffa451560 nmethod 4250 0x0000023f808a3110 code [0x0000023f808a3320, 0x0000023f808a38f8]

GC Heap History (20 events):
Event: 0.751 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4276K, committed 4480K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}
Event: 0.755 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3447K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71ddf38,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 4276K, committed 4480K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}
Event: 1.320 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 29047K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71ddf38,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 8000K, committed 8320K, reserved 1114112K
  class space    used 811K, committed 960K, reserved 1048576K
}
Event: 1.325 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4077K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767b5f0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 1863K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 2% used [0x0000000080000000,0x00000000801d1f28,0x0000000084300000)
 Metaspace       used 8000K, committed 8320K, reserved 1114112K
  class space    used 811K, committed 960K, reserved 1048576K
}
Event: 1.711 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29677K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767b5f0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 1863K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 2% used [0x0000000080000000,0x00000000801d1f28,0x0000000084300000)
 Metaspace       used 9338K, committed 9728K, reserved 1114112K
  class space    used 970K, committed 1152K, reserved 1048576K
}
Event: 1.716 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4094K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727f950,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 5274K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x0000000080526bd0,0x0000000084300000)
 Metaspace       used 9338K, committed 9728K, reserved 1114112K
  class space    used 970K, committed 1152K, reserved 1048576K
}
Event: 2.160 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29694K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727f950,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 5274K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x0000000080526bd0,0x0000000084300000)
 Metaspace       used 12109K, committed 12544K, reserved 1114112K
  class space    used 1256K, committed 1472K, reserved 1048576K
}
Event: 2.164 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4074K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767a928,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 7017K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 10% used [0x0000000080000000,0x00000000806da6b0,0x0000000084300000)
 Metaspace       used 12109K, committed 12544K, reserved 1114112K
  class space    used 1256K, committed 1472K, reserved 1048576K
}
Event: 2.787 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29674K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767a928,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 7017K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 10% used [0x0000000080000000,0x00000000806da6b0,0x0000000084300000)
 Metaspace       used 15418K, committed 15936K, reserved 1114112K
  class space    used 1541K, committed 1792K, reserved 1048576K
}
Event: 2.790 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4083K [0x00000000d5580000, 0x00000000d7a00000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e00000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727cc50,0x00000000d7280000)
  to   space 6144K, 0% used [0x00000000d7400000,0x00000000d7400000,0x00000000d7a00000)
 ParOldGen       total 68608K, used 8432K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x000000008083c150,0x0000000084300000)
 Metaspace       used 15418K, committed 15936K, reserved 1114112K
  class space    used 1541K, committed 1792K, reserved 1048576K
}
Event: 3.352 GC heap before
{Heap before GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 29171K [0x00000000d5580000, 0x00000000d7a00000, 0x0000000100000000)
  eden space 25088K, 100% used [0x00000000d5580000,0x00000000d6e00000,0x00000000d6e00000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727cc50,0x00000000d7280000)
  to   space 6144K, 0% used [0x00000000d7400000,0x00000000d7400000,0x00000000d7a00000)
 ParOldGen       total 68608K, used 8432K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x000000008083c150,0x0000000084300000)
 Metaspace       used 18535K, committed 19264K, reserved 1114112K
  class space    used 1887K, committed 2240K, reserved 1048576K
}
Event: 3.356 GC heap after
{Heap after GC invocations=6 (full 0):
 PSYoungGen      total 30208K, used 5412K [0x00000000d5580000, 0x00000000d7980000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 5632K, 96% used [0x00000000d7400000,0x00000000d7949308,0x00000000d7980000)
  to   space 5632K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7400000)
 ParOldGen       total 68608K, used 8440K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x000000008083e150,0x0000000084300000)
 Metaspace       used 18535K, committed 19264K, reserved 1114112K
  class space    used 1887K, committed 2240K, reserved 1048576K
}
Event: 3.807 GC heap before
{Heap before GC invocations=7 (full 0):
 PSYoungGen      total 30208K, used 26704K [0x00000000d5580000, 0x00000000d7980000, 0x0000000100000000)
  eden space 24576K, 86% used [0x00000000d5580000,0x00000000d6a4adc0,0x00000000d6d80000)
  from space 5632K, 96% used [0x00000000d7400000,0x00000000d7949308,0x00000000d7980000)
  to   space 5632K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7400000)
 ParOldGen       total 68608K, used 8440K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 12% used [0x0000000080000000,0x000000008083e150,0x0000000084300000)
 Metaspace       used 20726K, committed 21504K, reserved 1114112K
  class space    used 2120K, committed 2496K, reserved 1048576K
}
Event: 3.810 GC heap after
{Heap after GC invocations=7 (full 0):
 PSYoungGen      total 29696K, used 4756K [0x00000000d5580000, 0x00000000d7780000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 5120K, 92% used [0x00000000d6e80000,0x00000000d7325160,0x00000000d7380000)
  to   space 4096K, 0% used [0x00000000d7380000,0x00000000d7380000,0x00000000d7780000)
 ParOldGen       total 68608K, used 9785K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 14% used [0x0000000080000000,0x000000008098e7a0,0x0000000084300000)
 Metaspace       used 20726K, committed 21504K, reserved 1114112K
  class space    used 2120K, committed 2496K, reserved 1048576K
}
Event: 3.810 GC heap before
{Heap before GC invocations=8 (full 1):
 PSYoungGen      total 29696K, used 4756K [0x00000000d5580000, 0x00000000d7780000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 5120K, 92% used [0x00000000d6e80000,0x00000000d7325160,0x00000000d7380000)
  to   space 4096K, 0% used [0x00000000d7380000,0x00000000d7380000,0x00000000d7780000)
 ParOldGen       total 68608K, used 9785K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 14% used [0x0000000080000000,0x000000008098e7a0,0x0000000084300000)
 Metaspace       used 20726K, committed 21504K, reserved 1114112K
  class space    used 2120K, committed 2496K, reserved 1048576K
}
Event: 3.836 GC heap after
{Heap after GC invocations=8 (full 1):
 PSYoungGen      total 29696K, used 0K [0x00000000d5580000, 0x00000000d7780000, 0x0000000100000000)
  eden space 24576K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d80000)
  from space 5120K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7380000)
  to   space 4096K, 0% used [0x00000000d7380000,0x00000000d7380000,0x00000000d7780000)
 ParOldGen       total 68608K, used 13357K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 19% used [0x0000000080000000,0x0000000080d0b7e8,0x0000000084300000)
 Metaspace       used 20714K, committed 21504K, reserved 1114112K
  class space    used 2116K, committed 2496K, reserved 1048576K
}
Event: 4.049 GC heap before
{Heap before GC invocations=9 (full 1):
 PSYoungGen      total 29696K, used 24576K [0x00000000d5580000, 0x00000000d7780000, 0x0000000100000000)
  eden space 24576K, 100% used [0x00000000d5580000,0x00000000d6d80000,0x00000000d6d80000)
  from space 5120K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7380000)
  to   space 4096K, 0% used [0x00000000d7380000,0x00000000d7380000,0x00000000d7780000)
 ParOldGen       total 68608K, used 13357K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 19% used [0x0000000080000000,0x0000000080d0b7e8,0x0000000084300000)
 Metaspace       used 23373K, committed 24192K, reserved 1114112K
  class space    used 2287K, committed 2688K, reserved 1048576K
}
Event: 4.050 GC heap after
{Heap after GC invocations=9 (full 1):
 PSYoungGen      total 24576K, used 357K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 24064K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6d00000)
  from space 512K, 69% used [0x00000000d7380000,0x00000000d73d9560,0x00000000d7400000)
  to   space 2048K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7200000)
 ParOldGen       total 68608K, used 13365K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 19% used [0x0000000080000000,0x0000000080d0d7e8,0x0000000084300000)
 Metaspace       used 23373K, committed 24192K, reserved 1114112K
  class space    used 2287K, committed 2688K, reserved 1048576K
}
Event: 4.259 GC heap before
{Heap before GC invocations=10 (full 1):
 PSYoungGen      total 24576K, used 24421K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 24064K, 100% used [0x00000000d5580000,0x00000000d6d00000,0x00000000d6d00000)
  from space 512K, 69% used [0x00000000d7380000,0x00000000d73d9560,0x00000000d7400000)
  to   space 2048K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7200000)
 ParOldGen       total 68608K, used 13365K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 19% used [0x0000000080000000,0x0000000080d0d7e8,0x0000000084300000)
 Metaspace       used 26326K, committed 27136K, reserved 1114112K
  class space    used 2514K, committed 2880K, reserved 1048576K
}
Event: 4.261 GC heap after
{Heap after GC invocations=10 (full 1):
 PSYoungGen      total 25600K, used 1504K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 23552K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6c80000)
  from space 2048K, 73% used [0x00000000d7000000,0x00000000d7178300,0x00000000d7200000)
  to   space 2048K, 0% used [0x00000000d7200000,0x00000000d7200000,0x00000000d7400000)
 ParOldGen       total 68608K, used 14155K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 20% used [0x0000000080000000,0x0000000080dd2cf8,0x0000000084300000)
 Metaspace       used 26326K, committed 27136K, reserved 1114112K
  class space    used 2514K, committed 2880K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.033 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.098 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.103 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.106 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.110 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.136 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.226 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.341 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 2.993 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna5640518168908161892.dll

Deoptimization events (20 events):
Event: 4.148 Thread 0x0000023fd48f7400 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023f87cb3184 relative=0x0000000000000164
Event: 4.148 Thread 0x0000023fd48f7400 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023f87cb3184 method=org.lombokweb.asm.ByteVector.putByteArray([BII)Lorg/lombokweb/asm/ByteVector; @ 20 c2
Event: 4.148 Thread 0x0000023fd48f7400 DEOPT PACKING pc=0x0000023f87cb3184 sp=0x000000baf37f2360
Event: 4.148 Thread 0x0000023fd48f7400 DEOPT UNPACKING pc=0x0000023f87583aa2 sp=0x000000baf37f2330 mode 2
Event: 4.148 Thread 0x0000023fd48f7400 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023f87cb3858 relative=0x0000000000000398
Event: 4.148 Thread 0x0000023fd48f7400 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023f87cb3858 method=org.lombokweb.asm.Label.resolve([BLorg/lombokweb/asm/ByteVector;I)Z @ 81 c2
Event: 4.148 Thread 0x0000023fd48f7400 DEOPT PACKING pc=0x0000023f87cb3858 sp=0x000000baf37f23f0
Event: 4.148 Thread 0x0000023fd48f7400 DEOPT UNPACKING pc=0x0000023f87583aa2 sp=0x000000baf37f22f8 mode 2
Event: 4.450 Thread 0x0000023fd48f7400 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023f87bb6478 relative=0x00000000000018b8
Event: 4.450 Thread 0x0000023fd48f7400 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023f87bb6478 method=jdk.internal.jimage.ImageStringsReader.stringFromByteBufferMatches(Ljava/nio/ByteBuffer;ILjava/lang/String;I)I @ 48 c2
Event: 4.450 Thread 0x0000023fd48f7400 DEOPT PACKING pc=0x0000023f87bb6478 sp=0x000000baf37f42a0
Event: 4.450 Thread 0x0000023fd48f7400 DEOPT UNPACKING pc=0x0000023f87583aa2 sp=0x000000baf37f3fd8 mode 2
Event: 4.491 Thread 0x0000023fd48f7400 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000023f87c7f804 relative=0x0000000000001164
Event: 4.491 Thread 0x0000023fd48f7400 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000023f87c7f804 method=java.util.Properties.loadConvert([CIILjava/lang/StringBuilder;)Ljava/lang/String; @ 463 c2
Event: 4.491 Thread 0x0000023fd48f7400 DEOPT PACKING pc=0x0000023f87c7f804 sp=0x000000baf37f7990
Event: 4.491 Thread 0x0000023fd48f7400 DEOPT UNPACKING pc=0x0000023f87583aa2 sp=0x000000baf37f7980 mode 2
Event: 4.506 Thread 0x0000023fd48f7400 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000023f87b8c6ec relative=0x0000000000000a6c
Event: 4.506 Thread 0x0000023fd48f7400 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000023f87b8c6ec method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 56 c2
Event: 4.506 Thread 0x0000023fd48f7400 DEOPT PACKING pc=0x0000023f87b8c6ec sp=0x000000baf37f9520
Event: 4.506 Thread 0x0000023fd48f7400 DEOPT UNPACKING pc=0x0000023f87583aa2 sp=0x000000baf37f9488 mode 2

Classes loaded (20 events):
Event: 4.530 Loading class com/sun/org/apache/xerces/internal/dom/CharacterDataImpl
Event: 4.530 Loading class com/sun/org/apache/xerces/internal/dom/CharacterDataImpl done
Event: 4.530 Loading class com/sun/org/apache/xerces/internal/dom/TextImpl done
Event: 4.530 Loading class com/sun/org/apache/xerces/internal/dom/DeferredTextImpl done
Event: 4.530 Loading class com/sun/org/apache/xerces/internal/dom/CharacterDataImpl$1
Event: 4.530 Loading class com/sun/org/apache/xerces/internal/dom/CharacterDataImpl$1 done
Event: 4.530 Loading class com/sun/org/apache/xerces/internal/dom/AttributeMap
Event: 4.530 Loading class com/sun/org/apache/xerces/internal/dom/NamedNodeMapImpl
Event: 4.531 Loading class org/w3c/dom/NamedNodeMap
Event: 4.531 Loading class org/w3c/dom/NamedNodeMap done
Event: 4.531 Loading class com/sun/org/apache/xerces/internal/dom/NamedNodeMapImpl done
Event: 4.531 Loading class com/sun/org/apache/xerces/internal/dom/AttributeMap done
Event: 4.531 Loading class com/sun/org/apache/xerces/internal/dom/DeferredAttrImpl
Event: 4.531 Loading class com/sun/org/apache/xerces/internal/dom/AttrImpl
Event: 4.531 Loading class org/w3c/dom/Attr
Event: 4.531 Loading class org/w3c/dom/Attr done
Event: 4.531 Loading class com/sun/org/apache/xerces/internal/dom/AttrImpl done
Event: 4.531 Loading class com/sun/org/apache/xerces/internal/dom/DeferredAttrImpl done
Event: 4.533 Loading class org/w3c/dom/DOMException
Event: 4.533 Loading class org/w3c/dom/DOMException done

Classes unloaded (7 events):
Event: 3.814 Thread 0x0000023ffa412b80 Unloading class 0x0000023f901a4c00 'java/lang/invoke/LambdaForm$MH+0x0000023f901a4c00'
Event: 3.814 Thread 0x0000023ffa412b80 Unloading class 0x0000023f901a4800 'java/lang/invoke/LambdaForm$MH+0x0000023f901a4800'
Event: 3.814 Thread 0x0000023ffa412b80 Unloading class 0x0000023f901a4400 'java/lang/invoke/LambdaForm$MH+0x0000023f901a4400'
Event: 3.814 Thread 0x0000023ffa412b80 Unloading class 0x0000023f901a4000 'java/lang/invoke/LambdaForm$MH+0x0000023f901a4000'
Event: 3.814 Thread 0x0000023ffa412b80 Unloading class 0x0000023f901a3c00 'java/lang/invoke/LambdaForm$BMH+0x0000023f901a3c00'
Event: 3.814 Thread 0x0000023ffa412b80 Unloading class 0x0000023f901a3800 'java/lang/invoke/LambdaForm$DMH+0x0000023f901a3800'
Event: 3.814 Thread 0x0000023ffa412b80 Unloading class 0x0000023f901a2800 'java/lang/invoke/LambdaForm$DMH+0x0000023f901a2800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 3.080 Thread 0x0000023fd48f7400 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d6092c30}: Found class java.lang.Object, but interface was expected> (0x00000000d6092c30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 3.091 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d60c3ad0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d60c3ad0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.092 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d60cb4e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d60cb4e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.092 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d60cf4a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d60cf4a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.092 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d60d2fe8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d60d2fe8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.093 Thread 0x0000023fd4bfbd30 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d609b430}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d609b430) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.115 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d61c17d0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d61c17d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.285 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d69797e8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d69797e8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.350 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6dd6c38}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x00000000d6dd6c38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.490 Thread 0x0000023fd48f7400 Exception <a 'java/io/FileNotFoundException'{0x00000000d5d03ca8}> (0x00000000d5d03ca8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.491 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5d0c5f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x00000000d5d0c5f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.693 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65622a8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x00000000d65622a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.694 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6566440}: 'long java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object)'> (0x00000000d6566440) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.889 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d56ce238}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000d56ce238) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.890 Thread 0x0000023fd48f7400 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d56d18c8}: 'double java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000d56d18c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.992 Thread 0x0000023fd48f7400 Exception <a 'java/io/FileNotFoundException'{0x00000000d678a3c8}> (0x00000000d678a3c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.992 Thread 0x0000023fd48f7400 Exception <a 'java/io/FileNotFoundException'{0x00000000d678b510}> (0x00000000d678b510) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.992 Thread 0x0000023fd48f7400 Exception <a 'java/io/FileNotFoundException'{0x00000000d678c758}> (0x00000000d678c758) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 4.148 Thread 0x0000023fd48f7400 Implicit null exception at 0x0000023f87cb3075 to 0x0000023f87cb3168
Event: 4.268 Thread 0x0000023fd48f7400 Exception <a 'java/io/FileNotFoundException'{0x00000000d5c36660}> (0x00000000d5c36660) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 3.001 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.001 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.180 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.180 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.246 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.246 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.256 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.256 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.284 Executing VM operation: ICBufferFull
Event: 3.284 Executing VM operation: ICBufferFull done
Event: 3.352 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.356 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 3.806 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold)
Event: 3.836 Executing VM operation: CollectForMetadataAllocation (Metadata GC Threshold) done
Event: 4.049 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.050 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.259 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 4.261 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 4.486 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 4.486 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.413 Thread 0x0000023ffa451560 Thread added: 0x0000023fd40d8ae0
Event: 1.112 Thread 0x0000023ff1e793a0 Thread added: 0x0000023fd4833400
Event: 1.337 Thread 0x0000023ff1e793a0 Thread added: 0x0000023fd4627400
Event: 1.371 Thread 0x0000023fd4627400 Thread added: 0x0000023fd48f6530
Event: 1.412 Thread 0x0000023ff1e793a0 Thread added: 0x0000023fd48f7400
Event: 1.443 Thread 0x0000023fd40d8ae0 Thread exited: 0x0000023fd40d8ae0
Event: 1.443 Thread 0x0000023ffa668440 Thread exited: 0x0000023ffa668440
Event: 1.512 Thread 0x0000023ffa451560 Thread added: 0x0000023fd40d8ae0
Event: 1.548 Thread 0x0000023ffa451560 Thread added: 0x0000023fd4928550
Event: 1.765 Thread 0x0000023fd48f7400 Thread added: 0x0000023fd4a45fb0
Event: 1.949 Thread 0x0000023fd48f7400 Thread added: 0x0000023fd4bdb590
Event: 2.036 Thread 0x0000023fd48f7400 Thread added: 0x0000023fd4f9c6b0
Event: 2.081 Thread 0x0000023fd4928550 Thread exited: 0x0000023fd4928550
Event: 2.619 Thread 0x0000023fd48f7400 Thread added: 0x0000023fd45fef00
Event: 2.677 Thread 0x0000023fd40d8ae0 Thread exited: 0x0000023fd40d8ae0
Event: 2.799 Thread 0x0000023ffa451560 Thread added: 0x0000023fd49dec00
Event: 2.800 Thread 0x0000023ffa451560 Thread added: 0x0000023fd49df2a0
Event: 3.016 Thread 0x0000023fd48f7400 Thread added: 0x0000023fd4bfa2f0
Event: 3.060 Thread 0x0000023fd48f7400 Thread added: 0x0000023fd4bfbd30
Event: 3.095 Thread 0x0000023fd48f7400 Thread added: 0x0000023fd4bfb010


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff844e20000 - 0x00007ff844e65000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff861060000 - 0x00007ff861078000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ff860720000 - 0x00007ff860758000 	C:\Windows\system32\rsaenh.dll
0x00007ff8615f0000 - 0x00007ff86161e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ff861050000 - 0x00007ff86105c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ff860b40000 - 0x00007ff860b7b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ff8626f0000 - 0x00007ff8626f8000 	C:\Windows\System32\NSI.dll
0x00007ff83d810000 - 0x00007ff83d859000 	C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna5640518168908161892.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-146731693

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-574fff826d0515716894785faac1f2e4-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:36 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (1583M free)
TotalPageFile size 61318M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 171M, peak: 172M
current process commit charge ("private bytes"): 299M, peak: 299M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
