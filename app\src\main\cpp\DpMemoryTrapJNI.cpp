#include <jni.h>
#include "DpMemoryTrap.h"

extern "C" {

JNIEXPORT void JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeInitialize(JNIEnv *env, jobject thiz) {
    dp_initialize_memory_traps();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeDeployCaTraps(JNIEnv *env, jobject thiz, jint count) {
    dp_deploy_ca_traps(count);
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeDeployCbTraps(JNIEnv *env, jobject thiz) {
    dp_deploy_cb_traps();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeVerifyTraps(JNIEnv *env, jobject thiz) {
    dp_verify_traps();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeDumpMemoryInfo(JNIEnv *env, jobject thiz) {
    dp_dump_memory_info();
}

JNIEXPORT void JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeShutdown(JNIEnv *env, jobject thiz) {
    dp_shutdown();
}

JNIEXPORT jlong JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeGetCaMemorySize(JNIEnv *env, jobject thiz) {
    return static_cast<jlong>(DpTrap::DpMemoryTrapSystem::GetInstance().GetCaMemorySize());
}

JNIEXPORT jlong JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeGetCbMemorySize(JNIEnv *env, jobject thiz) {
    return static_cast<jlong>(DpTrap::DpMemoryTrapSystem::GetInstance().GetCbMemorySize());
}

JNIEXPORT jint JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeGetCaTrapCount(JNIEnv *env, jobject thiz) {
    return static_cast<jint>(DpTrap::DpMemoryTrapSystem::GetInstance().GetCaTrapCount());
}

JNIEXPORT jint JNICALL
Java_com_sy_newfwg_DpMemoryTrapManager_nativeGetCbTrapCount(JNIEnv *env, jobject thiz) {
    return static_cast<jint>(DpTrap::DpMemoryTrapSystem::GetInstance().GetCbTrapCount());
}

}
