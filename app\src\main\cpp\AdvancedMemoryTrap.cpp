#include "AdvancedMemoryTrap.h"
#include <sys/mman.h>
#include <signal.h>
#include <unistd.h>
#include <cstring>
#include <algorithm>
#include <cmath>
#include <sys/syscall.h>
#include <fstream>
#include <thread>
#include <ctime>
#include <malloc.h>

// 外部Cb陷阱库函数声明
extern "C" {
    void initialize_cb_traps();
    void* get_cb_trap_address();
    size_t get_cb_trap_size();
}

namespace {
    // dp方案：Ca区域自定义分配器
    class CAAllocator {
    public:
        static constexpr uint32_t MAGIC_HEADER = 0xACEACE;

        struct TrapHeader {
            uint32_t magic;
            size_t allocSize;
            uint32_t checksum;
            uint32_t trapType;
        };

        static void* Allocate(size_t size) {
            // 计算总大小（添加陷阱头部）
            size_t totalSize = sizeof(TrapHeader) + size;

            // 尝试在堆区高地址分配
            void* baseAddr = MapHighAddressMemory(totalSize);
            if (!baseAddr) {
                // 备用分配策略
                baseAddr = mmap(nullptr, totalSize, PROT_READ | PROT_WRITE,
                               MAP_ANONYMOUS | MAP_PRIVATE, -1, 0);
            }

            if (baseAddr == MAP_FAILED) return nullptr;

            // 设置陷阱头部
            TrapHeader* header = static_cast<TrapHeader*>(baseAddr);
            header->magic = MAGIC_HEADER;
            header->allocSize = size;
            header->trapType = 1; // 简化的陷阱类型

            // 填充诱饵数据
            void* userAddr = static_cast<char*>(baseAddr) + sizeof(TrapHeader);
            FillDecoyValues(userAddr, size);

            // 计算简化的校验和
            header->checksum = CalculateSimpleChecksum(userAddr, size);

            return userAddr;
        }

    private:
        static void* MapHighAddressMemory(size_t size) {
            // 尝试在0x70000000-0x80000000范围内分配
            const uintptr_t baseRange = 0x70000000;
            const uintptr_t rangeSize = 0x10000000;

            for (int i = 0; i < 5; i++) { // 最多尝试5次
                uintptr_t targetAddr = baseRange + ((i * 0x100000) % rangeSize);
                targetAddr = targetAddr & ~(4096 - 1); // 页对齐

                void* addr = mmap(reinterpret_cast<void*>(targetAddr), size,
                                 PROT_READ | PROT_WRITE,
                                 MAP_ANONYMOUS | MAP_PRIVATE | MAP_FIXED, -1, 0);

                if (addr != MAP_FAILED) return addr;
            }
            return nullptr;
        }

        static void FillDecoyValues(void* addr, size_t size) {
            // 常见游戏数值作为诱饵
            static const int decoyValues[] = {
                100, 500, 1000, 5000, 9999, 10000, 50000, 100000, 999999
            };

            size_t numInts = size / sizeof(int);
            int* data = static_cast<int*>(addr);

            for (size_t i = 0; i < numInts; i++) {
                data[i] = decoyValues[i % 9];

                // 10%概率创建指针链 (修复64位指针问题)
                if (i > 0 && (i % 10) == 0) {
                    data[i] = static_cast<int>(reinterpret_cast<uintptr_t>(&data[i-1]) & 0xFFFFFFFF);
                }
            }
        }

        static uint32_t CalculateSimpleChecksum(void* addr, size_t size) {
            uint32_t checksum = 0;
            uint8_t* data = static_cast<uint8_t*>(addr);
            for (size_t i = 0; i < size; i++) {
                checksum ^= data[i];
            }
            return checksum;
        }
    };

    // dp方案：Cb区域BSS段陷阱
    __attribute__((used, section(".bss.ace_trap")))
    static volatile uint8_t g_bssTrapArea[512 * 1024]; // 512KB BSS陷阱区

    __attribute__((used, section(".bss.ace_trap")))
    static volatile int g_bssTrapArray[27136]; // 108KB BSS陷阱数组

    // Cb分配器状态
    static size_t cb_current_offset = 0;
    static std::mutex cb_alloc_mutex;

    // Ca分配器状态
    static std::vector<void*> g_caTrapKeepAlive;
    static std::mutex ca_alloc_mutex;

    // 兼容性：为旧代码保留global_cb_reserve引用
    static uint8_t* global_cb_reserve = const_cast<uint8_t*>(g_bssTrapArea);
}

namespace AdvancedAntiCheat {

// 静态成员初始化
    struct sigaction MemoryTrapManager::oldSigsegvAction_;
    struct sigaction MemoryTrapManager::oldSigbusAction_;
    MemoryTrapManager *MemoryTrapManager::instance_ = nullptr;

// 统一陷阱分配器 - 确保陷阱分配到正确的内存区域
    class TrapAllocator {
    public:
        template<typename T>
        static T *allocateInRegion(MemoryRegion region, const T &value) {
            T *ptr = nullptr;

            switch (region) {
                case MemoryRegion::CA_CPP_ALLOC:
                    // 使用大块分配确保在Ca区域显示
                    ptr = allocateInCppAlloc<T>(value);
                    break;
                case MemoryRegion::CD_DATA_SEGMENT:
                    // 使用静态数据段
                    ptr = allocateInDataSegment<T>(value);
                    break;
                case MemoryRegion::CB_CPP_BSS:
                    // 使用静态BSS段
                    ptr = allocateInBssSegment<T>(value);
                    break;
                case MemoryRegion::JH_JAVA_HEAP:
                    // 使用Java堆风格分配
                    ptr = allocateInJavaHeap<T>(value);
                    break;
                    ptr = static_cast<T *>(malloc(sizeof(T)));
                    if (ptr) new(ptr) T(value);
                    break;
            }

            if (ptr) {
                LOGI("🎯 [陷阱分配器] 成功分配: 区域=%d, 地址=%p, 值=%d",
                     static_cast<int>(region), ptr, static_cast<int>(value));
            }

            return ptr;
        }

    private:
        // Ca区域Android专属分配器头部结构
        struct CaHeader {
            uint32_t magic = 0xCAFECAFE; // Ca区域魔术字
            size_t allocSize;
            uint32_t checksum;
        };

        // 豆包方案：专用Ca分配器，强制分配到特定地址范围
        static void* allocateCaRegion(size_t size) {
            LOGI("🎯 [豆包Ca分配器] 在0x70000000-0x80000000范围强制分配");

            // 计算总大小（包含头部）
            size_t totalSize = size + sizeof(CaHeader);

            // 尝试在Ca典型地址范围内分配（0x70000000 - 0x80000000）
            for (uintptr_t addr = 0x70000000; addr < 0x80000000; addr += 0x10000) {
                void* ptr = mmap(reinterpret_cast<void*>(addr), totalSize,
                                PROT_READ | PROT_WRITE,
                                MAP_PRIVATE | MAP_ANONYMOUS | MAP_FIXED,
                                -1, 0);

                if (ptr != MAP_FAILED) {
                    // 填充头部信息
                    CaHeader* header = static_cast<CaHeader*>(ptr);
                    header->magic = 0xCAFECAFE;
                    header->allocSize = size;
                    header->checksum = 0xDEADBEEF;

                    // 计算校验和
                    uint8_t* dataPtr = reinterpret_cast<uint8_t*>(ptr) + sizeof(CaHeader);
                    header->checksum = 0;
                    for (size_t i = 0; i < size; i++) {
                        header->checksum ^= dataPtr[i];
                    }

                    // 内存屏障防止编译器优化
                    __asm__ __volatile__("" ::: "memory");

                    LOGI("📦 [豆包Ca分配器] 成功分配: 地址=%p, 大小=%zu, 总大小=%zu",
                         dataPtr, size, totalSize);
                    return dataPtr;
                }

                // 如果当前地址不可用，尝试下一个64KB边界
                if (errno != EINVAL && errno != EEXIST) {
                    break;
                }
            }

            // 如果特定范围分配失败，使用标准分配作为后备
            LOGW("⚠️  Ca特定范围分配失败，使用标准分配");
            void* ptr = malloc(totalSize);
            if (ptr) {
                CaHeader* header = static_cast<CaHeader*>(ptr);
                header->magic = 0xCAFECAFE;
                header->allocSize = size;
                header->checksum = 0xDEADBEEF;

                void* dataPtr = reinterpret_cast<char*>(ptr) + sizeof(CaHeader);
                LOGI("📦 [豆包Ca后备] 标准分配成功: 地址=%p", dataPtr);
                return dataPtr;
            }

            LOGE("❌ Ca区域分配完全失败");
            return nullptr;
        }

        // 豆包方案：专用Cb分配器，确保分配在.bss段
        static void* allocateCbRegion(size_t size) {
            LOGI("🎯 [豆包Cb分配器] 从.bss段预留区域分配");

            std::lock_guard<std::mutex> lock(cb_alloc_mutex);

            const size_t CB_REGION_SIZE = sizeof(global_cb_reserve);

            // 检查空间是否足够
            if (cb_current_offset + size > CB_REGION_SIZE) {
                LOGE("❌ Cb区域内存不足: 需要%zu, 剩余%zu",
                     size, CB_REGION_SIZE - cb_current_offset);
                return nullptr;
            }

            // 分配内存
            void* ptr = &global_cb_reserve[cb_current_offset];
            cb_current_offset += size;

            // 填充非零值防止被优化
            memset(ptr, 0xCB, size);

            // 内存屏障确保写入生效
            __asm__ __volatile__("" ::: "memory");

            // 验证是否真的在.bss段
            if (isInBssSection(ptr)) {
                LOGI("📦 [豆包Cb分配器] 成功分配: 地址=%p, 大小=%zu, 偏移=%zu",
                     ptr, size, cb_current_offset);
            } else {
                LOGW("⚠️  Cb分配警告: 内存不在.bss段");
            }

            return ptr;
        }

        // 验证内存是否在.bss段
        static bool isInBssSection(void* ptr) {
            std::ifstream maps("/proc/self/maps");
            std::string line;
            uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);

            while (std::getline(maps, line)) {
                uintptr_t start, end;
                char perms[5];

                if (sscanf(line.c_str(), "%lx-%lx %4s", &start, &end, perms) >= 3) {
                    // 检查地址是否在这个范围内
                    if (addr >= start && addr < end) {
                        // 检查是否是.bss段或匿名可写段
                        if (line.find(".bss") != std::string::npos ||
                            (strstr(perms, "rw") && line.find("00:00 0") != std::string::npos)) {
                            return true;
                        }
                    }
                }
            }

            return false;
        }

        template<typename T>
        static T *allocateInCppAlloc(const T &value) {
            // 使用Android专属分配器
            void* trapAddr = allocateCaRegion(sizeof(T));
            if (!trapAddr) {
                LOGE("❌ Ca区域Android分配器分配失败");
                return nullptr;
            }

            T* trapPtr = static_cast<T*>(trapAddr);
            *trapPtr = value;

            // 添加内存屏障确保写入生效
            __asm__ __volatile__("" ::: "memory");

            LOGI("📦 [Ca分配器] Android专属分配成功: 地址=%p, 值=%d", trapPtr, static_cast<int>(value));
            return trapPtr;
        }

        template<typename T>
        static T *allocateInDataSegment(const T &value) {
            // 使用静态数据段数组
            extern int data_trap_array[];
            static size_t dataIndex = 1000; // 跳过预设值

            if (dataIndex >= 81920) {
                LOGW("⚠️  [Cd分配器] 数据段空间不足");
                return nullptr;
            }

            T *ptr = reinterpret_cast<T *>(&data_trap_array[dataIndex]);
            *ptr = value;
            dataIndex += (sizeof(T) / sizeof(int)) + 1;

            return ptr;
        }

        template<typename T>
        static T *allocateInBssSegment(const T &value) {
            // 使用静态BSS段数组
            extern int bss_trap_array[];
            static size_t bssIndex = 0;

            if (bssIndex >= 27136) {
                LOGW("⚠️  [Cb分配器] BSS段空间不足");
                return nullptr;
            }

            T *ptr = reinterpret_cast<T *>(&bss_trap_array[bssIndex]);
            *ptr = value;
            bssIndex += (sizeof(T) / sizeof(int)) + 1;

            return ptr;
        }

        template<typename T>
        static T *allocateInJavaHeap(const T &value) {
            // 使用Java堆风格的大块分配
            static size_t javaAllocOffset = 0;
            static void *javaHeapBlock = nullptr;

            if (!javaHeapBlock) {
                javaHeapBlock = malloc(1024 * 1024); // 1MB Java堆块
                LOGI("📦 [Jh分配器] 创建1MB Java堆块: %p", javaHeapBlock);
            }

            if (javaAllocOffset + sizeof(T) > 1024 * 1024) {
                LOGW("⚠️  [Jh分配器] Java堆空间不足，重置偏移");
                javaAllocOffset = 0;
            }

            T *ptr = reinterpret_cast<T *>(static_cast<char *>(javaHeapBlock) + javaAllocOffset);
            new(ptr) T(value);
            javaAllocOffset += sizeof(T) + 32; // Java对象对齐

            return ptr;
        }
    };

// ============================================================================
// BehaviorAnalyzer 实现
// ============================================================================

    BehaviorAnalyzer::BehaviorAnalyzer() : gen_(rd_()) {
        loadCheatSignatures();
        analysisActive_ = true;
        analysisThread_ = std::thread(&BehaviorAnalyzer::performRealTimeAnalysis, this);
    }

    BehaviorAnalyzer::~BehaviorAnalyzer() {
        analysisActive_ = false;
        if (analysisThread_.joinable()) {
            analysisThread_.join();
        }
    }

    void BehaviorAnalyzer::loadCheatSignatures() {
        // 加载已知GG修改器特征
        cheatSignatures_ = {
                {"GG Memory Search",   {0x2D, 0xE9, 0x00, 0x48, 0x80, 0xB0},
                                           {"com.guardian", "gg", "gameguardian"}, 90, 0x12345678},
                {"GG Value Scanner",   {0x0F, 0xB4, 0x2D, 0xE9, 0x00, 0x4C},
                                           {},                                     85, 0x87654321},
                {"GG Speed Hack",      {0x30, 0xB5, 0x92, 0xB0, 0x6F, 0x46},
                                           {},                                     80, 0xABCDEF00},
                {"Linear Memory Scan", {}, {},                                     70, 0x11111111},
                {"Pointer Chain Scan", {}, {},                                     75, 0x22222222}
        };
    }

    void BehaviorAnalyzer::recordAccess(const AccessRecord &record) {
        std::lock_guard<std::mutex> lock(accessMutex_);

        recentAccesses_.push_back(record);
        accessHistory_.push(record);

        // 保持最近1000次访问记录
        if (recentAccesses_.size() > 1000) {
            recentAccesses_.erase(recentAccesses_.begin());
        }

        // 保持最近5000次历史记录
        if (accessHistory_.size() > 5000) {
            accessHistory_.pop();
        }
    }

    void BehaviorAnalyzer::performRealTimeAnalysis() {
        while (analysisActive_) {
            analyzePatterns();
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
    }

    void BehaviorAnalyzer::analyzePatterns() {
        std::lock_guard<std::mutex> lock(accessMutex_);

        if (recentAccesses_.empty()) {
            return;
        }

        // 计算访问频率
        auto now = std::chrono::steady_clock::now();
        auto oneSecondAgo = now - std::chrono::seconds(1);

        int recentCount = 0;
        for (const auto &access: recentAccesses_) {
            if (access.timestamp > oneSecondAgo) {
                recentCount++;
            }
        }

        currentModel_.avgAccessFrequency = recentCount;

        // 计算访问熵值
        calculateAccessEntropy();

        // 更新区域分布
        std::array<int, 6> regionCounts = {0};
        for (const auto &access: recentAccesses_) {
            regionCounts[static_cast<int>(access.region)]++;
        }

        for (int i = 0; i < 6; i++) {
            currentModel_.regionDistribution[i] =
                    static_cast<double>(regionCounts[i]) / recentAccesses_.size();
        }

        // 检测威胁
        ThreatLevel newThreat = ThreatLevel::NONE;
        std::string reason;
        std::vector<std::string> evidence;

        // 检测线性扫描
        if (detectLinearScan()) {
            newThreat = std::max(newThreat, ThreatLevel::MEDIUM);
            reason = "检测到线性内存扫描模式";
            evidence.push_back("连续地址访问模式");
        }

        // 检测指针追踪
        if (detectPointerChasing()) {
            newThreat = std::max(newThreat, ThreatLevel::HIGH);
            reason = "检测到指针链追踪行为";
            evidence.push_back("多级指针解引用");
        }

        // 检测频率异常
        if (detectFrequencyAnomaly()) {
            newThreat = std::max(newThreat, ThreatLevel::LOW);
            if (reason.empty()) reason = "内存访问频率异常";
            evidence.push_back("访问频率超出正常范围");
        }

        // 检测作弊工具签名
        if (detectCheatToolSignatures()) {
            newThreat = ThreatLevel::CRITICAL;
            reason = "检测到已知作弊工具特征";
            evidence.push_back("匹配GG修改器签名");
        }

        // 更新威胁级别
        currentThreat_ = newThreat;
        lastThreatReason_ = reason;
        lastEvidence_ = evidence;
    }

    void BehaviorAnalyzer::calculateAccessEntropy() {
        if (recentAccesses_.empty()) {
            currentModel_.accessEntropy = 0.0;
            return;
        }

        // 计算地址访问的熵值
        std::map<uintptr_t, int> addressCounts;
        for (const auto &access: recentAccesses_) {
            uintptr_t addr = reinterpret_cast<uintptr_t>(access.address);
            // 按4KB页面对齐计算
            addr = addr & ~0xFFF;
            addressCounts[addr]++;
        }

        double entropy = 0.0;
        int totalAccesses = recentAccesses_.size();

        for (const auto &pair: addressCounts) {
            double probability = static_cast<double>(pair.second) / totalAccesses;
            if (probability > 0) {
                entropy -= probability * std::log2(probability);
            }
        }

        currentModel_.accessEntropy = entropy;
    }

    bool BehaviorAnalyzer::detectLinearScan() const {
        if (recentAccesses_.size() < 10) return false;

        // 检查最近10次访问是否呈线性模式
        std::vector<uintptr_t> addresses;
        for (size_t i = recentAccesses_.size() - 10; i < recentAccesses_.size(); i++) {
            addresses.push_back(reinterpret_cast<uintptr_t>(recentAccesses_[i].address));
        }

        std::sort(addresses.begin(), addresses.end());

        // 检查地址间隔是否相对均匀
        std::vector<uintptr_t> intervals;
        for (size_t i = 1; i < addresses.size(); i++) {
            intervals.push_back(addresses[i] - addresses[i - 1]);
        }

        // 计算间隔的标准差
        double mean = 0.0;
        for (auto interval: intervals) {
            mean += interval;
        }
        mean /= intervals.size();

        double variance = 0.0;
        for (auto interval: intervals) {
            variance += (interval - mean) * (interval - mean);
        }
        variance /= intervals.size();

        double stddev = std::sqrt(variance);

        // 如果标准差相对较小，可能是线性扫描
        return (stddev / mean) < 0.5 && mean > 0 && mean < 1024;
    }

    bool BehaviorAnalyzer::detectPointerChasing() const {
        if (recentAccesses_.size() < 5) return false;

        // 检查是否有指针解引用模式
        for (size_t i = 0; i < recentAccesses_.size() - 4; i++) {
            const auto &access1 = recentAccesses_[i];
            const auto &access2 = recentAccesses_[i + 1];
            const auto &access3 = recentAccesses_[i + 2];

            // 检查时间间隔很短的连续访问
            auto interval1 = access2.timestamp - access1.timestamp;
            auto interval2 = access3.timestamp - access2.timestamp;

            if (interval1 < std::chrono::milliseconds(10) &&
                interval2 < std::chrono::milliseconds(10)) {

                // 检查地址是否可能是指针解引用
                uintptr_t addr1 = reinterpret_cast<uintptr_t>(access1.address);
                uintptr_t addr2 = reinterpret_cast<uintptr_t>(access2.address);
                uintptr_t addr3 = reinterpret_cast<uintptr_t>(access3.address);

                // 简单的指针链检测：地址差值在合理范围内
                if (std::abs(static_cast<long>(addr2 - addr1)) < 0x1000 &&
                    std::abs(static_cast<long>(addr3 - addr2)) < 0x1000) {
                    return true;
                }
            }
        }

        return false;
    }

    bool BehaviorAnalyzer::detectRandomAccess() const {
        // 基于熵值判断是否为随机访问
        return currentModel_.accessEntropy > 8.0;
    }

    bool BehaviorAnalyzer::detectFrequencyAnomaly() const {
        // 正常游戏的内存访问频率通常在1-100次/秒
        return currentModel_.avgAccessFrequency > 200.0 ||
               (currentModel_.avgAccessFrequency > 50.0 && currentModel_.accessEntropy < 2.0);
    }

    bool BehaviorAnalyzer::detectCheatToolSignatures() const {
        for (const auto &access: recentAccesses_) {
            if (matchesCheatSignature(access)) {
                return true;
            }
        }
        return false;
    }

    bool BehaviorAnalyzer::matchesCheatSignature(const AccessRecord &record) const {
        for (const auto &signature: cheatSignatures_) {
            // 检查访问模式是否匹配
            if (signature.accessPattern != 0 &&
                record.accessPattern == signature.accessPattern) {
                return true;
            }

            // 这里可以添加更复杂的模式匹配逻辑
            // 比如检查操作码模式、进程名等
        }
        return false;
    }

    ThreatLevel BehaviorAnalyzer::getCurrentThreatLevel() const {
        return currentThreat_.load();
    }

    BehaviorAnalyzer::AnalysisResult BehaviorAnalyzer::getAnalysisResult() const {
        std::lock_guard<std::mutex> lock(accessMutex_);

        AnalysisResult result;
        result.threatLevel = currentThreat_.load();
        result.primaryReason = lastThreatReason_;
        result.evidence = lastEvidence_;
        result.accessFrequency = currentModel_.avgAccessFrequency;
        result.accessEntropy = currentModel_.accessEntropy;

        for (int i = 0; i < 6; i++) {
            result.regionDistribution[static_cast<MemoryRegion>(i)] =
                    currentModel_.regionDistribution[i];
        }

        return result;
    }

// ============================================================================
// MemoryTrapManager 实现
// ============================================================================

    MemoryTrapManager &MemoryTrapManager::getInstance() {
        static MemoryTrapManager instance;
        return instance;
    }

    MemoryTrapManager::MemoryTrapManager() : gen_(rd_()), dist_(0, 100) {
        instance_ = this;
    }

    MemoryTrapManager::~MemoryTrapManager() {
        shutdown();
    }

    bool MemoryTrapManager::initialize() {
        if (initialized_.load()) {
            return true;
        }

        LOGI("🚀 初始化高级内存陷阱系统...");

        // 创建行为分析器
        behaviorAnalyzer_ = std::make_unique<BehaviorAnalyzer>();

        // 安装信号处理器
        installSignalHandler();

        // 设置默认陷阱
        setupDefaultTraps();

        // 启动更新线程
        updateActive_ = true;
        updateThread_ = std::thread(&MemoryTrapManager::updateThreadFunc, this);

        // 启动主动检测
        startActiveDetection();

        // 豆包方案：预分配Ca和Cb区域确保内存增长
        setupCaRegionPreallocation();
        setupCbRegionPreallocation();

        initialized_ = true;
        LOGI("✅ 高级内存陷阱系统初始化完成");

        // 运行内存区域诊断 (关键诊断)
        dumpMemoryRegions();

        return true;
    }

    // dp方案：Ca区域预分配
    void MemoryTrapManager::setupCaRegionPreallocation() {
        LOGI("🎯 [dp预分配] 设置Ca区域预分配...");

        std::lock_guard<std::mutex> lock(ca_alloc_mutex);

        // 预分配多种类型的CA陷阱 - dp方案
        const size_t trapSizes[] = {16, 32, 64, 128, 256, 512, 1024, 2048, 4096};

        for (size_t size : trapSizes) {
            for (int i = 0; i < 10; i++) { // 每种大小分配10个
                void* ptr = CAAllocator::Allocate(size);
                if (ptr) {
                    // 保持引用防止被释放
                    g_caTrapKeepAlive.push_back(ptr);

                    // 验证地址范围
                    uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);
                    if (addr >= 0x70000000 && addr < 0x80000000) {
                        LOGI("✅ dp-Ca陷阱预分配: 地址=%p, 大小=%zu (目标范围)", ptr, size);
                    } else {
                        LOGI("✅ dp-Ca陷阱预分配: 地址=%p, 大小=%zu (标准分配)", ptr, size);
                    }
                }
            }
        }

        // 额外分配大块内存确保Ca区域可见
        for (int i = 0; i < 5; i++) {
            void* bigBlock = CAAllocator::Allocate(1024 * 1024); // 1MB块
            if (bigBlock) {
                g_caTrapKeepAlive.push_back(bigBlock);
                LOGI("✅ dp-Ca大块预分配: 地址=%p, 大小=1MB", bigBlock);
            }
        }

        LOGI("✅ dp-Ca区域预分配完成: 总计%zu个陷阱", g_caTrapKeepAlive.size());
    }

    // dp方案：Cb区域预分配
    void MemoryTrapManager::setupCbRegionPreallocation() {
        LOGI("🎯 [dp预分配] 设置Cb区域预分配...");

        // 1. 填充特定内存模式 - dp方案
        const uint32_t pattern[] = {0xDEADBEEF, 0xCAFEBABE, 0xBADDF00D, 0xFACEB00C};
        const size_t patternSize = sizeof(pattern);

        // 填充主陷阱区
        for (size_t i = 0; i < sizeof(g_bssTrapArea); i += patternSize) {
            memcpy(const_cast<uint8_t*>(&g_bssTrapArea[i]), pattern,
                  std::min(patternSize, sizeof(g_bssTrapArea) - i));
        }

        // 填充陷阱数组
        for (int i = 0; i < 27136; i += 4) {
            memcpy(const_cast<int*>(&g_bssTrapArray[i]), pattern,
                  std::min(patternSize, (27136 - i) * sizeof(int)));
        }

        // 2. 强制访问BSS段确保物理分配
        volatile uint8_t dummy1 = g_bssTrapArea[0];
        volatile int dummy2 = g_bssTrapArray[0];
        (void)dummy1; (void)dummy2; // 避免编译器警告

        // 3. 强制初始化整个BSS段
        for (size_t i = 0; i < sizeof(g_bssTrapArea); i += 4096) {
            const_cast<uint8_t*>(g_bssTrapArea)[i] = static_cast<uint8_t>(0xCB + (i % 256));
        }

        for (int i = 0; i < 27136; i += 1024) {
            const_cast<int*>(g_bssTrapArray)[i] = static_cast<int>(0xDEADBEEF) + i;
        }

        LOGI("✅ dp-Cb区域预分配成功:");
        LOGI("   - g_bssTrapArea: 地址=%p, 大小=512KB",
             const_cast<uint8_t*>(g_bssTrapArea));
        LOGI("   - g_bssTrapArray: 地址=%p, 大小=108KB",
             const_cast<int*>(g_bssTrapArray));
        LOGI("   - 总计: 620KB BSS段内存");
    }

    void MemoryTrapManager::shutdown() {
        if (!initialized_.load()) {
            return;
        }

        LOGI("🔄 关闭高级内存陷阱系统...");

        // 停止主动检测
        stopActiveDetection();

        // 停止更新线程
        updateActive_ = false;
        if (updateThread_.joinable()) {
            updateThread_.join();
        }

        // 卸载信号处理器
        uninstallSignalHandler();

        // 清理陷阱
        {
            std::lock_guard<std::mutex> lock(trapsMutex_);
            traps_.clear();
        }

        // 销毁行为分析器
        behaviorAnalyzer_.reset();

        initialized_ = false;
        LOGI("✅ 高级内存陷阱系统已关闭");
    }

    void MemoryTrapManager::installSignalHandler() {
        // 临时禁用信号处理器，防止死循环
        LOGI("⚠️ 信号处理器已临时禁用，防止死循环");
        return;

        struct sigaction sa;
        sa.sa_sigaction = signalHandler;
        sigemptyset(&sa.sa_mask);
        sa.sa_flags = SA_SIGINFO | SA_RESTART;

        if (sigaction(SIGSEGV, &sa, &oldSigsegvAction_) != 0) {
            LOGE("❌ 安装SIGSEGV处理器失败");
        }

        if (sigaction(SIGBUS, &sa, &oldSigbusAction_) != 0) {
            LOGE("❌ 安装SIGBUS处理器失败");
        }

        LOGI("✅ 信号处理器安装完成");
    }

    void MemoryTrapManager::uninstallSignalHandler() {
        sigaction(SIGSEGV, &oldSigsegvAction_, nullptr);
        sigaction(SIGBUS, &oldSigbusAction_, nullptr);
        LOGI("✅ 信号处理器已卸载");
    }

    void MemoryTrapManager::signalHandler(int sig, siginfo_t *info, void *context) {
        static std::atomic<bool> handling{false};

        // 防止递归调用
        if (handling.exchange(true)) {
            return;
        }

        if (instance_ && info && info->si_addr) {
            instance_->handleMemoryAccess(info->si_addr, sig == SIGSEGV);
        }

        handling = false;

        // 调用原始处理器
        if (sig == SIGSEGV && oldSigsegvAction_.sa_sigaction) {
            oldSigsegvAction_.sa_sigaction(sig, info, context);
        } else if (sig == SIGBUS && oldSigbusAction_.sa_sigaction) {
            oldSigbusAction_.sa_sigaction(sig, info, context);
        }
    }

    void MemoryTrapManager::handleMemoryAccess(void *address, bool isWrite) {
        std::lock_guard<std::mutex> lock(trapsMutex_);

        // 限制日志输出频率，防止日志洪水
        static std::atomic<int> logCount{0};
        static std::atomic<uintptr_t> lastAddr{0};

        uintptr_t currentAddr = reinterpret_cast<uintptr_t>(address);
        if (lastAddr != currentAddr && logCount.fetch_add(1) < 5) {
            LOGI("🚨 [内存访问] 检测到内存访问: 地址=%p, 写入=%s", address, isWrite ? "是" : "否");
            lastAddr = currentAddr;
        }

        // 查找匹配的陷阱
        for (auto &trap: traps_) {
            if (!trap.isActive) continue;

            uintptr_t trapStart = reinterpret_cast<uintptr_t>(trap.address);
            uintptr_t trapEnd = trapStart + trap.size;
            uintptr_t accessAddr = reinterpret_cast<uintptr_t>(address);

            if (accessAddr >= trapStart && accessAddr < trapEnd) {
                // 陷阱触发！
                trap.accessCount++;
                trap.lastAccess = std::chrono::steady_clock::now();

                const char *trapTypeStr = getTrapTypeString(trap.type);
                const char *regionStr = getRegionString(trap.region);

                LOGI("🎯 [陷阱触发] %s陷阱被触发! 区域=%s, 地址=0x%p, 访问次数=%d",
                     trapTypeStr, regionStr, trap.address, trap.accessCount);

                // 记录访问
                AccessRecord record;
                record.address = address;
                record.size = trap.size;
                record.timestamp = std::chrono::steady_clock::now();
                record.pid = getpid();
                record.tid = syscall(SYS_gettid);
                record.region = trap.region;
                record.isWrite = isWrite;
                record.accessPattern = 0; // 可以根据具体情况设置

                // 发送给行为分析器
                LOGI("🧠 [行为分析] 发送访问记录到行为分析器: PID=%d, TID=%d", record.pid,
                     (int) record.tid);
                if (behaviorAnalyzer_) {
                    behaviorAnalyzer_->recordAccess(record);

                    // 简单的威胁评估 - 基于访问频率
                    float threatLevel = std::min(1.0f, trap.accessCount / 10.0f);
                    LOGI("⚠️ [威胁评估] 当前威胁级别: %.1f%% (基于访问次数: %d)",
                         threatLevel * 100.0f, trap.accessCount);

                    if (threatLevel > 0.5f) {
                        LOGW("🚨 [高威胁] 检测到可疑行为! 威胁级别: %.1f%%", threatLevel * 100.0f);
                    }
                }

                // 更新统计
                {
                    std::lock_guard<std::mutex> statsLock(statsMutex_);
                    stats_.totalTriggers++;
                    stats_.triggersByType[trap.type]++;
                    stats_.triggersByRegion[trap.region]++;

                    LOGI("📊 [统计更新] 总触发次数: %d, 当前陷阱类型触发: %d",
                         stats_.totalTriggers, stats_.triggersByType[trap.type]);
                }

                // 调用回调
                if (trap.triggerCallback) {
                    LOGI("📞 [回调] 调用陷阱回调函数");
                    trap.triggerCallback(address, record);
                }

                LOGW("🚨 [陷阱总结] 陷阱触发完成! 地址=0x%p, 类型=%s, 区域=%s",
                     address, trapTypeStr, regionStr);

                break;
            }
        }
    }

    void MemoryTrapManager::setupDefaultTraps() {
        LOGI("🔧 设置默认陷阱配置...");
        LOGI("📊 [ACESDK策略] 基于腾讯ACESDK测试结果调整陷阱分布");

        // 首先创建一些特殊的测试陷阱，使用常见的修改器搜索值
        createSpecialTestTraps();

        // 根据ACESDK测试结果，重点关注以下区域：
        // Ca: C++ alloc, Cd: C++ .data, Cb: C++ .bss, A: Anonymous
        int totalTraps = 50; // 增加陷阱数量以提高检测率

        // 按照ACESDK实际测试结果分配陷阱（模拟真实大小比例）
        // Cd(.data): 315kB, Cb(.bss): 106kB, A(Anonymous): 400kB, Ca(alloc): 8.19kB
        // 总计约: 829kB，按比例分配陷阱
        int cdTraps = static_cast<int>(totalTraps * 0.38);  // 38% - C++ .data段（最大315kB）
        int aTraps = static_cast<int>(totalTraps * 0.32);   // 32% - Anonymous（增加400kB）
        int cbTraps = static_cast<int>(totalTraps * 0.20);  // 20% - C++ .bss段（106kB）
        int caTraps = static_cast<int>(totalTraps * 0.10);  // 10% - C++ alloc（8.19kB）

        LOGI("🎯 [陷阱分布] A=%d, Cd=%d, Cb=%d, Ca=%d (总计=%d)",
             aTraps, cdTraps, cbTraps, caTraps, totalTraps);

        deployAnonymousTraps(aTraps);      // A: Anonymous 区域
        deployDataSegmentTraps(cdTraps);   // Cd: C++ .data 区域
        deployCppBssTraps(cbTraps);        // Cb: C++ .bss 区域
        deployCppAllocTraps(caTraps);      // Ca: C++ alloc 区域

        LOGI("✅ ACESDK策略陷阱部署完成: A=%d, Cd=%d, Cb=%d, Ca=%d",
             aTraps, cdTraps, cbTraps, caTraps);
    }

    void MemoryTrapManager::createSpecialTestTraps() {
        LOGI("🎯 [特殊陷阱] 创建测试专用陷阱...");

        // 创建一系列常见的修改器搜索值
        std::vector<int> commonValues = {
                12345,    // 用户刚才搜索的值
                100, 200, 500, 1000,  // 常见游戏数值
                999, 9999, 99999,     // 常见最大值
                1, 10, 50,            // 小数值
                666, 888, 777         // 特殊数值
        };

        for (int value: commonValues) {
            // 在不同内存区域创建相同值的陷阱

            // 1. C++堆陷阱
            auto chTrap = createValueDecoy(MemoryRegion::CH_CPP_HEAP, value);
            chTrap.triggerCallback = [this, value](void *addr, const AccessRecord &record) {
                LOGW("🚨 [特殊陷阱触发] C++堆测试陷阱被触发! 值=%d, 地址=0x%p", value, addr);

                ThreatEvent event;
                event.severity = ThreatLevel::HIGH;
                event.description = "测试陷阱被触发 - 检测到修改器扫描";
                event.primaryReason = "搜索常见数值: " + std::to_string(value);
                event.triggerAddress = addr;
                event.region = MemoryRegion::CH_CPP_HEAP;
                event.trapType = TrapType::VALUE_DECOY;

                if (threatCallback_) {
                    threatCallback_(event);
                }
            };
            addTrap(std::move(chTrap));

            // 2. Java堆陷阱
            auto jhTrap = createValueDecoy(MemoryRegion::JH_JAVA_HEAP, value);
            jhTrap.triggerCallback = [this, value](void *addr, const AccessRecord &record) {
                LOGW("🚨 [特殊陷阱触发] Java堆测试陷阱被触发! 值=%d, 地址=0x%p", value, addr);

                ThreatEvent event;
                event.severity = ThreatLevel::HIGH;
                event.description = "测试陷阱被触发 - 检测到修改器扫描";
                event.primaryReason = "搜索常见数值: " + std::to_string(value);
                event.triggerAddress = addr;
                event.region = MemoryRegion::JH_JAVA_HEAP;
                event.trapType = TrapType::VALUE_DECOY;

                if (threatCallback_) {
                    threatCallback_(event);
                }
            };
            addTrap(std::move(jhTrap));

            // 3. 匿名内存陷阱
            auto aTrap = createValueDecoy(MemoryRegion::A_ANONYMOUS, value);
            aTrap.triggerCallback = [this, value](void *addr, const AccessRecord &record) {
                LOGW("🚨 [特殊陷阱触发] 匿名内存测试陷阱被触发! 值=%d, 地址=0x%p", value, addr);

                ThreatEvent event;
                event.severity = ThreatLevel::CRITICAL;
                event.description = "测试陷阱被触发 - 检测到修改器扫描";
                event.primaryReason = "搜索常见数值: " + std::to_string(value);
                event.triggerAddress = addr;
                event.region = MemoryRegion::A_ANONYMOUS;
                event.trapType = TrapType::VALUE_DECOY;

                if (threatCallback_) {
                    threatCallback_(event);
                }
            };
            addTrap(std::move(aTrap));
        }

        LOGI("✅ [特殊陷阱] 测试陷阱创建完成，共创建 %zu 个值的陷阱", commonValues.size());
    }

    void MemoryTrapManager::deployChHeapTraps(int count) {
        LOGI("🔧 部署C++堆陷阱 (%d个)...", count);

        for (int i = 0; i < count; i++) {
            // 1. 诱饵值陷阱 (60%)
            if (i < count * 0.6) {
                auto trap = createValueDecoy(MemoryRegion::CH_CPP_HEAP, generateMeaningfulDecoy());
                trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::MEDIUM;
                    event.description = "C++堆诱饵值陷阱触发";
                    event.primaryReason = "检测到对诱饵数据的访问";
                    event.triggerAddress = addr;
                    event.region = MemoryRegion::CH_CPP_HEAP;
                    event.trapType = TrapType::VALUE_DECOY;

                    if (threatCallback_) {
                        threatCallback_(event);
                    }
                };
                addTrap(std::move(trap));
            }
                // 2. 指针链陷阱 (25%)
            else if (i < count * 0.85) {
                auto trap = createPointerChainTrap(MemoryRegion::CH_CPP_HEAP, 3);
                trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::HIGH;
                    event.description = "C++堆指针链陷阱触发";
                    event.primaryReason = "检测到多级指针解引用";
                    event.triggerAddress = addr;
                    event.region = MemoryRegion::CH_CPP_HEAP;
                    event.trapType = TrapType::POINTER_CHAIN;

                    if (threatCallback_) {
                        threatCallback_(event);
                    }
                };
                addTrap(std::move(trap));
            }
                // 3. CRC校验陷阱 (15%)
            else {
                // 创建一个假的玩家数据结构
                struct FakePlayerData {
                    int health = 1111;  // 临时使用固定值
                    int mana = 100;   // 临时使用固定值
                    int level = 1;
                    float experience = 0.0f;
                };

                FakePlayerData *fakeData = new FakePlayerData();
                auto trap = createCrcTrap(fakeData, sizeof(FakePlayerData));
                trap.region = MemoryRegion::CH_CPP_HEAP;
                trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::HIGH;
                    event.description = "C++堆CRC校验陷阱触发";
                    event.primaryReason = "检测到关键数据结构被修改";
                    event.triggerAddress = addr;
                    event.region = MemoryRegion::CH_CPP_HEAP;
                    event.trapType = TrapType::CRC32_TRAP;

                    if (threatCallback_) {
                        threatCallback_(event);
                    }
                };
                addTrap(std::move(trap));
            }
        }

        LOGI("✅ C++堆陷阱部署完成");
    }

    void MemoryTrapManager::deployJhJavaTraps(int count) {
        LOGI("🔧 部署Java堆陷阱 (%d个)...", count);
        LOGI("📊 [ACESDK策略] 模拟Jh区域1MB内存增长");

        // 分配1MB大块内存模拟ACESDK在Jh区域的内存增长
        size_t totalSize = 1024 * 1024; // 1MB，模拟ACESDK的Jh区域增长
        void *javaHeapBlock = malloc(totalSize);

        if (javaHeapBlock) {
            LOGI("✅ 成功分配Jh区域大块内存: %zu bytes", totalSize);

            // 常见的Java层游戏数值
            const int javaGameValues[] = {
                    // 血量相关
                    100, 150, 200, 250, 300, 500, 1000,
                    // 金币相关
                    1000, 5000, 10000, 50000, 100000, 999999,
                    // 经验值相关
                    500, 1000, 2500, 5000, 10000, 25000,
                    // 等级相关
                    1, 5, 10, 20, 50, 100,
                    // 常见修改器搜索值
                    12345, 67890, 11111, 22222, 33333
            };

            // 在大块内存中创建多个陷阱点
            size_t trapSize = 1024; // 每个陷阱1KB
            int maxTraps = totalSize / trapSize;
            if (count > maxTraps) count = maxTraps;

            for (int i = 0; i < count; i++) {
                void *trapAddr = static_cast<char *>(javaHeapBlock) + (i * trapSize);

                // 设置Java风格的陷阱值
                int *intPtr = static_cast<int *>(trapAddr);
                int valueIndex = i % (sizeof(javaGameValues) / sizeof(int));
                *intPtr = javaGameValues[valueIndex];
                *(intPtr + 1) = 0xCAFEBABE; // Java堆标识 (Java字节码魔数)
                *(intPtr + 2) = i + 50000;   // 序列号
                *(intPtr + 3) = generateMeaningfulDecoy(); // 随机诱饵值

                TrapConfig trap;
                trap.address = trapAddr;
                trap.size = trapSize;
                trap.type = TrapType::VALUE_DECOY;
                trap.region = MemoryRegion::JH_JAVA_HEAP;
                trap.decoyValue = *intPtr;
                trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::CRITICAL;
                    event.description = "Jh区域大块陷阱触发";
                    event.primaryReason = "检测到对Java堆区域的内存扫描";
                    event.triggerAddress = addr;
                    event.region = MemoryRegion::JH_JAVA_HEAP;
                    event.trapType = TrapType::VALUE_DECOY;

                    if (threatCallback_) {
                        threatCallback_(event);
                    }
                };

                addTrap(std::move(trap));

                if (i < 10) { // 只记录前10个陷阱的详细信息
                    LOGI("✅ Jh陷阱已部署: 地址=%p, 值=%d", trapAddr, *intPtr);
                }
            }

            LOGI("📦 [Jh分配器] 总共部署%d个陷阱，占用1MB内存", count);
        } else {
            LOGE("❌ Jh区域1MB内存分配失败");
        }

        LOGI("✅ Java堆陷阱部署完成");
    }

    void MemoryTrapManager::deployAnonymousTraps(int count) {
        LOGI("🔧 部署匿名内存陷阱 (%d个)...", count);

        for (int i = 0; i < count; i++) {
            if (i < count * 0.5) {
                // 1. 保护页陷阱 (50%)
                auto trap = createGuardPage(4096);
                trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::CRITICAL;
                    event.description = "匿名内存保护页陷阱触发";
                    event.primaryReason = "检测到对保护页的非法访问";
                    event.triggerAddress = addr;
                    event.region = MemoryRegion::A_ANONYMOUS;
                    event.trapType = TrapType::GUARD_PAGE;

                    if (threatCallback_) {
                        threatCallback_(event);
                    }
                };
                addTrap(std::move(trap));
            } else {
                // 2. 定时访问陷阱 (50%)
                auto trap = createTimedAccessTrap(MemoryRegion::A_ANONYMOUS,
                                                  generateMeaningfulDecoy());
                trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::MEDIUM;
                    event.description = "匿名内存定时陷阱触发";
                    event.primaryReason = "检测到对定时变化数据的访问";
                    event.triggerAddress = addr;
                    event.region = MemoryRegion::A_ANONYMOUS;
                    event.trapType = TrapType::TIMED_ACCESS;

                    if (threatCallback_) {
                        threatCallback_(event);
                    }
                };
                addTrap(std::move(trap));
            }
        }

        LOGI("✅ 匿名内存陷阱部署完成");
    }

// 静态初始化数组，确保分配到.data段 (模拟ACESDK的315kB)
    static int data_trap_array[81920] = {
            // 常见修改器搜索值
            12345, 67890, 11111, 22222, 33333, 44444, 55555, 66666,
            77777, 88888, 99999, 10101, 20202, 30303, 40404, 50505,
            // 游戏相关诱饵值
            100, 200, 300, 400, 500, 600, 700, 800, 900, 1000,
            1500, 2000, 2500, 3000, 3500, 4000, 4500, 5000, 5500, 6000,
            // 金币相关
            10000, 20000, 50000, 100000, 500000, 999999,
            // 血量相关
            50, 75, 100, 150, 200, 250, 300, 500, 1000,
            // 经验值相关
            1000, 5000, 10000, 25000, 50000, 100000,
            // 其余元素会自动初始化为0，但我们会在运行时设置
    }; // 320KB的.data段陷阱数组 (接近ACESDK的315kB)

    void MemoryTrapManager::deployDataSegmentTraps(int count) {
        LOGI("🔧 部署数据段陷阱 (%d个)...", count);
        LOGI("📊 [ACESDK模拟] 使用静态初始化数组确保真正分配到.data段 (~315kB)");

        // 确保数组大小足够
        int maxTraps = sizeof(data_trap_array) / sizeof(int) / 4; // 每个陷阱4个int
        if (count > maxTraps) {
            count = maxTraps;
            LOGW("⚠️  陷阱数量超限，调整为 %d", count);
        }

        for (int i = 0; i < count; i++) {
            // 在静态数组中设置陷阱
            int *trapAddr = &data_trap_array[i * 4];

            // 设置.data段陷阱值（如果还是0的话）
            if (*trapAddr == 0) {
                *trapAddr = generateMeaningfulDecoy();
            }
            *(trapAddr + 1) = 0xDEADCAFE; // .data段特征值
            *(trapAddr + 2) = i + 40000;   // 序列号
            *(trapAddr + 3) = 0xFEEDFACE; // 结束标记

            TrapConfig trap;
            trap.address = trapAddr;
            trap.size = 16; // 4个int = 16字节
            trap.type = TrapType::VALUE_DECOY;
            trap.region = MemoryRegion::CD_DATA_SEGMENT;
            trap.decoyValue = *trapAddr;
            trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                ThreatEvent event;
                event.severity = ThreatLevel::CRITICAL; // .data段访问非常严重
                event.description = "C++ .data段静态陷阱触发";
                event.primaryReason = "检测到对.data段（静态初始化数据区）的扫描";
                event.triggerAddress = addr;
                event.region = MemoryRegion::CD_DATA_SEGMENT;
                event.trapType = TrapType::VALUE_DECOY;

                if (threatCallback_) {
                    threatCallback_(event);
                }
            };

            addTrap(std::move(trap));
        }

        LOGI("✅ 数据段陷阱部署完成，使用静态数组地址: %p", data_trap_array);
    }

    void MemoryTrapManager::deployCppAllocTraps(int count) {
        LOGI("🔧 部署C++分配区陷阱 (%d个)...", count);
        LOGI("📊 [豆包策略] 使用专用Ca分配器确保Ca区域正确映射");

        // 豆包方案：使用专用Ca分配器
        for (int i = 0; i < count; i++) {
            // 使用dp方案的CAAllocator分配内存
            void* trapAddr = CAAllocator::Allocate(sizeof(int));
            if (!trapAddr) {
                LOGW("⚠️  dp-Ca陷阱分配失败，跳过第%d个", i);
                continue;
            }

            // 生成有意义的诱饵值
            int decoyValue = generateMeaningfulDecoy();
            *static_cast<int*>(trapAddr) = decoyValue;

            TrapConfig trap;
            trap.address = trapAddr;
            trap.size = sizeof(int);
            trap.type = TrapType::VALUE_DECOY;
            trap.region = MemoryRegion::CA_CPP_ALLOC;
            trap.decoyValue = decoyValue;
            trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                ThreatEvent event;
                event.severity = ThreatLevel::CRITICAL;
                event.description = "dp-Ca区域陷阱触发";
                event.primaryReason = "检测到对dp专用Ca区域的扫描";
                event.triggerAddress = addr;
                event.region = MemoryRegion::CA_CPP_ALLOC;
                event.trapType = TrapType::VALUE_DECOY;

                if (threatCallback_) {
                    threatCallback_(event);
                }
            };

            addTrap(std::move(trap));

            // 验证分配地址范围
            uintptr_t addr = reinterpret_cast<uintptr_t>(trapAddr);
            if (addr >= 0x70000000 && addr < 0x80000000) {
                LOGI("✅ dp-Ca陷阱已部署: 地址=%p, 值=%d (目标范围)", trapAddr, decoyValue);
            } else {
                LOGI("✅ dp-Ca陷阱已部署: 地址=%p, 值=%d (标准分配)", trapAddr, decoyValue);
            }
        }

        LOGI("✅ C++分配区陷阱部署完成，共部署%d个陷阱", count);

        // 验证Ca陷阱地址范围 (关键修复)
        verifyCaTrapsAddressRange();
    }

    void MemoryTrapManager::verifyCaTrapsAddressRange() {
        LOGI("📡 验证Ca陷阱地址范围:");

        std::lock_guard<std::mutex> lock(trapsMutex_);
        for (auto& trap : traps_) {
            if (trap.region == MemoryRegion::CA_CPP_ALLOC) {
                uintptr_t addr = reinterpret_cast<uintptr_t>(trap.address);

                LOGI("🔍 Ca陷阱地址: 0x%lx, 值: %d", addr, trap.decoyValue);

                // 检查是否在合理的64位地址范围内 (修复地址范围检查)
                if (addr < 0x400000000000UL || addr > 0x800000000000UL) {
                    LOGW("⚠️  Ca陷阱地址可能异常: 0x%lx (超出64位用户空间)", addr);
                } else {
                    LOGI("✅ Ca陷阱地址正常: 0x%lx (64位用户空间)", addr);
                }

                // 验证内存是否可访问
                volatile int* testPtr = static_cast<volatile int*>(trap.address);
                try {
                    int testValue = *testPtr;
                    *testPtr = testValue; // 写回测试
                    LOGI("✅ Ca陷阱内存可读写: 值=%d", testValue);
                } catch (...) {
                    LOGE("❌ Ca陷阱内存访问失败: 0x%lx", addr);
                }
            }
        }
    }

    void MemoryTrapManager::deployNewDeleteTraps(int count) {
        LOGI("🎯 [Ca策略1] 使用new/delete分配%d个陷阱", count);

        // 常见的修改器搜索值
        const int commonValues[] = {100, 500, 1000, 5000, 9999, 12345, 67890,
                                   10000, 20000, 50000, 100000, 999999};

        for (int i = 0; i < count; i++) {
            try {
                // 使用new操作符分配 (更可能被识别为Ca区域)
                int* trapPtr = new int;
                int decoyValue = (i < static_cast<int>(sizeof(commonValues)/sizeof(int))) ?
                                commonValues[i] : generateMeaningfulDecoy();
                *trapPtr = decoyValue;

                TrapConfig trap;
                trap.address = trapPtr;
                trap.size = sizeof(int);
                trap.type = TrapType::VALUE_DECOY;
                trap.region = MemoryRegion::CA_CPP_ALLOC;
                trap.decoyValue = *trapPtr;
                trap.triggerCallback = [this](void* addr, const AccessRecord& record) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::CRITICAL;
                    event.description = "Ca区域new/delete陷阱触发";
                    event.primaryReason = "检测到对C++分配区(new)的内存扫描";
                    event.triggerAddress = addr;
                    event.region = MemoryRegion::CA_CPP_ALLOC;
                    event.trapType = TrapType::VALUE_DECOY;

                    if (threatCallback_) {
                        threatCallback_(event);
                    }
                };

                addTrap(std::move(trap));
                LOGI("✅ Ca(new)陷阱已部署: 地址=%p, 值=%d", trapPtr, *trapPtr);

            } catch (std::bad_alloc& e) {
                LOGE("❌ Ca区域new分配失败: %s", e.what());
            }
        }
    }

    void MemoryTrapManager::deployVectorTraps(int count) {
        LOGI("🎯 [Ca策略2] 使用vector动态分配%d个陷阱", count);

        // 创建多个vector，每个包含陷阱数据
        static std::vector<std::vector<int>*> trapVectors;

        for (int i = 0; i < count; i++) {
            try {
                // 创建vector (会使用堆分配)
                auto* vec = new std::vector<int>();
                vec->reserve(10); // 预分配空间

                // 填充诱饵数据
                for (int j = 0; j < 5; j++) {
                    vec->push_back(generateMeaningfulDecoy());
                }

                trapVectors.push_back(vec);

                // 注册第一个元素作为陷阱
                if (!vec->empty()) {
                    TrapConfig trap;
                    trap.address = &(*vec)[0];
                    trap.size = sizeof(int);
                    trap.type = TrapType::VALUE_DECOY;
                    trap.region = MemoryRegion::CA_CPP_ALLOC;
                    trap.decoyValue = (*vec)[0];
                    trap.triggerCallback = [this](void* addr, const AccessRecord& record) {
                        ThreatEvent event;
                        event.severity = ThreatLevel::CRITICAL;
                        event.description = "Ca区域vector陷阱触发";
                        event.primaryReason = "检测到对C++分配区(vector)的内存扫描";
                        event.triggerAddress = addr;
                        event.region = MemoryRegion::CA_CPP_ALLOC;
                        event.trapType = TrapType::VALUE_DECOY;

                        if (threatCallback_) {
                            threatCallback_(event);
                        }
                    };

                    addTrap(std::move(trap));
                    LOGI("✅ Ca(vector)陷阱已部署: 地址=%p, 值=%d", &(*vec)[0], (*vec)[0]);
                }

            } catch (std::bad_alloc& e) {
                LOGE("❌ Ca区域vector分配失败: %s", e.what());
            }
        }
    }

    void MemoryTrapManager::deployCustomAllocatorTraps(int count) {
        LOGI("🎯 [Ca策略3] 使用自定义分配器分配%d个陷阱", count);

        // 创建大块内存池，然后从中分配小块
        static void* memoryPool = nullptr;
        static size_t poolOffset = 0;
        const size_t poolSize = 1024 * 1024; // 1MB池

        if (!memoryPool) {
            memoryPool = malloc(poolSize);
            LOGI("📦 [Ca自定义分配器] 创建1MB内存池: %p", memoryPool);
        }

        for (int i = 0; i < count && poolOffset + sizeof(int) < poolSize; i++) {
            void* trapAddr = static_cast<char*>(memoryPool) + poolOffset;
            int* trapPtr = static_cast<int*>(trapAddr);
            *trapPtr = generateMeaningfulDecoy();

            poolOffset += 64; // 64字节对齐，增加被识别为Ca的可能性

            TrapConfig trap;
            trap.address = trapPtr;
            trap.size = sizeof(int);
            trap.type = TrapType::VALUE_DECOY;
            trap.region = MemoryRegion::CA_CPP_ALLOC;
            trap.decoyValue = *trapPtr;
            trap.triggerCallback = [this](void* addr, const AccessRecord& record) {
                ThreatEvent event;
                event.severity = ThreatLevel::CRITICAL;
                event.description = "Ca区域自定义分配器陷阱触发";
                event.primaryReason = "检测到对C++分配区(自定义)的内存扫描";
                event.triggerAddress = addr;
                event.region = MemoryRegion::CA_CPP_ALLOC;
                event.trapType = TrapType::VALUE_DECOY;

                if (threatCallback_) {
                    threatCallback_(event);
                }
            };

            addTrap(std::move(trap));
            LOGI("✅ Ca(自定义)陷阱已部署: 地址=%p, 值=%d", trapPtr, *trapPtr);
        }
    }

// 强制编译器分配.bss段内存 - 使用section属性确保正确分段
// 使用__attribute__((used))防止编译器优化移除
    __attribute__((used, section(".bss")))
    static volatile uint8_t cb_trap_section[1024 * 512]; // 512KB强制.bss段

    __attribute__((used, section(".bss")))
    static volatile int bss_trap_array[27136]; // 108KB的.bss段陷阱数组

    __attribute__((used, section(".bss")))
    static volatile char bss_buffer[1024 * 256]; // 256KB缓冲区

    __attribute__((used, section(".bss")))
    static volatile long bss_large_array[16384]; // 128KB long数组

    __attribute__((used, section(".bss")))
    static volatile double bss_double_array[8192]; // 64KB double数组

    void MemoryTrapManager::deployCppBssTraps(int count) {
        LOGI("🔧 部署C++ .bss段陷阱 (%d个)...", count);
        LOGI("📊 [ACESDK终极策略] 使用专用动态库确保Cb区域正确映射");

        // 策略1: 使用专用动态库创建真正的.bss段
        deployExternalCbTraps(count / 3);

        // 策略2: 延迟初始化静态变量
        deployDelayedInitTraps(count / 3);

        // 策略3: 大型静态数组初始化
        deployLargeStaticArrayTraps(count - (count / 3) * 2);

        LOGI("✅ .bss段陷阱部署完成，总计: ~1.5MB .bss段内存");
    }

    void MemoryTrapManager::deployExternalCbTraps(int count) {
        LOGI("🎯 [豆包Cb策略] 使用专用Cb分配器创建.bss段陷阱");

        // 豆包方案：使用专用Cb分配器
        for (int i = 0; i < count; i++) {
            // 使用专用Cb分配器分配内存 - 简化版
            void* trapAddr = nullptr;

            // 使用全局.bss段预留区域
            {
                std::lock_guard<std::mutex> lock(cb_alloc_mutex);
                const size_t CB_REGION_SIZE = sizeof(global_cb_reserve);

                if (cb_current_offset + sizeof(int) <= CB_REGION_SIZE) {
                    trapAddr = &global_cb_reserve[cb_current_offset];
                    cb_current_offset += sizeof(int);

                    // 填充非零值防止被优化
                    memset(trapAddr, 0xCB, sizeof(int));
                }
            }
            if (!trapAddr) {
                LOGW("⚠️  Cb陷阱分配失败，跳过第%d个", i);
                continue;
            }

            // 生成有意义的诱饵值
            int decoyValue = generateMeaningfulDecoy();
            *static_cast<int*>(trapAddr) = decoyValue;

            TrapConfig trap;
            trap.address = trapAddr;
            trap.size = sizeof(int);
            trap.type = TrapType::VALUE_DECOY;
            trap.region = MemoryRegion::CB_CPP_BSS;
            trap.decoyValue = decoyValue;
            trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                ThreatEvent event;
                event.severity = ThreatLevel::CRITICAL;
                event.description = "豆包Cb区域陷阱触发";
                event.primaryReason = "检测到对专用.bss段的扫描";
                event.triggerAddress = addr;
                event.region = MemoryRegion::CB_CPP_BSS;
                event.trapType = TrapType::VALUE_DECOY;

                if (threatCallback_) {
                    threatCallback_(event);
                }
            };

            addTrap(std::move(trap));

            // 验证是否在.bss段 - 简化版验证
            uintptr_t trapAddrVal = reinterpret_cast<uintptr_t>(trapAddr);
            uintptr_t globalAddr = reinterpret_cast<uintptr_t>(global_cb_reserve);
            if (trapAddr && trapAddrVal >= globalAddr && trapAddrVal < globalAddr + sizeof(global_cb_reserve)) {
                LOGI("✅ 豆包Cb陷阱已部署: 地址=%p, 值=%d (.bss段)", trapAddr, decoyValue);
            } else {
                LOGI("✅ 豆包Cb陷阱已部署: 地址=%p, 值=%d (警告:非.bss段)", trapAddr, decoyValue);
            }
        }
    }

    void MemoryTrapManager::deployDelayedInitTraps(int count) {
        LOGI("🎯 [Cb策略1] 延迟初始化静态变量%d个陷阱", count);

        // 声明BSS段变量 (首次访问时初始化)
        static bool s_initialized = false;
        static int s_decoyScore;
        static int s_decoyLevel;
        static int s_decoyExperience;
        static char s_decoyBuffer[256];
        static int s_gameValues[100];

        if (!s_initialized) {
            // 首次访问时初始化并设置陷阱
            s_decoyScore = 10000;
            s_decoyLevel = 50;
            s_decoyExperience = 25000;
            memset(s_decoyBuffer, 0, sizeof(s_decoyBuffer));

            // 填充游戏数值数组
            for (int i = 0; i < 100; i++) {
                s_gameValues[i] = generateMeaningfulDecoy();
            }

            // 注册陷阱
            addCbTrap(&s_decoyScore, sizeof(s_decoyScore), "分数陷阱");
            addCbTrap(&s_decoyLevel, sizeof(s_decoyLevel), "等级陷阱");
            addCbTrap(&s_decoyExperience, sizeof(s_decoyExperience), "经验陷阱");
            addCbTrap(s_decoyBuffer, 256, "缓冲区陷阱");

            // 注册数组中的部分元素作为陷阱
            for (int i = 0; i < count && i < 10; i++) {
                addCbTrap(&s_gameValues[i * 10], sizeof(int), "游戏数值陷阱");
            }

            s_initialized = true;
            LOGI("✅ BSS段延迟初始化完成，已设置%d个陷阱", count);
        }
    }

    void MemoryTrapManager::deployLargeStaticArrayTraps(int count) {
        LOGI("🎯 [Cb策略2] 大型静态数组初始化%d个陷阱", count);
        LOGI("📦 [Cb修复] 使用强制.bss段分配和内存模式填充");

        // 获取强制分配的.bss段数组引用
        extern volatile uint8_t cb_trap_section[];
        extern volatile int bss_trap_array[];
        extern volatile char bss_buffer[];
        extern volatile long bss_large_array[];
        extern volatile double bss_double_array[];

        // 强制初始化.bss段 - 防止编译器优化
        LOGI("🔧 强制初始化cb_trap_section (512KB)...");
        volatile uint8_t* forceAlloc = cb_trap_section;
        for (size_t i = 0; i < 1024 * 512; i += 4096) {
            forceAlloc[i] = i % 256; // 强制写入防止优化
        }

        // 使用特定内存模式填充.bss段
        const int pattern[4] = {
            static_cast<int>(0xDEADBEEF),
            static_cast<int>(0xCAFEBABE),
            static_cast<int>(0xBADDF00D),
            static_cast<int>(0xFACEB00C)
        };
        const size_t patternSize = sizeof(pattern);

        LOGI("🔧 使用内存模式填充bss_trap_array (108KB)...");
        for (int i = 0; i < 27136; i += 4) {
            // 复制模式到.bss段
            memcpy(const_cast<int*>(&bss_trap_array[i]), pattern,
                   std::min(patternSize, (27136 - i) * sizeof(int)));
        }

        LOGI("🔧 填充bss_buffer (256KB)...");
        for (int i = 0; i < 1024 * 256; i++) {
            bss_buffer[i] = static_cast<char>((i % 0xFE) + 1); // 非零值，避免0
        }

        LOGI("🔧 填充bss_large_array (128KB)...");
        for (int i = 0; i < 16384; i++) {
            bss_large_array[i] = 0xDEADBEEF + i; // 非零值
        }

        LOGI("🔧 填充bss_double_array (64KB)...");
        for (int i = 0; i < 8192; i++) {
            bss_double_array[i] = 3.14159 + i * 0.001; // 非零值
        }

        // 添加内存屏障确保所有写入生效
        __asm__ __volatile__("" ::: "memory");

        // 创建陷阱使用内存模式地址
        int maxTraps = std::min(count, 1000); // 限制陷阱数量

        for (int i = 0; i < maxTraps; i++) {
            // 使用cb_trap_section中的地址作为陷阱
            int* trapAddr = reinterpret_cast<int*>(
                const_cast<uint8_t*>(cb_trap_section) + (i * patternSize) % (1024 * 512)
            );

            // 复制模式到陷阱地址
            memcpy(trapAddr, pattern, patternSize);

            // 添加内存屏障
            __asm__ __volatile__("" ::: "memory");

            TrapConfig trap;
            trap.address = trapAddr;
            trap.size = patternSize;
            trap.type = TrapType::VALUE_DECOY;
            trap.region = MemoryRegion::CB_CPP_BSS;
            trap.decoyValue = *trapAddr;
            trap.triggerCallback = [this](void *addr, const AccessRecord &record) {
                ThreatEvent event;
                event.severity = ThreatLevel::CRITICAL;
                event.description = "Cb区域.bss段陷阱触发";
                event.primaryReason = "检测到对.bss段（静态未初始化数据区）的扫描";
                event.triggerAddress = addr;
                event.region = MemoryRegion::CB_CPP_BSS;
                event.trapType = TrapType::VALUE_DECOY;

                if (threatCallback_) {
                    threatCallback_(event);
                }
            };

            addTrap(std::move(trap));

            if (i < 10) { // 只记录前10个陷阱的详细信息
                LOGI("✅ Cb(.bss)陷阱已部署: 地址=%p, 模式=0x%08X", trapAddr, pattern[0]);
            }
        }

        // 验证内存地址并记录
        LOGI("✅ .bss段强制分配陷阱部署完成:");
        LOGI("   - cb_trap_section: %p - %p (512KB)",
             const_cast<uint8_t*>(cb_trap_section),
             const_cast<uint8_t*>(cb_trap_section) + 1024 * 512);
        LOGI("   - bss_trap_array: %p - %p (108KB)",
             const_cast<int*>(bss_trap_array),
             const_cast<int*>(bss_trap_array) + 27136);
        LOGI("   - bss_buffer: %p - %p (256KB)",
             const_cast<char*>(bss_buffer),
             const_cast<char*>(bss_buffer) + 1024 * 256);
        LOGI("   - bss_large_array: %p - %p (128KB)",
             const_cast<long*>(bss_large_array),
             const_cast<long*>(bss_large_array) + 16384);
        LOGI("   - bss_double_array: %p - %p (64KB)",
             const_cast<double*>(bss_double_array),
             const_cast<double*>(bss_double_array) + 8192);
        LOGI("   - 总计: ~1068KB .bss段内存已强制分配并填充");
        LOGI("   - 已部署%d个内存模式陷阱", maxTraps);

        // 分别设置各个段的内存权限 (修复mprotect问题)
        setMemoryPermissions(const_cast<uint8_t*>(cb_trap_section), 1024*512, "cb_trap_section");
        setMemoryPermissions(const_cast<int*>(bss_trap_array), 27136*sizeof(int), "bss_trap_array");
        setMemoryPermissions(const_cast<char*>(bss_buffer), 1024*256, "bss_buffer");
        setMemoryPermissions(const_cast<long*>(bss_large_array), 16384*sizeof(long), "bss_large_array");
        setMemoryPermissions(const_cast<double*>(bss_double_array), 8192*sizeof(double), "bss_double_array");

        // 验证.bss段是否可写
        volatile uint8_t* testPtr = cb_trap_section;
        uint8_t originalValue = testPtr[0];
        testPtr[0] = 0xAA;
        if (testPtr[0] == 0xAA) {
            LOGI("✅ .bss段写入测试成功");
            testPtr[0] = originalValue; // 恢复原值
        } else {
            LOGE("❌ .bss段写入测试失败");
        }
    }

    void MemoryTrapManager::addCbTrap(void* addr, size_t size, const char* description) {
        TrapConfig trap;
        trap.address = addr;
        trap.size = size;
        trap.type = TrapType::VALUE_DECOY;
        trap.region = MemoryRegion::CB_CPP_BSS;
        trap.decoyValue = *static_cast<int*>(addr);
        trap.triggerCallback = [this, description](void* addr, const AccessRecord& record) {
            ThreatEvent event;
            event.severity = ThreatLevel::CRITICAL;
            event.description = std::string("Cb区域") + description + "触发";
            event.primaryReason = "检测到对BSS段静态变量的访问";
            event.triggerAddress = addr;
            event.region = MemoryRegion::CB_CPP_BSS;
            event.trapType = TrapType::VALUE_DECOY;

            if (threatCallback_) {
                threatCallback_(event);
            }
        };

        addTrap(std::move(trap));
        LOGI("✅ Cb(%s)陷阱已部署: 地址=%p, 大小=%zu", description, addr, size);
    }

// 陷阱创建助手函数
    TrapConfig MemoryTrapManager::createValueDecoy(MemoryRegion region, int value) {
        TrapConfig trap;

        LOGI("🎯 [陷阱创建] 创建诱饵值陷阱: 区域=%d, 值=%d", static_cast<int>(region), value);

        // 根据区域选择分配方式
        switch (region) {
            case MemoryRegion::CH_CPP_HEAP:
                trap.address = new int(value);
                trap.size = sizeof(int);
                LOGI("✅ [Ch陷阱] C++堆陷阱创建成功: 地址=0x%p, 值=%d", trap.address, value);
                break;
            case MemoryRegion::JH_JAVA_HEAP:
                trap.address = malloc(sizeof(int));
                *static_cast<int *>(trap.address) = value;
                trap.size = sizeof(int);
                LOGI("✅ [Jh陷阱] Java堆陷阱创建成功: 地址=0x%p, 值=%d", trap.address, value);
                break;
            case MemoryRegion::A_ANONYMOUS:
                trap.address = mmap(nullptr, sizeof(int), PROT_READ | PROT_WRITE,
                                    MAP_ANONYMOUS | MAP_PRIVATE, -1, 0);
                if (trap.address != MAP_FAILED) {
                    *static_cast<int *>(trap.address) = value;
                    trap.size = sizeof(int);
                    LOGI("✅ [A陷阱] 匿名内存陷阱创建成功: 地址=0x%p, 值=%d", trap.address, value);
                } else {
                    LOGE("❌ [A陷阱] 匿名内存陷阱创建失败");
                }
                break;
            default:
                trap.address = malloc(sizeof(int));
                *static_cast<int *>(trap.address) = value;
                trap.size = sizeof(int);
                LOGI("✅ [默认陷阱] 默认陷阱创建成功: 地址=0x%p, 值=%d", trap.address, value);
                break;
        }

        trap.type = TrapType::VALUE_DECOY;
        trap.region = region;
        trap.decoyValue = value;

        LOGI("🎯 [陷阱完成] 诱饵值陷阱配置完成: 地址=0x%p, 大小=%zu", trap.address, trap.size);
        return std::move(trap);
    }

    TrapConfig MemoryTrapManager::createPointerChainTrap(MemoryRegion region, int depth) {
        TrapConfig trap;

        // 创建指针链
        void **chain = new void *[depth];
        trap.pointerChain = chain;
        trap.chainDepth = depth;

        // 构建指针链：ptr1 -> ptr2 -> ptr3 -> value
        for (int i = 0; i < depth - 1; i++) {
            chain[i] = &chain[i + 1];
        }

        // 最后一个指针指向诱饵值
        int *finalValue = new int(generateMeaningfulDecoy());
        chain[depth - 1] = finalValue;

        trap.address = chain;
        trap.size = sizeof(void *) * depth + sizeof(int);
        trap.type = TrapType::POINTER_CHAIN;
        trap.region = region;
        trap.decoyValue = *finalValue;

        return std::move(trap);
    }

    TrapConfig MemoryTrapManager::createGuardPage(size_t size) {
        TrapConfig trap;

        // 分配保护页
        void *guardPage = mmap(nullptr, size, PROT_NONE,
                               MAP_ANONYMOUS | MAP_PRIVATE, -1, 0);

        if (guardPage != MAP_FAILED) {
            trap.address = guardPage;
            trap.size = size;
            trap.type = TrapType::GUARD_PAGE;
            trap.region = MemoryRegion::A_ANONYMOUS;
        }

        return std::move(trap);
    }

    TrapConfig MemoryTrapManager::createCrcTrap(void *data, size_t size) {
        TrapConfig trap;

        // 计算初始CRC32
        uint32_t crc = 0xFFFFFFFF;
        uint8_t *bytes = static_cast<uint8_t *>(data);

        for (size_t i = 0; i < size; i++) {
            crc ^= bytes[i];
            for (int j = 0; j < 8; j++) {
                if (crc & 1) {
                    crc = (crc >> 1) ^ 0xEDB88320;
                } else {
                    crc >>= 1;
                }
            }
        }

        trap.address = data;
        trap.size = size;
        trap.type = TrapType::CRC32_TRAP;
        trap.crc32 = ~crc;

        return std::move(trap);
    }

    TrapConfig MemoryTrapManager::createTimedAccessTrap(MemoryRegion region, int value) {
        TrapConfig trap = createValueDecoy(region, value);
        trap.type = TrapType::TIMED_ACCESS;

        return std::move(trap);
    }

    bool MemoryTrapManager::addTrap(TrapConfig &&config) {
        if (!config.address) {
            LOGE("❌ 无效的陷阱地址");
            return false;
        }

        // 获取陷阱类型和区域的字符串表示
        const char *trapTypeStr = getTrapTypeString(config.type);
        const char *regionStr = getRegionString(config.region);

        LOGI("📍 [陷阱注册] 添加陷阱: 类型=%s, 区域=%s, 地址=0x%p, 大小=%zu",
             trapTypeStr, regionStr, config.address, config.size);

        std::lock_guard<std::mutex> lock(trapsMutex_);
        size_t trapIndex = traps_.size();
        traps_.emplace_back(std::move(config));

        // 更新统计
        {
            std::lock_guard<std::mutex> statsLock(statsMutex_);
            stats_.totalTraps++;
            if (config.isActive) {
                stats_.activeTraps++;
            }
        }

        LOGI("✅ [陷阱注册] 陷阱#%zu 注册成功, 总陷阱数: %d", trapIndex, stats_.totalTraps);
        return true;
    }

    void MemoryTrapManager::updateThreadFunc() {
        while (updateActive_.load()) {
            updateTraps();
            diversifyTraps();

            // 每5秒更新一次
            std::this_thread::sleep_for(std::chrono::seconds(5));
        }
    }

    void MemoryTrapManager::updateTraps() {
        std::lock_guard<std::mutex> lock(trapsMutex_);

        auto now = std::chrono::steady_clock::now();

        for (auto &trap: traps_) {
            if (!trap.isActive) continue;

            // 更新定时访问陷阱
            if (trap.type == TrapType::TIMED_ACCESS) {
                auto elapsed = now - trap.createTime;
                if (elapsed > std::chrono::seconds(10)) {
                    // 每10秒更新一次值
                    trap.decoyValue = generateMeaningfulDecoy();
                    if (trap.address) {
                        *static_cast<int *>(trap.address) = trap.decoyValue;
                    }
                    trap.createTime = now;
                }
            }

            // 验证CRC陷阱
            if (trap.type == TrapType::CRC32_TRAP) {
                // 重新计算CRC并检查是否被修改
                uint32_t currentCrc = 0xFFFFFFFF;
                uint8_t *bytes = static_cast<uint8_t *>(trap.address);

                for (size_t i = 0; i < trap.size; i++) {
                    currentCrc ^= bytes[i];
                    for (int j = 0; j < 8; j++) {
                        if (currentCrc & 1) {
                            currentCrc = (currentCrc >> 1) ^ 0xEDB88320;
                        } else {
                            currentCrc >>= 1;
                        }
                    }
                }

                if ((~currentCrc) != trap.crc32) {
                    // CRC不匹配，数据被修改
                    if (trap.triggerCallback) {
                        AccessRecord record;
                        record.address = trap.address;
                        record.size = trap.size;
                        record.timestamp = now;
                        record.region = trap.region;
                        record.isWrite = true;

                        trap.triggerCallback(trap.address, record);
                    }
                }
            }
        }
    }

    void MemoryTrapManager::diversifyTraps() {
        std::lock_guard<std::mutex> lock(trapsMutex_);

        for (auto &trap: traps_) {
            if (!trap.isActive) continue;

            // 30%概率改变诱饵值
            if (trap.type == TrapType::VALUE_DECOY && dist_(gen_) < 30) {
                trap.decoyValue = generateMeaningfulDecoy();
                if (trap.address) {
                    *static_cast<int *>(trap.address) = trap.decoyValue;
                }
            }

            // 20%概率重组指针链
            if (trap.type == TrapType::POINTER_CHAIN && dist_(gen_) < 20) {
                reorganizePointerChains();
            }

            // 10%概率切换陷阱状态
            if (dist_(gen_) < 10) {
                trap.isActive = !trap.isActive;
            }
        }
    }

    void MemoryTrapManager::reorganizePointerChains() {
        // 重新组织指针链结构
        for (auto &trap: traps_) {
            if (trap.type == TrapType::POINTER_CHAIN && trap.pointerChain && trap.chainDepth > 0) {
                // 随机改变指针链的顺序
                for (int i = 0; i < trap.chainDepth - 1; i++) {
                    if (dist_(gen_) < 50) {
                        // 50%概率交换相邻指针
                        std::swap(trap.pointerChain[i], trap.pointerChain[i + 1]);
                    }
                }
            }
        }
    }

    int MemoryTrapManager::generateMeaningfulDecoy() const {
        // 生成看起来像游戏数据的诱饵值
        static const std::vector<int> meaningfulValues = {
                100, 200, 500, 1000, 1111, 2222, 5000, 9999,
                10000, 50000, 99999, 100000, 999999
        };

        std::uniform_int_distribution<> valueDist(0, meaningfulValues.size() - 1);
        return meaningfulValues[valueDist(gen_)];
    }

    void MemoryTrapManager::setThreatCallback(std::function<void(const ThreatEvent &)> callback) {
        threatCallback_ = callback;
    }

    MemoryTrapManager::Statistics MemoryTrapManager::getStatistics() const {
        std::lock_guard<std::mutex> lock(statsMutex_);
        return stats_;
    }

// ============================================================================
// DefenseSystem 实现
// ============================================================================

    DefenseSystem &DefenseSystem::getInstance() {
        static DefenseSystem instance;
        return instance;
    }

    void DefenseSystem::initialize() {
        LOGI("🛡️ 初始化防御响应系统...");

        // 设置威胁事件回调
        auto &trapManager = MemoryTrapManager::getInstance();
        trapManager.setThreatCallback([this](const ThreatEvent &event) {
            handleThreatEvent(event);
        });

        LOGI("✅ 防御响应系统初始化完成");
    }

    void DefenseSystem::handleThreatEvent(const ThreatEvent &event) {
        LOGW("🚨 处理威胁事件: %s (级别: %d)",
             event.description.c_str(), static_cast<int>(event.severity));

        // 记录事件
        logEvent(event);

        // 根据威胁级别响应
        switch (event.severity) {
            case ThreatLevel::LOW:
                handleLowThreat(event);
                break;
            case ThreatLevel::MEDIUM:
                handleMediumThreat(event);
                break;
            case ThreatLevel::HIGH:
                handleHighThreat(event);
                break;
            case ThreatLevel::CRITICAL:
                handleCriticalThreat(event);
                break;
            default:
                break;
        }

        // 更新当前威胁级别
        if (event.severity > currentThreatLevel_.load()) {
            setThreatLevel(event.severity);
        }
    }

    void DefenseSystem::handleLowThreat(const ThreatEvent &event) {
        LOGI("🟡 处理低级别威胁: %s", event.description.c_str());

        // 1. 增加陷阱密度
        increaseLocalTrapDensity(event.region, 20);

        // 2. 记录详细日志
        LOGW("低威胁详情 - 地址: %p, 区域: %d, 类型: %d",
             event.triggerAddress, static_cast<int>(event.region),
             static_cast<int>(event.trapType));
    }

    void DefenseSystem::handleMediumThreat(const ThreatEvent &event) {
        LOGI("🟠 处理中等级别威胁: %s", event.description.c_str());

        handleLowThreat(event);

        // 3. 重组内存布局
        auto &trapManager = MemoryTrapManager::getInstance();
        trapManager.diversifyTraps();

        // 4. 增强监控
        increaseLocalTrapDensity(event.region, 50);
    }

    void DefenseSystem::handleHighThreat(const ThreatEvent &event) {
        LOGI("🔴 处理高级别威胁: %s", event.description.c_str());

        handleMediumThreat(event);

        // 5. 启用最大保护
        activateMaximumProtection();

        // 6. 调整全局陷阱策略
        auto &trapManager = MemoryTrapManager::getInstance();
        trapManager.adjustTrapsBasedOnThreat(ThreatLevel::HIGH);
    }

    void DefenseSystem::handleCriticalThreat(const ThreatEvent &event) {
        LOGI("🚨 处理严重威胁: %s", event.description.c_str());

        handleHighThreat(event);

        // 7. 立即响应
        LOGE("🚨 检测到严重安全威胁！可能存在内存修改器攻击！");

        // 8. 触发紧急防护模式
        activateMaximumProtection();

        // 这里可以添加更严厉的响应措施，如：
        // - 清除敏感数据
        // - 限制游戏功能
        // - 上报服务器
        // - 安全关闭等
    }

    void DefenseSystem::logEvent(const ThreatEvent &event) {
        std::lock_guard<std::mutex> lock(eventLogMutex_);
        eventLog_.push_back(event);

        // 保持最近1000个事件
        if (eventLog_.size() > 1000) {
            eventLog_.erase(eventLog_.begin());
        }
    }

    void DefenseSystem::increaseLocalTrapDensity(MemoryRegion region, int percentage) {
        LOGI("📈 增加区域 %d 的陷阱密度 %d%%", static_cast<int>(region), percentage);

        auto &trapManager = MemoryTrapManager::getInstance();

        // 根据区域类型增加相应陷阱
        int additionalTraps = std::max(1, percentage / 20);

        switch (region) {
            case MemoryRegion::CH_CPP_HEAP:
                trapManager.deployChHeapTraps(additionalTraps);
                break;
            case MemoryRegion::JH_JAVA_HEAP:
                trapManager.deployJhJavaTraps(additionalTraps);
                break;
            case MemoryRegion::A_ANONYMOUS:
                trapManager.deployAnonymousTraps(additionalTraps);
                break;
            case MemoryRegion::CD_DATA_SEGMENT:
                trapManager.deployDataSegmentTraps(additionalTraps);
                break;
            case MemoryRegion::CA_CPP_ALLOC:
                trapManager.deployCppAllocTraps(additionalTraps);
                break;
            default:
                break;
        }
    }

    void DefenseSystem::activateMaximumProtection() {
        LOGI("🛡️ 激活最大保护模式");

        auto &trapManager = MemoryTrapManager::getInstance();

        // 激活所有陷阱
        trapManager.activateAllTraps();

        // 增加陷阱多样性
        trapManager.diversifyTraps();

        // 设置最高威胁级别
        setThreatLevel(ThreatLevel::CRITICAL);
    }

    ThreatLevel DefenseSystem::getCurrentThreatLevel() const {
        return currentThreatLevel_.load();
    }

    void DefenseSystem::setThreatLevel(ThreatLevel level) {
        currentThreatLevel_ = level;
        LOGI("⚠️ 威胁级别更新为: %d", static_cast<int>(level));
    }

// 添加缺失的函数实现

    bool MemoryTrapManager::removeTrap(void *address) {
        std::lock_guard<std::mutex> lock(trapsMutex_);

        auto it = std::find_if(traps_.begin(), traps_.end(),
                               [address](const TrapConfig &trap) {
                                   return trap.address == address;
                               });

        if (it != traps_.end()) {
            // 清理资源
            if (it->type == TrapType::POINTER_CHAIN && it->pointerChain) {
                delete[] it->pointerChain;
            }

            traps_.erase(it);

            // 更新统计
            {
                std::lock_guard<std::mutex> statsLock(statsMutex_);
                stats_.totalTraps--;
            }

            return true;
        }

        return false;
    }

    void MemoryTrapManager::activateAllTraps() {
        std::lock_guard<std::mutex> lock(trapsMutex_);

        int activatedCount = 0;
        for (auto &trap: traps_) {
            if (!trap.isActive) {
                trap.isActive = true;
                activatedCount++;
            }
        }

        // 更新统计
        {
            std::lock_guard<std::mutex> statsLock(statsMutex_);
            stats_.activeTraps += activatedCount;
        }

        LOGI("✅ 激活了 %d 个陷阱", activatedCount);
    }

    void MemoryTrapManager::deactivateAllTraps() {
        std::lock_guard<std::mutex> lock(trapsMutex_);

        int deactivatedCount = 0;
        for (auto &trap: traps_) {
            if (trap.isActive) {
                trap.isActive = false;
                deactivatedCount++;
            }
        }

        // 更新统计
        {
            std::lock_guard<std::mutex> statsLock(statsMutex_);
            stats_.activeTraps -= deactivatedCount;
        }

        LOGI("⏸️ 停用了 %d 个陷阱", deactivatedCount);
    }

    void MemoryTrapManager::deployLayeredDefense() {
        LOGI("🛡️ 部署分层防御策略...");

        // 清除现有陷阱
        {
            std::lock_guard<std::mutex> lock(trapsMutex_);
            traps_.clear();
        }

        // 重新部署默认陷阱
        setupDefaultTraps();

        LOGI("✅ 分层防御策略部署完成");
    }

    void MemoryTrapManager::adjustTrapsBasedOnThreat(ThreatLevel level) {
        LOGI("⚙️ 根据威胁级别调整陷阱: %d", static_cast<int>(level));

        int multiplier = 1;
        switch (level) {
            case ThreatLevel::LOW:
                multiplier = 1;
                break;
            case ThreatLevel::MEDIUM:
                multiplier = 2;
                break;
            case ThreatLevel::HIGH:
                multiplier = 3;
                break;
            case ThreatLevel::CRITICAL:
                multiplier = 5;
                break;
            default:
                multiplier = 1;
                break;
        }

        // 增加各区域陷阱数量
        if (multiplier > 1) {
            deployChHeapTraps(5 * multiplier);
            deployJhJavaTraps(3 * multiplier);
            deployAnonymousTraps(2 * multiplier);
            deployDataSegmentTraps(1 * multiplier);
            deployCppAllocTraps(1 * multiplier);
        }

        // 增加陷阱活跃度
        diversifyTraps();

        LOGI("✅ 陷阱调整完成，倍数: %d", multiplier);
    }

    void MemoryTrapManager::applyTrapState(TrapConfig &trap) {
        // 根据陷阱状态应用相应的内存保护
        if (trap.type == TrapType::GUARD_PAGE && trap.address) {
            if (trap.isActive) {
                // 激活保护页
                mprotect(trap.address, trap.size, PROT_NONE);
            } else {
                // 取消保护
                mprotect(trap.address, trap.size, PROT_READ | PROT_WRITE);
            }
        }
    }

// 辅助函数：获取陷阱类型字符串
    const char *MemoryTrapManager::getTrapTypeString(TrapType type) {
        switch (type) {
            case TrapType::VALUE_DECOY:
                return "诱饵值";
            case TrapType::POINTER_CHAIN:
                return "指针链";
            case TrapType::GUARD_PAGE:
                return "保护页";
            case TrapType::TIMED_ACCESS:
                return "定时访问";
            default:
                return "未知";
        }
    }

// 辅助函数：获取内存区域字符串
    const char *MemoryTrapManager::getRegionString(MemoryRegion region) {
        switch (region) {
            case MemoryRegion::CH_CPP_HEAP:
                return "Ch(C++堆)";
            case MemoryRegion::JH_JAVA_HEAP:
                return "Jh(Java堆)";
            case MemoryRegion::A_ANONYMOUS:
                return "A(匿名内存)";
            case MemoryRegion::CD_DATA_SEGMENT:
                return "Cd(数据段)";
            case MemoryRegion::CA_CPP_ALLOC:
                return "Ca(C++分配)";
            default:
                return "未知区域";
        }
    }

// 主动检测实现
    void MemoryTrapManager::startActiveDetection() {
        if (activeDetectionRunning_.load()) {
            LOGI("🔍 主动检测已在运行中");
            return;
        }

        LOGI("🚀 启动主动内存扫描检测...");
        activeDetectionRunning_.store(true);
        lastMemoryCheck_ = std::chrono::steady_clock::now();

        detectionThread_ = std::thread([this]() {
            LOGI("🔍 主动检测线程已启动");
            while (activeDetectionRunning_.load()) {
                checkMemoryScanning();
                std::this_thread::sleep_for(std::chrono::milliseconds(200));
            }
            LOGI("🔍 主动检测线程已停止");
        });
    }

    void MemoryTrapManager::stopActiveDetection() {
        if (!activeDetectionRunning_.load()) {
            return;
        }

        LOGI("🛑 停止主动检测...");
        activeDetectionRunning_.store(false);
        if (detectionThread_.joinable()) {
            detectionThread_.join();
        }
        LOGI("✅ 主动检测已停止");
    }

    void MemoryTrapManager::checkMemoryScanning() {
        auto now = std::chrono::steady_clock::now();
        auto timeSinceLastCheck = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - lastMemoryCheck_).count();

        lastMemoryCheck_ = now;

        // 检查/proc/self/maps的变化
        std::ifstream maps("/proc/self/maps");
        if (!maps.is_open()) {
            return;
        }

        std::string line;
        int mapCount = 0;
        int rwMaps = 0;
        int execMaps = 0;

        while (std::getline(maps, line)) {
            mapCount++;

            // 统计可读写内存区域
            if (line.find("rw-p") != std::string::npos) {
                rwMaps++;
            }

            // 统计可执行内存区域
            if (line.find("r-xp") != std::string::npos || line.find("rwxp") != std::string::npos) {
                execMaps++;
            }

            // 检查可疑的内存映射 (添加系统库白名单)
            if (isSuspiciousMapping(line)) {
                LOGW("🚨 检测到可疑内存映射: %s", line.c_str());

                if (threatCallback_) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::CRITICAL;
                    event.description = "检测到可疑内存映射";
                    event.primaryReason = "发现修改器相关内存映射";
                    event.timestamp = now;
                    threatCallback_(event);
                }
            }
        }

        // 检查内存映射数量的异常变化 (调整阈值，避免误报)
        static int lastMapCount = 0;
        static int lastRwMaps = 0;
        static int initializationPhase = 5; // 初始化阶段，忽略前5次检查

        if (lastMapCount > 0 && initializationPhase <= 0) {
            int mapDiff = abs(mapCount - lastMapCount);
            int rwDiff = abs(rwMaps - lastRwMaps);

            // 提高阈值，避免将正常的系统库加载误判为异常
            // 只有在映射变化非常大时才报警
            if (mapDiff > 50 || rwDiff > 20) {
                LOGW("🚨 内存映射异常变化: 总映射变化=%d, 可写映射变化=%d", mapDiff, rwDiff);

                if (threatCallback_) {
                    ThreatEvent event;
                    event.severity = ThreatLevel::HIGH;
                    event.description = "内存映射异常变化";
                    event.primaryReason = "映射数量变化: " + std::to_string(mapDiff);
                    event.timestamp = now;
                    threatCallback_(event);
                }
            }
        }

        lastMapCount = mapCount;
        lastRwMaps = rwMaps;

        // 减少初始化阶段计数器
        if (initializationPhase > 0) {
            initializationPhase--;
        }

        // 每10次检查输出一次统计信息
        static int checkCount = 0;
        if (++checkCount % 50 == 0) {
            LOGI("🔍 内存检测统计: 总映射=%d, 可写映射=%d, 可执行映射=%d",
                 mapCount, rwMaps, execMaps);
        }
    }

    // 检查是否为可疑内存映射 (添加系统库白名单)
    bool MemoryTrapManager::isSuspiciousMapping(const std::string& line) {
        // 系统库和正常内存映射白名单
        const std::vector<std::string> systemLibWhitelist = {
            "/system/lib64/libdebuggerd_client.so",
            "/system/lib64/libc.so",
            "/system/lib64/libm.so",
            "/system/lib64/libdl.so",
            "/system/lib64/liblog.so",
            "/system/lib64/libz.so",
            "/system/lib64/libutils.so",
            "/system/lib64/libcutils.so",
            "/system/lib64/libbinder.so",
            "/system/lib64/libbase.so",
            "/system/lib64/libunwind.so",
            "/system/lib64/libbacktrace.so",
            "/system/lib64/libprocessgroup.so",
            "/system/lib64/libselinux.so",
            "/system/lib64/libpcre2.so",
            "/system/lib64/libcrypto.so",
            "/system/lib64/libssl.so",
            "/system/lib64/libxml2.so",
            "/system/lib64/libandroid_runtime.so",
            "/system/lib64/libnativehelper.so",
            "/system/lib64/libnativeloader.so",
            "/system/lib64/libandroid.so",
            "/system/lib64/libjnigraphics.so",
            "/apex/",
            "/vendor/lib64/",
            "/odm/lib64/",
            "/product/lib64/",
            "/system_ext/lib64/",
            // Android Dalvik/ART相关内存映射
            "[anon:dalvik-",
            "[anon:libc_malloc]",
            "[anon:scudo:",
            "[anon:thread signal stack]",
            "[anon:bionic TLS]",
            "[stack",
            "[vdso]",
            "[vectors]"
        };

        // 检查是否在系统库白名单中
        for (const auto& whitelistItem : systemLibWhitelist) {
            if (line.find(whitelistItem) != std::string::npos) {
                return false; // 在白名单中，不是可疑映射
            }
        }

        // 检查真正可疑的内容
        return (line.find("/data/local/tmp") != std::string::npos ||
                line.find("GameGuardian") != std::string::npos ||
                line.find("gg") != std::string::npos ||
                line.find("cheat") != std::string::npos ||
                line.find("hack") != std::string::npos ||
                line.find("mod") != std::string::npos ||
                line.find("xposed") != std::string::npos ||
                line.find("frida") != std::string::npos ||
                line.find("substrate") != std::string::npos);
    }

    // 内存区域诊断工具 (关键诊断功能)
    void MemoryTrapManager::dumpMemoryRegions() {
        LOGI("🔍 ===== 内存区域诊断报告 =====");

        std::ifstream maps("/proc/self/maps");
        if (!maps.is_open()) {
            LOGE("❌ 无法打开/proc/self/maps");
            return;
        }

        std::string line;
        bool foundCa = false;
        bool foundCb = false;
        int heapCount = 0;
        int anonCount = 0;

        while (std::getline(maps, line)) {
            // 检查Ca区域(堆相关)
            if (line.find("heap") != std::string::npos ||
                line.find("[anon:libc_malloc]") != std::string::npos) {
                LOGI("🏷️  Ca区域候选(堆): %s", line.c_str());
                foundCa = true;
                heapCount++;
            }

            // 检查Cb区域(.bss相关)
            if (line.find(".bss") != std::string::npos ||
                line.find("cb_trap") != std::string::npos ||
                (line.find("rw-p") != std::string::npos && line.find("00:00 0") != std::string::npos)) {
                LOGI("🏷️  Cb区域候选(BSS): %s", line.c_str());
                foundCb = true;
            }

            // 统计匿名内存
            if (line.find("[anon:") != std::string::npos) {
                anonCount++;
            }
        }

        LOGI("📊 内存区域统计: 堆区域=%d, 匿名区域=%d", heapCount, anonCount);

        if (!foundCa) LOGW("⚠️  未检测到明确的Ca区域!");
        if (!foundCb) LOGW("⚠️  未检测到明确的Cb区域!");

        // 输出陷阱地址详情
        dumpTrapAddresses();

        LOGI("🔍 ===== 诊断报告结束 =====");
    }

    void MemoryTrapManager::dumpTrapAddresses() {
        LOGI("📍 陷阱地址详情:");

        std::lock_guard<std::mutex> lock(trapsMutex_);
        int caCount = 0, cbCount = 0;

        for (const auto& trap : traps_) {
            if (trap.region == MemoryRegion::CA_CPP_ALLOC) {
                LOGI("📍 Ca陷阱[%d]: 地址=%p, 大小=%zu, 值=%d",
                     caCount++, trap.address, trap.size, trap.decoyValue);
            }
            if (trap.region == MemoryRegion::CB_CPP_BSS) {
                LOGI("📍 Cb陷阱[%d]: 地址=%p, 大小=%zu, 值=%d",
                     cbCount++, trap.address, trap.size, trap.decoyValue);
            }
        }

        LOGI("📊 陷阱统计: Ca陷阱=%d个, Cb陷阱=%d个", caCount, cbCount);

        // 验证关键内存区域
        extern volatile uint8_t cb_trap_section[];
        LOGI("🔧 cb_trap_section地址: %p", const_cast<uint8_t*>(cb_trap_section));
        LOGI("🔧 cb_trap_section[0]值: 0x%02X", cb_trap_section[0]);
    }

    // 设置内存权限的辅助方法
    void MemoryTrapManager::setMemoryPermissions(void* addr, size_t size, const char* name) {
        // 获取页面大小并对齐
        size_t pageSize = static_cast<size_t>(sysconf(_SC_PAGESIZE));
        uintptr_t alignedAddr = reinterpret_cast<uintptr_t>(addr) & ~(pageSize - 1);
        size_t alignedSize = ((size + pageSize - 1) / pageSize) * pageSize;

        if (mprotect(reinterpret_cast<void*>(alignedAddr), alignedSize, PROT_READ | PROT_WRITE) == 0) {
            LOGI("✅ %s内存权限设置成功: %p (大小: %zu)", name, addr, size);
        } else {
            // 如果mprotect失败，可能是因为内存已经有正确的权限，这不是错误
            LOGW("⚠️  %s内存权限设置跳过: %s (可能已有正确权限)", name, strerror(errno));
        }
    }

} // namespace AdvancedAntiCheat
