@echo off
echo ========================================
echo 内存区域验证脚本 - 检查CA和CB区域大小
echo ========================================

echo.
echo 1. 获取应用进程ID...
for /f "tokens=2" %%i in ('adb shell ps ^| findstr com.sy.newfwg') do set PID=%%i
if "%PID%"=="" (
    echo 应用未运行，请先启动应用！
    pause
    exit /b 1
)
echo 应用进程ID: %PID%

echo.
echo 2. 检查内存映射区域...
echo 查看/proc/%PID%/maps中的内存区域：
adb shell cat /proc/%PID%/maps | findstr -E "(heap|anon|\.so)"

echo.
echo 3. 检查内存统计信息...
echo 查看/proc/%PID%/status中的内存使用：
adb shell cat /proc/%PID%/status | findstr -E "(VmSize|VmRSS|VmData|VmStk|VmExe)"

echo.
echo 4. 使用GG修改器扫描测试...
echo 请手动执行以下步骤：
echo 1. 打开GG修改器
echo 2. 选择进程：com.sy.newfwg
echo 3. 搜索数值：100
echo 4. 查看搜索结果数量
echo 5. 检查CA和CB区域是否显示大小 ^> 0B

echo.
echo 5. 监控内存陷阱触发日志...
echo 查找检测日志：
adb logcat -c
echo 开始监控检测日志（按Ctrl+C停止）...
adb logcat | findstr /i "检测到 修改器 扫描 陷阱 触发"

pause
