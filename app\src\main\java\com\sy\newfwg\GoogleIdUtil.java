package com.sy.newfwg;

import android.content.Context;
import android.os.AsyncTask;
import android.provider.Settings;
import android.util.Log;

import com.google.android.gms.ads.identifier.AdvertisingIdClient;
import com.google.android.gms.auth.GoogleAuthException;
import com.google.android.gms.auth.GoogleAuthUtil;
import com.google.android.gms.common.ConnectionResult;
import com.google.android.gms.common.GoogleApiAvailability;
import com.google.android.gms.common.GooglePlayServicesNotAvailableException;
import com.google.android.gms.common.GooglePlayServicesRepairableException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Google服务相关ID获取工具类
 * 用于获取gsfId（Google Services Framework ID）和gadId（Google Advertising ID）
 * 需依赖Google Play Services，仅在海外设备生效
 */
public class GoogleIdUtil {
    private static final String TAG = "GoogleIdUtil";
    // 定义结果中ID的键名
    public static final String KEY_GSF_ID = "gsf_id";
    public static final String KEY_GAD_ID = "gad_id";

    /**
     * 单次调用同时获取gsfId和gadId
     * @param context 上下文
     * @param callback 统一回调（返回包含两个ID的Map）
     */
    public static void getGoogleIdsAsync(Context context, GoogleIdsCallback callback) {
        if (context == null) {
            callback.onResult(null);
            return;
        }

        // 检查Google服务是否可用
        if (!isGooglePlayServicesAvailable(context)) {
            Log.w(TAG, "Google Play Services不可用，无法获取ID");
            callback.onResult(null);
            return;
        }

        // 异步并行获取两个ID
        new AsyncTask<Void, Void, String>() {
            @Override
            protected String doInBackground(Void... voids) {
                Map<String, String> resultMap = new HashMap<>();
                String id = null;

                // 获取gadId
                try {
                    com.google.android.gms.ads.identifier.AdvertisingIdClient.Info adInfo =
                            com.google.android.gms.ads.identifier.AdvertisingIdClient.getAdvertisingIdInfo(context);
                    String gadId = adInfo.getId();
                    id = gadId;
                    Log.d(TAG, "gadId获取成功");
                } catch (Exception e) {
                    Log.e(TAG, "gadId获取失败: " + e.getMessage());
                    id = null;
                }

                return id;
            }

            @Override
            protected void onPostExecute(String  result) {
                callback.onResult(result);
            }
        }.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
    }

    // 检查Google服务是否可用
    public static boolean isGooglePlayServicesAvailable(Context context) {
        com.google.android.gms.common.GoogleApiAvailability apiAvailability =
                com.google.android.gms.common.GoogleApiAvailability.getInstance();
        int resultCode = apiAvailability.isGooglePlayServicesAvailable(context);
        return resultCode == com.google.android.gms.common.ConnectionResult.SUCCESS;
    }

    /**
     * 统一回调接口：返回包含gsfId和gadId的Map
     * 键为 KEY_GSF_ID 和 KEY_GAD_ID，值为ID字符串（null表示获取失败）
     */
    public interface GoogleIdsCallback {
        void onResult(String id);
    }
}