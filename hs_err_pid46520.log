#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1500496 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=46520, tid=46576
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-1f1461b6953a0a9a22ba68cf6041b0bd-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:34:43 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 19.594679 seconds (0d 0h 0m 19s)

---------------  T H R E A D  ---------------

Current thread (0x0000012a78adcd50):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=46576, stack(0x00000007df100000,0x00000007df200000) (1024K)]


Current CompileTask:
C2:19594 8297 % !   4       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> @ 81 (2135 bytes)

Stack: [0x00000007df100000,0x00000007df200000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b68f2]
V  [jvm.dll+0x1dfe33]
V  [jvm.dll+0x247c42]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000012a5070b1f0, length=51, elements={
0x0000012a70395600, 0x0000012a77db38e0, 0x0000012a77db5850, 0x0000012a78ad5740,
0x0000012a78ad66f0, 0x0000012a78ada4c0, 0x0000012a78adbf20, 0x0000012a78adcd50,
0x0000012a77dbe050, 0x0000012a78b9c370, 0x0000012a7e0fe4f0, 0x0000012a7e827790,
0x0000012a7e88fee0, 0x0000012a7ead43c0, 0x0000012a7ea60650, 0x0000012a7ec58940,
0x0000012a7ed736a0, 0x0000012a7ef390d0, 0x0000012a7ef3ab10, 0x0000012a7ef38a40,
0x0000012a7ef3a480, 0x0000012a7ef39760, 0x0000012a7ef3b1a0, 0x0000012a7ef383b0,
0x0000012a7ef37d20, 0x0000012a505c3020, 0x0000012a505c08c0, 0x0000012a505c36b0,
0x0000012a505c0230, 0x0000012a505c0f50, 0x0000012a505c2990, 0x0000012a505c2300,
0x0000012a505c15e0, 0x0000012a505c1c70, 0x0000012a5135be70, 0x0000012a51359710,
0x0000012a5135d8b0, 0x0000012a51359da0, 0x0000012a513589f0, 0x0000012a51359080,
0x0000012a5135a430, 0x0000012a51357cd0, 0x0000012a5135b7e0, 0x0000012a51356920,
0x0000012a5135c500, 0x0000012a5135b150, 0x0000012a5135df40, 0x0000012a51356fb0,
0x0000012a5135d220, 0x0000012a52d3b8d0, 0x0000012a53801c50
}

Java Threads: ( => current thread )
  0x0000012a70395600 JavaThread "main"                              [_thread_blocked, id=12404, stack(0x00000007de700000,0x00000007de800000) (1024K)]
  0x0000012a77db38e0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=42780, stack(0x00000007deb00000,0x00000007dec00000) (1024K)]
  0x0000012a77db5850 JavaThread "Finalizer"                  daemon [_thread_blocked, id=39568, stack(0x00000007dec00000,0x00000007ded00000) (1024K)]
  0x0000012a78ad5740 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=44024, stack(0x00000007ded00000,0x00000007dee00000) (1024K)]
  0x0000012a78ad66f0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=23104, stack(0x00000007dee00000,0x00000007def00000) (1024K)]
  0x0000012a78ada4c0 JavaThread "Service Thread"             daemon [_thread_blocked, id=25008, stack(0x00000007def00000,0x00000007df000000) (1024K)]
  0x0000012a78adbf20 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=17176, stack(0x00000007df000000,0x00000007df100000) (1024K)]
=>0x0000012a78adcd50 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=46576, stack(0x00000007df100000,0x00000007df200000) (1024K)]
  0x0000012a77dbe050 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=42068, stack(0x00000007df200000,0x00000007df300000) (1024K)]
  0x0000012a78b9c370 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=32780, stack(0x00000007df300000,0x00000007df400000) (1024K)]
  0x0000012a7e0fe4f0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=3192, stack(0x00000007df500000,0x00000007df600000) (1024K)]
  0x0000012a7e827790 JavaThread "Active Thread: Equinox Container: 81674bdf-6213-4449-afd8-6ae696cd719f"        [_thread_blocked, id=2796, stack(0x00000007dfd00000,0x00000007dfe00000) (1024K)]
  0x0000012a7e88fee0 JavaThread "Refresh Thread: Equinox Container: 81674bdf-6213-4449-afd8-6ae696cd719f" daemon [_thread_blocked, id=1984, stack(0x00000007dff00000,0x00000007e0000000) (1024K)]
  0x0000012a7ead43c0 JavaThread "Framework Event Dispatcher: Equinox Container: 81674bdf-6213-4449-afd8-6ae696cd719f" daemon [_thread_blocked, id=36212, stack(0x00000007e0000000,0x00000007e0100000) (1024K)]
  0x0000012a7ea60650 JavaThread "Start Level: Equinox Container: 81674bdf-6213-4449-afd8-6ae696cd719f" daemon [_thread_blocked, id=30584, stack(0x00000007e0100000,0x00000007e0200000) (1024K)]
  0x0000012a7ec58940 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=21296, stack(0x00000007e0200000,0x00000007e0300000) (1024K)]
  0x0000012a7ed736a0 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=45376, stack(0x00000007e0300000,0x00000007e0400000) (1024K)]
  0x0000012a7ef390d0 JavaThread "Worker-JM"                         [_thread_blocked, id=4824, stack(0x00000007e0500000,0x00000007e0600000) (1024K)]
  0x0000012a7ef3ab10 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=44576, stack(0x00000007e0600000,0x00000007e0700000) (1024K)]
  0x0000012a7ef38a40 JavaThread "Worker-0"                          [_thread_blocked, id=27324, stack(0x00000007e0700000,0x00000007e0800000) (1024K)]
  0x0000012a7ef3a480 JavaThread "Worker-1"                          [_thread_blocked, id=39888, stack(0x00000007e0800000,0x00000007e0900000) (1024K)]
  0x0000012a7ef39760 JavaThread "Java indexing"              daemon [_thread_in_Java, id=45328, stack(0x00000007e0900000,0x00000007e0a00000) (1024K)]
  0x0000012a7ef3b1a0 JavaThread "Worker-2: Java indexing... "        [_thread_blocked, id=45888, stack(0x00000007e0c00000,0x00000007e0d00000) (1024K)]
  0x0000012a7ef383b0 JavaThread "Worker-3: Check JDK Index"         [_thread_blocked, id=35888, stack(0x00000007e0d00000,0x00000007e0e00000) (1024K)]
  0x0000012a7ef37d20 JavaThread "Worker-4"                          [_thread_blocked, id=37808, stack(0x00000007e0e00000,0x00000007e0f00000) (1024K)]
  0x0000012a505c3020 JavaThread "Thread-2"                   daemon [_thread_in_native, id=21284, stack(0x00000007e0f00000,0x00000007e1000000) (1024K)]
  0x0000012a505c08c0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=14692, stack(0x00000007e1000000,0x00000007e1100000) (1024K)]
  0x0000012a505c36b0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=45408, stack(0x00000007e1100000,0x00000007e1200000) (1024K)]
  0x0000012a505c0230 JavaThread "Thread-5"                   daemon [_thread_in_native, id=31708, stack(0x00000007e1200000,0x00000007e1300000) (1024K)]
  0x0000012a505c0f50 JavaThread "Thread-6"                   daemon [_thread_in_native, id=45252, stack(0x00000007e1300000,0x00000007e1400000) (1024K)]
  0x0000012a505c2990 JavaThread "Thread-7"                   daemon [_thread_in_native, id=18492, stack(0x00000007e1400000,0x00000007e1500000) (1024K)]
  0x0000012a505c2300 JavaThread "Thread-8"                   daemon [_thread_in_native, id=42420, stack(0x00000007e1500000,0x00000007e1600000) (1024K)]
  0x0000012a505c15e0 JavaThread "Thread-9"                   daemon [_thread_in_native, id=20984, stack(0x00000007e1600000,0x00000007e1700000) (1024K)]
  0x0000012a505c1c70 JavaThread "Thread-10"                  daemon [_thread_in_native, id=44656, stack(0x00000007e1700000,0x00000007e1800000) (1024K)]
  0x0000012a5135be70 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=43500, stack(0x00000007e1800000,0x00000007e1900000) (1024K)]
  0x0000012a51359710 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=6600, stack(0x00000007e1900000,0x00000007e1a00000) (1024K)]
  0x0000012a5135d8b0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=44848, stack(0x00000007e1a00000,0x00000007e1b00000) (1024K)]
  0x0000012a51359da0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=25652, stack(0x00000007e1b00000,0x00000007e1c00000) (1024K)]
  0x0000012a513589f0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=19996, stack(0x00000007e1c00000,0x00000007e1d00000) (1024K)]
  0x0000012a51359080 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=38244, stack(0x00000007e1d00000,0x00000007e1e00000) (1024K)]
  0x0000012a5135a430 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=32392, stack(0x00000007e1e00000,0x00000007e1f00000) (1024K)]
  0x0000012a51357cd0 JavaThread "Timer-0"                           [_thread_blocked, id=43168, stack(0x00000007e1f00000,0x00000007e2000000) (1024K)]
  0x0000012a5135b7e0 JavaThread "Exec process"                      [_thread_blocked, id=41156, stack(0x00000007e0400000,0x00000007e0500000) (1024K)]
  0x0000012a51356920 JavaThread "Exec process Thread 2"             [_thread_blocked, id=36344, stack(0x00000007e2100000,0x00000007e2200000) (1024K)]
  0x0000012a5135c500 JavaThread "Exec process Thread 3"             [_thread_blocked, id=28124, stack(0x00000007e2200000,0x00000007e2300000) (1024K)]
  0x0000012a5135b150 JavaThread "Timer-1"                           [_thread_blocked, id=25216, stack(0x00000007df600000,0x00000007df700000) (1024K)]
  0x0000012a5135df40 JavaThread "Timer-2"                           [_thread_blocked, id=41880, stack(0x00000007e2000000,0x00000007e2100000) (1024K)]
  0x0000012a51356fb0 JavaThread "Timer-3"                           [_thread_blocked, id=46868, stack(0x00000007e2300000,0x00000007e2400000) (1024K)]
  0x0000012a5135d220 JavaThread "Worker-5"                          [_thread_blocked, id=16416, stack(0x00000007e2600000,0x00000007e2700000) (1024K)]
  0x0000012a52d3b8d0 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=41828, stack(0x00000007df400000,0x00000007df500000) (1024K)]
  0x0000012a53801c50 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=14268, stack(0x00000007e2500000,0x00000007e2600000) (1024K)]
Total: 51

Other Threads:
  0x0000012a78ad29e0 VMThread "VM Thread"                           [id=38104, stack(0x00000007dea00000,0x00000007deb00000) (1024K)]
  0x0000012a703c3950 WatcherThread "VM Periodic Task Thread"        [id=23852, stack(0x00000007de900000,0x00000007dea00000) (1024K)]
  0x0000012a703b3830 WorkerThread "GC Thread#0"                     [id=42948, stack(0x00000007de800000,0x00000007de900000) (1024K)]
  0x0000012a7e00c9f0 WorkerThread "GC Thread#1"                     [id=43512, stack(0x00000007df700000,0x00000007df800000) (1024K)]
  0x0000012a7e7a41f0 WorkerThread "GC Thread#2"                     [id=45724, stack(0x00000007df800000,0x00000007df900000) (1024K)]
  0x0000012a7e7a4590 WorkerThread "GC Thread#3"                     [id=45052, stack(0x00000007df900000,0x00000007dfa00000) (1024K)]
  0x0000012a7e77ecf0 WorkerThread "GC Thread#4"                     [id=20780, stack(0x00000007dfa00000,0x00000007dfb00000) (1024K)]
  0x0000012a7e77f090 WorkerThread "GC Thread#5"                     [id=31724, stack(0x00000007dfb00000,0x00000007dfc00000) (1024K)]
  0x0000012a7e69b470 WorkerThread "GC Thread#6"                     [id=43636, stack(0x00000007dfc00000,0x00000007dfd00000) (1024K)]
  0x0000012a7e88f5f0 WorkerThread "GC Thread#7"                     [id=45160, stack(0x00000007dfe00000,0x00000007dff00000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  19922 8297 % !   4       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> @ 81 (2135 bytes)
C2 CompilerThread1  19922 8308       4       org.eclipse.jdt.internal.core.index.DiskIndex::writeDocumentNumbers (384 bytes)
C2 CompilerThread2  19922 8302       4       org.eclipse.jdt.internal.core.index.DiskIndex::computeDocumentNames (607 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000012a0f000000-0x0000012a0fba0000-0x0000012a0fba0000), size 12189696, SharedBaseAddress: 0x0000012a0f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000012a10000000-0x0000012a50000000, reserved size: 1073741824
Narrow klass base: 0x0000012a0f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 9216K, used 6080K [0x00000000d5580000, 0x00000000d6400000, 0x0000000100000000)
  eden space 4608K, 38% used [0x00000000d5580000,0x00000000d5737450,0x00000000d5a00000)
  from space 4608K, 93% used [0x00000000d5f80000,0x00000000d63b8ed8,0x00000000d6400000)
  to   space 4608K, 0% used [0x00000000d5b00000,0x00000000d5b00000,0x00000000d5f80000)
 ParOldGen       total 70144K, used 48873K [0x0000000080000000, 0x0000000084480000, 0x00000000d5580000)
  object space 70144K, 69% used [0x0000000080000000,0x0000000082fba7b0,0x0000000084480000)
 Metaspace       used 63328K, committed 64704K, reserved 1114112K
  class space    used 7216K, committed 7808K, reserved 1048576K

Card table byte_map: [0x0000012a6fcc0000,0x0000012a700d0000] _byte_map_base: 0x0000012a6f8c0000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x0000012a737c0000, 0x0000012a757c0000)
 End Bits:   [0x0000012a757c0000, 0x0000012a777c0000)

Polling page: 0x0000012a6fa20000

Metaspace:

Usage:
  Non-class:     54.80 MB used.
      Class:      7.05 MB used.
       Both:     61.84 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      55.56 MB ( 87%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       7.62 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      63.19 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  7.98 MB
       Class:  8.38 MB
        Both:  16.36 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 105.25 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1004.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 1011.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 23.
num_chunks_taken_from_freelist: 3544.
num_chunk_merges: 15.
num_chunk_splits: 2324.
num_chunks_enlarged: 1517.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=5578Kb max_used=6902Kb free=114421Kb
 bounds [0x0000012a07ad0000, 0x0000012a08190000, 0x0000012a0f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=13791Kb max_used=16488Kb free=106208Kb
 bounds [0x0000012a00000000, 0x0000012a01020000, 0x0000012a07530000]
CodeHeap 'non-nmethods': size=5760Kb used=1441Kb max_used=1535Kb free=4319Kb
 bounds [0x0000012a07530000, 0x0000012a077a0000, 0x0000012a07ad0000]
 total_blobs=7642 nmethods=6892 adapters=656
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 18.903 Thread 0x0000012a78adcd50 nmethod 8294 0x0000012a08189510 code [0x0000012a081897c0, 0x0000012a0818ace8]
Event: 19.076 Thread 0x0000012a77dbe050 8295   !   3       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> (2135 bytes)
Event: 19.082 Thread 0x0000012a77dbe050 nmethod 8295 0x0000012a00ff5e10 code [0x0000012a00ff6900, 0x0000012a00fff9d8]
Event: 19.096 Thread 0x0000012a77dbe050 8296       3       org.eclipse.jdt.internal.compiler.classfmt.AnnotationMethodInfo::createAnnotationMethod (575 bytes)
Event: 19.097 Thread 0x0000012a77dbe050 nmethod 8296 0x0000012a01005f10 code [0x0000012a01006380, 0x0000012a01008738]
Event: 19.127 Thread 0x0000012a78adcd50 8297 % !   4       org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader::<init> @ 81 (2135 bytes)
Event: 19.270 Thread 0x0000012a77dbe050 8298       3       java.util.concurrent.atomic.AtomicLong::updateAndGet (59 bytes)
Event: 19.270 Thread 0x0000012a77dbe050 nmethod 8298 0x0000012a01009610 code [0x0000012a01009800, 0x0000012a01009d08]
Event: 19.271 Thread 0x0000012a77dbe050 8299       3       org.eclipse.jdt.ls.core.internal.handlers.ProgressReporterManager$ProgressReporter::sendProgress (78 bytes)
Event: 19.271 Thread 0x0000012a77dbe050 nmethod 8299 0x0000012a01009f90 code [0x0000012a0100a1a0, 0x0000012a0100a7f8]
Event: 19.279 Thread 0x0000012a77dbe050 8300       3       org.eclipse.jdt.internal.core.index.DiskIndex::writeDocumentNumbers (384 bytes)
Event: 19.280 Thread 0x0000012a77dbe050 nmethod 8300 0x0000012a0100aa10 code [0x0000012a0100aca0, 0x0000012a0100b9c8]
Event: 19.288 Thread 0x0000012a77dbe050 8301       1       org.eclipse.core.internal.jobs.InternalJob::setWaitQueueStamp (6 bytes)
Event: 19.289 Thread 0x0000012a77dbe050 nmethod 8301 0x0000012a0818bd10 code [0x0000012a0818bea0, 0x0000012a0818bf68]
Event: 19.313 Thread 0x0000012a77dbe050 8303       3       org.eclipse.core.runtime.SubMonitor::consume (134 bytes)
Event: 19.313 Thread 0x0000012a77dbe050 nmethod 8303 0x0000012a0100bf90 code [0x0000012a0100c160, 0x0000012a0100c5b8]
Event: 19.325 Thread 0x0000012a77dbe050 8305       3       org.eclipse.core.runtime.SubMonitor::<init> (57 bytes)
Event: 19.325 Thread 0x0000012a77dbe050 nmethod 8305 0x0000012a0100c690 code [0x0000012a0100c840, 0x0000012a0100ca80]
Event: 19.325 Thread 0x0000012a77dbe050 8306       3       java.util.HashSet::remove (20 bytes)
Event: 19.325 Thread 0x0000012a77dbe050 nmethod 8306 0x0000012a0100cb90 code [0x0000012a0100cd40, 0x0000012a0100cf70]

GC Heap History (20 events):
Event: 19.139 GC heap before
{Heap before GC invocations=165 (full 3):
 PSYoungGen      total 6656K, used 5360K [0x00000000d5580000, 0x00000000d5f00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 2048K, 36% used [0x00000000d5b00000,0x00000000d5bbc010,0x00000000d5d00000)
  to   space 1024K, 0% used [0x00000000d5e00000,0x00000000d5e00000,0x00000000d5f00000)
 ParOldGen       total 244736K, used 244381K [0x0000000080000000, 0x000000008ef00000, 0x00000000d5580000)
  object space 244736K, 99% used [0x0000000080000000,0x000000008eea7498,0x000000008ef00000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.140 GC heap after
{Heap after GC invocations=165 (full 3):
 PSYoungGen      total 5632K, used 640K [0x00000000d5580000, 0x00000000d5f00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1024K, 62% used [0x00000000d5e00000,0x00000000d5ea0000,0x00000000d5f00000)
  to   space 1024K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5e00000)
 ParOldGen       total 245248K, used 244971K [0x0000000080000000, 0x000000008ef80000, 0x00000000d5580000)
  object space 245248K, 99% used [0x0000000080000000,0x000000008ef3afa0,0x000000008ef80000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.151 GC heap before
{Heap before GC invocations=166 (full 3):
 PSYoungGen      total 5632K, used 5248K [0x00000000d5580000, 0x00000000d5f00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1024K, 62% used [0x00000000d5e00000,0x00000000d5ea0000,0x00000000d5f00000)
  to   space 1024K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5e00000)
 ParOldGen       total 245248K, used 244971K [0x0000000080000000, 0x000000008ef80000, 0x00000000d5580000)
  object space 245248K, 99% used [0x0000000080000000,0x000000008ef3afa0,0x000000008ef80000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.152 GC heap after
{Heap after GC invocations=166 (full 3):
 PSYoungGen      total 5632K, used 640K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1024K, 62% used [0x00000000d5d00000,0x00000000d5da0000,0x00000000d5e00000)
  to   space 512K, 0% used [0x00000000d5e00000,0x00000000d5e00000,0x00000000d5e80000)
 ParOldGen       total 245760K, used 245491K [0x0000000080000000, 0x000000008f000000, 0x00000000d5580000)
  object space 245760K, 99% used [0x0000000080000000,0x000000008efbcfa0,0x000000008f000000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.164 GC heap before
{Heap before GC invocations=167 (full 3):
 PSYoungGen      total 5632K, used 5248K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1024K, 62% used [0x00000000d5d00000,0x00000000d5da0000,0x00000000d5e00000)
  to   space 512K, 0% used [0x00000000d5e00000,0x00000000d5e00000,0x00000000d5e80000)
 ParOldGen       total 245760K, used 245491K [0x0000000080000000, 0x000000008f000000, 0x00000000d5580000)
  object space 245760K, 99% used [0x0000000080000000,0x000000008efbcfa0,0x000000008f000000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.166 GC heap after
{Heap after GC invocations=167 (full 3):
 PSYoungGen      total 5120K, used 512K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 512K, 100% used [0x00000000d5e00000,0x00000000d5e80000,0x00000000d5e80000)
  to   space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
 ParOldGen       total 246272K, used 246259K [0x0000000080000000, 0x000000008f080000, 0x00000000d5580000)
  object space 246272K, 99% used [0x0000000080000000,0x000000008f07cfa8,0x000000008f080000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.176 GC heap before
{Heap before GC invocations=168 (full 3):
 PSYoungGen      total 5120K, used 5120K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 512K, 100% used [0x00000000d5e00000,0x00000000d5e80000,0x00000000d5e80000)
  to   space 1536K, 0% used [0x00000000d5b80000,0x00000000d5b80000,0x00000000d5d00000)
 ParOldGen       total 246272K, used 246259K [0x0000000080000000, 0x000000008f080000, 0x00000000d5580000)
  object space 246272K, 99% used [0x0000000080000000,0x000000008f07cfa8,0x000000008f080000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.178 GC heap after
{Heap after GC invocations=168 (full 3):
 PSYoungGen      total 6144K, used 672K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1536K, 43% used [0x00000000d5b80000,0x00000000d5c28000,0x00000000d5d00000)
  to   space 1024K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5e80000)
 ParOldGen       total 246784K, used 246723K [0x0000000080000000, 0x000000008f100000, 0x00000000d5580000)
  object space 246784K, 99% used [0x0000000080000000,0x000000008f0f0fa8,0x000000008f100000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.215 GC heap before
{Heap before GC invocations=169 (full 3):
 PSYoungGen      total 6144K, used 5280K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1536K, 43% used [0x00000000d5b80000,0x00000000d5c28000,0x00000000d5d00000)
  to   space 1024K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5e80000)
 ParOldGen       total 246784K, used 246723K [0x0000000080000000, 0x000000008f100000, 0x00000000d5580000)
  object space 246784K, 99% used [0x0000000080000000,0x000000008f0f0fa8,0x000000008f100000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.216 GC heap after
{Heap after GC invocations=169 (full 3):
 PSYoungGen      total 5632K, used 576K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1024K, 56% used [0x00000000d5d80000,0x00000000d5e10000,0x00000000d5e80000)
  to   space 1024K, 0% used [0x00000000d5c80000,0x00000000d5c80000,0x00000000d5d80000)
 ParOldGen       total 247296K, used 247275K [0x0000000080000000, 0x000000008f180000, 0x00000000d5580000)
  object space 247296K, 99% used [0x0000000080000000,0x000000008f17afa8,0x000000008f180000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.232 GC heap before
{Heap before GC invocations=170 (full 3):
 PSYoungGen      total 5632K, used 5184K [0x00000000d5580000, 0x00000000d5e80000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1024K, 56% used [0x00000000d5d80000,0x00000000d5e10000,0x00000000d5e80000)
  to   space 1024K, 0% used [0x00000000d5c80000,0x00000000d5c80000,0x00000000d5d80000)
 ParOldGen       total 247296K, used 247275K [0x0000000080000000, 0x000000008f180000, 0x00000000d5580000)
  object space 247296K, 99% used [0x0000000080000000,0x000000008f17afa8,0x000000008f180000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.233 GC heap after
{Heap after GC invocations=170 (full 3):
 PSYoungGen      total 5632K, used 960K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 1024K, 93% used [0x00000000d5c80000,0x00000000d5d70000,0x00000000d5d80000)
  to   space 512K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5e00000)
 ParOldGen       total 247808K, used 247795K [0x0000000080000000, 0x000000008f200000, 0x00000000d5580000)
  object space 247808K, 99% used [0x0000000080000000,0x000000008f1fcfa8,0x000000008f200000)
 Metaspace       used 63281K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.283 GC heap before
{Heap before GC invocations=171 (full 3):
 PSYoungGen      total 5632K, used 5568K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 1024K, 93% used [0x00000000d5c80000,0x00000000d5d70000,0x00000000d5d80000)
  to   space 512K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5e00000)
 ParOldGen       total 247808K, used 247795K [0x0000000080000000, 0x000000008f200000, 0x00000000d5580000)
  object space 247808K, 99% used [0x0000000080000000,0x000000008f1fcfa8,0x000000008f200000)
 Metaspace       used 63283K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.284 GC heap after
{Heap after GC invocations=171 (full 3):
 PSYoungGen      total 5120K, used 192K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 512K, 37% used [0x00000000d5d80000,0x00000000d5db0000,0x00000000d5e00000)
  to   space 512K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5d80000)
 ParOldGen       total 248320K, used 248251K [0x0000000080000000, 0x000000008f280000, 0x00000000d5580000)
  object space 248320K, 99% used [0x0000000080000000,0x000000008f26efa8,0x000000008f280000)
 Metaspace       used 63283K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.296 GC heap before
{Heap before GC invocations=172 (full 3):
 PSYoungGen      total 5120K, used 4800K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 512K, 37% used [0x00000000d5d80000,0x00000000d5db0000,0x00000000d5e00000)
  to   space 512K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5d80000)
 ParOldGen       total 248320K, used 248251K [0x0000000080000000, 0x000000008f280000, 0x00000000d5580000)
  object space 248320K, 99% used [0x0000000080000000,0x000000008f26efa8,0x000000008f280000)
 Metaspace       used 63283K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.297 GC heap after
{Heap after GC invocations=172 (full 3):
 PSYoungGen      total 5120K, used 512K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 512K, 100% used [0x00000000d5d00000,0x00000000d5d80000,0x00000000d5d80000)
  to   space 512K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5e00000)
 ParOldGen       total 248832K, used 248331K [0x0000000080000000, 0x000000008f300000, 0x00000000d5580000)
  object space 248832K, 99% used [0x0000000080000000,0x000000008f282fa8,0x000000008f300000)
 Metaspace       used 63283K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.316 GC heap before
{Heap before GC invocations=173 (full 3):
 PSYoungGen      total 5120K, used 5120K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 512K, 100% used [0x00000000d5d00000,0x00000000d5d80000,0x00000000d5d80000)
  to   space 512K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5e00000)
 ParOldGen       total 248832K, used 248331K [0x0000000080000000, 0x000000008f300000, 0x00000000d5580000)
  object space 248832K, 99% used [0x0000000080000000,0x000000008f282fa8,0x000000008f300000)
 Metaspace       used 63284K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.317 GC heap after
{Heap after GC invocations=173 (full 3):
 PSYoungGen      total 5120K, used 288K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 512K, 56% used [0x00000000d5d80000,0x00000000d5dc8000,0x00000000d5e00000)
  to   space 512K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5d80000)
 ParOldGen       total 248832K, used 248411K [0x0000000080000000, 0x000000008f300000, 0x00000000d5580000)
  object space 248832K, 99% used [0x0000000080000000,0x000000008f296fa8,0x000000008f300000)
 Metaspace       used 63284K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.493 GC heap before
{Heap before GC invocations=174 (full 3):
 PSYoungGen      total 5120K, used 4896K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 100% used [0x00000000d5580000,0x00000000d5a00000,0x00000000d5a00000)
  from space 512K, 56% used [0x00000000d5d80000,0x00000000d5dc8000,0x00000000d5e00000)
  to   space 512K, 0% used [0x00000000d5d00000,0x00000000d5d00000,0x00000000d5d80000)
 ParOldGen       total 248832K, used 248411K [0x0000000080000000, 0x000000008f300000, 0x00000000d5580000)
  object space 248832K, 99% used [0x0000000080000000,0x000000008f296fa8,0x000000008f300000)
 Metaspace       used 63285K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}
Event: 19.494 GC heap after
{Heap after GC invocations=174 (full 3):
 PSYoungGen      total 5120K, used 483K [0x00000000d5580000, 0x00000000d5e00000, 0x0000000100000000)
  eden space 4608K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d5a00000)
  from space 512K, 94% used [0x00000000d5d00000,0x00000000d5d78ca8,0x00000000d5d80000)
  to   space 512K, 0% used [0x00000000d5d80000,0x00000000d5d80000,0x00000000d5e00000)
 ParOldGen       total 248832K, used 248515K [0x0000000080000000, 0x000000008f300000, 0x00000000d5580000)
  object space 248832K, 99% used [0x0000000080000000,0x000000008f2b0fa8,0x000000008f300000)
 Metaspace       used 63285K, committed 64640K, reserved 1114112K
  class space    used 7214K, committed 7808K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.033 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.087 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.092 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.094 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.097 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.113 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.187 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.308 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 3.036 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna14366339538133383123.dll
Event: 8.955 Loaded shared library C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
Event: 9.683 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 9.686 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
Event: 10.576 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
Event: 10.707 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll

Deoptimization events (20 events):
Event: 19.075 Thread 0x0000012a7ef39760 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000012a0816662c relative=0x000000000000794c
Event: 19.075 Thread 0x0000012a7ef39760 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000012a0816662c method=org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader.<init>([B[CZ)V @ 1488 c2
Event: 19.075 Thread 0x0000012a7ef39760 DEOPT PACKING pc=0x0000012a0816662c sp=0x00000007e09fec10
Event: 19.076 Thread 0x0000012a7ef39760 DEOPT UNPACKING pc=0x0000012a07583aa2 sp=0x00000007e09febc0 mode 2
Event: 19.076 Thread 0x0000012a7ef39760 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000012a07feb9b4 relative=0x0000000000006574
Event: 19.076 Thread 0x0000012a7ef39760 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000012a07feb9b4 method=org.eclipse.jdt.internal.compiler.classfmt.ClassFileReader.<init>([B[CZ)V @ 1488 c2
Event: 19.076 Thread 0x0000012a7ef39760 DEOPT PACKING pc=0x0000012a07feb9b4 sp=0x00000007e09fec10
Event: 19.076 Thread 0x0000012a7ef39760 DEOPT UNPACKING pc=0x0000012a07583aa2 sp=0x00000007e09febc0 mode 2
Event: 19.116 Thread 0x0000012a7ef39760 DEOPT PACKING pc=0x0000012a00ff4d6b sp=0x00000007e09fedc0
Event: 19.116 Thread 0x0000012a7ef39760 DEOPT UNPACKING pc=0x0000012a07584242 sp=0x00000007e09fe260 mode 0
Event: 19.212 Thread 0x0000012a7ef39760 DEOPT PACKING pc=0x0000012a00ffdfbe sp=0x00000007e09feb10
Event: 19.212 Thread 0x0000012a7ef39760 DEOPT UNPACKING pc=0x0000012a07584242 sp=0x00000007e09fe0a8 mode 0
Event: 19.219 Thread 0x0000012a7ef39760 DEOPT PACKING pc=0x0000012a00ffdfbe sp=0x00000007e09feb10
Event: 19.219 Thread 0x0000012a7ef39760 DEOPT UNPACKING pc=0x0000012a07584242 sp=0x00000007e09fe0a8 mode 0
Event: 19.278 Thread 0x0000012a7ef39760 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000012a0815c1b0 relative=0x0000000000001830
Event: 19.278 Thread 0x0000012a7ef39760 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000012a0815c1b0 method=org.eclipse.jdt.internal.core.index.DiskIndex.writeDocumentNumbers([ILjava/io/OutputStream;)V @ 127 c2
Event: 19.278 Thread 0x0000012a7ef39760 DEOPT PACKING pc=0x0000012a0815c1b0 sp=0x00000007e09fea70
Event: 19.278 Thread 0x0000012a7ef39760 DEOPT UNPACKING pc=0x0000012a07583aa2 sp=0x00000007e09feab0 mode 2
Event: 19.305 Thread 0x0000012a7ef39760 DEOPT PACKING pc=0x0000012a00fdd759 sp=0x00000007e09feb80
Event: 19.305 Thread 0x0000012a7ef39760 DEOPT UNPACKING pc=0x0000012a07584242 sp=0x00000007e09fe0a0 mode 0

Classes loaded (20 events):
Event: 11.877 Loading class java/util/Collections$SynchronizedRandomAccessList
Event: 11.877 Loading class java/util/Collections$SynchronizedList
Event: 11.877 Loading class java/util/Collections$SynchronizedList done
Event: 11.877 Loading class java/util/Collections$SynchronizedRandomAccessList done
Event: 11.931 Loading class java/util/concurrent/CompletableFuture$Signaller
Event: 11.947 Loading class java/util/concurrent/CompletableFuture$Signaller done
Event: 12.678 Loading class sun/net/www/http/ChunkedInputStream
Event: 12.678 Loading class sun/net/www/http/ChunkedInputStream done
Event: 12.678 Loading class sun/net/www/protocol/http/HttpURLConnection$HttpInputStream
Event: 12.678 Loading class sun/net/www/protocol/http/HttpURLConnection$HttpInputStream done
Event: 12.762 Loading class java/util/Hashtable$KeySet
Event: 12.762 Loading class java/util/Hashtable$KeySet done
Event: 12.855 Loading class java/util/stream/DistinctOps
Event: 12.859 Loading class java/util/stream/DistinctOps done
Event: 12.859 Loading class java/util/stream/DistinctOps$1
Event: 12.859 Loading class java/util/stream/DistinctOps$1 done
Event: 12.860 Loading class java/util/stream/DistinctOps$1$2
Event: 12.860 Loading class java/util/stream/DistinctOps$1$2 done
Event: 14.100 Loading class java/security/DigestInputStream
Event: 14.100 Loading class java/security/DigestInputStream done

Classes unloaded (7 events):
Event: 3.759 Thread 0x0000012a78ad29e0 Unloading class 0x0000012a101a4c00 'java/lang/invoke/LambdaForm$MH+0x0000012a101a4c00'
Event: 3.759 Thread 0x0000012a78ad29e0 Unloading class 0x0000012a101a4800 'java/lang/invoke/LambdaForm$MH+0x0000012a101a4800'
Event: 3.759 Thread 0x0000012a78ad29e0 Unloading class 0x0000012a101a4400 'java/lang/invoke/LambdaForm$MH+0x0000012a101a4400'
Event: 3.759 Thread 0x0000012a78ad29e0 Unloading class 0x0000012a101a4000 'java/lang/invoke/LambdaForm$MH+0x0000012a101a4000'
Event: 3.759 Thread 0x0000012a78ad29e0 Unloading class 0x0000012a101a3c00 'java/lang/invoke/LambdaForm$BMH+0x0000012a101a3c00'
Event: 3.759 Thread 0x0000012a78ad29e0 Unloading class 0x0000012a101a3800 'java/lang/invoke/LambdaForm$DMH+0x0000012a101a3800'
Event: 3.759 Thread 0x0000012a78ad29e0 Unloading class 0x0000012a101a2800 'java/lang/invoke/LambdaForm$DMH+0x0000012a101a2800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 13.432 Thread 0x0000012a5135d220 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5bee6f0}> (0x00000000d5bee6f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.433 Thread 0x0000012a5135d220 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5beed40}> (0x00000000d5beed40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.433 Thread 0x0000012a5135d220 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5befb78}> (0x00000000d5befb78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.434 Thread 0x0000012a5135d220 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5bf01c8}> (0x00000000d5bf01c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.975 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d575a408}> (0x00000000d575a408) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 13.979 Thread 0x0000012a7ef39760 Implicit null exception at 0x0000012a07f01231 to 0x0000012a07f01d28
Event: 14.066 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d55de778}> (0x00000000d55de778) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.088 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d597a370}> (0x00000000d597a370) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.172 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5c6e1b8}> (0x00000000d5c6e1b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.215 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5bd2c78}> (0x00000000d5bd2c78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.264 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d59d8dd0}> (0x00000000d59d8dd0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.473 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5a71830}> (0x00000000d5a71830) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.481 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5ac4858}> (0x00000000d5ac4858) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.565 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5c64418}> (0x00000000d5c64418) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 18.599 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d57db048}> (0x00000000d57db048) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 18.858 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d58851c8}> (0x00000000d58851c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 19.269 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d58562f0}> (0x00000000d58562f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 19.280 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d59fd640}> (0x00000000d59fd640) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 19.311 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d59650a8}> (0x00000000d59650a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 19.323 Thread 0x0000012a7ef39760 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d568f1d0}> (0x00000000d568f1d0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 19.139 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.140 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.151 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.152 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.164 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.166 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.176 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.178 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.215 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.216 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.232 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.233 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.283 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.284 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.296 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.297 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.316 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.317 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 19.493 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 19.494 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a008d7c90
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a008d8510
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a008d8890
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a008dac10
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a008e4910
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a008e9090
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a00939b90
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0093b690
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0093cf90
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0094ac90
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0094b410
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0094b790
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0094bb10
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0094cd90
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a00955a10
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a00967f90
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0096b490
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0096ec10
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a0098cb90
Event: 10.410 Thread 0x0000012a78ad29e0 flushing  nmethod 0x0000012a009c2c90

Events (20 events):
Event: 10.206 Thread 0x0000012a7ef37d20 Thread added: 0x0000012a5135cb90
Event: 10.210 Thread 0x0000012a77dbe050 Thread added: 0x0000012a539d84d0
Event: 10.217 Thread 0x0000012a7ef3a480 Thread added: 0x0000012a5135d220
Event: 10.217 Thread 0x0000012a5135cb90 Thread exited: 0x0000012a5135cb90
Event: 11.244 Thread 0x0000012a7ef3a480 Thread added: 0x0000012a5135cb90
Event: 11.245 Thread 0x0000012a5135cb90 Thread exited: 0x0000012a5135cb90
Event: 11.291 Thread 0x0000012a539d84d0 Thread exited: 0x0000012a539d84d0
Event: 11.305 Thread 0x0000012a50750890 Thread exited: 0x0000012a50750890
Event: 11.403 Thread 0x0000012a77dbe050 Thread added: 0x0000012a50750890
Event: 11.503 Thread 0x0000012a7ef3a480 Thread added: 0x0000012a5135cb90
Event: 11.504 Thread 0x0000012a5135cb90 Thread exited: 0x0000012a5135cb90
Event: 11.592 Thread 0x0000012a50750890 Thread exited: 0x0000012a50750890
Event: 12.570 Thread 0x0000012a77dbe050 Thread added: 0x0000012a50750890
Event: 12.779 Thread 0x0000012a7ef3a480 Thread added: 0x0000012a5135cb90
Event: 12.779 Thread 0x0000012a5135cb90 Thread exited: 0x0000012a5135cb90
Event: 12.906 Thread 0x0000012a77dbe050 Thread added: 0x0000012a53801c50
Event: 12.959 Thread 0x0000012a7ef3a480 Thread added: 0x0000012a5135cb90
Event: 12.983 Thread 0x0000012a5135cb90 Thread exited: 0x0000012a5135cb90
Event: 14.562 Thread 0x0000012a53801c50 Thread exited: 0x0000012a53801c50
Event: 15.289 Thread 0x0000012a50750890 Thread exited: 0x0000012a50750890


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff83ffb0000 - 0x00007ff83fff5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff861060000 - 0x00007ff861078000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ff860720000 - 0x00007ff860758000 	C:\Windows\system32\rsaenh.dll
0x00007ff8615f0000 - 0x00007ff86161e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ff861050000 - 0x00007ff86105c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ff860b40000 - 0x00007ff860b7b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ff8626f0000 - 0x00007ff8626f8000 	C:\Windows\System32\NSI.dll
0x00007ff83c130000 - 0x00007ff83c179000 	C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna14366339538133383123.dll
0x00007ff85c020000 - 0x00007ff85c037000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ff85bf90000 - 0x00007ff85bfad000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ff856940000 - 0x00007ff856967000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ff856830000 - 0x00007ff85683a000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ff83da10000 - 0x00007ff83da1b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ff84cdc0000 - 0x00007ff84cdcc000 	C:\Windows\SYSTEM32\secur32.dll
0x00007ff861620000 - 0x00007ff861652000 	C:\Windows\SYSTEM32\SSPICLI.DLL
0x00007ff8275f0000 - 0x00007ff8275fe000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
0x00007ff861c70000 - 0x00007ff861dcd000 	C:\Windows\System32\CRYPT32.dll
0x00007ff8611e0000 - 0x00007ff861207000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ff8611a0000 - 0x00007ff8611db000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ff860b80000 - 0x00007ff860c4a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00000000605a0000 - 0x00000000605c6000 	C:\Program Files\Bonjour\mdnsNSP.dll
0x00007ff85aa70000 - 0x00007ff85aa7a000 	C:\Windows\System32\rasadhlp.dll
0x00007ff85a6a0000 - 0x00007ff85a720000 	C:\Windows\System32\fwpuclnt.dll
0x00007ff820100000 - 0x00007ff820109000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-146731693;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Program Files\Bonjour

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-1f1461b6953a0a9a22ba68cf6041b0bd-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:46 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (1332M free)
TotalPageFile size 61318M (AvailPageFile size 229M)
current process WorkingSet (physical memory assigned to process): 322M, peak: 489M
current process commit charge ("private bytes"): 385M, peak: 1675M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
