#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=45472, tid=14484
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-2f249d3684dfb80cf5364d86c295de80-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:16:54 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 1.022624 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x00000188f1423490):  JavaThread "main"             [_thread_in_vm, id=14484, stack(0x00000032e9c00000,0x00000032e9d00000) (1024K)]

Stack: [0x00000032e9c00000,0x00000032e9d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0x8a41ee]
V  [jvm.dll+0x670575]
V  [jvm.dll+0x1e474c]
V  [jvm.dll+0x1e451e]
V  [jvm.dll+0x672e72]
V  [jvm.dll+0x672c92]
V  [jvm.dll+0x670f4e]
V  [jvm.dll+0x26a886]
V  [jvm.dll+0x216127]
V  [jvm.dll+0x20bbae]
V  [jvm.dll+0x5ae58c]
V  [jvm.dll+0x82186d]
V  [jvm.dll+0x472052]
V  [jvm.dll+0x485466]
C  [java.dll+0x1657]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 917  java.lang.ClassLoader.defineClass0(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class; java.base@21.0.7 (0 bytes) @ 0x0000018887b2a03c [0x0000018887b29f40+0x00000000000000fc]
j  java.lang.System$2.defineClass(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BLjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;+17 java.base@21.0.7
j  java.lang.invoke.MethodHandles$Lookup$ClassDefiner.defineClass(ZLjava/lang/Object;)Ljava/lang/Class;+57 java.base@21.0.7
j  java.lang.invoke.InvokerBytecodeGenerator.loadMethod([B)Ljava/lang/invoke/MemberName;+22 java.base@21.0.7
j  java.lang.invoke.InvokerBytecodeGenerator.generateCustomizedCode(Ljava/lang/invoke/LambdaForm;Ljava/lang/invoke/MethodType;)Ljava/lang/invoke/MemberName;+30 java.base@21.0.7
j  java.lang.invoke.LambdaForm.compileToBytecode()V+69 java.base@21.0.7
j  java.lang.invoke.LambdaForm.customize(Ljava/lang/invoke/MethodHandle;)Ljava/lang/invoke/LambdaForm;+49 java.base@21.0.7
j  java.lang.invoke.MethodHandle$1.apply(Ljava/lang/invoke/LambdaForm;)Ljava/lang/invoke/LambdaForm;+5 java.base@21.0.7
j  java.lang.invoke.MethodHandle$1.apply(Ljava/lang/Object;)Ljava/lang/Object;+5 java.base@21.0.7
j  java.lang.invoke.MethodHandle.updateForm(Ljava/util/function/Function;)V+22 java.base@21.0.7
j  java.lang.invoke.MethodHandle.customize()V+9 java.base@21.0.7
j  java.lang.invoke.MethodHandle.maybeCustomize()V+23 java.base@21.0.7
J 1025 c1 java.lang.invoke.Invokers.maybeCustomize(Ljava/lang/invoke/MethodHandle;)V java.base@21.0.7 (5 bytes) @ 0x0000018880194224 [0x0000018880194140+0x00000000000000e4]
J 1024 c1 java.lang.invoke.Invokers.checkCustomized(Ljava/lang/invoke/MethodHandle;)V java.base@21.0.7 (23 bytes) @ 0x0000018880193d9c [0x0000018880193cc0+0x00000000000000dc]
j  java.lang.invoke.Invokers$Holder.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+14 java.base@21.0.7
j  jdk.internal.reflect.DirectConstructorHandleAccessor.invokeImpl([Ljava/lang/Object;)Ljava/lang/Object;+40 java.base@21.0.7
j  jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance([Ljava/lang/Object;)Ljava/lang/Object;+60 java.base@21.0.7
j  java.lang.reflect.Constructor.newInstanceWithCaller([Ljava/lang/Object;ZLjava/lang/Class;)Ljava/lang/Object;+41 java.base@21.0.7
j  java.lang.reflect.Constructor.newInstance([Ljava/lang/Object;)Ljava/lang/Object;+30 java.base@21.0.7
j  java.security.Provider$Service.newInstanceOf()Ljava/lang/Object;+9 java.base@21.0.7
j  java.security.Provider$Service.newInstanceUtil(Ljava/lang/Class;Ljava/lang/Object;)Ljava/lang/Object;+5 java.base@21.0.7
j  java.security.Provider$Service.newInstance(Ljava/lang/Object;)Ljava/lang/Object;+216 java.base@21.0.7
j  sun.security.jca.GetInstance.getInstance(Ljava/security/Provider$Service;Ljava/lang/Class;)Lsun/security/jca/GetInstance$Instance;+2 java.base@21.0.7
j  sun.security.jca.GetInstance.getInstance(Ljava/lang/String;Ljava/lang/Class;Ljava/lang/String;Ljava/security/Provider;)Lsun/security/jca/GetInstance$Instance;+7 java.base@21.0.7
j  java.security.Security.getImpl(Ljava/lang/String;Ljava/lang/String;Ljava/security/Provider;)[Ljava/lang/Object;+7 java.base@21.0.7
j  java.security.MessageDigest.getInstance(Ljava/lang/String;Ljava/security/Provider;)Ljava/security/MessageDigest;+25 java.base@21.0.7
j  sun.security.util.ManifestEntryVerifier.setEntry(Ljava/lang/String;Ljava/util/jar/JarEntry;)V+228 java.base@21.0.7
j  java.util.jar.JarVerifier.beginEntry(Ljava/util/jar/JarEntry;Lsun/security/util/ManifestEntryVerifier;)V+218 java.base@21.0.7
j  java.util.jar.JarVerifier$VerifierStream.<init>(Ljava/util/jar/Manifest;Ljava/util/jar/JarEntry;Ljava/io/InputStream;Ljava/util/jar/JarVerifier;)V+47 java.base@21.0.7
j  java.util.jar.JarFile.getInputStream(Ljava/util/zip/ZipEntry;)Ljava/io/InputStream;+84 java.base@21.0.7
j  jdk.internal.loader.URLClassPath$JarLoader$2.getInputStream()Ljava/io/InputStream;+11 java.base@21.0.7
j  jdk.internal.loader.Resource.cachedInputStream()Ljava/io/InputStream;+9 java.base@21.0.7
j  jdk.internal.loader.Resource.getByteBuffer()Ljava/nio/ByteBuffer;+1 java.base@21.0.7
j  java.net.URLClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class;+132 java.base@21.0.7
j  java.net.URLClassLoader$1.run()Ljava/lang/Class;+43 java.base@21.0.7
j  java.net.URLClassLoader$1.run()Ljava/lang/Object;+1 java.base@21.0.7
J 1171 c1 java.security.AccessController.executePrivileged(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;Ljava/lang/Class;)Ljava/lang/Object; java.base@21.0.7 (65 bytes) @ 0x00000188801dc9c4 [0x00000188801dc860+0x0000000000000164]
j  java.security.AccessController.doPrivileged(Ljava/security/PrivilegedExceptionAction;Ljava/security/AccessControlContext;)Ljava/lang/Object;+13 java.base@21.0.7
j  java.net.URLClassLoader.findClass(Ljava/lang/String;)Ljava/lang/Class;+13 java.base@21.0.7
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+69 java.base@21.0.7
J 1026 c1 java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class; java.base@21.0.7 (7 bytes) @ 0x00000188801946d4 [0x00000188801945c0+0x0000000000000114]
v  ~StubRoutines::call_stub 0x000001888753100d
j  org.eclipse.osgi.internal.connect.ConnectHookConfigurator.addHooks(Lorg/eclipse/osgi/internal/hookregistry/HookRegistry;)V+14
j  org.eclipse.osgi.internal.hookregistry.HookRegistry.loadConfigurators(Ljava/util/List;Ljava/util/List;)V+52
j  org.eclipse.osgi.internal.hookregistry.HookRegistry.initialize()V+120
j  org.eclipse.osgi.internal.framework.EquinoxContainer.<init>(Ljava/util/Map;Lorg/osgi/framework/connect/ModuleConnector;)V+141
j  org.eclipse.osgi.launch.Equinox.<init>(Ljava/util/Map;Lorg/osgi/framework/connect/ModuleConnector;)V+10
j  org.eclipse.osgi.launch.Equinox.<init>(Ljava/util/Map;)V+3
j  org.eclipse.core.runtime.adaptor.EclipseStarter.startup([Ljava/lang/String;Ljava/lang/Runnable;)Lorg/osgi/framework/BundleContext;+28
j  org.eclipse.core.runtime.adaptor.EclipseStarter.run([Ljava/lang/String;Ljava/lang/Runnable;)Ljava/lang/Object;+21
j  java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+11 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x000001889008a800.invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+54 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x0000018890002c00.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+22 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invokeImpl(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+72 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+23 java.base@21.0.7
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+102 java.base@21.0.7
j  org.eclipse.equinox.launcher.Main.invokeFramework([Ljava/lang/String;[Ljava/net/URL;)V+155
j  org.eclipse.equinox.launcher.Main.basicRun([Ljava/lang/String;)V+191
j  org.eclipse.equinox.launcher.Main.run([Ljava/lang/String;)I+4
j  org.eclipse.equinox.launcher.Main.main([Ljava/lang/String;)V+10
v  ~StubRoutines::call_stub 0x000001888753100d

Compiled method (n/a) 1079  917     n 0       java.lang.ClassLoader::defineClass0 (native)
 total in heap  [0x0000018887b29d90,0x0000018887b2a248] = 1208
 relocation     [0x0000018887b29ef0,0x0000018887b29f28] = 56
 main code      [0x0000018887b29f40,0x0000018887b2a241] = 769
 stub code      [0x0000018887b2a241,0x0000018887b2a248] = 7

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x000001888f4309d8} 'defineClass0' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # parm0:    rdx:rdx   = 'java/lang/ClassLoader'
  # parm1:    r8:r8     = 'java/lang/Class'
  # parm2:    r9:r9     = 'java/lang/String'
  # parm3:    rdi:rdi   = '[B'
  # parm4:    rsi       = int
  # parm5:    rcx       = int
  # parm6:    [sp+0xb0]   = 'java/security/ProtectionDomain'  (sp of caller)
  # parm7:    [sp+0xb8]   = boolean
  # parm8:    [sp+0xc0]   = int
  # parm9:    [sp+0xc8]   = 'java/lang/Object'
  0x0000018887b29f40: 448b 5208 | 49bb 0000 | 008f 8801 | 0000 4d03 | d349 3bc2 | 0f84 0600 

  0x0000018887b29f58: ;   {runtime_call ic_miss_stub}
  0x0000018887b29f58: 0000 e921 | 45a5 ff90 
[Verified Entry Point]
  0x0000018887b29f60: 8984 2400 | 80ff ff55 | 488b ec48 | 81ec a000 | 0000 6690 | 4181 7f20 | 0100 0000 

  0x0000018887b29f7c: ;   {runtime_call StubRoutines (final stubs)}
  0x0000018887b29f7c: 7405 e85d | eaa3 ff48 | 837d 2800 | 488d 4528 | 480f 4445 | 2848 8944 | 2458 4863 | 4520 4889 
  0x0000018887b29f9c: 4424 5048 | 6345 1848 | 8944 2448 | 4883 7d10 | 0048 8d45 | 1048 0f44 | 4510 4889 | 4424 4048 
  0x0000018887b29fbc: 894c 2438 | 4889 7424 | 3048 897c | 2478 4883 | ff00 488d | 4424 7848 | 0f44 4424 | 7848 8944 
  0x0000018887b29fdc: 2428 4c89 | 4c24 7049 | 83f9 0048 | 8d44 2470 | 480f 4444 | 2470 4889 | 4424 204c | 8944 2468 
  0x0000018887b29ffc: 4983 f800 | 4c8d 4c24 | 684c 0f44 | 4c24 6848 | 8954 2460 | 4883 fa00 | 4c8d 4424 | 604c 0f44 
  0x0000018887b2a01c: ;   {oop(a 'java/lang/Class'{0x00000000d6ecbb38} = 'java/lang/ClassLoader')}
  0x0000018887b2a01c: 4424 6049 | be38 bbec | d600 0000 | 004c 89b4 | 2490 0000 | 004c 8db4 | 2490 0000 | 0049 8bd6 
  0x0000018887b2a03c: ;   {internal_word}
  0x0000018887b2a03c: c5f8 7749 | ba3c a0b2 | 8788 0100 | 004d 8997 | a003 0000 | 4989 a798 

  0x0000018887b2a054: ;   {external_word}
  0x0000018887b2a054: 0300 0049 | ba3d 5b44 | efff 7f00 | 0041 803a | 000f 844e | 0000 0052 | 4150 4151 

  0x0000018887b2a070: ;   {metadata({method} {0x000001888f4309d8} 'defineClass0' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018887b2a070: 48ba d009 | 438f 8801 | 0000 498b | cf48 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 

  0x0000018887b2a08c: ;   {runtime_call}
  0x0000018887b2a08c: 83ec 0848 | b850 4bee | eeff 7f00 | 00ff d048 | 83c4 08e9 | 0c00 0000 

  0x0000018887b2a0a4: ;   {runtime_call}
  0x0000018887b2a0a4: 48b8 504b | eeee ff7f | 0000 ffd0 | 4883 c420 | 4159 4158 | 5a49 8d8f | b803 0000 | 41c7 8744 
  0x0000018887b2a0c4: 0400 0004 

  0x0000018887b2a0c8: ;   {runtime_call}
  0x0000018887b2a0c8: 0000 0048 | b81c 154e | 00f8 7f00 | 00ff d0c5 | f877 41c7 | 8744 0400 | 0005 0000 | 00f0 8344 
  0x0000018887b2a0e8: 24c0 0049 | 3baf 4804 | 0000 0f87 | 0e00 0000 | 4183 bf40 | 0400 0000 | 0f84 2b00 | 0000 c5f8 
  0x0000018887b2a108: 7748 8945 | f849 8bcf | 4c8b e448 | 83ec 2048 

  0x0000018887b2a118: ;   {runtime_call}
  0x0000018887b2a118: 83e4 f048 | b800 d1b9 | eeff 7f00 | 00ff d049 | 8be4 4d33 | e448 8b45 | f841 c787 | 4404 0000 
  0x0000018887b2a138: 0800 0000 | 4183 bfc0 | 0400 0002 | 0f84 ca00 

  0x0000018887b2a148: ;   {external_word}
  0x0000018887b2a148: 0000 49ba | 3d5b 44ef | ff7f 0000 | 4180 3a00 | 0f84 4c00 | 0000 4889 

  0x0000018887b2a160: ;   {metadata({method} {0x000001888f4309d8} 'defineClass0' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000018887b2a160: 45f8 48ba | d009 438f | 8801 0000 | 498b cf48 | 83ec 2040 | f6c4 0f0f | 8419 0000 | 0048 83ec 
  0x0000018887b2a180: ;   {runtime_call}
  0x0000018887b2a180: 0848 b850 | 4bee eeff | 7f00 00ff | d048 83c4 | 08e9 0c00 

  0x0000018887b2a194: ;   {runtime_call}
  0x0000018887b2a194: 0000 48b8 | 504b eeee | ff7f 0000 | ffd0 4883 | c420 488b | 45f8 49c7 | 8798 0300 | 0000 0000 
  0x0000018887b2a1b4: 0049 c787 | a003 0000 | 0000 0000 | c5f8 7748 | 85c0 0f84 | 2500 0000 | a803 0f85 | 0800 0000 
  0x0000018887b2a1d4: 488b 00e9 | 1500 0000 | a801 0f85 | 0900 0000 | 488b 40fe | e904 0000 | 0048 8b40 | ff49 8b8f 
  0x0000018887b2a1f4: 2804 0000 | c781 0001 | 0000 0000 | 0000 c949 | 837f 0800 | 0f85 0100 

  0x0000018887b2a20c: ;   {runtime_call StubRoutines (initial stubs)}
  0x0000018887b2a20c: 0000 c3e9 | ec6c a0ff | c5f8 7748 | 8945 f84c | 8be4 4883 | ec20 4883 

  0x0000018887b2a224: ;   {runtime_call}
  0x0000018887b2a224: e4f0 48b8 | 3083 eeee | ff7f 0000 | ffd0 498b | e44d 33e4 | 488b 45f8 | e909 ffff | fff4 f4f4 
  0x0000018887b2a244: f4f4 f4f4 
[/MachCode]


Compiled method (c1) 1103 1025       3       java.lang.invoke.Invokers::maybeCustomize (5 bytes)
 total in heap  [0x0000018880193f90,0x0000018880194388] = 1016
 relocation     [0x00000188801940f0,0x0000018880194130] = 64
 main code      [0x0000018880194140,0x00000188801942a8] = 360
 stub code      [0x00000188801942a8,0x00000188801942f0] = 72
 metadata       [0x00000188801942f0,0x0000018880194308] = 24
 scopes data    [0x0000018880194308,0x0000018880194320] = 24
 scopes pcs     [0x0000018880194320,0x0000018880194370] = 80
 dependencies   [0x0000018880194370,0x0000018880194378] = 8
 nul chk table  [0x0000018880194378,0x0000018880194388] = 16

[Constant Pool (empty)]

[MachCode]
[Verified Entry Point]
  # {method} {0x000001888f3d9c18} 'maybeCustomize' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers'
  # parm0:    rdx:rdx   = 'java/lang/invoke/MethodHandle'
  #           [sp+0x30]  (sp of caller)
  0x0000018880194140: 8984 2400 | 80ff ff55 | 4883 ec20 | 4181 7f20 | 0100 0000 

  0x0000018880194154: ;   {runtime_call StubRoutines (final stubs)}
  0x0000018880194154: 7405 e885 

  0x0000018880194158: ;   {metadata(method data for {method} {0x000001888f3d9c18} 'maybeCustomize' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x0000018880194158: 483d 0748 | bef0 bb2a | fb88 0100 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 fe07 
  0x0000018880194178: 0000 85ff | 0f84 bd00 | 0000 483b | 0248 8bf2 

  0x0000018880194188: ;   {metadata(method data for {method} {0x000001888f3d9c18} 'maybeCustomize' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x0000018880194188: 48bf f0bb | 2afb 8801 | 0000 8b76 | 0849 ba00 | 0000 8f88 | 0100 0049 | 03f2 483b | b720 0100 
  0x00000188801941a8: 0075 0d48 | 8387 2801 | 0000 01e9 | 6000 0000 | 483b b730 | 0100 0075 | 0d48 8387 | 3801 0000 
  0x00000188801941c8: 01e9 4a00 | 0000 4883 | bf20 0100 | 0000 7517 | 4889 b720 | 0100 0048 | c787 2801 | 0000 0100 
  0x00000188801941e8: 0000 e929 | 0000 0048 | 83bf 3001 | 0000 0075 | 1748 89b7 | 3001 0000 | 48c7 8738 | 0100 0001 
  0x0000018880194208: 0000 00e9 | 0800 0000 | 4883 8710 | 0100 0001 | 0f1f 8000 

  0x000001888019421c: ;   {optimized virtual_call}
  0x000001888019421c: 0000 00e8 

  0x0000018880194220: ; ImmutableOopMap {}
                      ;*invokevirtual maybeCustomize {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.Invokers::maybeCustomize@1
  0x0000018880194220: 8900 0000 

  0x0000018880194224: ;   {other}
  0x0000018880194224: 0f1f 8400 | 9402 0000 | 4883 c420 

  0x0000018880194230: ;   {poll_return}
  0x0000018880194230: 5d49 3ba7 | 4804 0000 | 0f87 2700 

  0x000001888019423c: ;   {metadata({method} {0x000001888f3d9c18} 'maybeCustomize' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x000001888019423c: 0000 c349 | ba10 9c3d | 8f88 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000018880194254: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000018880194254: ffff e825 

  0x0000018880194258: ; ImmutableOopMap {rdx=Oop }
                      ;*synchronization entry
                      ; - java.lang.invoke.Invokers::maybeCustomize@-1
  0x0000018880194258: 294a 07e9 | 22ff ffff 

  0x0000018880194260: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000018880194260: e89b ec40 

  0x0000018880194264: ; ImmutableOopMap {rdx=Oop }
                      ;*invokevirtual maybeCustomize {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.Invokers::maybeCustomize@1
                      ;   {internal_word}
  0x0000018880194264: 0749 ba31 | 4219 8088 | 0100 004d | 8997 6004 

  0x0000018880194274: ;   {runtime_call SafepointBlob}
  0x0000018880194274: 0000 e985 | 083f 0749 | 8b87 f804 | 0000 49c7 | 87f8 0400 | 0000 0000 | 0049 c787 | 0005 0000 
  0x0000018880194294: 0000 0000 | 4883 c420 

  0x000001888019429c: ;   {runtime_call unwind_exception Runtime1 stub}
  0x000001888019429c: 5de9 5efb | 4007 f4f4 | f4f4 f4f4 
[Stub Code]
  0x00000188801942a8: ;   {no_reloc}
  0x00000188801942a8: 0f1f 4400 

  0x00000188801942ac: ;   {static_stub}
  0x00000188801942ac: 0048 bb90 | 1406 8f88 

  0x00000188801942b4: ;   {runtime_call I2C/C2I adapters}
  0x00000188801942b4: 0100 00e9 | 2799 3e07 
[Exception Handler]
  0x00000188801942bc: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x00000188801942bc: e83f ce40 

  0x00000188801942c0: ;   {external_word}
  0x00000188801942c0: 0748 b990 | 7f1a efff | 7f00 0048 

  0x00000188801942cc: ;   {runtime_call}
  0x00000188801942cc: 83e4 f048 | b8e0 44dd | eeff 7f00 | 00ff d0f4 
[Deopt Handler Code]
  0x00000188801942dc: ;   {section_word}
  0x00000188801942dc: 49ba dc42 | 1980 8801 | 0000 4152 

  0x00000188801942e8: ;   {runtime_call DeoptimizationBlob}
  0x00000188801942e8: e9b3 fa3e | 07f4 f4f4 
[/MachCode]


Compiled method (c1) 1128 1024       3       java.lang.invoke.Invokers::checkCustomized (23 bytes)
 total in heap  [0x0000018880193b10,0x0000018880193f50] = 1088
 relocation     [0x0000018880193c70,0x0000018880193cc0] = 80
 main code      [0x0000018880193cc0,0x0000018880193e40] = 384
 stub code      [0x0000018880193e40,0x0000018880193e80] = 64
 metadata       [0x0000018880193e80,0x0000018880193e90] = 16
 scopes data    [0x0000018880193e90,0x0000018880193ec0] = 48
 scopes pcs     [0x0000018880193ec0,0x0000018880193f30] = 112
 dependencies   [0x0000018880193f30,0x0000018880193f38] = 8
 nul chk table  [0x0000018880193f38,0x0000018880193f50] = 24

[Constant Pool (empty)]

[MachCode]
[Verified Entry Point]
  # {method} {0x000001888f3d93d8} 'checkCustomized' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers'
  # parm0:    rdx:rdx   = 'java/lang/invoke/MethodHandle'
  #           [sp+0x40]  (sp of caller)
  0x0000018880193cc0: 8984 2400 | 80ff ff55 | 4883 ec30 | 4181 7f20 | 0100 0000 

  0x0000018880193cd4: ;   {runtime_call StubRoutines (final stubs)}
  0x0000018880193cd4: 7405 e805 

  0x0000018880193cd8: ;   {metadata(method data for {method} {0x000001888f3d93d8} 'checkCustomized' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x0000018880193cd8: 4d3d 0748 | be18 b92a | fb88 0100 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 fe07 
  0x0000018880193cf8: 0000 85ff | 0f84 b500 

  0x0000018880193d00: ;   {metadata(method data for {method} {0x000001888f3d93d8} 'checkCustomized' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x0000018880193d00: 0000 48be | 18b9 2afb | 8801 0000 | 4883 8610 | 0100 0001 

  0x0000018880193d14: ;   {metadata(method data for {method} {0x000001888f095ff0} 'isCompileConstant' '(Ljava/lang/Object;)Z' in 'java/lang/invoke/MethodHandleImpl')}
  0x0000018880193d14: 48be c8ba | 2afb 8801 | 0000 8bbe | cc00 0000 | 83c7 0289 | becc 0000 | 0081 e7fe | ff1f 0085 
  0x0000018880193d34: ff0f 849d 

  0x0000018880193d38: ;   {metadata(method data for {method} {0x000001888f3d93d8} 'checkCustomized' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x0000018880193d38: 0000 0048 | be18 b92a | fb88 0100 | 00ff 8620 | 0100 008b | 7214 8b76 | 1c48 85f6 

  0x0000018880193d54: ;   {metadata(method data for {method} {0x000001888f3d93d8} 'checkCustomized' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x0000018880193d54: 48be 18b9 | 2afb 8801 | 0000 48c7 | c740 0100 | 0075 0748 | c7c7 5001 | 0000 488b | 1c3e 488d 
  0x0000018880193d74: 5b01 4889 | 1c3e 0f85 | 2400 0000 

  0x0000018880193d80: ;   {metadata(method data for {method} {0x000001888f3d93d8} 'checkCustomized' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x0000018880193d80: 48be 18b9 | 2afb 8801 | 0000 4883 | 8660 0100 | 0001 0f1f 

  0x0000018880193d94: ;   {static_call}
  0x0000018880193d94: 4400 00e8 

  0x0000018880193d98: ; ImmutableOopMap {}
                      ;*invokestatic maybeCustomize {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.Invokers::checkCustomized@19
  0x0000018880193d98: a403 0000 

  0x0000018880193d9c: ;   {other}
  0x0000018880193d9c: 0f1f 8400 | 8c02 0000 | 4883 c430 

  0x0000018880193da8: ;   {poll_return}
  0x0000018880193da8: 5d49 3ba7 | 4804 0000 | 0f87 4d00 

  0x0000018880193db4: ;   {metadata({method} {0x000001888f3d93d8} 'checkCustomized' '(Ljava/lang/invoke/MethodHandle;)V' in 'java/lang/invoke/Invokers')}
  0x0000018880193db4: 0000 c349 | bad0 933d | 8f88 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000018880193dcc: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000018880193dcc: ffff e8ad 

  0x0000018880193dd0: ; ImmutableOopMap {rdx=Oop }
                      ;*synchronization entry
                      ; - java.lang.invoke.Invokers::checkCustomized@-1
  0x0000018880193dd0: 2d4a 07e9 | 2aff ffff 

  0x0000018880193dd8: ;   {metadata({method} {0x000001888f095ff0} 'isCompileConstant' '(Ljava/lang/Object;)Z' in 'java/lang/invoke/MethodHandleImpl')}
  0x0000018880193dd8: 49ba e85f | 098f 8801 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x0000018880193dec: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000018880193dec: ffff ffe8 

  0x0000018880193df0: ; ImmutableOopMap {rdx=Oop }
                      ;*synchronization entry
                      ; - java.lang.invoke.MethodHandleImpl::isCompileConstant@-1
                      ; - java.lang.invoke.Invokers::checkCustomized@1
  0x0000018880193df0: 8c2d 4a07 | e942 ffff 

  0x0000018880193df8: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000018880193df8: ffe8 02f1 

  0x0000018880193dfc: ; ImmutableOopMap {rdx=Oop }
                      ;*getfield form {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.invoke.Invokers::checkCustomized@9
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000018880193dfc: 4007 e8fd 

  0x0000018880193e00: ; ImmutableOopMap {rdx=Oop }
                      ;*getfield customized {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.invoke.Invokers::checkCustomized@12
                      ;   {internal_word}
  0x0000018880193e00: f040 0749 | baa9 3d19 | 8088 0100 | 004d 8997 | 6004 0000 

  0x0000018880193e14: ;   {runtime_call SafepointBlob}
  0x0000018880193e14: e9e7 0c3f | 0749 8b87 | f804 0000 | 49c7 87f8 | 0400 0000 | 0000 0049 | c787 0005 | 0000 0000 
  0x0000018880193e34: 0000 4883 

  0x0000018880193e38: ;   {runtime_call unwind_exception Runtime1 stub}
  0x0000018880193e38: c430 5de9 | c0ff 4007 
[Stub Code]
  0x0000018880193e40: ;   {no_reloc}
  0x0000018880193e40: 48bb 0000 | 0000 0000 

  0x0000018880193e48: ;   {runtime_call nmethod}
  0x0000018880193e48: 0000 e9fb 

  0x0000018880193e4c: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x0000018880193e4c: ffff ffe8 | acd2 4007 

  0x0000018880193e54: ;   {external_word}
  0x0000018880193e54: 48b9 907f | 1aef ff7f | 0000 4883 

  0x0000018880193e60: ;   {runtime_call}
  0x0000018880193e60: e4f0 48b8 | e044 ddee | ff7f 0000 

  0x0000018880193e6c: ;   {section_word}
  0x0000018880193e6c: ffd0 f449 | ba6f 3e19 | 8088 0100 

  0x0000018880193e78: ;   {runtime_call DeoptimizationBlob}
  0x0000018880193e78: 0041 52e9 | 20ff 3e07 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000188f9d949f0, length=13, elements={
0x00000188f1423490, 0x00000188f1351ce0, 0x00000188f9a65730, 0x00000188f9a66440,
0x00000188f9a67d40, 0x00000188f9a69d80, 0x00000188f9a6c7e0, 0x00000188f9a6d610,
0x00000188f9ad0ab0, 0x00000188f9b5e290, 0x00000188f9da4de0, 0x00000188f9cb73d0,
0x00000188f9cb7ca0
}

Java Threads: ( => current thread )
=>0x00000188f1423490 JavaThread "main"                              [_thread_in_vm, id=14484, stack(0x00000032e9c00000,0x00000032e9d00000) (1024K)]
  0x00000188f1351ce0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=42832, stack(0x00000032ea000000,0x00000032ea100000) (1024K)]
  0x00000188f9a65730 JavaThread "Finalizer"                  daemon [_thread_blocked, id=29880, stack(0x00000032ea100000,0x00000032ea200000) (1024K)]
  0x00000188f9a66440 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=46672, stack(0x00000032ea200000,0x00000032ea300000) (1024K)]
  0x00000188f9a67d40 JavaThread "Attach Listener"            daemon [_thread_blocked, id=20244, stack(0x00000032ea300000,0x00000032ea400000) (1024K)]
  0x00000188f9a69d80 JavaThread "Service Thread"             daemon [_thread_blocked, id=15772, stack(0x00000032ea400000,0x00000032ea500000) (1024K)]
  0x00000188f9a6c7e0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=45432, stack(0x00000032ea500000,0x00000032ea600000) (1024K)]
  0x00000188f9a6d610 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=34464, stack(0x00000032ea600000,0x00000032ea700000) (1024K)]
  0x00000188f9ad0ab0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=8356, stack(0x00000032ea700000,0x00000032ea800000) (1024K)]
  0x00000188f9b5e290 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=38684, stack(0x00000032ea800000,0x00000032ea900000) (1024K)]
  0x00000188f9da4de0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=46568, stack(0x00000032ea900000,0x00000032eaa00000) (1024K)]
  0x00000188f9cb73d0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=23908, stack(0x00000032eaa00000,0x00000032eab00000) (1024K)]
  0x00000188f9cb7ca0 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=29480, stack(0x00000032eab00000,0x00000032eac00000) (1024K)]
Total: 13

Other Threads:
  0x00000188f1343580 VMThread "VM Thread"                           [id=9436, stack(0x00000032e9f00000,0x00000032ea000000) (1024K)]
  0x00000188f1452180 WatcherThread "VM Periodic Task Thread"        [id=44676, stack(0x00000032e9e00000,0x00000032e9f00000) (1024K)]
  0x00000188f14429b0 WorkerThread "GC Thread#0"                     [id=14344, stack(0x00000032e9d00000,0x00000032e9e00000) (1024K)]
  0x00000188ff5fbc80 WorkerThread "GC Thread#1"                     [id=32724, stack(0x00000032eac00000,0x00000032ead00000) (1024K)]
  0x00000188ff7295e0 WorkerThread "GC Thread#2"                     [id=37772, stack(0x00000032ead00000,0x00000032eae00000) (1024K)]
  0x00000188ff729980 WorkerThread "GC Thread#3"                     [id=28924, stack(0x00000032eae00000,0x00000032eaf00000) (1024K)]
  0x00000188ff5fc020 WorkerThread "GC Thread#4"                     [id=46572, stack(0x00000032eaf00000,0x00000032eb000000) (1024K)]
  0x00000188ff5fc3c0 WorkerThread "GC Thread#5"                     [id=19008, stack(0x00000032eb000000,0x00000032eb100000) (1024K)]
  0x00000188ff5fc760 WorkerThread "GC Thread#6"                     [id=46692, stack(0x00000032eb100000,0x00000032eb200000) (1024K)]
Total: 9

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45c308] Metaspace_lock - owner thread: 0x00000188f1423490

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001888f000000-0x000001888fba0000-0x000001888fba0000), size 12189696, SharedBaseAddress: 0x000001888f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000018890000000-0x00000188d0000000, reserved size: 1073741824
Narrow klass base: 0x000001888f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 6443K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 11% used [0x00000000d5580000,0x00000000d586b338,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71dfc78,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4783K, committed 4992K, reserved 1114112K
  class space    used 512K, committed 640K, reserved 1048576K

Card table byte_map: [0x00000188f0d50000,0x00000188f1160000] _byte_map_base: 0x00000188f0950000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x00000188f4850000, 0x00000188f6850000)
 End Bits:   [0x00000188f6850000, 0x00000188f8850000)

Polling page: 0x00000188f0a40000

Metaspace:

Usage:
  Non-class:      4.17 MB used.
      Class:    512.33 KB used.
       Both:      4.67 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       4.25 MB (  7%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     640.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       4.88 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  10.87 MB
       Class:  15.39 MB
        Both:  26.26 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 188.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 78.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 264.
num_chunk_merges: 0.
num_chunk_splits: 170.
num_chunks_enlarged: 104.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=465Kb max_used=465Kb free=119535Kb
 bounds [0x0000018887ad0000, 0x0000018887d40000, 0x000001888f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=1918Kb max_used=1918Kb free=118082Kb
 bounds [0x0000018880000000, 0x0000018880270000, 0x0000018887530000]
CodeHeap 'non-nmethods': size=5760Kb used=1198Kb max_used=1223Kb free=4561Kb
 bounds [0x0000018887530000, 0x00000188877a0000, 0x0000018887ad0000]
 total_blobs=1637 nmethods=1177 adapters=367
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.987 Thread 0x00000188f9ad0ab0 1171       3       java.security.AccessController::executePrivileged (65 bytes)
Event: 0.987 Thread 0x00000188f9ad0ab0 nmethod 1171 0x00000188801dc690 code [0x00000188801dc860, 0x00000188801dcb60]
Event: 0.987 Thread 0x00000188f9ad0ab0 1172       3       java.util.HashMap::hash (20 bytes)
Event: 0.987 Thread 0x00000188f9ad0ab0 nmethod 1172 0x00000188801dcc90 code [0x00000188801dce40, 0x00000188801dd048]
Event: 0.987 Thread 0x00000188f9ad0ab0 1173       3       java.util.HashMap::get (19 bytes)
Event: 0.988 Thread 0x00000188f9ad0ab0 nmethod 1173 0x00000188801dd110 code [0x00000188801dd2c0, 0x00000188801dd470]
Event: 0.988 Thread 0x00000188f9ad0ab0 1174       3       java.util.HashMap::getNode (150 bytes)
Event: 0.989 Thread 0x00000188f9ad0ab0 nmethod 1174 0x00000188801dd590 code [0x00000188801dd7e0, 0x00000188801de310]
Event: 0.989 Thread 0x00000188f9ad0ab0 1175       3       java.util.ArrayDeque::poll (5 bytes)
Event: 0.989 Thread 0x00000188f9ad0ab0 nmethod 1175 0x00000188801de610 code [0x00000188801de7c0, 0x00000188801de918]
Event: 0.989 Thread 0x00000188f9ad0ab0 1176       1       java.security.Provider::getName (5 bytes)
Event: 0.989 Thread 0x00000188f9ad0ab0 nmethod 1176 0x0000018887b43e10 code [0x0000018887b43fa0, 0x0000018887b44068]
Event: 0.989 Thread 0x00000188f9ad0ab0 1177       1       sun.security.provider.DigestBase::engineGetDigestLength (5 bytes)
Event: 0.989 Thread 0x00000188f9ad0ab0 nmethod 1177 0x0000018887b44110 code [0x0000018887b442a0, 0x0000018887b44368]
Event: 0.990 Thread 0x00000188f9ad0ab0 1178       3       java.util.jar.JarFile::maybeInstantiateVerifier (42 bytes)
Event: 0.990 Thread 0x00000188f9ad0ab0 nmethod 1178 0x00000188801dea10 code [0x00000188801debe0, 0x00000188801def58]
Event: 0.991 Thread 0x00000188f9ad0ab0 1179       3       java.lang.invoke.MethodType::changeReturnType (20 bytes)
Event: 0.992 Thread 0x00000188f9ad0ab0 nmethod 1179 0x00000188801df090 code [0x00000188801df240, 0x00000188801df400]
Event: 0.992 Thread 0x00000188f9ad0ab0 1180       3       java.util.ArrayDeque::inc (12 bytes)
Event: 0.992 Thread 0x00000188f9ad0ab0 nmethod 1180 0x00000188801df490 code [0x00000188801df620, 0x00000188801df748]

GC Heap History (2 events):
Event: 0.923 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4283K, committed 4480K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}
Event: 0.936 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3455K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71dfc78,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4283K, committed 4480K, reserved 1114112K
  class space    used 459K, committed 576K, reserved 1048576K
}

Dll operation events (8 events):
Event: 0.012 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.050 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.197 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.202 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.204 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.208 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.262 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.363 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (20 events):
Event: 0.979 Thread 0x00000188f1423490 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018887b141d4 relative=0x00000000000005b4
Event: 0.979 Thread 0x00000188f1423490 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018887b141d4 method=java.util.Collections$UnmodifiableCollection$1.<init>(Ljava/util/Collections$UnmodifiableCollection;)V @ 17 c2
Event: 0.979 Thread 0x00000188f1423490 DEOPT PACKING pc=0x0000018887b141d4 sp=0x00000032e9cfec90
Event: 0.979 Thread 0x00000188f1423490 DEOPT UNPACKING pc=0x0000018887583aa2 sp=0x00000032e9cfebd8 mode 2
Event: 0.979 Thread 0x00000188f1423490 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018887b31a1c relative=0x000000000000033c
Event: 0.979 Thread 0x00000188f1423490 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018887b31a1c method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.979 Thread 0x00000188f1423490 DEOPT PACKING pc=0x0000018887b31a1c sp=0x00000032e9cfec40
Event: 0.979 Thread 0x00000188f1423490 DEOPT UNPACKING pc=0x0000018887583aa2 sp=0x00000032e9cfead8 mode 2
Event: 0.980 Thread 0x00000188f1423490 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018887b2fb34 relative=0x0000000000000354
Event: 0.980 Thread 0x00000188f1423490 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018887b2fb34 method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.980 Thread 0x00000188f1423490 DEOPT PACKING pc=0x0000018887b2fb34 sp=0x00000032e9cfebe0
Event: 0.980 Thread 0x00000188f1423490 DEOPT UNPACKING pc=0x0000018887583aa2 sp=0x00000032e9cfeae8 mode 2
Event: 0.980 Thread 0x00000188f1423490 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000018887b2e154 relative=0x00000000000000b4
Event: 0.980 Thread 0x00000188f1423490 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000018887b2e154 method=java.util.HashMap.hash(Ljava/lang/Object;)I @ 1 c2
Event: 0.980 Thread 0x00000188f1423490 DEOPT PACKING pc=0x0000018887b2e154 sp=0x00000032e9cfec60
Event: 0.980 Thread 0x00000188f1423490 DEOPT UNPACKING pc=0x0000018887583aa2 sp=0x00000032e9cfebf8 mode 2
Event: 0.980 Thread 0x00000188f1423490 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000018887b141d4 relative=0x00000000000005b4
Event: 0.980 Thread 0x00000188f1423490 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000018887b141d4 method=java.util.Collections$UnmodifiableCollection$1.<init>(Ljava/util/Collections$UnmodifiableCollection;)V @ 17 c2
Event: 0.980 Thread 0x00000188f1423490 DEOPT PACKING pc=0x0000018887b141d4 sp=0x00000032e9cfec90
Event: 0.980 Thread 0x00000188f1423490 DEOPT UNPACKING pc=0x0000018887583aa2 sp=0x00000032e9cfebd8 mode 2

Classes loaded (20 events):
Event: 0.965 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$NonfairSync
Event: 0.965 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$Sync
Event: 0.965 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$Sync done
Event: 0.965 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$NonfairSync done
Event: 0.965 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$Sync$ThreadLocalHoldCounter
Event: 0.965 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$Sync$ThreadLocalHoldCounter done
Event: 0.965 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock
Event: 0.966 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$ReadLock done
Event: 0.966 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock
Event: 0.966 Loading class java/util/concurrent/locks/ReentrantReadWriteLock$WriteLock done
Event: 0.981 Loading class java/net/URLClassLoader$3
Event: 0.981 Loading class java/net/URLClassLoader$3 done
Event: 0.981 Loading class java/net/URLClassLoader$3$1
Event: 0.981 Loading class java/net/URLClassLoader$3$1 done
Event: 0.981 Loading class jdk/internal/loader/URLClassPath$FileLoader
Event: 0.981 Loading class jdk/internal/loader/URLClassPath$FileLoader done
Event: 0.986 Loading class java/lang/ClassFormatError
Event: 0.986 Loading class java/lang/ClassFormatError done
Event: 0.992 Loading class java/lang/invoke/MethodHandle$1
Event: 0.992 Loading class java/lang/invoke/MethodHandle$1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.667 Thread 0x00000188f1423490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d6499838}: sun/net/www/protocol/c/Handler> (0x00000000d6499838) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.681 Thread 0x00000188f1423490 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f7588}> (0x00000000d64f7588) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.682 Thread 0x00000188f1423490 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f8aa0}> (0x00000000d64f8aa0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.682 Thread 0x00000188f1423490 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f98e0}> (0x00000000d64f98e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.683 Thread 0x00000188f1423490 Exception <a 'java/io/FileNotFoundException'{0x00000000d64fa5d8}> (0x00000000d64fa5d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.725 Thread 0x00000188f1423490 Exception <a 'java/io/FileNotFoundException'{0x00000000d6533cf0}> (0x00000000d6533cf0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.734 Thread 0x00000188f1423490 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d656cdb0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d656cdb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.736 Thread 0x00000188f1423490 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65803f8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d65803f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.739 Thread 0x00000188f1423490 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6597650}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6597650) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.757 Thread 0x00000188f1423490 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d662c4f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000d662c4f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.875 Thread 0x00000188f1423490 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6aa6558}: 'java.lang.ClassLoader java.lang.ClassLoader.getPlatformClassLoader(java.lang.Class)'> (0x00000000d6aa6558) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.941 Thread 0x00000188f1423490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d55bdc90}: sun/net/www/protocol/c/Handler> (0x00000000d55bdc90) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.942 Thread 0x00000188f1423490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d55bf010}: sun/net/www/protocol/f/Handler> (0x00000000d55bf010) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.942 Thread 0x00000188f1423490 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d55c0190}: sun/net/www/protocol/c/Handler> (0x00000000d55c0190) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.950 Thread 0x00000188f1423490 Exception <a 'java/io/FileNotFoundException'{0x00000000d55e8d88}> (0x00000000d55e8d88) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.979 Thread 0x00000188f1423490 Implicit null exception at 0x0000018887b3f449 to 0x0000018887b3ff54
Event: 0.979 Thread 0x00000188f1423490 Implicit null exception at 0x0000018887b31741 to 0x0000018887b31a04
Event: 0.979 Thread 0x00000188f1423490 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5796ea0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5796ea0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.980 Thread 0x00000188f1423490 Implicit null exception at 0x0000018887b2f840 to 0x0000018887b2fb1c
Event: 0.980 Thread 0x00000188f1423490 Implicit null exception at 0x0000018887b2e0c0 to 0x0000018887b2e145

ZGC Phase Switch (0 events):
No events

VM Operations (12 events):
Event: 0.139 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.180 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.271 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.271 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.648 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.648 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.661 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.661 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.871 Executing VM operation: ICBufferFull
Event: 0.871 Executing VM operation: ICBufferFull done
Event: 0.923 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 0.936 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (13 events):
Event: 0.026 Thread 0x00000188f1423490 Thread added: 0x00000188f1423490
Event: 0.072 Thread 0x00000188f1423490 Thread added: 0x00000188f1351ce0
Event: 0.072 Thread 0x00000188f1423490 Thread added: 0x00000188f9a65730
Event: 0.072 Thread 0x00000188f1423490 Thread added: 0x00000188f9a66440
Event: 0.072 Thread 0x00000188f1423490 Thread added: 0x00000188f9a67d40
Event: 0.072 Thread 0x00000188f1423490 Thread added: 0x00000188f9a69d80
Event: 0.072 Thread 0x00000188f1423490 Thread added: 0x00000188f9a6c7e0
Event: 0.072 Thread 0x00000188f1423490 Thread added: 0x00000188f9a6d610
Event: 0.111 Thread 0x00000188f1423490 Thread added: 0x00000188f9ad0ab0
Event: 0.130 Thread 0x00000188f1423490 Thread added: 0x00000188f9b5e290
Event: 0.503 Thread 0x00000188f1423490 Thread added: 0x00000188f9da4de0
Event: 0.568 Thread 0x00000188f9ad0ab0 Thread added: 0x00000188f9cb73d0
Event: 0.581 Thread 0x00000188f9a6d610 Thread added: 0x00000188f9cb7ca0


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-2f249d3684dfb80cf5364d86c295de80-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:28 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (3858M free)
TotalPageFile size 61318M (AvailPageFile size 286M)
current process WorkingSet (physical memory assigned to process): 87M, peak: 87M
current process commit charge ("private bytes"): 238M, peak: 238M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
