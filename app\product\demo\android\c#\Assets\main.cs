﻿using UnityEngine;
using System.Runtime.InteropServices;
using System.Collections.Generic;

public class main : MonoBehaviour {
	//如需要测试初始化之后一段时间登录，则将以下变量置为true
	private bool enableLoginClick = false;
	private bool hasLogin = false;

	void OnMouseDown() {
		if (enableLoginClick) {
			if (!hasLogin)
            {
				UserLogin();
				hasLogin = true;
			}
		}
	}

	void Update() {
		if (hasLogin) {
			this.transform.Rotate(Time.deltaTime, 1, 1);
		} else {
			this.transform.Rotate(Time.deltaTime, 0, 0);
		}
	}

	private class MyTssInfoReceiver:tss.TssInfoReceiver{
		public void onReceive(int tssInfoType, string info){
            // 此函数不能被阻塞
			if (tssInfoType == tss.TssInfoPublisher.TSS_INFO_TYPE_DETECT_RESULT) {
				// 处理检测结果 如果不关心，可以忽略
				string plain = Tp2Sdk.Tp2DecTssInfo(info);
				if (plain.Equals("-1"))
					return;
                Debug.Log ("C# Info:" + tssInfoType + "|" + plain);
			}
            else if (tssInfoType == tss.TssInfoPublisher.TSS_INFO_TYPE_HEARTBEAT){
                // 处理心跳，如果不关心，可以忽略
				string plain = Tp2Sdk.Tp2DecTssInfo(info);
				if (plain.Equals("-1"))
					return;
				Debug.Log ("C# Info:" + tssInfoType + "|" + plain);
            }
		}
	}

	private MyTssInfoReceiver mTssInfoReceiver;
	void Awake () {
		// 在游戏启动的第一时间调用
		mTssInfoReceiver = new MyTssInfoReceiver();
		Tp2Sdk.Tp2RegistTssInfoReceiver (mTssInfoReceiver);
		Tp2Sdk.Tp2SdkInitEx(19257, "d5ab8dc7ef67ca92e41d730982c5c602"); 
		Debug.Log ("C#:main::Awake");
	}

	void UserLogin() {
		// 第一次调用在获取用户信息并登录游戏时,如[微信登录].[QQ登录]或[开始游戏]按钮事件触发
		// 在每次获取用户授权信息之后都要调用Tp2UserLogin,如断线重连
		int account_type = (int)Tp2Entry.ENTRY_ID_QZONE;		/*与运营平台相关的帐号类型*/
		int world_id = 101;										/*用户游戏角色的大区信息*/
		string open_id = "B73B36366565F9E02C7525516DCF31C2";	/*与登录平台相关的帐号id*/
		string role_id = "paladin";								/*区分用户创建的不同角色的标识*/

		Tp2Sdk.Tp2UserLogin(account_type, world_id, open_id, role_id);
	}
		

	void Start () {
		if (!enableLoginClick) {
			UserLogin();
			hasLogin = true;

            // 开启Android设备上应用安装列表扫描功能
            // 7.0及之后版本建议游戏在用户登录后调用
            Tp2Sdk.Tp2Ioctl("AllowAPKCollect");

			// 7.3版本之后的版本支持上报举报信息至ACE控制台
			string reported_account_id = "8B57B75C79A3E34E718C";   // 被举报人的账号 openid
			int reported_account_type = ENTRY_ID_QZONE;                 // 被举报人的账号类型
			int report_type = REPORT_TYPE_CHEAT;                          // 举报的类型，辱骂、作弊等
			string report_scene = "5v5_mode";                              // 举报发生的场景, 游戏可自定义，会直接展示在控制台
			string report_reason = "肯定开透视了，我蹲草里不动都能看到我";      // 举报的详情信息，游戏可自定义
			// 举报信息最长为1024Byte，否则在上报时会截断
			string complaintMsg = "ReportComplaint:reportedId=" + reported_account_id + 
					";reportedType="+ reported_account_type.ToString() + 
					";type=" + report_type.ToString() + 
					";scene=" + report_scene + 
					";reason=" + report_reason;
            Tp2Sdk.Tp2Ioctl(complaintMsg);
		}
	}

	void OnApplicationPause (bool pause) {
		if (pause) {
			Tp2Sdk.Tp2SetGamestatus (Tp2GameStatus.BACKEND);
		} else {
			Tp2Sdk.Tp2SetGamestatus (Tp2GameStatus.FRONTEND);
		}
	}

	void OnGUI () {
		GUI.skin.label.fontSize = 100;
		if (hasLogin) {
			GUI.Label(new Rect(10, 10, Screen.width, Screen.height), "已调用登录接口");
		} else {
			GUI.Label(new Rect(10, 10, Screen.width, Screen.height), "点击正方形进行登录");
		}
	}
		
}
