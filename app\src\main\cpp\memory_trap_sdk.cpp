#include "memory_trap_sdk.h"
#include <android/log.h>
#include <sys/mman.h>
#include <unistd.h>
#include <cstring>
#include <random>
#include <algorithm>
#include <queue>
#include <cmath>
#include <cstdio>

// 内存陷阱检测器声明

// 声明comprehensive_detector中的函数
namespace MemoryTrapSDK {
    bool startComprehensiveDetection();
    void setCallbackComprehensive(DetectionCallback callback);
    void setTrapsComprehensive(const std::vector<TrapInfo>& traps);
    bool detectSuspiciousProcesses();
    bool detectSuspiciousMemoryMaps();
    bool detectDebugger();
}
namespace MemoryTrapSDK {
    void setMemTrapCallback(DetectionCallback callback);
    bool startMemTrapDetection(uint32_t target_value, size_t trap_count);
    void stopMemTrapDetection();
}

#define TAG "MemoryTrapSDK"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)

namespace MemoryTrapSDK {

// ========== 全局变量陷阱（.data和.bss段） ==========

// .data段陷阱（初始化的全局变量）
static int g_data_trap1[1024] = {0};     // 4KB
static int g_data_trap2[2048] = {0};     // 8KB  
static int g_data_trap3[4096] = {0};     // 16KB
static int g_data_trap4[1024] = {0};     // 4KB

// .bss段陷阱（未初始化的全局变量）
static int g_bss_trap1[1024];           // 4KB
static int g_bss_trap2[2048];           // 8KB

// ========== TrapManager内部实现 ==========

// 静态成员初始化
TrapManager::Impl* TrapManager::Impl::instance = nullptr;

// Impl类函数的具体实现
TrapManager::Impl::Impl() {
    instance = this;
}

TrapManager::Impl::~Impl() {
    cleanup();
    instance = nullptr;
}

bool TrapManager::Impl::startDetection(DetectionCallback callback) {
    // 实现检测启动逻辑
    if (is_detecting.load()) {
        LOGW("检测已在运行中");
        return true;
    }
    
    // 安装信号处理器
    if (!installSignalHandler()) {
        LOGE("❌ 信号处理器安装失败");
        return false;
    }
    
    // 初始化分析线程
    initAnalysisThread();
    
    // 设置回调
    this->callback = callback;
    
    // 启动检测逻辑
    is_detecting.store(true);
    LOGI("检测已启动");
    return true;
}

void TrapManager::Impl::stopDetection() {
    // 实现检测停止逻辑
    if (!is_detecting.load()) {
        LOGW("检测未在运行");
        return;
    }
    
    // 停止分析线程
    stopAnalysisThread();
    
    // 卸载信号处理器
    uninstallSignalHandler();
    
    // 停止检测逻辑
    is_detecting.store(false);
    callback = nullptr;
    LOGI("检测已停止");
}

void TrapManager::Impl::cleanup() {
    // 实现清理逻辑
    stopDetection();
    std::lock_guard<std::mutex> lock(this->traps_mutex);
    traps.clear();
    LOGI("资源清理完成");
}

std::vector<TrapInfo> TrapManager::Impl::getTrapInfos() {
    std::lock_guard<std::mutex> lock(this->traps_mutex);
    return traps;
}

size_t TrapManager::Impl::createAnonymousTraps(const TrapConfig& config) {
    LOGI("🎯 创建Anonymous匿名映射陷阱...");

    size_t created_count = 0;
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> offset_dist(0, 0x1000000); // 16MB范围随机偏移

    for (size_t i = 0; i < config.trap_count; ++i) {
        // 在GG常扫描的低地址范围分配
        uintptr_t base_addr = 0x60000000; // 1.5GB基址
        uintptr_t target_addr = base_addr + offset_dist(gen);
        target_addr &= ~(getpagesize() - 1); // 页对齐

        // 创建匿名映射内存
        void* trap_addr = mmap((void*)target_addr, config.trap_size,
                               PROT_READ | PROT_WRITE,
                               MAP_ANONYMOUS | MAP_PRIVATE | MAP_FIXED,
                               -1, 0);

        if (trap_addr != MAP_FAILED) {
            // 填充陷阱数据
            uint32_t target_count = fillTrapData(trap_addr, config.trap_size,
                                                 config.target_value, config.fill_interval);

            // 创建陷阱信息
            TrapInfo trap_info{};
            trap_info.address = trap_addr;
            trap_info.size = config.trap_size;
            trap_info.region = MemoryRegion::ANONYMOUS;
            trap_info.target_count = target_count;
            trap_info.is_protected = false;
            trap_info.created_time = std::chrono::steady_clock::now();

            // 添加到陷阱列表
            {
                std::lock_guard<std::mutex> lock(traps_mutex);
                traps.push_back(trap_info);
            }

            created_count++;
            LOGI("✅ Anonymous陷阱[%zu]: 地址=%p, 大小=%zu, 目标数=%u",
                 i, trap_addr, config.trap_size, target_count);
        } else {
            LOGE("❌ Anonymous陷阱创建失败[%zu]: %s", i, strerror(errno));
        }
    }

    LOGI("🎉 Anonymous陷阱创建完成: 成功%zu个", created_count);
    return created_count;
}

size_t TrapManager::Impl::createDataSegmentTraps(const TrapConfig& config) {
    LOGI("🎯 创建Data段陷阱...");

    size_t created_count = 0;

    // 使用预分配的.data段数组
    static int data_trap_array[4096] = {0}; // 16KB初始化数组

    struct {
        void* addr;
        size_t size;
    } trap_arrays[] = {
        {data_trap_array, sizeof(data_trap_array)}
    };

    for (auto& array : trap_arrays) {
        // 填充陷阱数据
        uint32_t target_count = fillTrapData(array.addr, array.size,
                                             config.target_value, config.fill_interval);

        // 创建陷阱信息
        TrapInfo trap_info{};
        trap_info.address = array.addr;
        trap_info.size = array.size;
        trap_info.region = MemoryRegion::CPP_DATA;
        trap_info.target_count = target_count;
        trap_info.is_protected = false;
        trap_info.created_time = std::chrono::steady_clock::now();

        // 添加到陷阱列表
        {
            std::lock_guard<std::mutex> lock(traps_mutex);
            traps.push_back(trap_info);
        }

        created_count++;
        LOGI("✅ Data段陷阱: 地址=%p, 大小=%zu, 目标数=%u",
             array.addr, array.size, target_count);
    }

    LOGI("🎉 Data段陷阱创建完成: 成功%zu个", created_count);
    return created_count;
}

size_t TrapManager::Impl::createBssSegmentTraps(const TrapConfig& config) {
    LOGI("🎯 创建BSS段陷阱...");

    size_t created_count = 0;

    // 使用预分配的.bss段数组
    static int bss_trap_array[4096]; // 16KB未初始化数组

    struct {
        void* addr;
        size_t size;
    } trap_arrays[] = {
        {bss_trap_array, sizeof(bss_trap_array)}
    };

    for (auto& array : trap_arrays) {
        // 填充陷阱数据
        uint32_t target_count = fillTrapData(array.addr, array.size,
                                             config.target_value, config.fill_interval);

        // 创建陷阱信息
        TrapInfo trap_info{};
        trap_info.address = array.addr;
        trap_info.size = array.size;
        trap_info.region = MemoryRegion::CPP_BSS;
        trap_info.target_count = target_count;
        trap_info.is_protected = false;
        trap_info.created_time = std::chrono::steady_clock::now();

        // 添加到陷阱列表
        {
            std::lock_guard<std::mutex> lock(traps_mutex);
            traps.push_back(trap_info);
        }

        created_count++;
        LOGI("✅ BSS段陷阱: 地址=%p, 大小=%zu, 目标数=%u",
             array.addr, array.size, target_count);
    }

    LOGI("🎉 BSS段陷阱创建完成: 成功%zu个", created_count);
    return created_count;
}

uint32_t TrapManager::Impl::fillTrapData(void* addr, size_t size, uint32_t target_value, uint32_t interval) {
    if (!addr || size == 0) return 0;

    uint32_t target_count = 0;
    uint32_t* data = static_cast<uint32_t*>(addr);
    size_t data_count = size / sizeof(uint32_t);

    // 按间隔填充目标值
    for (size_t i = 0; i < data_count; i += interval) {
        data[i] = target_value;
        target_count++;
    }

    // 填充干扰数据
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<uint32_t> dis(1, 1000000);

    for (size_t i = 0; i < data_count; i++) {
        if (i % interval != 0) { // 跳过目标值位置
            data[i] = dis(gen);
        }
    }

    LOGI("✅ 陷阱数据填充完成: 总计%u个目标值", target_count);
    return target_count;
}

bool TrapManager::Impl::protectTrap(void* addr, size_t size) {
    if (mprotect(addr, size, PROT_NONE) == -1) {
        LOGE("❌ mprotect失败: %s", strerror(errno));
        return false;
    }
    LOGI("🔒 陷阱保护成功: 地址=%p, 大小=%zu", addr, size);
    return true;
}

// 信号处理相关函数实现
bool TrapManager::Impl::installSignalHandler() {
    struct sigaction sa;
    memset(&sa, 0, sizeof(sa));
    sa.sa_sigaction = TrapManager::Impl::signalHandler;
    sa.sa_flags = SA_SIGINFO | SA_RESTART;
    
    if (sigaction(SIGSEGV, &sa, &instance->original_sigsegv) == -1) {
        LOGE("安装SIGSEGV处理器失败: %s", strerror(errno));
        return false;
    }
    
    if (sigaction(SIGBUS, &sa, &instance->original_sigbus) == -1) {
        LOGE("安装SIGBUS处理器失败: %s", strerror(errno));
        sigaction(SIGSEGV, &instance->original_sigsegv, nullptr);
        return false;
    }
    
    LOGI("✅ 信号处理器安装成功");
    return true;
}

void TrapManager::Impl::uninstallSignalHandler() {
    if (instance) {
        sigaction(SIGSEGV, &instance->original_sigsegv, nullptr);
        sigaction(SIGBUS, &instance->original_sigbus, nullptr);
        LOGI("✅ 信号处理器卸载成功");
    }
}

void TrapManager::Impl::initAnalysisThread() {
    // TODO: 实现分析线程初始化
    LOGI("初始化分析线程");
}

void TrapManager::Impl::stopAnalysisThread() {
    // TODO: 实现分析线程停止
    LOGI("停止分析线程");
}

void TrapManager::Impl::signalHandler(int signum, siginfo_t* info, void* context) {
    // TODO: 实现信号处理
    LOGI("信号处理: %d", signum);
}

// ========== TrapManager实现 ==========

TrapManager::TrapManager() : impl_(std::make_unique<Impl>()) {
}

TrapManager::~TrapManager() = default;

bool TrapManager::initialize() {
    LOGI("🔧 初始化陷阱管理器...");
    return true;
}

bool TrapManager::createTraps(const TrapConfig& config) {
    LOGI("🎯 创建陷阱: 区域=%d, 数量=%zu, 大小=%zu, 目标值=%u", 
         (int)config.region, config.trap_count, config.trap_size, config.target_value);
    
    std::lock_guard<std::mutex> lock(impl_->traps_mutex);
    size_t created_count = 0;
    
    switch (config.region) {
        case MemoryRegion::ANONYMOUS:
            created_count = impl_->createAnonymousTraps(config);
            break;
            
        case MemoryRegion::CPP_DATA:
            created_count = impl_->createDataSegmentTraps(config);
            break;
            
        case MemoryRegion::CPP_BSS:
            created_count = impl_->createBssSegmentTraps(config);
            break;
            
        default:
            LOGW("暂不支持的内存区域: %d", (int)config.region);
            break;
    }
    
    LOGI("✅ 成功创建 %zu 个陷阱", created_count);
    return created_count > 0;
}

bool TrapManager::startDetection() {
    // 使用已设置的回调启动检测
    return impl_->startDetection(impl_->callback);
}

void TrapManager::stopDetection() {
    impl_->stopDetection();
}

void TrapManager::setDetectionCallback(DetectionCallback callback) {
    std::lock_guard<std::mutex> lock(impl_->traps_mutex);
    impl_->callback = callback;
}

size_t TrapManager::getActiveTrapCount() const {
    auto traps = impl_->getTrapInfos();
    size_t count = 0;
    for (const auto& trap : traps) {
        if (trap.is_protected) {
            count++;
        }
    }
    return count;
}

size_t TrapManager::getTotalTrapCount() const {
    auto traps = impl_->getTrapInfos();
    return traps.size();
}

std::vector<TrapInfo> TrapManager::getTrapInfos() const {
    return impl_->getTrapInfos();
}

void TrapManager::cleanup() {
    impl_->cleanup();
}

// ========== 陷阱创建实现 ==========

// 将保持不变，这些函数实现已经正确位于TrapManager类中

// ========== 全局函数实现 ==========

// 初始化内存陷阱系统
bool initializeMemoryTrapSystem() {
    return MemoryTrapSDK::getInstance().initialize();
}

// ========== MemoryTrapSDK实现 ==========

MemoryTrapSDK::MemoryTrapSDK() 
    : trap_manager_(std::make_unique<TrapManager>())
    , is_initialized_(false)
    , is_detecting_(false)
{
    LOGI("MemoryTrapSDK构造函数");
}

MemoryTrapSDK::~MemoryTrapSDK() {
    LOGI("MemoryTrapSDK析构函数");
    cleanup();
}

MemoryTrapSDK& MemoryTrapSDK::getInstance() {
    static MemoryTrapSDK instance;
    return instance;
}

bool MemoryTrapSDK::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (is_initialized_.load()) {
        LOGW("SDK已经初始化");
        return true;
    }

    if (!trap_manager_->initialize()) {
        LOGE("陷阱管理器初始化失败");
        return false;
    }

    is_initialized_.store(true);
    LOGI("✅ SDK初始化成功");
    return true;
}

size_t MemoryTrapSDK::deployAntiGGTraps(uint32_t target_value, DetectionCallback callback) {
    if (!is_initialized_.load()) {
        LOGE("SDK未初始化");
        return 0;
    }

    size_t total_deployed = 0;
    
    // 配置匿名区域陷阱（最重要）
    TrapConfig anon_config{};
    anon_config.region = MemoryRegion::ANONYMOUS;
    anon_config.trap_count = 50;  // 50个匿名陷阱
    anon_config.trap_size = 4096; // 4KB每个陷阱
    anon_config.target_value = target_value;
    anon_config.fill_interval = 16; // 每16个DWORD填充一个目标值
    
    total_deployed += trap_manager_->createTraps(anon_config);
    
    // 配置.data段陷阱
    TrapConfig data_config{};
    data_config.region = MemoryRegion::CPP_DATA;
    data_config.trap_count = 1;
    data_config.trap_size = 16384; // 16KB
    data_config.target_value = target_value;
    data_config.fill_interval = 8;
    
    total_deployed += trap_manager_->createTraps(data_config);
    
    // 配置.bss段陷阱
    TrapConfig bss_config{};
    bss_config.region = MemoryRegion::CPP_BSS;
    bss_config.trap_count = 1;
    bss_config.trap_size = 8192; // 8KB
    bss_config.target_value = target_value;
    bss_config.fill_interval = 8;
    
    total_deployed += trap_manager_->createTraps(bss_config);
    
    // 设置回调
    if (callback) {
        setDetectionCallback(callback);
    }
    
    LOGI("🎯 部署Anti-GG陷阱完成: 总计%zu个", total_deployed);
    return total_deployed;
}

size_t MemoryTrapSDK::deployCustomTraps(const std::vector<TrapConfig>& configs, DetectionCallback callback) {
    if (!is_initialized_.load()) {
        LOGE("SDK未初始化");
        return 0;
    }

    size_t total_deployed = 0;
    for (const auto& config : configs) {
        total_deployed += trap_manager_->createTraps(config);
    }
    
    // 设置回调
    if (callback) {
        setDetectionCallback(callback);
    }
    
    LOGI("🎯 部署自定义陷阱完成: 总计%zu个", total_deployed);
    return total_deployed;
}

bool MemoryTrapSDK::startDetection(DetectionCallback callback) {
    if (!is_initialized_.load()) {
        LOGE("SDK未初始化");
        return false;
    }
    
    if (is_detecting_.load()) {
        LOGW("检测已在运行中");
        return true;
    }
    
    // 设置回调
    if (callback) {
        setDetectionCallback(callback);
    }
    
    // 启动陷阱管理器检测
    if (!trap_manager_->startDetection()) {
        LOGE("❌ 启动检测失败");
        return false;
    }
    
    is_detecting_.store(true);
    LOGI("✅ 检测启动成功");
    return true;
}

bool MemoryTrapSDK::startDetection() {
    return startDetection(nullptr);
}

void MemoryTrapSDK::stopDetection() {
    if (!is_detecting_.load()) {
        LOGW("检测未在运行");
        return;
    }
    
    trap_manager_->stopDetection();
    is_detecting_.store(false);
    LOGI("⏹️ 检测已停止");
}

void MemoryTrapSDK::setDetectionCallback(DetectionCallback callback) {
    std::lock_guard<std::mutex> lock(mutex_);
    detection_callback_ = callback;
    // 将回调传递给陷阱管理器
    trap_manager_->setDetectionCallback(callback);
}

size_t MemoryTrapSDK::getActiveTrapCount() const {
    if (!is_initialized_.load()) {
        return 0;
    }
    return trap_manager_->getActiveTrapCount();
}

size_t MemoryTrapSDK::getTotalTrapCount() const {
    if (!is_initialized_.load()) {
        return 0;
    }
    return trap_manager_->getTotalTrapCount();
}

MemoryTrapSDK::Statistics MemoryTrapSDK::getStatistics() const {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!is_initialized_.load()) {
        return {};
    }
    
    Statistics stats = statistics_;
    stats.total_traps = getTotalTrapCount();
    stats.protected_traps = getActiveTrapCount();
    // TODO: 实现检测次数和最后检测时间的统计
    
    return stats;
}

void MemoryTrapSDK::cleanup() {
    std::lock_guard<std::mutex> lock(mutex_);
    if (!is_initialized_.load()) {
        return;
    }
    
    LOGI("🧹 清理SDK资源...");
    stopDetection();
    trap_manager_->cleanup();
    is_initialized_.store(false);
    is_detecting_.store(false);
    LOGI("✅ SDK资源清理完成");
}

} // namespace MemoryTrapSDK
