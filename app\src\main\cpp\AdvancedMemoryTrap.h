#pragma once

#include <vector>
#include <functional>
#include <atomic>
#include <mutex>
#include <map>
#include <chrono>
#include <memory>
#include <thread>
#include <queue>
#include <random>
#include <array>
#include <android/log.h>
#include <sys/types.h>
#include <unistd.h>
#include "memory_trap_sdk.h"

#define TAG "AdvancedMemoryTrap"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, __VA_ARGS__)

namespace AdvancedAntiCheat {

// 前向声明
class MemoryTrapSDK;

// 内存区域枚举
enum class MemoryRegion {
    CH_CPP_HEAP = 0,    // C++堆 - 40%陷阱
    JH_JAVA_HEAP = 1,   // Java堆 - 30%陷阱
    A_ANONYMOUS = 2,    // 匿名内存 - 32%陷阱
    CD_DATA_SEGMENT = 3,// C++数据段 - 38%陷阱 (ACESDK重点区域)
    CA_CPP_ALLOC = 4,   // C++分配区 - 10%陷阱
    CB_CPP_BSS = 5,     // C++ .bss段 - 20%陷阱 (ACESDK重点区域)
    UNKNOWN = 6
};

// 陷阱类型枚举
enum class TrapType {
    VALUE_DECOY,        // 诱饵值陷阱
    POINTER_CHAIN,      // 指针链陷阱
    GUARD_PAGE,         // 保护页陷阱
    CRC32_TRAP,         // CRC校验陷阱
    TIMED_ACCESS,       // 定时访问陷阱
    API_HOOK,           // API Hook陷阱
    DECOY_STRUCT        // 诱饵结构体陷阱
};

// 威胁级别
enum class ThreatLevel {
    NONE = 0,
    LOW = 25,
    MEDIUM = 50, 
    HIGH = 75,
    CRITICAL = 100
};

// 访问记录结构
struct AccessRecord {
    void* address;
    size_t size;
    std::chrono::steady_clock::time_point timestamp;
    pid_t pid;
    pid_t tid;
    MemoryRegion region;
    bool isWrite;
    uint32_t accessPattern;
};

// 陷阱配置结构
struct TrapConfig {
    void* address = nullptr;
    size_t size = 0;
    TrapType type;
    MemoryRegion region;
    std::function<void(void*, const AccessRecord&)> triggerCallback;
    bool isActive = true;  // 改为普通bool，避免原子类型的拷贝问题

    // 特定陷阱数据
    int decoyValue = 0;
    uint32_t crc32 = 0;
    void** pointerChain = nullptr;
    int chainDepth = 0;
    std::chrono::steady_clock::time_point createTime;
    std::chrono::steady_clock::time_point lastAccess;
    int accessCount = 0;  // 改为普通int

    // 构造函数
    TrapConfig() : createTime(std::chrono::steady_clock::now()) {}

    // 移动构造函数
    TrapConfig(TrapConfig&& other) noexcept
        : address(other.address)
        , size(other.size)
        , type(other.type)
        , region(other.region)
        , triggerCallback(std::move(other.triggerCallback))
        , isActive(other.isActive)
        , decoyValue(other.decoyValue)
        , crc32(other.crc32)
        , pointerChain(other.pointerChain)
        , chainDepth(other.chainDepth)
        , createTime(other.createTime)
        , lastAccess(other.lastAccess)
        , accessCount(other.accessCount)
    {
        other.address = nullptr;
        other.pointerChain = nullptr;
    }

    // 移动赋值操作符
    TrapConfig& operator=(TrapConfig&& other) noexcept {
        if (this != &other) {
            address = other.address;
            size = other.size;
            type = other.type;
            region = other.region;
            triggerCallback = std::move(other.triggerCallback);
            isActive = other.isActive;
            decoyValue = other.decoyValue;
            crc32 = other.crc32;
            pointerChain = other.pointerChain;
            chainDepth = other.chainDepth;
            createTime = other.createTime;
            lastAccess = other.lastAccess;
            accessCount = other.accessCount;

            other.address = nullptr;
            other.pointerChain = nullptr;
        }
        return *this;
    }

    // 禁用拷贝构造和拷贝赋值
    TrapConfig(const TrapConfig&) = delete;
    TrapConfig& operator=(const TrapConfig&) = delete;
};

// 威胁事件结构
struct ThreatEvent {
    ThreatLevel severity;
    std::string description;
    std::string primaryReason;
    std::vector<std::string> supportingEvidence;
    std::chrono::steady_clock::time_point timestamp;
    void* triggerAddress;
    MemoryRegion region;
    TrapType trapType;
    bool userFacing = false;
    std::string userId;
    
    ThreatEvent() : timestamp(std::chrono::steady_clock::now()) {}
};

// 行为分析引擎
class BehaviorAnalyzer {
public:
    BehaviorAnalyzer();
    ~BehaviorAnalyzer();
    
    void recordAccess(const AccessRecord& record);
    void analyzePatterns();
    ThreatLevel getCurrentThreatLevel() const;
    
    // 扫描特征检测
    bool detectLinearScan() const;
    bool detectPointerChasing() const;
    bool detectRandomAccess() const;
    bool detectFrequencyAnomaly() const;
    bool detectCheatToolSignatures() const;
    
    // 获取分析结果
    struct AnalysisResult {
        ThreatLevel threatLevel = ThreatLevel::NONE;
        std::string primaryReason;
        std::vector<std::string> evidence;
        double accessFrequency = 0.0;
        double accessEntropy = 0.0;
        std::map<MemoryRegion, int> regionDistribution;
    };
    
    AnalysisResult getAnalysisResult() const;

private:
    void performRealTimeAnalysis();
    void calculateAccessEntropy();
    void updateBehaviorModel();
    bool matchesCheatSignature(const AccessRecord& record) const;
    
    mutable std::mutex accessMutex_;
    std::vector<AccessRecord> recentAccesses_;
    std::queue<AccessRecord> accessHistory_;
    
    // 行为模型
    struct BehaviorModel {
        double avgAccessFrequency = 0.0;
        double accessEntropy = 0.0;
        double timeOfDayFactor = 1.0;
        std::array<double, 6> regionDistribution = {0};
    } normalModel_, currentModel_;
    
    std::atomic<ThreatLevel> currentThreat_{ThreatLevel::NONE};
    std::string lastThreatReason_;
    std::vector<std::string> lastEvidence_;
    
    // 分析线程
    std::thread analysisThread_;
    std::atomic<bool> analysisActive_{false};

    // 随机数生成器
    std::random_device rd_;
    std::mt19937 gen_;
    
    // 已知作弊工具签名
    struct CheatSignature {
        std::string name;
        std::vector<uint8_t> opcodePattern;
        std::vector<std::string> processNames;
        int severity;
        uint32_t accessPattern;
    };
    
    std::vector<CheatSignature> cheatSignatures_;
    void loadCheatSignatures();
};

// 内存陷阱管理器
class MemoryTrapManager {
public:
    static MemoryTrapManager& getInstance();
    
    bool initialize();
    void shutdown();
    
    // 陷阱管理
    bool addTrap(TrapConfig&& config);
    bool removeTrap(void* address);
    void activateAllTraps();
    void deactivateAllTraps();
    void updateTraps();
    
    // 陷阱创建助手
    TrapConfig createValueDecoy(MemoryRegion region, int value);
    TrapConfig createPointerChainTrap(MemoryRegion region, int depth);
    TrapConfig createGuardPage(size_t size);
    TrapConfig createCrcTrap(void* data, size_t size);
    TrapConfig createTimedAccessTrap(MemoryRegion region, int value);
    
    // 分层部署策略
    void deployLayeredDefense();
    void deployChHeapTraps(int count);      // C++堆陷阱 40%
    void deployJhJavaTraps(int count);      // Java堆陷阱 30%
    void deployAnonymousTraps(int count);   // 匿名内存陷阱 15%
    void deployDataSegmentTraps(int count); // 数据段陷阱 10%
    void deployCppAllocTraps(int count);    // C++分配陷阱 10%
    void deployCppBssTraps(int count);      // C++ .bss段陷阱 20% (ACESDK重点区域)

    // Ca区域多策略部署方法
    void deployNewDeleteTraps(int count);        // 使用new/delete
    void deployVectorTraps(int count);           // 使用vector动态分配
    void deployCustomAllocatorTraps(int count);  // 使用自定义分配器

    // Cb区域多策略部署方法
    void deployExternalCbTraps(int count);       // 外部动态库.bss段陷阱
    void deployDelayedInitTraps(int count);      // 延迟初始化静态变量
    void deployLargeStaticArrayTraps(int count); // 大型静态数组
    void addCbTrap(void* addr, size_t size, const char* description); // BSS段陷阱助手
    
    // 动态调整
    void adjustTrapsBasedOnThreat(ThreatLevel level);
    void diversifyTraps();
    
    // 统计信息
    struct Statistics {
        int totalTraps = 0;
        int activeTraps = 0;
        int totalTriggers = 0;
        int lastHourTriggers = 0;
        std::map<TrapType, int> triggersByType;
        std::map<MemoryRegion, int> triggersByRegion;
    };
    
    Statistics getStatistics() const;
    
    // 设置回调
    void setThreatCallback(std::function<void(const ThreatEvent&)> callback);

private:
    MemoryTrapManager();
    ~MemoryTrapManager();
    
    void installSignalHandler();
    void uninstallSignalHandler();
    static void signalHandler(int sig, siginfo_t* info, void* context);
    void handleMemoryAccess(void* address, bool isWrite);
    
    void setupDefaultTraps();
    void createSpecialTestTraps();
    void reorganizePointerChains();
    int generateMeaningfulDecoy() const;
    void applyTrapState(TrapConfig& trap);

    // 主动检测方法
    void startActiveDetection();
    void stopActiveDetection();
    void checkMemoryScanning();
    bool isSuspiciousMapping(const std::string& line); // 检查可疑内存映射

    // 内存区域诊断和验证
    void dumpMemoryRegions();                    // 内存区域诊断工具
    void dumpTrapAddresses();                    // 陷阱地址详情
    void verifyCaTrapsAddressRange();            // 验证Ca陷阱地址范围
    void setMemoryPermissions(void* addr, size_t size, const char* name); // 设置内存权限

    // 豆包方案：预分配策略
    void setupCaRegionPreallocation();           // Ca区域预分配
    void setupCbRegionPreallocation();           // Cb区域预分配

    // 辅助函数
    const char* getTrapTypeString(TrapType type);
    const char* getRegionString(MemoryRegion region);
    
    std::vector<TrapConfig> traps_;
    mutable std::mutex trapsMutex_;
    std::atomic<bool> initialized_{false};
    
    // 行为分析器
    std::unique_ptr<BehaviorAnalyzer> behaviorAnalyzer_;
    
    // 威胁回调
    std::function<void(const ThreatEvent&)> threatCallback_;
    
    // 统计信息
    Statistics stats_;
    mutable std::mutex statsMutex_;

    // 主动检测
    std::atomic<bool> activeDetectionRunning_{false};
    std::thread detectionThread_;
    std::chrono::steady_clock::time_point lastMemoryCheck_;

    // 信号处理
    static struct sigaction oldSigsegvAction_;
    static struct sigaction oldSigbusAction_;
    static MemoryTrapManager* instance_;
    
    // 随机数生成器
    mutable std::random_device rd_;
    mutable std::mt19937 gen_;
    mutable std::uniform_int_distribution<> dist_;
    
    // 更新线程
    std::thread updateThread_;
    std::atomic<bool> updateActive_{false};
    void updateThreadFunc();
};

// 防御响应系统
class DefenseSystem {
public:
    static DefenseSystem& getInstance();
    
    void initialize();
    void handleThreatEvent(const ThreatEvent& event);
    
    // 威胁级别响应
    void handleLowThreat(const ThreatEvent& event);
    void handleMediumThreat(const ThreatEvent& event);
    void handleHighThreat(const ThreatEvent& event);
    void handleCriticalThreat(const ThreatEvent& event);
    
    ThreatLevel getCurrentThreatLevel() const;
    void setThreatLevel(ThreatLevel level);

private:
    DefenseSystem() = default;
    
    void logEvent(const ThreatEvent& event);
    void increaseLocalTrapDensity(MemoryRegion region, int percentage);
    void activateMaximumProtection();
    
    std::atomic<ThreatLevel> currentThreatLevel_{ThreatLevel::NONE};
    std::mutex eventLogMutex_;
    std::vector<ThreatEvent> eventLog_;
};

} // namespace AdvancedAntiCheat
