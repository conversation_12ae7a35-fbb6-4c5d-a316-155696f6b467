#include "napi/native_api.h"
#include "tp2_sdk.h"

napi_value UserLogin(napi_env env, napi_callback_info info)
{
    tp2_setuserinfo(ENTRY_ID_QZONE, 0, "123456", ""); // 账号类型和openid需要修改成当前登录玩家的信息
    return nullptr;
}

napi_value GameOnResume(napi_env env, napi_callback_info info)
{
    // 游戏恢复到前台
    tp2_setgamestatus(TP2_GAME_STATUS_FRONTEND);
    return nullptr;
}

napi_value GameOnPause(napi_env env, napi_callback_info info)
{
    // 游戏被挂起到后台
    tp2_setgamestatus(TP2_GAME_STATUS_BACKEND);
    return nullptr;
}

napi_value GameStart(napi_env env, napi_callback_info info) {
    // 游戏启动
    tp2_sdk_init_ex(20059, "9dfc0ed2c072399ec0be1bec2f86017a");
    return nullptr;
}

EXTERN_C_START
static napi_value Init(napi_env env, napi_value exports)
{
    napi_property_descriptor desc[] = {
        { "gameOnResumeNative", nullptr, GameOnResume, nullptr, nullptr, nullptr, napi_default, nullptr},
        { "gameOnPauseNative", nullptr, GameOnPause, nullptr, nullptr, nullptr, napi_default, nullptr},
        { "gameUserLogin", nullptr, UserLogin, nullptr, nullptr, nullptr, napi_default, nullptr},
        { "gameStart", nullptr, GameStart, nullptr, nullptr, nullptr, napi_default, nullptr}
    };
    napi_define_properties(env, exports, sizeof(desc) / sizeof(desc[0]), desc);
    return exports;
}
EXTERN_C_END

static napi_module demoModule = {
    .nm_version = 1,
    .nm_flags = 0,
    .nm_filename = nullptr,
    .nm_register_func = Init,
    .nm_modname = "entry",
    .nm_priv = ((void*)0),
    .reserved = { 0 },
};

extern "C" __attribute__((constructor)) void RegisterEntryModule(void)
{
    napi_module_register(&demoModule);
}
