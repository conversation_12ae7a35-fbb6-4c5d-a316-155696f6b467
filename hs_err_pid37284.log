#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=37284, tid=9308
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-dce2e3da99307a614c97d32a419f5fbe-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:18:49 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 29.186342 seconds (0d 0h 0m 29s)

---------------  T H R E A D  ---------------

Current thread (0x0000019a7bbba4d0):  JavaThread "pool-1-thread-1"        [_thread_in_vm, id=9308, stack(0x0000000122600000,0x0000000122700000) (1024K)]

Stack: [0x0000000122600000,0x0000000122700000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0x8a41ee]
V  [jvm.dll+0x670575]
V  [jvm.dll+0x6705da]
V  [jvm.dll+0x672dc2]
V  [jvm.dll+0x672c92]
V  [jvm.dll+0x670f4e]
V  [jvm.dll+0x26a8d0]
V  [jvm.dll+0x216127]
V  [jvm.dll+0x20bbae]
V  [jvm.dll+0x5ae58c]
V  [jvm.dll+0x21d26a]
V  [jvm.dll+0x820d6c]
V  [jvm.dll+0x821d94]
V  [jvm.dll+0x822362]
V  [jvm.dll+0x821fe8]
V  [jvm.dll+0x26cf1b]
V  [jvm.dll+0x3d493e]
C  0x0000019a6724925f

The last pc belongs to new (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.util.logging.Logger.log(Ljava/util/logging/Level;Ljava/lang/String;Ljava/lang/Throwable;)V+9 java.logging@21.0.7
j  org.eclipse.lsp4j.jsonrpc.json.ConcurrentMessageProcessor.run()V+36
j  java.util.concurrent.Executors$RunnableAdapter.call()Ljava/lang/Object;+4 java.base@21.0.7
j  java.util.concurrent.FutureTask.run()V+39 java.base@21.0.7
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@21.0.7
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@21.0.7
j  java.lang.Thread.runWith(Ljava/lang/Object;Ljava/lang/Runnable;)V+5 java.base@21.0.7
j  java.lang.Thread.run()V+19 java.base@21.0.7
v  ~StubRoutines::call_stub 0x0000019a6723100d
new  187 new  [0x0000019a672490e0, 0x0000019a672492e8]  520 bytes
[MachCode]
  0x0000019a672490e0: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x0000019a67249100: 4424 0800 | 0000 00eb | 0150 410f | b755 010f | cac1 ea10 | 488b 4de8 | 488b 4908 | 488b 4908 
  0x0000019a67249120: 488b 4108 | 807c 1004 | 070f 85d3 | 0000 0066 | 8b54 d148 | 488b 4928 | 488b 4cd1 | 0851 80b9 
  0x0000019a67249140: 2101 0000 | 040f 85b6 | 0000 008b | 5108 f6c2 | 010f 85aa | 0000 0049 | 8b87 b801 | 0000 488d 
  0x0000019a67249160: 1c10 493b | 9fc8 0100 | 000f 8792 | 0000 0049 | 899f b801 | 0000 4883 | ea10 0f84 | 0f00 0000 
  0x0000019a67249180: 33c9 c1ea | 0348 894c | d008 48ff | ca75 f648 | c700 0100 | 0000 5933 | f689 700c | 49ba 0000 
  0x0000019a672491a0: 0000 9a01 | 0000 492b | ca89 4808 | 49ba 3e5b | 44ef ff7f | 0000 4180 | 3a00 0f84 | 3c00 0000 
  0x0000019a672491c0: 5048 8bc8 | 4883 ec20 | 40f6 c40f | 0f84 1900 | 0000 4883 | ec08 48b8 | 904b eeee | ff7f 0000 
  0x0000019a672491e0: ffd0 4883 | c408 e90c | 0000 0048 | b890 4bee | eeff 7f00 | 00ff d048 | 83c4 2058 | e9cb 0000 
  0x0000019a67249200: 0059 488b | 55e8 488b | 5208 488b | 5208 450f | b745 0141 | 0fc8 41c1 | e810 e805 | 0000 00e9 
  0x0000019a67249220: a800 0000 | 488d 4424 | 084c 896d | c049 8bcf | c5f8 7749 | 89af a803 | 0000 4989 | 8798 0300 
  0x0000019a67249240: 0048 83ec | 2040 f6c4 | 0f0f 8419 | 0000 0048 | 83ec 0848 | b8f0 48b8 | eeff 7f00 | 00ff d048 
  0x0000019a67249260: 83c4 08e9 | 0c00 0000 | 48b8 f048 | b8ee ff7f | 0000 ffd0 | 4883 c420 | 49c7 8798 | 0300 0000 
  0x0000019a67249280: 0000 0049 | c787 a803 | 0000 0000 | 0000 49c7 | 87a0 0300 | 0000 0000 | 00c5 f877 | 4983 7f08 
  0x0000019a672492a0: 000f 8405 | 0000 00e9 | 547c feff | 498b 87f0 | 0300 0049 | c787 f003 | 0000 0000 | 0000 4c8b 
  0x0000019a672492c0: 6dc0 4c8b | 75c8 4e8d | 74f5 00c3 | 410f b65d | 0349 83c5 | 0349 bab0 | 2047 efff | 7f00 0041 
  0x0000019a672492e0: ff24 da0f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000019a7bc00530, length=31, elements={
0x0000019a5cb88f60, 0x0000019a732f1d70, 0x0000019a732f3670, 0x0000019a732f4580,
0x0000019a732f5290, 0x0000019a74136f60, 0x0000019a741389c0, 0x0000019a741397f0,
0x0000019a732fb4a0, 0x0000019a5cbb92d0, 0x0000019a74470840, 0x0000019a7a255920,
0x0000019a75762760, 0x0000019a7a1d8c90, 0x0000019a7a028650, 0x0000019a7573edb0,
0x0000019a7a548580, 0x0000019a7a546220, 0x0000019a7a78c680, 0x0000019a7a78d3a0,
0x0000019a7a78da30, 0x0000019a7a78fb00, 0x0000019a7a78ede0, 0x0000019a7a78f470,
0x0000019a7a78e0c0, 0x0000019a7a78cd10, 0x0000019a7a78e750, 0x0000019a7bbb9120,
0x0000019a7bbb8a90, 0x0000019a7bbbb880, 0x0000019a7bbba4d0
}

Java Threads: ( => current thread )
  0x0000019a5cb88f60 JavaThread "main"                              [_thread_blocked, id=17508, stack(0x000000011fa00000,0x000000011fb00000) (1024K)]
  0x0000019a732f1d70 JavaThread "Reference Handler"          daemon [_thread_blocked, id=46136, stack(0x000000011fe00000,0x000000011ff00000) (1024K)]
  0x0000019a732f3670 JavaThread "Finalizer"                  daemon [_thread_blocked, id=43500, stack(0x000000011ff00000,0x0000000120000000) (1024K)]
  0x0000019a732f4580 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=9556, stack(0x0000000120000000,0x0000000120100000) (1024K)]
  0x0000019a732f5290 JavaThread "Attach Listener"            daemon [_thread_blocked, id=46152, stack(0x0000000120100000,0x0000000120200000) (1024K)]
  0x0000019a74136f60 JavaThread "Service Thread"             daemon [_thread_blocked, id=44848, stack(0x0000000120200000,0x0000000120300000) (1024K)]
  0x0000019a741389c0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=9344, stack(0x0000000120300000,0x0000000120400000) (1024K)]
  0x0000019a741397f0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=46296, stack(0x0000000120400000,0x0000000120500000) (1024K)]
  0x0000019a732fb4a0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=38728, stack(0x0000000120500000,0x0000000120600000) (1024K)]
  0x0000019a5cbb92d0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=43584, stack(0x0000000120600000,0x0000000120700000) (1024K)]
  0x0000019a74470840 JavaThread "Notification Thread"        daemon [_thread_blocked, id=46232, stack(0x0000000120700000,0x0000000120800000) (1024K)]
  0x0000019a7a255920 JavaThread "Active Thread: Equinox Container: 34129203-06b0-4e70-8809-962390e81565"        [_thread_blocked, id=21752, stack(0x0000000121000000,0x0000000121100000) (1024K)]
  0x0000019a75762760 JavaThread "Framework Event Dispatcher: Equinox Container: 34129203-06b0-4e70-8809-962390e81565" daemon [_thread_blocked, id=10548, stack(0x0000000121100000,0x0000000121200000) (1024K)]
  0x0000019a7a1d8c90 JavaThread "Start Level: Equinox Container: 34129203-06b0-4e70-8809-962390e81565" daemon [_thread_blocked, id=40388, stack(0x0000000121200000,0x0000000121300000) (1024K)]
  0x0000019a7a028650 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=45032, stack(0x0000000121400000,0x0000000121500000) (1024K)]
  0x0000019a7573edb0 JavaThread "Worker-JM"                         [_thread_blocked, id=46704, stack(0x0000000121600000,0x0000000121700000) (1024K)]
  0x0000019a7a548580 JavaThread "Worker-0"                          [_thread_blocked, id=15876, stack(0x0000000120900000,0x0000000120a00000) (1024K)]
  0x0000019a7a546220 JavaThread "Worker-1"                          [_thread_blocked, id=16324, stack(0x0000000121700000,0x0000000121800000) (1024K)]
  0x0000019a7a78c680 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=9436, stack(0x0000000120800000,0x0000000120900000) (1024K)]
  0x0000019a7a78d3a0 JavaThread "Thread-2"                   daemon [_thread_in_native, id=44676, stack(0x0000000121b00000,0x0000000121c00000) (1024K)]
  0x0000019a7a78da30 JavaThread "Thread-3"                   daemon [_thread_in_native, id=28944, stack(0x0000000121c00000,0x0000000121d00000) (1024K)]
  0x0000019a7a78fb00 JavaThread "Thread-4"                   daemon [_thread_in_native, id=22344, stack(0x0000000121d00000,0x0000000121e00000) (1024K)]
  0x0000019a7a78ede0 JavaThread "Thread-5"                   daemon [_thread_in_native, id=25360, stack(0x0000000121e00000,0x0000000121f00000) (1024K)]
  0x0000019a7a78f470 JavaThread "Thread-6"                   daemon [_thread_in_native, id=25388, stack(0x0000000121f00000,0x0000000122000000) (1024K)]
  0x0000019a7a78e0c0 JavaThread "Thread-7"                   daemon [_thread_in_native, id=20816, stack(0x0000000122000000,0x0000000122100000) (1024K)]
  0x0000019a7a78cd10 JavaThread "Thread-8"                   daemon [_thread_in_native, id=29880, stack(0x0000000122100000,0x0000000122200000) (1024K)]
  0x0000019a7a78e750 JavaThread "Thread-9"                   daemon [_thread_in_native, id=39776, stack(0x0000000122200000,0x0000000122300000) (1024K)]
  0x0000019a7bbb9120 JavaThread "Thread-10"                  daemon [_thread_in_native, id=38380, stack(0x0000000122300000,0x0000000122400000) (1024K)]
  0x0000019a7bbb8a90 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=38692, stack(0x0000000122400000,0x0000000122500000) (1024K)]
  0x0000019a7bbbb880 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=41576, stack(0x0000000122500000,0x0000000122600000) (1024K)]
=>0x0000019a7bbba4d0 JavaThread "pool-1-thread-1"                   [_thread_in_vm, id=9308, stack(0x0000000122600000,0x0000000122700000) (1024K)]
Total: 31

Other Threads:
  0x0000019a5cbc3ed0 VMThread "VM Thread"                           [id=45460, stack(0x000000011fd00000,0x000000011fe00000) (1024K)]
  0x0000019a5cbb7c10 WatcherThread "VM Periodic Task Thread"        [id=5116, stack(0x000000011fc00000,0x000000011fd00000) (1024K)]
  0x0000019a5cba6e90 WorkerThread "GC Thread#0"                     [id=24364, stack(0x000000011fb00000,0x000000011fc00000) (1024K)]
  0x0000019a7578f600 WorkerThread "GC Thread#1"                     [id=36980, stack(0x0000000120a00000,0x0000000120b00000) (1024K)]
  0x0000019a7586fc20 WorkerThread "GC Thread#2"                     [id=16412, stack(0x0000000120b00000,0x0000000120c00000) (1024K)]
  0x0000019a7578f9a0 WorkerThread "GC Thread#3"                     [id=30976, stack(0x0000000120c00000,0x0000000120d00000) (1024K)]
  0x0000019a7578fd40 WorkerThread "GC Thread#4"                     [id=23208, stack(0x0000000120d00000,0x0000000120e00000) (1024K)]
  0x0000019a757900e0 WorkerThread "GC Thread#5"                     [id=17804, stack(0x0000000120e00000,0x0000000120f00000) (1024K)]
  0x0000019a75790480 WorkerThread "GC Thread#6"                     [id=28552, stack(0x0000000120f00000,0x0000000121000000) (1024K)]
  0x0000019a7a1fc810 WorkerThread "GC Thread#7"                     [id=5276, stack(0x0000000121300000,0x0000000121400000) (1024K)]
Total: 10

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45c308] Metaspace_lock - owner thread: 0x0000019a7bbba4d0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000019a00000000-0x0000019a00ba0000-0x0000019a00ba0000), size 12189696, SharedBaseAddress: 0x0000019a00000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000019a01000000-0x0000019a41000000, reserved size: 1073741824
Narrow klass base: 0x0000019a00000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 26112K, used 11897K [0x00000000d5580000, 0x00000000d7380000, 0x0000000100000000)
  eden space 22528K, 38% used [0x00000000d5580000,0x00000000d5dee0e8,0x00000000d6b80000)
  from space 3584K, 91% used [0x00000000d7000000,0x00000000d73305d8,0x00000000d7380000)
  to   space 3584K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 10415K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 15% used [0x0000000080000000,0x0000000080a2bd28,0x0000000084300000)
 Metaspace       used 28818K, committed 29568K, reserved 1114112K
  class space    used 2823K, committed 3200K, reserved 1048576K

Card table byte_map: [0x0000019a5c3c0000,0x0000019a5c7d0000] _byte_map_base: 0x0000019a5bfc0000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x0000019a6ed00000, 0x0000019a70d00000)
 End Bits:   [0x0000019a70d00000, 0x0000019a72d00000)

Polling page: 0x0000019a5c0e0000

Metaspace:

Usage:
  Non-class:     25.39 MB used.
      Class:      2.76 MB used.
       Both:     28.14 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      25.75 MB ( 40%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       3.12 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      28.88 MB (  3%) committed. 

Chunk freelists:
   Non-Class:  5.83 MB
       Class:  12.89 MB
        Both:  18.72 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 35.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 3.
num_arena_births: 548.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 462.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 17.
num_chunks_taken_from_freelist: 1611.
num_chunk_merges: 8.
num_chunk_splits: 1071.
num_chunks_enlarged: 706.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1816Kb max_used=1816Kb free=118183Kb
 bounds [0x0000019a677d0000, 0x0000019a67a40000, 0x0000019a6ed00000]
CodeHeap 'profiled nmethods': size=120000Kb used=7196Kb max_used=7196Kb free=112804Kb
 bounds [0x0000019a5fd00000, 0x0000019a60410000, 0x0000019a67230000]
CodeHeap 'non-nmethods': size=5760Kb used=1345Kb max_used=1387Kb free=4415Kb
 bounds [0x0000019a67230000, 0x0000019a674a0000, 0x0000019a677d0000]
 total_blobs=4300 nmethods=3664 adapters=542
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 16.820 Thread 0x0000019a741397f0 3656  s    4       java.util.Hashtable::put (104 bytes)
Event: 16.827 Thread 0x0000019a741397f0 nmethod 3656 0x0000019a67993e90 code [0x0000019a679940a0, 0x0000019a67994a18]
Event: 16.827 Thread 0x0000019a741397f0 3651       4       java.lang.StringLatin1::compareTo (41 bytes)
Event: 16.830 Thread 0x0000019a741397f0 nmethod 3651 0x0000019a67994f90 code [0x0000019a67995140, 0x0000019a67995428]
Event: 16.830 Thread 0x0000019a741397f0 3657       4       java.util.Hashtable::addEntry (87 bytes)
Event: 16.833 Thread 0x0000019a741397f0 nmethod 3657 0x0000019a67995690 code [0x0000019a67995860, 0x0000019a67995bb8]
Event: 24.395 Thread 0x0000019a732fb4a0 3659       3       java.util.concurrent.locks.ReentrantReadWriteLock::readLock (5 bytes)
Event: 24.395 Thread 0x0000019a732fb4a0 nmethod 3659 0x0000019a60403610 code [0x0000019a604037a0, 0x0000019a604038d8]
Event: 24.525 Thread 0x0000019a732fb4a0 3660       3       jdk.internal.misc.Unsafe::copyMemory (33 bytes)
Event: 24.526 Thread 0x0000019a732fb4a0 nmethod 3660 0x0000019a60403990 code [0x0000019a60403c60, 0x0000019a60404920]
Event: 24.526 Thread 0x0000019a732fb4a0 3661       3       jdk.internal.misc.Unsafe::copyMemoryChecks (21 bytes)
Event: 24.526 Thread 0x0000019a732fb4a0 nmethod 3661 0x0000019a60404f10 code [0x0000019a604051e0, 0x0000019a60405e98]
Event: 24.861 Thread 0x0000019a732fb4a0 3662       3       jdk.internal.misc.Unsafe::checkOffset (32 bytes)
Event: 24.861 Thread 0x0000019a732fb4a0 nmethod 3662 0x0000019a60406410 code [0x0000019a604065c0, 0x0000019a60406830]
Event: 25.005 Thread 0x0000019a732fb4a0 3663       1       sun.nio.ch.Iocp$CompletionStatus::completionKey (5 bytes)
Event: 25.005 Thread 0x0000019a732fb4a0 nmethod 3663 0x0000019a67995e90 code [0x0000019a67996020, 0x0000019a679960e8]
Event: 27.576 Thread 0x0000019a732fb4a0 3664       3       java.nio.charset.CoderResult::isOverflow (14 bytes)
Event: 27.576 Thread 0x0000019a732fb4a0 nmethod 3664 0x0000019a60406910 code [0x0000019a60406aa0, 0x0000019a60406c00]
Event: 28.774 Thread 0x0000019a732fb4a0 3665       3       java.util.concurrent.ConcurrentHashMap$ValueIterator::<init> (12 bytes)
Event: 28.774 Thread 0x0000019a732fb4a0 nmethod 3665 0x0000019a60406c90 code [0x0000019a60406e40, 0x0000019a60406f78]

GC Heap History (18 events):
Event: 0.971 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4298K, committed 4544K, reserved 1114112K
  class space    used 460K, committed 576K, reserved 1048576K
}
Event: 0.978 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3495K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 85% used [0x00000000d6e80000,0x00000000d71e9f28,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 8K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080002000,0x0000000084300000)
 Metaspace       used 4298K, committed 4544K, reserved 1114112K
  class space    used 460K, committed 576K, reserved 1048576K
}
Event: 1.718 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 29095K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 85% used [0x00000000d6e80000,0x00000000d71e9f28,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 8K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080002000,0x0000000084300000)
 Metaspace       used 8123K, committed 8384K, reserved 1114112K
  class space    used 841K, committed 960K, reserved 1048576K
}
Event: 1.722 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4093K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767f5e0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 1035K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 1% used [0x0000000080000000,0x0000000080102eb8,0x0000000084300000)
 Metaspace       used 8123K, committed 8384K, reserved 1114112K
  class space    used 841K, committed 960K, reserved 1048576K
}
Event: 2.171 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29693K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767f5e0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 1035K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 1% used [0x0000000080000000,0x0000000080102eb8,0x0000000084300000)
 Metaspace       used 12926K, committed 13440K, reserved 1114112K
  class space    used 1329K, committed 1536K, reserved 1048576K
}
Event: 2.174 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4093K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727f4f8,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 2859K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 4% used [0x0000000080000000,0x00000000802cafe0,0x0000000084300000)
 Metaspace       used 12926K, committed 13440K, reserved 1114112K
  class space    used 1329K, committed 1536K, reserved 1048576K
}
Event: 2.480 GC heap before
{Heap before GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 29693K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727f4f8,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 2859K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 4% used [0x0000000080000000,0x00000000802cafe0,0x0000000084300000)
 Metaspace       used 15863K, committed 16448K, reserved 1114112K
  class space    used 1605K, committed 1856K, reserved 1048576K
}
Event: 2.483 GC heap after
{Heap after GC invocations=4 (full 0):
 PSYoungGen      total 29696K, used 4092K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767f0c0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 5088K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x00000000804f8068,0x0000000084300000)
 Metaspace       used 15863K, committed 16448K, reserved 1114112K
  class space    used 1605K, committed 1856K, reserved 1048576K
}
Event: 2.745 GC heap before
{Heap before GC invocations=5 (full 0):
 PSYoungGen      total 29696K, used 29692K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767f0c0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 5088K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x00000000804f8068,0x0000000084300000)
 Metaspace       used 20011K, committed 20608K, reserved 1114112K
  class space    used 1903K, committed 2176K, reserved 1048576K
}
Event: 2.748 GC heap after
{Heap after GC invocations=5 (full 0):
 PSYoungGen      total 29184K, used 4087K [0x00000000d5580000, 0x00000000d7900000, 0x0000000100000000)
  eden space 25088K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e00000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727de68,0x00000000d7280000)
  to   space 5632K, 0% used [0x00000000d7380000,0x00000000d7380000,0x00000000d7900000)
 ParOldGen       total 68608K, used 5547K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 8% used [0x0000000080000000,0x000000008056aed8,0x0000000084300000)
 Metaspace       used 20011K, committed 20608K, reserved 1114112K
  class space    used 1903K, committed 2176K, reserved 1048576K
}
Event: 3.017 GC heap before
{Heap before GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 24206K [0x00000000d5580000, 0x00000000d7900000, 0x0000000100000000)
  eden space 25088K, 80% used [0x00000000d5580000,0x00000000d6925ad8,0x00000000d6e00000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d727de68,0x00000000d7280000)
  to   space 5632K, 0% used [0x00000000d7380000,0x00000000d7380000,0x00000000d7900000)
 ParOldGen       total 68608K, used 5547K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 8% used [0x0000000080000000,0x000000008056aed8,0x0000000084300000)
 Metaspace       used 20916K, committed 21504K, reserved 1114112K
  class space    used 1956K, committed 2240K, reserved 1048576K
}
Event: 3.020 GC heap after
{Heap after GC invocations=6 (full 0):
 PSYoungGen      total 29184K, used 5601K [0x00000000d5580000, 0x00000000d7c00000, 0x0000000100000000)
  eden space 23552K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6c80000)
  from space 5632K, 99% used [0x00000000d7380000,0x00000000d78f86e0,0x00000000d7900000)
  to   space 7168K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d7380000)
 ParOldGen       total 68608K, used 6344K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 9% used [0x0000000080000000,0x0000000080632050,0x0000000084300000)
 Metaspace       used 20916K, committed 21504K, reserved 1114112K
  class space    used 1956K, committed 2240K, reserved 1048576K
}
Event: 3.020 GC heap before
{Heap before GC invocations=7 (full 1):
 PSYoungGen      total 29184K, used 5601K [0x00000000d5580000, 0x00000000d7c00000, 0x0000000100000000)
  eden space 23552K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6c80000)
  from space 5632K, 99% used [0x00000000d7380000,0x00000000d78f86e0,0x00000000d7900000)
  to   space 7168K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d7380000)
 ParOldGen       total 68608K, used 6344K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 9% used [0x0000000080000000,0x0000000080632050,0x0000000084300000)
 Metaspace       used 20916K, committed 21504K, reserved 1114112K
  class space    used 1956K, committed 2240K, reserved 1048576K
}
Event: 3.038 GC heap after
{Heap after GC invocations=7 (full 1):
 PSYoungGen      total 29184K, used 0K [0x00000000d5580000, 0x00000000d7c00000, 0x0000000100000000)
  eden space 23552K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6c80000)
  from space 5632K, 0% used [0x00000000d7380000,0x00000000d7380000,0x00000000d7900000)
  to   space 7168K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d7380000)
 ParOldGen       total 68608K, used 10399K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 15% used [0x0000000080000000,0x0000000080a27d28,0x0000000084300000)
 Metaspace       used 20904K, committed 21504K, reserved 1114112K
  class space    used 1953K, committed 2240K, reserved 1048576K
}
Event: 3.744 GC heap before
{Heap before GC invocations=8 (full 1):
 PSYoungGen      total 29184K, used 23552K [0x00000000d5580000, 0x00000000d7c00000, 0x0000000100000000)
  eden space 23552K, 100% used [0x00000000d5580000,0x00000000d6c80000,0x00000000d6c80000)
  from space 5632K, 0% used [0x00000000d7380000,0x00000000d7380000,0x00000000d7900000)
  to   space 7168K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d7380000)
 ParOldGen       total 68608K, used 10399K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 15% used [0x0000000080000000,0x0000000080a27d28,0x0000000084300000)
 Metaspace       used 25069K, committed 25792K, reserved 1114112K
  class space    used 2386K, committed 2688K, reserved 1048576K
}
Event: 3.746 GC heap after
{Heap after GC invocations=8 (full 1):
 PSYoungGen      total 25600K, used 2050K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 23040K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6c00000)
  from space 2560K, 80% used [0x00000000d6c80000,0x00000000d6e80ae0,0x00000000d6f00000)
  to   space 4096K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7400000)
 ParOldGen       total 68608K, used 10407K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 15% used [0x0000000080000000,0x0000000080a29d28,0x0000000084300000)
 Metaspace       used 25069K, committed 25792K, reserved 1114112K
  class space    used 2386K, committed 2688K, reserved 1048576K
}
Event: 6.169 GC heap before
{Heap before GC invocations=9 (full 1):
 PSYoungGen      total 25600K, used 25090K [0x00000000d5580000, 0x00000000d7400000, 0x0000000100000000)
  eden space 23040K, 100% used [0x00000000d5580000,0x00000000d6c00000,0x00000000d6c00000)
  from space 2560K, 80% used [0x00000000d6c80000,0x00000000d6e80ae0,0x00000000d6f00000)
  to   space 4096K, 0% used [0x00000000d7000000,0x00000000d7000000,0x00000000d7400000)
 ParOldGen       total 68608K, used 10407K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 15% used [0x0000000080000000,0x0000000080a29d28,0x0000000084300000)
 Metaspace       used 28101K, committed 28928K, reserved 1114112K
  class space    used 2739K, committed 3136K, reserved 1048576K
}
Event: 6.171 GC heap after
{Heap after GC invocations=9 (full 1):
 PSYoungGen      total 26112K, used 3265K [0x00000000d5580000, 0x00000000d7380000, 0x0000000100000000)
  eden space 22528K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6b80000)
  from space 3584K, 91% used [0x00000000d7000000,0x00000000d73305d8,0x00000000d7380000)
  to   space 3584K, 0% used [0x00000000d6c80000,0x00000000d6c80000,0x00000000d7000000)
 ParOldGen       total 68608K, used 10415K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 15% used [0x0000000080000000,0x0000000080a2bd28,0x0000000084300000)
 Metaspace       used 28101K, committed 28928K, reserved 1114112K
  class space    used 2739K, committed 3136K, reserved 1048576K
}

Dll operation events (10 events):
Event: 0.013 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.063 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.130 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.135 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.137 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.141 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.176 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.256 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.705 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 3.653 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna17176408055758091470.dll

Deoptimization events (20 events):
Event: 6.192 Thread 0x0000019a7bbba4d0 Uncommon trap: trap_request=0xffffff76 fr.pc=0x0000019a6794829c relative=0x00000000000009dc
Event: 6.192 Thread 0x0000019a7bbba4d0 Uncommon trap: reason=predicate action=maybe_recompile pc=0x0000019a6794829c method=java.util.TreeMap.put(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; @ 27 c2
Event: 6.192 Thread 0x0000019a7bbba4d0 DEOPT PACKING pc=0x0000019a6794829c sp=0x00000001226fdf20
Event: 6.192 Thread 0x0000019a7bbba4d0 DEOPT UNPACKING pc=0x0000019a67283aa2 sp=0x00000001226fdee0 mode 2
Event: 6.217 Thread 0x0000019a7a546220 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019a67909ff4 relative=0x0000000000000894
Event: 6.217 Thread 0x0000019a7a546220 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019a67909ff4 method=java.util.concurrent.ConcurrentHashMap.putVal(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; @ 97 c2
Event: 6.217 Thread 0x0000019a7a546220 DEOPT PACKING pc=0x0000019a67909ff4 sp=0x00000001217fd690
Event: 6.217 Thread 0x0000019a7a546220 DEOPT UNPACKING pc=0x0000019a67283aa2 sp=0x00000001217fd608 mode 2
Event: 14.959 Thread 0x0000019a7bbba4d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019a67818a84 relative=0x0000000000000504
Event: 14.959 Thread 0x0000019a7bbba4d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019a67818a84 method=java.util.HashMap$HashIterator.<init>(Ljava/util/HashMap;)V @ 45 c2
Event: 14.959 Thread 0x0000019a7bbba4d0 DEOPT PACKING pc=0x0000019a67818a84 sp=0x00000001226fe7d0
Event: 14.959 Thread 0x0000019a7bbba4d0 DEOPT UNPACKING pc=0x0000019a67283aa2 sp=0x00000001226fe640 mode 2
Event: 14.969 Thread 0x0000019a7bbba4d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019a67990508 relative=0x0000000000000588
Event: 14.969 Thread 0x0000019a7bbba4d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019a67990508 method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 173 c2
Event: 14.969 Thread 0x0000019a7bbba4d0 DEOPT PACKING pc=0x0000019a67990508 sp=0x00000001226fe750
Event: 14.969 Thread 0x0000019a7bbba4d0 DEOPT UNPACKING pc=0x0000019a67283aa2 sp=0x00000001226fe650 mode 2
Event: 28.775 Thread 0x0000019a7bbba4d0 Uncommon trap: trap_request=0x0000002e fr.pc=0x0000019a67980d70 relative=0x0000000000000490
Event: 28.775 Thread 0x0000019a7bbba4d0 Uncommon trap: reason=unloaded action=reinterpret pc=0x0000019a67980d70 method=org.eclipse.jdt.ls.core.internal.ConnectionStreamFactory$NamedPipeInputStream.read()I @ 119 c2
Event: 28.775 Thread 0x0000019a7bbba4d0 DEOPT PACKING pc=0x0000019a67980d70 sp=0x00000001226ff190
Event: 28.775 Thread 0x0000019a7bbba4d0 DEOPT UNPACKING pc=0x0000019a67283aa2 sp=0x00000001226ff130 mode 2

Classes loaded (20 events):
Event: 6.114 Loading class sun/reflect/generics/repository/FieldRepository done
Event: 6.117 Loading class jdk/internal/reflect/MethodHandleIntegerFieldAccessorImpl
Event: 6.117 Loading class jdk/internal/reflect/MethodHandleIntegerFieldAccessorImpl done
Event: 6.151 Loading class java/io/InvalidObjectException
Event: 6.151 Loading class java/io/ObjectStreamException
Event: 6.151 Loading class java/io/ObjectStreamException done
Event: 6.151 Loading class java/io/InvalidObjectException done
Event: 6.215 Loading class java/util/concurrent/CompletableFuture$AltResult
Event: 6.215 Loading class java/util/concurrent/CompletableFuture$AltResult done
Event: 6.215 Loading class java/util/concurrent/CompletableFuture$Completion
Event: 6.215 Loading class java/util/concurrent/CompletableFuture$AsynchronousCompletionTask
Event: 6.215 Loading class java/util/concurrent/CompletableFuture$AsynchronousCompletionTask done
Event: 6.215 Loading class java/util/concurrent/CompletableFuture$Completion done
Event: 6.220 Loading class sun/nio/ch/WindowsAsynchronousFileChannelImpl$WriteTask
Event: 6.220 Loading class sun/nio/ch/WindowsAsynchronousFileChannelImpl$WriteTask done
Event: 28.774 Loading class java/util/concurrent/CancellationException
Event: 28.775 Loading class java/util/concurrent/CancellationException done
Event: 28.775 Loading class java/nio/channels/ClosedChannelException
Event: 28.775 Loading class java/nio/channels/ClosedChannelException done
Event: 28.775 Loading class java/util/logging/LogRecord

Classes unloaded (7 events):
Event: 3.023 Thread 0x0000019a5cbc3ed0 Unloading class 0x0000019a01182c00 'java/lang/invoke/LambdaForm$MH+0x0000019a01182c00'
Event: 3.023 Thread 0x0000019a5cbc3ed0 Unloading class 0x0000019a01182800 'java/lang/invoke/LambdaForm$MH+0x0000019a01182800'
Event: 3.023 Thread 0x0000019a5cbc3ed0 Unloading class 0x0000019a01182400 'java/lang/invoke/LambdaForm$MH+0x0000019a01182400'
Event: 3.023 Thread 0x0000019a5cbc3ed0 Unloading class 0x0000019a01182000 'java/lang/invoke/LambdaForm$MH+0x0000019a01182000'
Event: 3.023 Thread 0x0000019a5cbc3ed0 Unloading class 0x0000019a01181c00 'java/lang/invoke/LambdaForm$BMH+0x0000019a01181c00'
Event: 3.023 Thread 0x0000019a5cbc3ed0 Unloading class 0x0000019a01181800 'java/lang/invoke/LambdaForm$DMH+0x0000019a01181800'
Event: 3.023 Thread 0x0000019a5cbc3ed0 Unloading class 0x0000019a01180800 'java/lang/invoke/LambdaForm$DMH+0x0000019a01180800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 3.697 Thread 0x0000019a7a1d8c90 Exception <a 'java/lang/UnsatisfiedLinkError'{0x00000000d689ff30}: 找不到指定的程序。

> (0x00000000d689ff30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 539]
Event: 3.700 Thread 0x0000019a7a1d8c90 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d68ac1f8}: 'void java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d68ac1f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.724 Thread 0x0000019a5cb88f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6b843d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long, java.lang.Object)'> (0x00000000d6b843d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.725 Thread 0x0000019a5cb88f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6b87a38}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, java.lang.Object)'> (0x00000000d6b87a38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.725 Thread 0x0000019a5cb88f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6b8d158}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, long)'> (0x00000000d6b8d158) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.770 Thread 0x0000019a7a546220 Exception <a 'java/io/FileNotFoundException'{0x00000000d579a838}> (0x00000000d579a838) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.771 Thread 0x0000019a7a546220 Exception <a 'java/io/FileNotFoundException'{0x00000000d579be28}> (0x00000000d579be28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.772 Thread 0x0000019a7a546220 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57aa158}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d57aa158) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.772 Thread 0x0000019a7a546220 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57adfb0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d57adfb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.772 Thread 0x0000019a7a546220 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57b1448}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d57b1448) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.773 Thread 0x0000019a7a546220 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57bce08}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d57bce08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.777 Thread 0x0000019a7a546220 Exception <a 'java/io/FileNotFoundException'{0x00000000d57e6b90}> (0x00000000d57e6b90) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.777 Thread 0x0000019a7a546220 Exception <a 'java/io/FileNotFoundException'{0x00000000d57e7b30}> (0x00000000d57e7b30) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 3.932 Thread 0x0000019a5cb88f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d625bc70}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d625bc70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 3.933 Thread 0x0000019a5cb88f60 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d6264030}: Found class java.lang.Object, but interface was expected> (0x00000000d6264030) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 3.933 Thread 0x0000019a5cb88f60 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d6265e20}: Found class java.lang.Object, but interface was expected> (0x00000000d6265e20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 6.154 Thread 0x0000019a7bbba4d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6a1a050}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d6a1a050) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.165 Thread 0x0000019a7bbba4d0 Exception <a 'java/io/FileNotFoundException'{0x00000000d6aa59b0}> (0x00000000d6aa59b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 6.192 Thread 0x0000019a7bbba4d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d59f9350}> (0x00000000d59f9350) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 14.958 Thread 0x0000019a7bbba4d0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5c8e8e0}: Found class java.lang.Object, but interface was expected> (0x00000000d5c8e8e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 3.455 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.455 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.663 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 3.663 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 3.732 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 3.746 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 5.746 Executing VM operation: Cleanup
Event: 5.746 Executing VM operation: Cleanup done
Event: 6.159 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 6.159 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 6.169 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 6.171 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 7.171 Executing VM operation: Cleanup
Event: 7.171 Executing VM operation: Cleanup done
Event: 15.175 Executing VM operation: Cleanup
Event: 15.175 Executing VM operation: Cleanup done
Event: 17.175 Executing VM operation: Cleanup
Event: 17.175 Executing VM operation: Cleanup done
Event: 18.175 Executing VM operation: Cleanup
Event: 18.175 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 3.132 Thread 0x0000019a7a78cd10 Thread exited: 0x0000019a7a78cd10
Event: 3.632 Thread 0x0000019a75169e00 Thread exited: 0x0000019a75169e00
Event: 3.668 Thread 0x0000019a7a1d8c90 Thread added: 0x0000019a7a78c680
Event: 3.714 Thread 0x0000019a732fb4a0 Thread added: 0x0000019a7a783fa0
Event: 3.731 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7a78d3a0
Event: 3.731 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7a78da30
Event: 3.731 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7a78fb00
Event: 3.731 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7a78ede0
Event: 3.732 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7a78f470
Event: 3.732 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7a78e0c0
Event: 3.732 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7a78cd10
Event: 3.732 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7a78e750
Event: 3.732 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7bbb9120
Event: 3.798 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7bbb8a90
Event: 3.944 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7bbbb880
Event: 3.944 Thread 0x0000019a5cb88f60 Thread added: 0x0000019a7bbba4d0
Event: 6.097 Thread 0x0000019a7a783fa0 Thread exited: 0x0000019a7a783fa0
Event: 6.217 Thread 0x0000019a732fb4a0 Thread added: 0x0000019a7ae94250
Event: 7.386 Thread 0x0000019a7574d600 Thread exited: 0x0000019a7574d600
Event: 11.244 Thread 0x0000019a7ae94250 Thread exited: 0x0000019a7ae94250


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff8400b0000 - 0x00007ff8400f5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff861060000 - 0x00007ff861078000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ff860720000 - 0x00007ff860758000 	C:\Windows\system32\rsaenh.dll
0x00007ff8615f0000 - 0x00007ff86161e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ff861050000 - 0x00007ff86105c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ff860b40000 - 0x00007ff860b7b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ff8626f0000 - 0x00007ff8626f8000 	C:\Windows\System32\NSI.dll
0x00007ff83d810000 - 0x00007ff83d859000 	C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna17176408055758091470.dll
0x00007ff85c020000 - 0x00007ff85c037000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ff85bf90000 - 0x00007ff85bfad000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-146731693

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-dce2e3da99307a614c97d32a419f5fbe-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:30 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (2881M free)
TotalPageFile size 61318M (AvailPageFile size 2068M)
current process WorkingSet (physical memory assigned to process): 145M, peak: 166M
current process commit charge ("private bytes"): 274M, peak: 301M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
