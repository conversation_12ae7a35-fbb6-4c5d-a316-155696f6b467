// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		82BB8AFE23580139004E0BF7 /* tersafe2.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 82BB8AFD23580139004E0BF7 /* tersafe2.framework */; };
		82BB8AFF2358015E004E0BF7 /* tersafe2.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 82BB8AFD23580139004E0BF7 /* tersafe2.framework */; };
		82BB8B002358015E004E0BF7 /* tersafe2.framework in Embed Frameworks */ = {isa = PBXBuildFile; fileRef = 82BB8AFD23580139004E0BF7 /* tersafe2.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		B8F7E30C1CC764B200973FFB /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = B8F7E30B1CC764B200973FFB /* main.m */; };
		B8F7E30F1CC764B200973FFB /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = B8F7E30E1CC764B200973FFB /* AppDelegate.m */; };
		B8F7E3121CC764B200973FFB /* Tp2DemoC.xcdatamodeld in Sources */ = {isa = PBXBuildFile; fileRef = B8F7E3101CC764B200973FFB /* Tp2DemoC.xcdatamodeld */; };
		B8F7E3151CC764B200973FFB /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = B8F7E3141CC764B200973FFB /* ViewController.m */; };
		B8F7E3181CC764B200973FFB /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = B8F7E3161CC764B200973FFB /* Main.storyboard */; };
		B8F7E31A1CC764B200973FFB /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = B8F7E3191CC764B200973FFB /* Images.xcassets */; };
		B8F7E31D1CC764B200973FFB /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = B8F7E31B1CC764B200973FFB /* LaunchScreen.xib */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B8F7E3231CC764B200973FFB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B8F7E2FE1CC764B200973FFB /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B8F7E3051CC764B200973FFB;
			remoteInfo = Tp2DemoC;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		82BB8B012358015E004E0BF7 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				82BB8B002358015E004E0BF7 /* tersafe2.framework in Embed Frameworks */,
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		35E908031F9A214900FC5D52 /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		82BB8AFD23580139004E0BF7 /* tersafe2.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = tersafe2.framework; sourceTree = "<group>"; };
		B8F7E3061CC764B200973FFB /* Tp2DemoC.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Tp2DemoC.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B8F7E30A1CC764B200973FFB /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		B8F7E30B1CC764B200973FFB /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		B8F7E30D1CC764B200973FFB /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		B8F7E30E1CC764B200973FFB /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		B8F7E3111CC764B200973FFB /* Tp2DemoC.xcdatamodel */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcdatamodel; path = Tp2DemoC.xcdatamodel; sourceTree = "<group>"; };
		B8F7E3131CC764B200973FFB /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		B8F7E3141CC764B200973FFB /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		B8F7E3171CC764B200973FFB /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		B8F7E3191CC764B200973FFB /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		B8F7E31C1CC764B200973FFB /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		B8F7E3221CC764B200973FFB /* Tp2DemoCTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Tp2DemoCTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		B8F7E3031CC764B200973FFB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				82BB8AFF2358015E004E0BF7 /* tersafe2.framework in Frameworks */,
				82BB8AFE23580139004E0BF7 /* tersafe2.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B8F7E31F1CC764B200973FFB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		35E908011F9A213300FC5D52 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				82BB8AFD23580139004E0BF7 /* tersafe2.framework */,
				35E908031F9A214900FC5D52 /* SystemConfiguration.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B8F7E2FD1CC764B200973FFB = {
			isa = PBXGroup;
			children = (
				B8F7E3081CC764B200973FFB /* Tp2DemoC */,
				B8F7E3071CC764B200973FFB /* Products */,
				35E908011F9A213300FC5D52 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		B8F7E3071CC764B200973FFB /* Products */ = {
			isa = PBXGroup;
			children = (
				B8F7E3061CC764B200973FFB /* Tp2DemoC.app */,
				B8F7E3221CC764B200973FFB /* Tp2DemoCTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B8F7E3081CC764B200973FFB /* Tp2DemoC */ = {
			isa = PBXGroup;
			children = (
				B8F7E30D1CC764B200973FFB /* AppDelegate.h */,
				B8F7E30E1CC764B200973FFB /* AppDelegate.m */,
				B8F7E3131CC764B200973FFB /* ViewController.h */,
				B8F7E3141CC764B200973FFB /* ViewController.m */,
				B8F7E3161CC764B200973FFB /* Main.storyboard */,
				B8F7E3191CC764B200973FFB /* Images.xcassets */,
				B8F7E31B1CC764B200973FFB /* LaunchScreen.xib */,
				B8F7E3101CC764B200973FFB /* Tp2DemoC.xcdatamodeld */,
				B8F7E3091CC764B200973FFB /* Supporting Files */,
			);
			path = Tp2DemoC;
			sourceTree = "<group>";
		};
		B8F7E3091CC764B200973FFB /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				B8F7E30A1CC764B200973FFB /* Info.plist */,
				B8F7E30B1CC764B200973FFB /* main.m */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B8F7E3051CC764B200973FFB /* Tp2DemoC */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B8F7E32C1CC764B200973FFB /* Build configuration list for PBXNativeTarget "Tp2DemoC" */;
			buildPhases = (
				B8F7E3021CC764B200973FFB /* Sources */,
				B8F7E3031CC764B200973FFB /* Frameworks */,
				B8F7E3041CC764B200973FFB /* Resources */,
				82BB8B012358015E004E0BF7 /* Embed Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Tp2DemoC;
			productName = Tp2DemoC;
			productReference = B8F7E3061CC764B200973FFB /* Tp2DemoC.app */;
			productType = "com.apple.product-type.application";
		};
		B8F7E3211CC764B200973FFB /* Tp2DemoCTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B8F7E32F1CC764B200973FFB /* Build configuration list for PBXNativeTarget "Tp2DemoCTests" */;
			buildPhases = (
				B8F7E31E1CC764B200973FFB /* Sources */,
				B8F7E31F1CC764B200973FFB /* Frameworks */,
				B8F7E3201CC764B200973FFB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B8F7E3241CC764B200973FFB /* PBXTargetDependency */,
			);
			name = Tp2DemoCTests;
			productName = Tp2DemoCTests;
			productReference = B8F7E3221CC764B200973FFB /* Tp2DemoCTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B8F7E2FE1CC764B200973FFB /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0640;
				ORGANIZATIONNAME = TX;
				TargetAttributes = {
					B8F7E3051CC764B200973FFB = {
						CreatedOnToolsVersion = 6.4;
						DevelopmentTeam = QFY695AQ8Y;
					};
					B8F7E3211CC764B200973FFB = {
						CreatedOnToolsVersion = 6.4;
						TestTargetID = B8F7E3051CC764B200973FFB;
					};
				};
			};
			buildConfigurationList = B8F7E3011CC764B200973FFB /* Build configuration list for PBXProject "Tp2DemoC" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
				Base,
			);
			mainGroup = B8F7E2FD1CC764B200973FFB;
			productRefGroup = B8F7E3071CC764B200973FFB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B8F7E3051CC764B200973FFB /* Tp2DemoC */,
				B8F7E3211CC764B200973FFB /* Tp2DemoCTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B8F7E3041CC764B200973FFB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B8F7E3181CC764B200973FFB /* Main.storyboard in Resources */,
				B8F7E31D1CC764B200973FFB /* LaunchScreen.xib in Resources */,
				B8F7E31A1CC764B200973FFB /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B8F7E3201CC764B200973FFB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B8F7E3021CC764B200973FFB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B8F7E3121CC764B200973FFB /* Tp2DemoC.xcdatamodeld in Sources */,
				B8F7E30F1CC764B200973FFB /* AppDelegate.m in Sources */,
				B8F7E3151CC764B200973FFB /* ViewController.m in Sources */,
				B8F7E30C1CC764B200973FFB /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B8F7E31E1CC764B200973FFB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B8F7E3241CC764B200973FFB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B8F7E3051CC764B200973FFB /* Tp2DemoC */;
			targetProxy = B8F7E3231CC764B200973FFB /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		B8F7E3161CC764B200973FFB /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				B8F7E3171CC764B200973FFB /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		B8F7E31B1CC764B200973FFB /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				B8F7E31C1CC764B200973FFB /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		B8F7E32A1CC764B200973FFB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B8F7E32B1CC764B200973FFB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B8F7E32D1CC764B200973FFB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_OBJC_ARC = NO;
				COPY_PHASE_STRIP = YES;
				DEPLOYMENT_POSTPROCESSING = YES;
				DEVELOPMENT_TEAM = QFY695AQ8Y;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Tp2DemoC",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = Tp2DemoC/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Tp2DemoC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.tenc3ent.Tp2DemoC;
				PRODUCT_NAME = "$(TARGET_NAME)";
				VALIDATE_PRODUCT = YES;
				VALID_ARCHS = "arm64 armv7";
			};
			name = Debug;
		};
		B8F7E32E1CC764B200973FFB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_OBJC_ARC = NO;
				COPY_PHASE_STRIP = YES;
				DEPLOYMENT_POSTPROCESSING = YES;
				DEVELOPMENT_TEAM = QFY695AQ8Y;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Tp2DemoC",
					"$(PROJECT_DIR)",
				);
				GCC_OPTIMIZATION_LEVEL = s;
				INFOPLIST_FILE = Tp2DemoC/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Tp2DemoC",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.tenc3ent.Tp2DemoC;
				PRODUCT_NAME = "$(TARGET_NAME)";
				VALIDATE_PRODUCT = YES;
				VALID_ARCHS = "arm64 armv7";
			};
			name = Release;
		};
		B8F7E3301CC764B200973FFB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = Tp2DemoCTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Tp2DemoC.app/Tp2DemoC";
			};
			name = Debug;
		};
		B8F7E3311CC764B200973FFB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(inherited)",
				);
				INFOPLIST_FILE = Tp2DemoCTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Tp2DemoC.app/Tp2DemoC";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B8F7E3011CC764B200973FFB /* Build configuration list for PBXProject "Tp2DemoC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B8F7E32A1CC764B200973FFB /* Debug */,
				B8F7E32B1CC764B200973FFB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B8F7E32C1CC764B200973FFB /* Build configuration list for PBXNativeTarget "Tp2DemoC" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B8F7E32D1CC764B200973FFB /* Debug */,
				B8F7E32E1CC764B200973FFB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B8F7E32F1CC764B200973FFB /* Build configuration list for PBXNativeTarget "Tp2DemoCTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B8F7E3301CC764B200973FFB /* Debug */,
				B8F7E3311CC764B200973FFB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCVersionGroup section */
		B8F7E3101CC764B200973FFB /* Tp2DemoC.xcdatamodeld */ = {
			isa = XCVersionGroup;
			children = (
				B8F7E3111CC764B200973FFB /* Tp2DemoC.xcdatamodel */,
			);
			currentVersion = B8F7E3111CC764B200973FFB /* Tp2DemoC.xcdatamodel */;
			path = Tp2DemoC.xcdatamodeld;
			sourceTree = "<group>";
			versionGroupType = wrapper.xcdatamodel;
		};
/* End XCVersionGroup section */
	};
	rootObject = B8F7E2FE1CC764B200973FFB /* Project object */;
}
