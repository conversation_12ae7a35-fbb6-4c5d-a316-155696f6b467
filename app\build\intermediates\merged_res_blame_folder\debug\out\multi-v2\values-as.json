{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-as\\values-as.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,312,419,510,612,732,809,884,975,1068,1163,1257,1357,1450,1545,1639,1730,1821,1907,2020,2128,2231,2340,2456,2576,2743,2845", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "208,307,414,505,607,727,804,879,970,1063,1158,1252,1352,1445,1540,1634,1725,1816,1902,2015,2123,2226,2335,2451,2571,2738,2840,2923"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "277,385,484,591,682,784,904,981,1056,1147,1240,1335,1429,1529,1622,1717,1811,1902,1993,2079,2192,2300,2403,2512,2628,2748,2915,9360", "endColumns": "107,98,106,90,101,119,76,74,90,92,94,93,99,92,94,93,90,90,85,112,107,102,108,115,119,166,101,82", "endOffsets": "380,479,586,677,779,899,976,1051,1142,1235,1330,1424,1524,1617,1712,1806,1897,1988,2074,2187,2295,2398,2507,2623,2743,2910,3012,9438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,227,304,406,529,608,673,762,827,886,972,1036,1099,1169,1233,1287,1392,1450,1512,1566,1638,1755,1842,1925,2035,2112,2193,2284,2351,2417,2487,2564,2651,2722,2799,2868,2937,3028,3100,3189,3278,3352,3424,3510,3560,3626,3706,3790,3852,3916,3979,4079,4176,4268,4367", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,80", "endOffsets": "222,299,401,524,603,668,757,822,881,967,1031,1094,1164,1228,1282,1387,1445,1507,1561,1633,1750,1837,1920,2030,2107,2188,2279,2346,2412,2482,2559,2646,2717,2794,2863,2932,3023,3095,3184,3273,3347,3419,3505,3555,3621,3701,3785,3847,3911,3974,4074,4171,4263,4362,4443"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3017,3094,3196,3319,5520,5585,5674,5739,5798,5884,5948,6011,6081,6145,6199,6304,6362,6424,6478,6550,6667,6754,6837,6947,7024,7105,7196,7263,7329,7399,7476,7563,7634,7711,7780,7849,7940,8012,8101,8190,8264,8336,8422,8472,8538,8618,8702,8764,8828,8891,8991,9088,9180,9279", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,76,101,122,78,64,88,64,58,85,63,62,69,63,53,104,57,61,53,71,116,86,82,109,76,80,90,66,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,80", "endOffsets": "272,3089,3191,3314,3393,5580,5669,5734,5793,5879,5943,6006,6076,6140,6194,6299,6357,6419,6473,6545,6662,6749,6832,6942,7019,7100,7191,7258,7324,7394,7471,7558,7629,7706,7775,7844,7935,8007,8096,8185,8259,8331,8417,8467,8533,8613,8697,8759,8823,8886,8986,9083,9175,9274,9355"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9443", "endColumns": "100", "endOffsets": "9539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-as\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3398,3506,3659,3782,3892,4022,4144,4257,4495,4638,4747,4897,5022,5155,5308,5368,5434", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "3501,3654,3777,3887,4017,4139,4252,4364,4633,4742,4892,5017,5150,5303,5363,5429,5515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-as\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4369", "endColumns": "125", "endOffsets": "4490"}}]}]}