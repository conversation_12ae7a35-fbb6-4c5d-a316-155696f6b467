#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=14484, tid=43576
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-d96048e3bea9b8c31f19547286186b1e-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:19:54 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 0.705289 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x0000023765f48f60):  JavaThread "main"             [_thread_in_vm, id=43576, stack(0x0000007384c00000,0x0000007384d00000) (1024K)]

Stack: [0x0000007384c00000,0x0000007384d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0x8a41ee]
V  [jvm.dll+0x670575]
V  [jvm.dll+0x1e474c]
V  [jvm.dll+0x1e451e]
V  [jvm.dll+0x672e72]
V  [jvm.dll+0x672c92]
V  [jvm.dll+0x670f4e]
V  [jvm.dll+0x3becef]
V  [jvm.dll+0x20d78b]
V  [jvm.dll+0x5ae5b6]
V  [jvm.dll+0x821706]
V  [jvm.dll+0x4718d6]
V  [jvm.dll+0x4777a8]
C  [java.dll+0x17ec]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  java.lang.ClassLoader.defineClass1(Ljava/lang/ClassLoader;Ljava/lang/String;[BIILjava/security/ProtectionDomain;Ljava/lang/String;)Ljava/lang/Class;+0 java.base@21.0.7
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;[BIILjava/security/ProtectionDomain;)Ljava/lang/Class;+27 java.base@21.0.7
j  java.lang.ClassLoader.defineClass(Ljava/lang/String;[BII)Ljava/lang/Class;+7 java.base@21.0.7
j  lombok.launch.ShadowClassLoader.urlToDefineClass(Ljava/lang/String;Ljava/net/URL;Z)Ljava/lang/Class;+155
j  lombok.launch.ShadowClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+238
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.7
v  ~StubRoutines::call_stub 0x000002377066100d
j  lombok.eclipse.agent.EclipsePatcher.patchSetGeneratedFlag(Llombok/patcher/ScriptManager;)V+81
j  lombok.eclipse.agent.EclipsePatcher.registerPatchScripts(Ljava/lang/instrument/Instrumentation;ZLjava/lang/Class;)V+72
j  lombok.eclipse.agent.EclipsePatcher.runAgent(Ljava/lang/String;Ljava/lang/instrument/Instrumentation;ZLjava/lang/Class;)V+4
j  lombok.core.AgentLauncher.runAgents(Ljava/lang/String;Ljava/lang/instrument/Instrumentation;ZLjava/lang/Class;)V+62
j  java.lang.invoke.LambdaForm$DMH+0x0000023701004000.invokeStatic(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V+16 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x0000023701008400.invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+124 java.base@21.0.7
j  java.lang.invoke.Invokers$Holder.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+20 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invokeImpl(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+104 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+23 java.base@21.0.7
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+102 java.base@21.0.7
j  lombok.launch.Agent.runLauncher(Ljava/lang/String;Ljava/lang/instrument/Instrumentation;Z)V+73
j  lombok.launch.Agent.premain(Ljava/lang/String;Ljava/lang/instrument/Instrumentation;)V+3
j  java.lang.invoke.LambdaForm$DMH+0x0000023701000c00.invokeStatic(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V+11 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x0000023701002800.invoke(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+54 java.base@21.0.7
j  java.lang.invoke.LambdaForm$MH+0x0000023701002c00.invokeExact_MT(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+22 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invokeImpl(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+72 java.base@21.0.7
j  jdk.internal.reflect.DirectMethodHandleAccessor.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+23 java.base@21.0.7
j  java.lang.reflect.Method.invoke(Ljava/lang/Object;[Ljava/lang/Object;)Ljava/lang/Object;+102 java.base@21.0.7
j  sun.instrument.InstrumentationImpl.loadClassAndStartAgent(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V+165 java.instrument@21.0.7
j  sun.instrument.InstrumentationImpl.loadClassAndCallPremain(Ljava/lang/String;Ljava/lang/String;)V+6 java.instrument@21.0.7
v  ~StubRoutines::call_stub 0x000002377066100d

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002377dee4610, length=10, elements={
0x0000023765f48f60, 0x000002377cfc1d40, 0x000002377de036e0, 0x000002377de06ef0,
0x000002377de09c10, 0x000002377de0aba0, 0x000002377de0c600, 0x000002377de0d430,
0x000002377de70aa0, 0x0000023765f75db0
}

Java Threads: ( => current thread )
=>0x0000023765f48f60 JavaThread "main"                              [_thread_in_vm, id=43576, stack(0x0000007384c00000,0x0000007384d00000) (1024K)]
  0x000002377cfc1d40 JavaThread "Reference Handler"          daemon [_thread_blocked, id=34860, stack(0x0000007385000000,0x0000007385100000) (1024K)]
  0x000002377de036e0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=38744, stack(0x0000007385100000,0x0000007385200000) (1024K)]
  0x000002377de06ef0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=22168, stack(0x0000007385200000,0x0000007385300000) (1024K)]
  0x000002377de09c10 JavaThread "Attach Listener"            daemon [_thread_blocked, id=16024, stack(0x0000007385300000,0x0000007385400000) (1024K)]
  0x000002377de0aba0 JavaThread "Service Thread"             daemon [_thread_blocked, id=47096, stack(0x0000007385400000,0x0000007385500000) (1024K)]
  0x000002377de0c600 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=46816, stack(0x0000007385500000,0x0000007385600000) (1024K)]
  0x000002377de0d430 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=15800, stack(0x0000007385600000,0x0000007385700000) (1024K)]
  0x000002377de70aa0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=29840, stack(0x0000007385700000,0x0000007385800000) (1024K)]
  0x0000023765f75db0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=17144, stack(0x0000007385800000,0x0000007385900000) (1024K)]
Total: 10

Other Threads:
  0x0000023763c8e970 VMThread "VM Thread"                           [id=46088, stack(0x0000007384f00000,0x0000007385000000) (1024K)]
  0x0000023765f74fe0 WatcherThread "VM Periodic Task Thread"        [id=29964, stack(0x0000007384e00000,0x0000007384f00000) (1024K)]
  0x0000023765f66f90 WorkerThread "GC Thread#0"                     [id=42288, stack(0x0000007384d00000,0x0000007384e00000) (1024K)]
Total: 3

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45c308] Metaspace_lock - owner thread: 0x0000023765f48f60

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000023700000000-0x0000023700ba0000-0x0000023700ba0000), size 12189696, SharedBaseAddress: 0x0000023700000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000023701000000-0x0000023741000000, reserved size: 1073741824
Narrow klass base: 0x0000023700000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 9092K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 35% used [0x00000000d5580000,0x00000000d5e61000,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 1098K, committed 1216K, reserved 1114112K
  class space    used 96K, committed 128K, reserved 1048576K

Card table byte_map: [0x0000023778310000,0x0000023778720000] _byte_map_base: 0x0000023777f10000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x00000237789d0000, 0x000002377a9d0000)
 End Bits:   [0x000002377a9d0000, 0x000002377c9d0000)

Polling page: 0x0000023765dd0000

Metaspace:

Usage:
  Non-class:   1001.48 KB used.
      Class:     96.90 KB used.
       Both:      1.07 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       1.06 MB (  2%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     128.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       1.19 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.69 MB
       Class:  15.67 MB
        Both:  27.36 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 74.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 19.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 94.
num_chunk_merges: 0.
num_chunk_splits: 65.
num_chunks_enlarged: 42.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=178Kb max_used=178Kb free=119821Kb
 bounds [0x0000023770c00000, 0x0000023770e70000, 0x0000023778130000]
CodeHeap 'profiled nmethods': size=120000Kb used=710Kb max_used=710Kb free=119289Kb
 bounds [0x0000023769130000, 0x00000237693a0000, 0x0000023770660000]
CodeHeap 'non-nmethods': size=5760Kb used=1151Kb max_used=1171Kb free=4608Kb
 bounds [0x0000023770660000, 0x00000237708d0000, 0x0000023770c00000]
 total_blobs=922 nmethods=522 adapters=307
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.695 Thread 0x000002377de70aa0 nmethod 510 0x00000237691d9e90 code [0x00000237691da060, 0x00000237691da278]
Event: 0.696 Thread 0x000002377de70aa0  511   !   3       java.io.WinNTFileSystem::resolve (362 bytes)
Event: 0.698 Thread 0x000002377de70aa0 nmethod 511 0x00000237691da390 code [0x00000237691dab40, 0x00000237691de438]
Event: 0.699 Thread 0x000002377de70aa0  513       1       java.net.URL::getUserInfo (5 bytes)
Event: 0.699 Thread 0x000002377de70aa0 nmethod 513 0x0000023770c2a490 code [0x0000023770c2a620, 0x0000023770c2a6e8]
Event: 0.700 Thread 0x000002377de70aa0  514       3       java.lang.StringLatin1::indexOf (121 bytes)
Event: 0.700 Thread 0x000002377de0d430  515 %     4       lombok.launch.PackageShader::apply @ 76 (252 bytes)
Event: 0.700 Thread 0x000002377de70aa0 nmethod 514 0x00000237691dfc10 code [0x00000237691dfe00, 0x00000237691e02b8]
Event: 0.701 Thread 0x000002377de70aa0  517       3       java.util.ArrayList::iterator (9 bytes)
Event: 0.701 Thread 0x000002377de70aa0 nmethod 517 0x00000237691e0510 code [0x00000237691e06c0, 0x00000237691e0918]
Event: 0.701 Thread 0x000002377de70aa0  518       3       java.util.ArrayList$Itr::<init> (26 bytes)
Event: 0.701 Thread 0x000002377de70aa0 nmethod 518 0x00000237691e0a10 code [0x00000237691e0bc0, 0x00000237691e0d58]
Event: 0.702 Thread 0x000002377de70aa0  519       3       java.util.WeakHashMap::maskNull (12 bytes)
Event: 0.702 Thread 0x000002377de70aa0 nmethod 519 0x00000237691e0e90 code [0x00000237691e1020, 0x00000237691e1158]
Event: 0.702 Thread 0x000002377de70aa0  520       3       java.util.WeakHashMap::hash (28 bytes)
Event: 0.702 Thread 0x000002377de70aa0 nmethod 520 0x00000237691e1210 code [0x00000237691e13c0, 0x00000237691e15b8]
Event: 0.702 Thread 0x000002377de70aa0  521       3       java.util.WeakHashMap::getTable (9 bytes)
Event: 0.702 Thread 0x000002377de70aa0 nmethod 521 0x00000237691e1690 code [0x00000237691e1840, 0x00000237691e1990]
Event: 0.702 Thread 0x000002377de70aa0  522       1       java.net.URLConnection::getUseCaches (5 bytes)
Event: 0.702 Thread 0x000002377de70aa0 nmethod 522 0x0000023770c2a790 code [0x0000023770c2a920, 0x0000023770c2a9f0]

GC Heap History (0 events):
No events

Dll operation events (8 events):
Event: 0.016 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.050 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.141 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.145 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.148 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.152 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.194 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.341 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (18 events):
Event: 0.155 Thread 0x0000023765f48f60 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000023770c0f8e8 relative=0x00000000000007c8
Event: 0.155 Thread 0x0000023765f48f60 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000023770c0f8e8 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 152 c2
Event: 0.155 Thread 0x0000023765f48f60 DEOPT PACKING pc=0x0000023770c0f8e8 sp=0x0000007384cfe1f0
Event: 0.155 Thread 0x0000023765f48f60 DEOPT UNPACKING pc=0x00000237706b3aa2 sp=0x0000007384cfe150 mode 2
Event: 0.155 Thread 0x0000023765f48f60 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000023770c0f8e8 relative=0x00000000000007c8
Event: 0.155 Thread 0x0000023765f48f60 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000023770c0f8e8 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 152 c2
Event: 0.155 Thread 0x0000023765f48f60 DEOPT PACKING pc=0x0000023770c0f8e8 sp=0x0000007384cfe1f0
Event: 0.155 Thread 0x0000023765f48f60 DEOPT UNPACKING pc=0x00000237706b3aa2 sp=0x0000007384cfe150 mode 2
Event: 0.201 Thread 0x0000023765f48f60 DEOPT PACKING pc=0x0000023769166049 sp=0x0000007384cfdfd0
Event: 0.201 Thread 0x0000023765f48f60 DEOPT UNPACKING pc=0x00000237706b4242 sp=0x0000007384cfd458 mode 0
Event: 0.201 Thread 0x0000023765f48f60 DEOPT PACKING pc=0x0000023769166049 sp=0x0000007384cfdfd0
Event: 0.201 Thread 0x0000023765f48f60 DEOPT UNPACKING pc=0x00000237706b4242 sp=0x0000007384cfd458 mode 0
Event: 0.202 Thread 0x0000023765f48f60 DEOPT PACKING pc=0x0000023769166049 sp=0x0000007384cfdfd0
Event: 0.202 Thread 0x0000023765f48f60 DEOPT UNPACKING pc=0x00000237706b4242 sp=0x0000007384cfd458 mode 0
Event: 0.202 Thread 0x0000023765f48f60 DEOPT PACKING pc=0x0000023769166049 sp=0x0000007384cfdfd0
Event: 0.202 Thread 0x0000023765f48f60 DEOPT UNPACKING pc=0x00000237706b4242 sp=0x0000007384cfd458 mode 0
Event: 0.203 Thread 0x0000023765f48f60 DEOPT PACKING pc=0x0000023769166049 sp=0x0000007384cfdfd0
Event: 0.203 Thread 0x0000023765f48f60 DEOPT UNPACKING pc=0x00000237706b4242 sp=0x0000007384cfd458 mode 0

Classes loaded (20 events):
Event: 0.235 Loading class java/lang/invoke/BoundMethodHandle$Species_LI
Event: 0.235 Loading class java/lang/invoke/BoundMethodHandle$Species_LI done
Event: 0.345 Loading class java/lang/IllegalAccessException
Event: 0.346 Loading class java/lang/IllegalAccessException done
Event: 0.397 Loading class java/lang/IllegalStateException
Event: 0.397 Loading class java/lang/IllegalStateException done
Event: 0.404 Loading class java/lang/instrument/ClassFileTransformer
Event: 0.404 Loading class java/lang/instrument/ClassFileTransformer done
Event: 0.406 Loading class java/lang/UnsupportedOperationException
Event: 0.406 Loading class java/lang/UnsupportedOperationException done
Event: 0.423 Loading class java/lang/instrument/UnmodifiableClassException
Event: 0.423 Loading class java/lang/instrument/UnmodifiableClassException done
Event: 0.423 Loading class java/lang/instrument/ClassDefinition
Event: 0.423 Loading class java/lang/instrument/ClassDefinition done
Event: 0.443 Loading class java/lang/invoke/WrongMethodTypeException
Event: 0.443 Loading class java/lang/invoke/WrongMethodTypeException done
Event: 0.466 Loading class java/util/regex/Pattern$Loop
Event: 0.466 Loading class java/util/regex/Pattern$Loop done
Event: 0.466 Loading class java/util/regex/Pattern$Prolog
Event: 0.466 Loading class java/util/regex/Pattern$Prolog done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (11 events):
Event: 0.121 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57e11f0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d57e11f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.162 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d594a8a8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d594a8a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.234 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a03040}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5a03040) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.236 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a15a28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5a15a28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.238 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a20e68}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a20e68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.238 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a248a0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5a248a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.241 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a3d590}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000d5a3d590) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.242 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a41ee0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a41ee0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.242 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a45a70}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a45a70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.243 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a48ee0}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a48ee0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.426 Thread 0x0000023765f48f60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5c08e60}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5c08e60) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (4 events):
Event: 0.123 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.123 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.209 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.209 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (10 events):
Event: 0.029 Thread 0x0000023765f48f60 Thread added: 0x0000023765f48f60
Event: 0.060 Thread 0x0000023765f48f60 Thread added: 0x000002377cfc1d40
Event: 0.060 Thread 0x0000023765f48f60 Thread added: 0x000002377de036e0
Event: 0.060 Thread 0x0000023765f48f60 Thread added: 0x000002377de06ef0
Event: 0.060 Thread 0x0000023765f48f60 Thread added: 0x000002377de09c10
Event: 0.060 Thread 0x0000023765f48f60 Thread added: 0x000002377de0aba0
Event: 0.060 Thread 0x0000023765f48f60 Thread added: 0x000002377de0c600
Event: 0.060 Thread 0x0000023765f48f60 Thread added: 0x000002377de0d430
Event: 0.091 Thread 0x0000023765f48f60 Thread added: 0x000002377de70aa0
Event: 0.114 Thread 0x0000023765f48f60 Thread added: 0x0000023765f75db0


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-d96048e3bea9b8c31f19547286186b1e-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:31 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (1989M free)
TotalPageFile size 61318M (AvailPageFile size 513M)
current process WorkingSet (physical memory assigned to process): 52M, peak: 52M
current process commit charge ("private bytes"): 227M, peak: 228M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
