@echo off
echo ========================================
echo 调试诱饵数据创建问题
echo ========================================
echo.

echo [1] 安装更新的APK...
adb install app\build\outputs\apk\debug\app-debug.apk
if %errorlevel% neq 0 (
    echo 安装失败!
    pause
    exit /b 1
)

echo.
echo [2] 启动应用...
adb shell am start -n com.sy.newfwg/.MainActivity

echo.
echo [3] 等待3秒让应用完全启动...
timeout /t 3 /nobreak > nul

echo.
echo [4] 清除日志缓存...
adb logcat -c

echo.
echo [5] 开始监控相关日志...
echo 请观察以下日志输出，查找诱饵数据创建相关信息：
echo.
echo 关键日志标识：
echo - "进入createDecoyData方法"
echo - "创建诱饵数据，目标值"
echo - "诱饵数据创建成功/失败"
echo - "nativeCreateDecoyData"
echo.
echo 按Ctrl+C停止监控
echo ========================================

adb logcat | findstr /i "createDecoyData 诱饵数据 decoy 进入createDecoyData memoryTrapManager"
