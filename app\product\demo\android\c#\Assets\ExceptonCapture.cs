using System;
using System.Collections;
using UnityEngine;

public class ExceptionCapture
{
	private static void throwException (Exception e)
	{
		if (e == null)
			return;
		
		testDeepFrame (e);
	}
	
	private static void testDeepFrame (Exception e)
	{
		throw e;
	}
	
	private static void findGameObjectByTag ()
	{
		System.Console.Write ("it will throw UnityException");
		GameObject go = GameObject.FindWithTag ("test");
		
		string gName = go.name;
		System.Console.Write (gName);
	}
	
	private static void findGameObject ()
	{
		System.Console.Write ("it will throw NullReferenceException");
		
		GameObject go = GameObject.Find ("test");
		string gName = go.name;
		
		System.Console.Write (gName);
	}

	private static void DoCrash ()
	{
		System.Console.Write ("it will Crash...");
		
		string[] strs = new string[100000000];
		int len = strs [0].Length;
		Debug.Log ("strings len:" + len);
		DoCrash ();
	}
	
	private static string[] selGridItems = new string[] {
		"Exception",
		"SystemException",
		"ApplicationException",
		"ArgumentException",
		"FormatException",
		"...",
		"MemberAccessException",
		"FileAccessException",
		"MethodAccessException",
		"MissingMemberException",
		"MissingMethodException",
		"MissingFieldException",
		"IndexOutOfException",
		"ArrayTypeMismatchException",
		"RankException",
		"IOException",
		"DirectionNotFoundException",
		"FileNotFoundException",
		"EndOfStreamException",
		"FileLoadException",
		"PathTooLongException",
		"ArithmeticException",
		"NotFiniteNumberException",
		"DivideByZeroException",
		"OutOfMemoryException",
		"NullReferenceException",
		"InvalidCastException",
		"InvalidOperationException"
	};

	public static void TrigException (int selGridInt)
	{
		
		switch (selGridInt) {
		case 0:
			throwException (new System.Exception ("Non-fatal error, an base C# exception"));
			break;
		case 1:
			throwException (new System.SystemException ("Fatal error, a system exception"));
			break;
		case 2:
			throwException (new System.ApplicationException ("Fatal error, an application exception"));
			break;
		case 3:
			throwException (new System.ArgumentException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 4:
			throwException (new System.FormatException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 5: // ignore
			break;
		case 6:
			throwException (new System.MemberAccessException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 7:
			throwException (new System.FieldAccessException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 8:
			throwException (new System.MethodAccessException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 9:
			throwException (new System.MissingMemberException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 10:
			throwException (new System.MissingMethodException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 11:
			throwException (new System.MissingFieldException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 12:
			throwException (new System.IndexOutOfRangeException (string.Format ("Non-Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 13:
			throwException (new System.ArrayTypeMismatchException (string.Format ("Non-Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 14:
			throwException (new System.RankException (string.Format ("Non-Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 15:
		case 16:
		case 17:
		case 18:
		case 19:
		case 20:
		case 21:
			throwException (new System.ArithmeticException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 22:
			throwException (new System.NotFiniteNumberException (string.Format ("Fatal error, {0} ", selGridItems [selGridInt])));
			break;
		case 23:
			int i = 0;
			i = 2 / i;
			break;
		case 24:
			throwException (new System.OutOfMemoryException ("Fatal error, OOM"));
			break;
		case 25:
			findGameObject ();
			break;
		case 26:
			System.Exception excep = null;
			System.IndexOutOfRangeException iore = (System.IndexOutOfRangeException)excep;
			System.Console.Write ("" + iore);
			break;
		case 27:
			findGameObjectByTag ();
			break;
		case 28:
			DoCrash ();
			break;
		case 29:
		default:
			try {
				throwException (new System.OutOfMemoryException ("Fatal error, out of memory"));
			} catch (System.Exception e) {
				//				BuglyAgent.ReportException (e, "Caught Exception");
				UnityEngine.Debug.LogError("There is an error:" + e.Message);
			}
			break;
		}
		
	}
}