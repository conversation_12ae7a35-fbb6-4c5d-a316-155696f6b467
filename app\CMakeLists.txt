cmake_minimum_required(VERSION 3.18.1)

project("newfwg")

# 添加综合陷阱系统库
add_library(
    comprehensive_trap
    SHARED
    src/main/cpp/comprehensive_trap_system.cpp
    src/main/cpp/comprehensive_trap_jni.cpp
    src/main/cpp/memory_trap_sdk.cpp
    src/main/cpp/memory_trap_sdk_jni.cpp
    src/main/cpp/AdvancedMemoryTrap.cpp
    src/main/cpp/AdvancedAntiCheatJNI.cpp
    src/main/cpp/DpMemoryTrap.cpp
    src/main/cpp/DpMemoryTrapJNI.cpp
    src/main/cpp/cb_trap_lib.cpp
    src/main/cpp/comprehensive_detector.cpp
    src/main/cpp/enhanced_detector.cpp
    src/main/cpp/mem_trap_detector.cpp
    src/main/cpp/simple_detector.cpp
)

# 查找预构建的NDK库
find_library(
    log-lib
    log
)

# 链接库
target_link_libraries(
    comprehensive_trap
    ${log-lib}
)