@echo off
echo ========================================
echo CA和CB内存范围修复测试脚本
echo ========================================

echo.
echo 1. 清理并重新编译项目...
call gradlew clean
if %errorlevel% neq 0 (
    echo 编译清理失败！
    pause
    exit /b 1
)

call gradlew assembleDebug
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 2. 安装应用到设备...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %errorlevel% neq 0 (
    echo 安装失败！
    pause
    exit /b 1
)

echo.
echo 3. 启动应用...
adb shell am start -n com.sy.newfwg/.MainActivity

echo.
echo 4. 等待应用初始化...
timeout /t 3 /nobreak

echo.
echo 5. 监控CA和CB内存范围创建日志...
echo 查找关键日志：
echo - "CA区域修复"
echo - "CB区域修复" 
echo - "内存池数量"
echo - "陷阱数量"
echo.

adb logcat -c
echo 开始监控日志（按Ctrl+C停止）...
adb logcat | findstr /i "DpMemoryTrap CA CB 修复 陷阱 内存池"

pause
