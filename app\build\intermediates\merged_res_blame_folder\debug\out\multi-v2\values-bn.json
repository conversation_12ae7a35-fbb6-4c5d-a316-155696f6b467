{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-bn\\values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,226,309,413,530,611,677,768,834,895,985,1052,1113,1182,1244,1298,1405,1464,1525,1579,1653,1773,1858,1942,2047,2118,2188,2275,2342,2408,2481,2561,2656,2725,2801,2881,2950,3045,3128,3218,3313,3387,3461,3554,3608,3675,3761,3846,3908,3972,4035,4137,4242,4335,4441", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,82,103,116,80,65,90,65,60,89,66,60,68,61,53,106,58,60,53,73,119,84,83,104,70,69,86,66,65,72,79,94,68,75,79,68,94,82,89,94,73,73,92,53,66,85,84,61,63,62,101,104,92,105,79", "endOffsets": "221,304,408,525,606,672,763,829,890,980,1047,1108,1177,1239,1293,1400,1459,1520,1574,1648,1768,1853,1937,2042,2113,2183,2270,2337,2403,2476,2556,2651,2720,2796,2876,2945,3040,3123,3213,3308,3382,3456,3549,3603,3670,3756,3841,3903,3967,4030,4132,4237,4330,4436,4516"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3006,3089,3193,3310,5558,5624,5715,5781,5842,5932,5999,6060,6129,6191,6245,6352,6411,6472,6526,6600,6720,6805,6889,6994,7065,7135,7222,7289,7355,7428,7508,7603,7672,7748,7828,7897,7992,8075,8165,8260,8334,8408,8501,8555,8622,8708,8793,8855,8919,8982,9084,9189,9282,9388", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,82,103,116,80,65,90,65,60,89,66,60,68,61,53,106,58,60,53,73,119,84,83,104,70,69,86,66,65,72,79,94,68,75,79,68,94,82,89,94,73,73,92,53,66,85,84,61,63,62,101,104,92,105,79", "endOffsets": "271,3084,3188,3305,3386,5619,5710,5776,5837,5927,5994,6055,6124,6186,6240,6347,6406,6467,6521,6595,6715,6800,6884,6989,7060,7130,7217,7284,7350,7423,7503,7598,7667,7743,7823,7892,7987,8070,8160,8255,8329,8403,8496,8550,8617,8703,8788,8850,8914,8977,9079,9184,9277,9383,9463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3391,3500,3659,3787,3898,4034,4156,4268,4524,4667,4776,4932,5060,5193,5341,5401,5468", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "3495,3654,3782,3893,4029,4151,4263,4367,4662,4771,4927,5055,5188,5336,5396,5463,5553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4372", "endColumns": "151", "endOffsets": "4519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9555", "endColumns": "100", "endOffsets": "9651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "276,384,490,596,685,790,911,994,1076,1167,1260,1354,1448,1548,1641,1736,1830,1921,2012,2098,2208,2312,2415,2523,2631,2736,2901,9468", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "379,485,591,680,785,906,989,1071,1162,1255,1349,1443,1543,1636,1731,1825,1916,2007,2093,2203,2307,2410,2518,2626,2731,2896,3001,9550"}}]}]}