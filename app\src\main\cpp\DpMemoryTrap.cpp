#include "DpMemoryTrap.h"
#include <sys/mman.h>
#include <cstring>
#include <cstdlib>
#include <unistd.h>
#include <errno.h>
#include <vector>
#include <string>
#include <memory>
#include <list>
#include <deque>
#include <map>
#include <thread>
#include <chrono>
#include <atomic>
#include <fstream>  // 添加这一行来包含 fstream 头文件
#include <sstream>  // 添加这一行来包含 sstream 头文件
#include <random>   // 添加随机数生成器
#include <algorithm> // 添加算法库

namespace DpTrap {

// ===== V4.0: ELF段属性和链接器符号 =====

// 创建特定的ELF段，模拟真实游戏的内存布局
// 使用__attribute__((section))指定段名，让GG修改器能正确识别

// 修复：创建GG修改器能识别的内存区域
// Ca区域：不使用section属性，而是通过动态分配创建
// Cb区域：使用标准.bss段（GG修改器识别为C++ .bss区域）
__attribute__((section(".bss")))
static volatile char cb_section_marker[512 * 1024]; // 512KB标记段（未初始化）

// 全局变量，确保在.data段中
static volatile char ca_global_data[1024 * 1024] = {0xCA}; // 1MB全局数据

// 新增：强制分配的CA区域内存池
static volatile char* g_ca_memory_pool[100] = {nullptr}; // CA内存池指针数组
static volatile size_t g_ca_pool_sizes[100] = {0}; // CA内存池大小数组
static volatile int g_ca_pool_count = 0; // 已分配的CA内存块数量

// 新增：强制分配的CB区域内存池
static volatile char* g_cb_memory_pool[50] = {nullptr}; // CB内存池指针数组
static volatile size_t g_cb_pool_sizes[50] = {0}; // CB内存池大小数组
static volatile int g_cb_pool_count = 0; // 已分配的CB内存块数量

// 链接器符号已在头文件中声明

// ===== 真正的C++全局对象（进入真正的BSS段）=====

// 全局C++类，会进入真正的BSS段
class GlobalCppObject {
public:
    char bss_data[1024 * 1024];  // 1MB BSS数据
    int counter;
    std::vector<int> global_vector;

    GlobalCppObject() : counter(0) {
        memset(bss_data, 0, sizeof(bss_data));
        global_vector.reserve(1000);
    }

    void touch() {
        counter++;
        bss_data[0] = static_cast<char>(counter);
        global_vector.push_back(counter);
    }
};

// 静态全局对象 - 真正的BSS段
static GlobalCppObject g_real_bss_object;
static char g_large_bss_array[2 * 1024 * 1024] = {0}; // 2MB BSS
static int g_bss_int_array[256 * 1024] = {0}; // 1MB BSS

// 🚀 新策略：自定义分配器模拟真正的Ca区域
template<typename T>
class CaAllocator {
public:
    typedef T value_type;
    typedef T* pointer;
    typedef const T* const_pointer;
    typedef T& reference;
    typedef const T& const_reference;
    typedef std::size_t size_type;
    typedef std::ptrdiff_t difference_type;

    template<typename U>
    struct rebind {
        typedef CaAllocator<U> other;
    };

    CaAllocator() noexcept {}
    template<typename U>
    CaAllocator(const CaAllocator<U>&) noexcept {}

    pointer allocate(size_type n) {
        // 使用mmap创建特定标识的内存区域
        size_t size = n * sizeof(T);
        void* ptr = mmap(nullptr, size, PROT_READ | PROT_WRITE,
                        MAP_ANONYMOUS | MAP_PRIVATE, -1, 0);
        if (ptr == MAP_FAILED) {
            throw std::bad_alloc();
        }

        // 设置内存标识，让GG识别为Ca区域
        madvise(ptr, size, MADV_WILLNEED);

        LOGI("🎯 CaAllocator分配: 地址=%p, 大小=%zu", ptr, size);
        return static_cast<pointer>(ptr);
    }

    void deallocate(pointer p, size_type n) {
        if (p) {
            munmap(p, n * sizeof(T));
        }
    }

    template<typename U, typename... Args>
    void construct(U* p, Args&&... args) {
        new(p) U(std::forward<Args>(args)...);
    }

    template<typename U>
    void destroy(U* p) {
        p->~U();
    }
};

template<typename T, typename U>
bool operator==(const CaAllocator<T>&, const CaAllocator<U>&) { return true; }

template<typename T, typename U>
bool operator!=(const CaAllocator<T>&, const CaAllocator<U>&) { return false; }

// 使用自定义分配器的容器类型
typedef std::vector<char, CaAllocator<char>> CaVector;
typedef std::basic_string<char, std::char_traits<char>, CaAllocator<char>> CaString;
typedef std::list<int, CaAllocator<int>> CaList;

// 真正的C++容器全局变量（会使用标准堆分配器）
static std::vector<char>* g_heap_vector = nullptr;
static std::string* g_heap_string = nullptr;
static std::list<int>* g_heap_list = nullptr;
static std::deque<char>* g_heap_deque = nullptr;
static std::map<int, std::string>* g_heap_map = nullptr;

// 使用自定义分配器的容器
static CaVector* g_ca_vector = nullptr;
static CaString* g_ca_string = nullptr;
static CaList* g_ca_list = nullptr;

// dp方案：修复 - 使用标准.bss段（移除自定义后缀）
__attribute__((used, section(".bss")))
static volatile uint8_t g_dpBssTrapArea[512 * 1024]; // 512KB BSS陷阱区

__attribute__((used, section(".bss")))
static volatile int g_dpBssTrapArray[27136]; // 108KB BSS陷阱数组

__attribute__((used, section(".bss")))
static volatile char g_dpBssBuffer[256 * 1024]; // 256KB BSS缓冲区

// ===== CAAllocator实现 =====

void* CAAllocator::Allocate(size_t size) {
    LOGI("🎯 [dp-CAAllocator] 分配内存: 大小=%zu", size);
    
    // 计算总大小（添加陷阱头部）
    size_t totalSize = sizeof(TrapHeader) + size;
    
    // 尝试在堆区高地址分配
    void* baseAddr = MapHighAddressMemory(totalSize);
    if (!baseAddr) {
        // 备用分配策略
        baseAddr = mmap(nullptr, totalSize, PROT_READ | PROT_WRITE, 
                       MAP_ANONYMOUS | MAP_PRIVATE, -1, 0);
    }
    
    if (baseAddr == MAP_FAILED) {
        LOGE("❌ dp-CAAllocator分配失败: %s", strerror(errno));
        return nullptr;
    }
    
    // 设置陷阱头部
    TrapHeader* header = static_cast<TrapHeader*>(baseAddr);
    header->magic = MAGIC_HEADER;
    header->allocatorTag = 0x41435452; // "ACTR"标识
    header->allocSize = size;
    header->trapType = SelectTrapType();

    // 关键修复：分配后立即执行写操作，触发物理页分配
    uint8_t* userAddr = static_cast<uint8_t*>(baseAddr) + sizeof(TrapHeader);
    memset(userAddr, 0xCA, size); // 写入CA特征值（0xCA）

    // 再填充诱饵值（覆盖部分区域，保留CA特征）
    FillDecoyValues(userAddr, size);

    // 计算校验和
    header->checksum = CalculateChecksum(userAddr, size);
    
    uintptr_t addr = reinterpret_cast<uintptr_t>(userAddr);
    if (addr >= 0x78000000 && addr < 0x80000000) {
        LOGI("✅ dp-CAAllocator分配成功: 地址=%p, 大小=%zu (CA专属范围)", userAddr, size);
    } else {
        LOGI("✅ dp-CAAllocator分配成功: 地址=%p, 大小=%zu (标准分配)", userAddr, size);
    }
    
    return userAddr;
}

void* CAAllocator::MapHighAddressMemory(size_t size) {
    // 动态获取堆内存范围（示例代码）
    std::ifstream maps("/proc/self/maps");
    std::string line;
    std::vector<std::pair<uintptr_t, uintptr_t>> heapRanges;

    while (std::getline(maps, line)) {
        if (line.find("heap") != std::string::npos) {
            // 解析堆内存范围
            std::istringstream iss(line);
            std::string startHex, endHex;
            iss >> startHex >> endHex;
            uintptr_t start = strtoul(startHex.c_str(), nullptr, 16);
            uintptr_t end = strtoul(endHex.c_str(), nullptr, 16);
            heapRanges.emplace_back(start, end);
        }
    }

    for (const auto& range : heapRanges) {
        uintptr_t targetAddr = range.first + 0x100000; // 在堆范围内选择一个地址
        void* addr = mmap(reinterpret_cast<void*>(targetAddr), size,
                         PROT_READ | PROT_WRITE,
                         MAP_ANONYMOUS | MAP_PRIVATE | MAP_FIXED, -1, 0);

        if (addr != MAP_FAILED) {
            LOGI("📍 dp-CAAllocator高地址分配成功: 0x%lx (CA专属范围)", targetAddr);
            return addr;
        }
    }

    LOGW("⚠️  dp-CAAllocator高地址分配失败，使用标准分配");
    return nullptr;
}

void CAAllocator::FillDecoyValues(void* addr, size_t size) {
    // 常见游戏数值作为诱饵
    static const int decoyValues[] = {
        100, 500, 1000, 5000, 9999, 10000, 50000, 100000, 999999
    };
    
    size_t numInts = size / sizeof(int);
    int* data = static_cast<int*>(addr);
    
    for (size_t i = 0; i < numInts; i++) {
        data[i] = decoyValues[i % 9];
        
        // 10%概率创建指针链
        if (i > 0 && (i % 10) == 0) {
            data[i] = static_cast<int>(reinterpret_cast<uintptr_t>(&data[i-1]) & 0xFFFFFFFF);
        }
    }
    
    // 填充剩余字节
    size_t remainingBytes = size % sizeof(int);
    if (remainingBytes > 0) {
        uint8_t* byteData = static_cast<uint8_t*>(addr) + (numInts * sizeof(int));
        memset(byteData, 0xCA, remainingBytes);
    }
}

uint32_t CAAllocator::CalculateChecksum(void* addr, size_t size) {
    uint32_t checksum = 0;
    uint8_t* data = static_cast<uint8_t*>(addr);
    for (size_t i = 0; i < size; i++) {
        checksum ^= data[i];
    }
    return checksum;
}

uint32_t CAAllocator::SelectTrapType() {
    static uint32_t counter = 0;
    return (++counter) % 4 + 1; // 返回1-4的陷阱类型
}

bool CAAllocator::Verify(void* ptr) {
    if (!ptr) return false;
    
    void* basePtr = static_cast<char*>(ptr) - sizeof(TrapHeader);
    TrapHeader* header = static_cast<TrapHeader*>(basePtr);
    
    if (header->magic != MAGIC_HEADER) {
        return false;
    }
    
    uint32_t currentChecksum = CalculateChecksum(ptr, header->allocSize);
    return currentChecksum == header->checksum;
}

void CAAllocator::Deallocate(void* ptr) {
    if (!ptr) return;
    
    void* basePtr = static_cast<char*>(ptr) - sizeof(TrapHeader);
    TrapHeader* header = static_cast<TrapHeader*>(basePtr);
    
    if (header->magic == MAGIC_HEADER) {
        size_t totalSize = sizeof(TrapHeader) + header->allocSize;
        munmap(basePtr, totalSize);
        LOGI("🗑️  dp-CAAllocator释放内存: 地址=%p, 大小=%zu", ptr, header->allocSize);
    }
}

// ===== CBTrapManager实现 =====

CBTrapManager& CBTrapManager::GetInstance() {
    static CBTrapManager instance;
    return instance;
}

void CBTrapManager::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (initialized_) {
        return;
    }
    
    LOGI("🎯 [dp-CBTrapManager] 初始化BSS段陷阱...");
    
    InitializeBssArea();
    InitializeBssArray();
    
    initialized_ = true;
    LOGI("✅ dp-CBTrapManager初始化完成");
}

void CBTrapManager::InitializeBssArea() {
    LOGI("🔧 修复初始化g_dpBssTrapArea (512KB)...");

    // 修复：强制读写，确保物理页分配（写入非零值避免被视为未使用）
    for (size_t i = 0; i < sizeof(g_dpBssTrapArea); i += 4096) {
        const_cast<uint8_t*>(g_dpBssTrapArea)[i] = 0xCB; // CB特征值
    }

    // 填充特定内存模式
    FillMemoryPattern(const_cast<uint8_t*>(g_dpBssTrapArea), sizeof(g_dpBssTrapArea));

    // 读取操作（确保编译器不优化）
    volatile uint8_t check = g_dpBssTrapArea[0];
    (void)check; // 抑制未使用警告

    LOGI("✅ g_dpBssTrapArea修复初始化完成: 地址=%p", const_cast<uint8_t*>(g_dpBssTrapArea));
}

void CBTrapManager::InitializeBssArray() {
    LOGI("🔧 修复初始化g_dpBssTrapArray (108KB)...");

    // 修复：强制读写，确保物理页分配
    for (int i = 0; i < 27136; i += 1024) {
        const_cast<int*>(g_dpBssTrapArray)[i] = static_cast<int>(0xDEADBEEF) + i;
    }

    // 填充特定内存模式
    FillMemoryPattern(const_cast<int*>(g_dpBssTrapArray), sizeof(g_dpBssTrapArray));

    // 读取操作（确保编译器不优化）
    volatile int check = g_dpBssTrapArray[0];
    const_cast<int*>(g_dpBssTrapArray)[0] = check;

    LOGI("✅ g_dpBssTrapArray修复初始化完成: 地址=%p", const_cast<int*>(g_dpBssTrapArray));
}

void CBTrapManager::FillMemoryPattern(void* addr, size_t size) {
    const uint32_t pattern[] = {0xDEADBEEF, 0xCAFEBABE, 0xBADDF00D, 0xFACEB00C};
    const size_t patternSize = sizeof(pattern);
    
    uint8_t* data = static_cast<uint8_t*>(addr);
    for (size_t i = 0; i < size; i += patternSize) {
        size_t copySize = std::min(patternSize, size - i);
        memcpy(data + i, pattern, copySize);
    }
}

size_t CBTrapManager::GetTotalBssSize() const {
    return sizeof(g_dpBssTrapArea) + sizeof(g_dpBssTrapArray) + sizeof(g_dpBssBuffer);
}

void* CBTrapManager::GetBssAreaAddress() const {
    return const_cast<uint8_t*>(g_dpBssTrapArea);
}

void* CBTrapManager::GetBssArrayAddress() const {
    return const_cast<int*>(g_dpBssTrapArray);
}

// ===== DpMemoryTrapSystem实现 =====

DpMemoryTrapSystem::DpMemoryTrapSystem() 
    : initialized_(false)
    , isDetecting_(false)
    , caMemorySize_(0)
    , cbMemorySize_(0)
    , dataSegmentMemorySize_(0)
    , caTrapCount_(0)
    , cbTrapCount_(0) {
    LOGI("DpMemoryTrapSystem构造函数");
}

DpMemoryTrapSystem::~DpMemoryTrapSystem() {
    LOGI("DpMemoryTrapSystem析构函数");
    Shutdown();
}

DpMemoryTrapSystem& DpMemoryTrapSystem::GetInstance() {
    static DpMemoryTrapSystem instance;
    return instance;
}

bool DpTrap::DpMemoryTrapSystem::Initialize() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (initialized_) {
        LOGI("⚠️ [dp-MemoryTrapSystem] 系统已经初始化过了，跳过重复初始化");
        return true;
    }

    LOGI("🚀 [dp-MemoryTrapSystem] 初始化dp内存陷阱系统...");
    LOGI("🔥 ===== 版本标识: V4.0-ELF段属性优化版本 =====");
    LOGI("🔥 特征: ELF段属性 + 链接器符号 + 特定内存布局 + 持续使用");
    LOGI("🔥 ===== 版本标识结束 =====");

    // V4.0: 初始化ELF段属性和链接器符号
    InitializeElfSections();

    // 初始化Cb陷阱管理器
    CBTrapManager::GetInstance().Initialize();

    // 预分配内存
    PreallocateMemory();

    // 部署陷阱
    DeployCaTraps(50);
    DeployCbTraps();

    initialized_ = true;
    LOGI("✅ dp内存陷阱系统初始化完成");

    // 强制内存使用，确保物理页分配
    forceMemoryUsage();

    // 输出统计信息
    DumpMemoryInfo();

    return true;
}

// 模拟真实游戏数据结构
struct GameData {
    int health;           // 血量
    int mana;            // 魔法值
    float positionX;     // X坐标
    float positionY;     // Y坐标
    int experience;      // 经验值
    int level;          // 等级
    char playerName[32]; // 玩家名称
    int gold;           // 金币
};

void DpTrap::DpMemoryTrapSystem::PreallocateMemory() {
    LOGI("🎯 [ACE模拟] 创建真实游戏内存保护系统...");

    // 🚀 ACE策略: 模拟腾讯ACE内存保护机制
    LOGI("🛡️ 初始化游戏数据保护层...");

    try {
        // 策略1: 创建受保护的游戏数据结构
        for (int i = 0; i < 100; i++) {
            // 分配游戏数据结构
            GameData* gameData = static_cast<GameData*>(malloc(sizeof(GameData)));
            if (gameData) {
                // 初始化真实游戏数据
                gameData->health = 100 + (rand() % 900);      // 血量100-999
                gameData->mana = 50 + (rand() % 450);         // 魔法值50-499
                gameData->positionX = (rand() % 1000) / 10.0f; // X坐标0-99.9
                gameData->positionY = (rand() % 1000) / 10.0f; // Y坐标0-99.9
                gameData->experience = rand() % 100000;        // 经验值0-99999
                gameData->level = 1 + (rand() % 99);          // 等级1-99
                gameData->gold = rand() % 50000;              // 金币0-49999
                snprintf(gameData->playerName, sizeof(gameData->playerName), "Player_%d", i);

                // 🛡️ 安全的内存保护（避免崩溃）
                uintptr_t addr = reinterpret_cast<uintptr_t>(gameData);

                // 暂时跳过内存保护，避免崩溃
                // Android系统对mprotect有严格限制
                LOGI("✅ 游戏数据[%d]: 地址=0x%lx, 血量=%d, 等级=%d, 金币=%d",
                     i, addr, gameData->health, gameData->level, gameData->gold);

                caTraps_.push_back(gameData);
                caMemorySize_ += sizeof(GameData);
                caTrapCount_++;
            }
        }

        // 策略2: 分配大块连续内存（模拟游戏资源）
        LOGI("🎮 分配大块游戏资源内存...");
        for (int i = 0; i < 10; i++) {
            size_t bigSize = 512 * 1024; // 512KB
            void* bigPtr = malloc(bigSize);
            if (bigPtr) {
                // 模拟游戏资源数据（纹理、音频等）
                uint8_t* data = static_cast<uint8_t*>(bigPtr);
                for (size_t j = 0; j < bigSize; j++) {
                    data[j] = (j % 256); // 模拟资源数据模式
                }

                caTraps_.push_back(bigPtr);
                caMemorySize_ += bigSize;
                caTrapCount_++;

                uintptr_t addr = reinterpret_cast<uintptr_t>(bigPtr);
                LOGI("✅ 加密资源[%d]: 地址=0x%lx, 大小=512KB", i, addr);
            }
        }

        // 策略3: 使用calloc分配（零初始化的堆内存）
        LOGI("🧮 使用calloc分配零初始化内存...");
        for (int i = 0; i < 50; i++) {
            size_t count = 1024;
            size_t size = sizeof(int);
            void* callocPtr = calloc(count, size);
            if (callocPtr) {
                // calloc自动零初始化，我们添加一些游戏数据
                int* intData = static_cast<int*>(callocPtr);
                for (size_t j = 0; j < count; j++) {
                    intData[j] = i * 1000 + j; // 模拟游戏ID
                }

                caTraps_.push_back(callocPtr);
                caMemorySize_ += count * size;
                caTrapCount_++;
            }
        }

        LOGI("✅ Ca区域分配完成: %d个内存块, 总大小=%.2fMB",
             caTrapCount_, caMemorySize_ / 1024.0 / 1024.0);

    } catch (const std::exception& e) {
        LOGE("❌ Ca区域分配异常: %s", e.what());
    }

    // 🚀 新策略2: 使用真正的C++标准容器（会触发标准堆分配器）
    LOGI("🧪 测试C++标准容器分配...");

    try {
        // vector分配 - 真正的C++堆
        g_heap_vector = new std::vector<char>(5 * 1024 * 1024, 0xCA); // 5MB
        if (g_heap_vector && !g_heap_vector->empty()) {
            caTraps_.push_back(g_heap_vector->data());
            caMemorySize_ += g_heap_vector->size();
            caTrapCount_++;
            LOGI("✅ std::vector分配成功: 地址=%p, 大小=5MB", g_heap_vector->data());
        }

        // string分配 - 真正的C++堆
        g_heap_string = new std::string(3 * 1024 * 1024, 'C'); // 3MB
        if (g_heap_string && !g_heap_string->empty()) {
            caTraps_.push_back(const_cast<char*>(g_heap_string->data()));
            caMemorySize_ += g_heap_string->size();
            caTrapCount_++;
            LOGI("✅ std::string分配成功: 地址=%p, 大小=3MB", g_heap_string->data());
        }

        // list分配 - 真正的C++堆
        g_heap_list = new std::list<int>();
        for (int i = 0; i < 100000; i++) {
            g_heap_list->push_back(0xCAFE0000 + i);
        }
        if (g_heap_list && !g_heap_list->empty()) {
            caTrapCount_++;
            caMemorySize_ += g_heap_list->size() * sizeof(int);
            LOGI("✅ std::list分配成功: 节点数=%zu", g_heap_list->size());
        }

        // deque分配 - 真正的C++堆
        g_heap_deque = new std::deque<char>(2 * 1024 * 1024, 0xDE); // 2MB
        if (g_heap_deque && !g_heap_deque->empty()) {
            caTrapCount_++;
            caMemorySize_ += g_heap_deque->size();
            LOGI("✅ std::deque分配成功: 大小=2MB");
        }

        // map分配 - 真正的C++堆
        g_heap_map = new std::map<int, std::string>();
        for (int i = 0; i < 10000; i++) {
            (*g_heap_map)[i] = std::string(100, 'M'); // 每个字符串100字节
        }
        if (g_heap_map && !g_heap_map->empty()) {
            caTrapCount_++;
            caMemorySize_ += g_heap_map->size() * 100;
            LOGI("✅ std::map分配成功: 键值对数=%zu", g_heap_map->size());
        }

    } catch (const std::exception& e) {
        LOGE("❌ C++标准容器分配异常: %s", e.what());
    }

    // 🚀 新策略2: 使用new[]分配大块内存（真正的C++堆）
    LOGI("🧪 测试new[]大块分配...");
    for (int i = 0; i < 10; i++) {
        try {
            char* newPtr = new char[1024 * 1024]; // 1MB
            if (newPtr) {
                // 填充数据确保内存被使用
                memset(newPtr, 0xCA, 1024 * 1024);
                caTraps_.push_back(newPtr);
                caMemorySize_ += 1024 * 1024;
                caTrapCount_++;
                LOGI("✅ new[]分配成功[%d]: 地址=%p, 大小=1MB", i, newPtr);
            }
        } catch (...) {
            LOGE("❌ new[]分配失败[%d]", i);
        }
    }

    // 🚀 新策略3: 触发全局BSS对象
    LOGI("🧪 触发全局BSS对象...");
    g_real_bss_object.touch();

    // 填充大型BSS数组
    for (int i = 0; i < 1000; i++) {
        g_large_bss_array[i * 1024] = static_cast<char>(i);
        g_bss_int_array[i * 100] = 0xBEEF0000 + i;
    }

    LOGI("✅ 全局BSS对象触发完成");

    LOGI("✅ 真正C++标准库分配完成: Ca陷阱=%d个, 总大小=%.2fMB",
         caTrapCount_, caMemorySize_ / 1024.0 / 1024.0);
}

void DpTrap::DpMemoryTrapSystem::DeployCaTraps(int count) {
    LOGI("🔧🔧🔧 [CA区域修复] 强制创建真实CA区域内存 (%d个)...", count);
    LOGI("🚀🚀🚀 [腾讯策略] 即将执行基于腾讯SO库分析的改进策略！");

    // 修复策略1: 使用malloc强制分配堆内存，确保被GG识别为Ca区域
    for (int i = 0; i < count && i < 100; i++) {
        const size_t sizes[] = {4096, 8192, 16384, 32768, 65536}; // 使用页对齐大小
        size_t size = sizes[i % 5];

        // 强制使用malloc分配，确保在堆区域
        void* trapAddr = malloc(size);

        if (trapAddr) {
            // 立即写入数据，确保物理页分配
            memset(trapAddr, 0xCA, size); // 填充CA特征值

            // 填充游戏相关数值，增加被扫描的概率
            uint32_t* data = static_cast<uint32_t*>(trapAddr);
            size_t dataCount = size / sizeof(uint32_t);
            for (size_t j = 0; j < dataCount; j++) {
                data[j] = 100 + (j % 1000); // 填充100-1099的数值
            }

            // 强制读写操作，确保内存活跃
            volatile uint32_t* vdata = static_cast<volatile uint32_t*>(trapAddr);
            vdata[0] = 0xCAFEBABE;
            volatile uint32_t readback = vdata[0];
            (void)readback;

            // 保存到全局内存池，防止被释放
            g_ca_memory_pool[g_ca_pool_count] = static_cast<volatile char*>(trapAddr);
            g_ca_pool_sizes[g_ca_pool_count] = size;
            g_ca_pool_count++;

            caTraps_.push_back(trapAddr);
            caMemorySize_ += size;
            caTrapCount_++;

            uintptr_t addr = reinterpret_cast<uintptr_t>(trapAddr);
            LOGI("✅ CA陷阱[%d]: malloc分配成功, 地址=0x%lx, 大小=%zu KB",
                 i, addr, size/1024);
        } else {
            LOGE("❌ CA陷阱[%d]: malloc分配失败", i);
        }
    }

    // 修复策略2: 使用calloc分配零初始化内存
    LOGI("🔧 [CA区域修复] 使用calloc创建零初始化CA内存...");
    for (int i = 0; i < 20 && g_ca_pool_count < 100; i++) {
        size_t count_elements = 1024; // 1024个元素
        size_t element_size = sizeof(uint32_t); // 每个元素4字节

        void* callocAddr = calloc(count_elements, element_size);
        if (callocAddr) {
            // 填充游戏数据
            uint32_t* data = static_cast<uint32_t*>(callocAddr);
            for (size_t j = 0; j < count_elements; j++) {
                data[j] = 100 + (i * 1000) + j; // 确保包含100这个值
            }

            size_t totalSize = count_elements * element_size;

            // 保存到全局内存池
            g_ca_memory_pool[g_ca_pool_count] = static_cast<volatile char*>(callocAddr);
            g_ca_pool_sizes[g_ca_pool_count] = totalSize;
            g_ca_pool_count++;

            caTraps_.push_back(callocAddr);
            caMemorySize_ += totalSize;
            caTrapCount_++;

            uintptr_t addr = reinterpret_cast<uintptr_t>(callocAddr);
            LOGI("✅ CA陷阱[calloc-%d]: 地址=0x%lx, 大小=%zu KB",
                 i, addr, totalSize/1024);
        }
    }

    // 修复策略3: 创建大量小块内存，模拟真实的C++对象分配
    LOGI("🎯 [CA修复] 创建大量小块内存，模拟C++对象分配...");
    for (int i = 0; i < 500 && g_ca_pool_count < 100; i++) {
        size_t size = 32 + (i % 64) * 16; // 32-1056字节的小块内存
        void* trapAddr = malloc(size);
        if (trapAddr) {
            // 填充C++对象模式的数据
            uint32_t* data = static_cast<uint32_t*>(trapAddr);
            size_t dataCount = size / sizeof(uint32_t);
            for (size_t j = 0; j < dataCount; j++) {
                data[j] = 0xDEADBEEF + j; // C++对象特征值
            }

            // 保存到全局内存池
            g_ca_memory_pool[g_ca_pool_count] = static_cast<volatile char*>(trapAddr);
            g_ca_pool_sizes[g_ca_pool_count] = size;
            g_ca_pool_count++;

            caTraps_.push_back(trapAddr);
            caMemorySize_ += size;
            caTrapCount_++;
        }
    }

    // 修复策略4: 使用new操作符分配内存（真正的C++ alloc）
    LOGI("🎯 [CA修复] 使用new操作符分配C++对象...");
    for (int i = 0; i < 100 && g_ca_pool_count < 100; i++) {
        try {
            size_t size = 1024 + i * 512; // 1KB-51KB的对象
            char* obj = new char[size];
            if (obj) {
                // 填充对象数据
                for (size_t j = 0; j < size; j++) {
                    obj[j] = static_cast<char>(0xCA + (j % 256));
                }

                // 保存到全局内存池
                g_ca_memory_pool[g_ca_pool_count] = static_cast<volatile char*>(obj);
                g_ca_pool_sizes[g_ca_pool_count] = size;
                g_ca_pool_count++;

                caTraps_.push_back(obj);
                caMemorySize_ += size;
                caTrapCount_++;
            }
        } catch (...) {
            // 忽略分配失败
        }
    }

    // 修复策略5: 创建GG修改器特定识别的内存模式
    LOGI("🎯 [CA修复] 创建GG修改器特定识别模式...");
    CreateGGRecognizableMemory();

    LOGI("✅ CA区域修复完成: 总计%d个陷阱, 内存池数量=%d", caTrapCount_, g_ca_pool_count);
}

void DpTrap::DpMemoryTrapSystem::DeployCbTraps() {
    LOGI("🔧 [CB区域修复] 强制创建真实CB(.bss)区域内存...");

    // 修复策略1: 强制初始化静态.bss段变量
    LOGI("🎯 [CB修复] 强制初始化静态.bss段变量...");

    // 强制写入ca_global_data，确保被分配
    for (size_t i = 0; i < sizeof(ca_global_data); i += 4096) {
        ca_global_data[i] = 0xCA;
        volatile char readback = ca_global_data[i];
        (void)readback;
    }

    // 强制写入cb_section_marker，确保被分配
    for (size_t i = 0; i < sizeof(cb_section_marker); i += 4096) {
        cb_section_marker[i] = 0xCB;
        volatile char readback = cb_section_marker[i];
        (void)readback;
    }

    LOGI("✅ 静态段变量强制初始化完成: CA段=%zu KB, CB段=%zu KB",
         sizeof(ca_global_data)/1024, sizeof(cb_section_marker)/1024);

    // 修复策略2: 使用malloc创建大量CB区域内存
    LOGI("🔧 [CB修复] 使用malloc创建CB区域内存...");
    for (int i = 0; i < 30 && g_cb_pool_count < 50; i++) {
        size_t cbSize = 8192; // 8KB CB内存块

        void* cbMem = malloc(cbSize);
        if (cbMem) {
            // 初始化为零（模拟.bss段特征）
            memset(cbMem, 0, cbSize);

            // 填充CB特征数据
            uint32_t* cbData = static_cast<uint32_t*>(cbMem);
            size_t dataCount = cbSize / sizeof(uint32_t);

            for (size_t j = 0; j < dataCount; j++) {
                // CB区域特征：未初始化全局变量的典型值
                static const uint32_t cbValues[] = {
                    0x00000000, 0xCBCBCBCB, 0xDEADBEEF, 0xBEEFCAFE,
                    100, 500, 1000, 5000, 9999, 10000, 50000, 100000, 999999
                };
                cbData[j] = cbValues[j % 13];
            }

            // 强制读写操作，确保内存活跃
            volatile uint32_t* vcbData = static_cast<volatile uint32_t*>(cbMem);
            vcbData[0] = 0xCBCBCBCB;
            volatile uint32_t readback = vcbData[0];
            (void)readback;

            // 保存到全局内存池
            g_cb_memory_pool[g_cb_pool_count] = static_cast<volatile char*>(cbMem);
            g_cb_pool_sizes[g_cb_pool_count] = cbSize;
            g_cb_pool_count++;

            cbTraps_.push_back(cbMem);
            cbMemorySize_ += cbSize;
            cbTrapCount_++;

            uintptr_t addr = reinterpret_cast<uintptr_t>(cbMem);
            LOGI("✅ CB陷阱[%d]: malloc分配成功, 地址=0x%lx, 大小=%zu KB",
                 i, addr, cbSize/1024);
        } else {
            LOGE("❌ CB陷阱[%d]: malloc分配失败", i);
        }
    }

    // 修复策略3: 强制初始化预定义的.bss段变量
    LOGI("🎯 [CB修复] 强制初始化预定义.bss段变量...");

    // 初始化g_dpBssTrapArea
    for (size_t i = 0; i < sizeof(g_dpBssTrapArea); i += sizeof(uint32_t)) {
        uint32_t* dataPtr = reinterpret_cast<uint32_t*>(const_cast<uint8_t*>(g_dpBssTrapArea + i));
        static const uint32_t bssGameValues[] = {
            100, 500, 1000, 5000, 9999, 10000, 50000, 100000, 999999
        };
        *dataPtr = bssGameValues[(i/sizeof(uint32_t)) % 9] + static_cast<uint32_t>(i);
    }

    // 初始化g_dpBssTrapArray
    for (size_t i = 0; i < 27136; i++) {
        const_cast<int*>(g_dpBssTrapArray)[i] = static_cast<int>(100 + i * 10);
    }

    // 初始化g_dpBssBuffer
    memset(const_cast<char*>(g_dpBssBuffer), 0xCB, sizeof(g_dpBssBuffer));

    // 记录预定义.bss段陷阱
    cbTraps_.push_back(const_cast<uint8_t*>(g_dpBssTrapArea));
    cbTraps_.push_back(const_cast<int*>(g_dpBssTrapArray));
    cbTraps_.push_back(const_cast<char*>(g_dpBssBuffer));

    size_t totalBssSize = sizeof(g_dpBssTrapArea) + sizeof(g_dpBssTrapArray) + sizeof(g_dpBssBuffer);
    cbMemorySize_ += totalBssSize;
    cbTrapCount_ += 3;

    LOGI("✅ 预定义.bss段陷阱激活:");
    LOGI("   - BSS区域: %p, 大小=512KB", g_dpBssTrapArea);
    LOGI("   - BSS数组: %p, 大小=108KB", g_dpBssTrapArray);
    LOGI("   - BSS缓冲: %p, 大小=256KB", g_dpBssBuffer);
    LOGI("   - 总计.bss段大小: %.2fKB", totalBssSize / 1024.0);

    LOGI("✅ CB区域修复完成: 总计%d个陷阱, 内存池数量=%d, 总大小=%.2fKB",
         cbTrapCount_, g_cb_pool_count, cbMemorySize_ / 1024.0);


    // 修复策略4: 创建持续访问线程，确保内存保持活跃
    LOGI("🚀 [CB修复] 启动内存保持活跃机制...");
    StartCbMemoryKeepAlive();
}

// 新增：CB内存保持活跃机制
void DpTrap::DpMemoryTrapSystem::StartCbMemoryKeepAlive() {
    // 避免重复启动线程
    static std::atomic<bool> cbThreadStarted{false};
    if (cbThreadStarted.exchange(true)) {
        LOGI("🎯 CB内存保持活跃线程已经启动，跳过");
        return;
    }

    // 创建一个简单的定时器，定期访问CB内存
    static std::thread* cbKeepAliveThread = new std::thread([this]() {
        LOGI("🎯 CB内存保持活跃线程启动");

        while (initialized_) {
            try {
                // 访问静态段变量
                if (sizeof(ca_global_data) > 0) {
                    volatile char* ca_ptr = const_cast<volatile char*>(ca_global_data);
                    ca_ptr[0] = 0xCA;
                    volatile char readback = ca_ptr[0];
                    (void)readback;
                }

                if (sizeof(cb_section_marker) > 0) {
                    volatile char* cb_ptr = const_cast<volatile char*>(cb_section_marker);
                    cb_ptr[0] = 0xCB;
                    volatile char readback = cb_ptr[0];
                    (void)readback;
                }

                // 访问内存池中的CB内存
                for (int i = 0; i < g_cb_pool_count; i++) {
                    if (g_cb_memory_pool[i] && g_cb_pool_sizes[i] > 0) {
                        volatile char* ptr = g_cb_memory_pool[i];
                        ptr[0] = 0xCB;
                        volatile char readback = ptr[0];
                        (void)readback;
                    }
                }

                // 访问预定义.bss段变量
                if (sizeof(g_dpBssTrapArea) > 0) {
                    volatile uint8_t* bss_ptr = const_cast<volatile uint8_t*>(g_dpBssTrapArea);
                    bss_ptr[0] = 0xCB;
                    volatile uint8_t readback = bss_ptr[0];
                    (void)readback;
                }

                std::this_thread::sleep_for(std::chrono::milliseconds(100));

            } catch (...) {
                // 忽略异常，继续运行
            }
        }

        LOGI("🎯 CB内存保持活跃线程结束");
    });

    // 安全地分离线程
    if (cbKeepAliveThread && cbKeepAliveThread->joinable()) {
        cbKeepAliveThread->detach();
    }
}

void DpTrap::DpMemoryTrapSystem::VerifyTraps() {
    LOGI("🔍 [dp-验证] 验证陷阱完整性...");

    int validCaTraps = 0;
    for (void* ptr : caTraps_) {
        if (CAAllocator::Verify(ptr)) {
            validCaTraps++;
        }
    }

    LOGI("✅ dp-验证完成: Ca陷阱 %d/%d 有效", validCaTraps, caTrapCount_);
}

void DpTrap::DpMemoryTrapSystem::DumpMemoryInfo() {
    LOGI("📊 ===== dp内存陷阱统计信息 =====");
    LOGI("🎯 Ca区域 (C++ alloc):");
    LOGI("   - 陷阱数量: %d个", caTrapCount_);
    LOGI("   - 内存大小: %.2fMB", caMemorySize_ / 1024.0 / 1024.0);
    LOGI("🎯 Cb区域 (C++ .bss):");
    LOGI("   - 陷阱数量: %d个", cbTrapCount_);
    LOGI("   - 内存大小: %.2fKB", cbMemorySize_ / 1024.0);
    LOGI("🎯 总计:");
    LOGI("   - 总陷阱数: %d个", caTrapCount_ + cbTrapCount_);
    LOGI("   - 总内存: %.2fMB", (caMemorySize_ + cbMemorySize_) / 1024.0 / 1024.0);
    LOGI("📊 ===== 统计信息结束 =====");
}

// 强制内存使用，确保物理页分配
void DpTrap::DpMemoryTrapSystem::forceMemoryUsage() {
    LOGI("🔧 启动持续内存使用线程，模拟真实游戏活动...");

    StartMemoryUsageThread();

    LOGI("✅ 持续内存使用线程启动完成");
}

// 启动持续内存使用线程
void DpTrap::DpMemoryTrapSystem::StartMemoryUsageThread() {
    if (memoryUsageRunning_.load()) {
        return; // 已经在运行
    }

    memoryUsageRunning_.store(true);
    memoryUsageThread_ = std::thread(&DpMemoryTrapSystem::MemoryUsageWorker, this);

    LOGI("🚀 持续内存使用线程已启动");
}

// 停止持续内存使用线程
void DpTrap::DpMemoryTrapSystem::StopMemoryUsageThread() {
    if (!memoryUsageRunning_.load()) {
        return; // 已经停止
    }

    memoryUsageRunning_.store(false);
    if (memoryUsageThread_.joinable()) {
        memoryUsageThread_.join();
    }

    LOGI("🛑 持续内存使用线程已停止");
}

// 持续内存使用工作线程
void DpTrap::DpMemoryTrapSystem::MemoryUsageWorker() {
    LOGI("🎮 内存使用工作线程开始运行...");

    int cycle = 0;
    while (memoryUsageRunning_.load()) {
        try {
            // 🎯 策略1: 持续读写Ca区域内存（模拟游戏数据更新）
            for (size_t i = 0; i < caTraps_.size() && memoryUsageRunning_.load(); i++) {
                void* ptr = caTraps_[i];
                if (ptr) {
                    // 模拟游戏数据更新：血量、经验值、金币等
                    volatile int* gameData = static_cast<volatile int*>(ptr);
                    *gameData = 0xCAFE0000 + cycle + i;

                    // 读取验证
                    volatile int readBack = *gameData;
                    (void)readBack; // 防止编译器优化
                }

                // 每处理10个内存块就休眠一下，避免CPU占用过高
                if (i % 10 == 0) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(1));
                }
            }

            // 🎯 策略2: 持续读写C++标准容器（模拟游戏逻辑）
            if (g_heap_vector && !g_heap_vector->empty() && memoryUsageRunning_.load()) {
                for (size_t i = 0; i < g_heap_vector->size() && i < 1000 && memoryUsageRunning_.load(); i += 100) {
                    (*g_heap_vector)[i] = static_cast<char>(0xCA + (cycle % 256));
                }
            }

            if (g_heap_string && !g_heap_string->empty() && memoryUsageRunning_.load()) {
                if (g_heap_string->size() > 1000) {
                    (*g_heap_string)[100] = static_cast<char>(0xFE + (cycle % 256));
                }
            }

            if (g_heap_list && !g_heap_list->empty() && memoryUsageRunning_.load()) {
                auto it = g_heap_list->begin();
                if (it != g_heap_list->end()) {
                    *it = 0xBEEF0000 + cycle;
                }
            }

            if (g_heap_deque && !g_heap_deque->empty() && memoryUsageRunning_.load()) {
                if (g_heap_deque->size() > 100) {
                    (*g_heap_deque)[100] = static_cast<char>(0xDE + (cycle % 256));
                }
            }

            if (g_heap_map && !g_heap_map->empty() && memoryUsageRunning_.load()) {
                auto it = g_heap_map->find(100);
                if (it != g_heap_map->end()) {
                    it->second[0] = static_cast<char>(0xAD + (cycle % 256));
                }
            }

            // 🎯 策略3: 持续读写Cb区域内存（模拟代码段数据）
            for (size_t i = 0; i < cbTraps_.size() && memoryUsageRunning_.load(); i++) {
                void* ptr = cbTraps_[i];
                if (ptr) {
                    // 模拟代码段数据更新
                    volatile int* codeData = static_cast<volatile int*>(ptr);
                    *codeData = 0xDEAD0000 + cycle + i;

                    // 读取验证
                    volatile int readBack = *codeData;
                    (void)readBack; // 防止编译器优化
                }
            }

            cycle++;
            std::this_thread::sleep_for(std::chrono::milliseconds(50)); // 调整休眠时间

        } catch (const std::exception& e) {
            LOGE("❌ 内存使用工作线程异常: %s", e.what());
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    LOGI("🎮 内存使用工作线程结束");
}

// V4.0: 初始化ELF段属性和链接器符号
void DpTrap::DpMemoryTrapSystem::InitializeElfSections() {
    LOGI("🔧 [V4.0-ELF段属性] 初始化特殊ELF段...");

    // 初始化Ca段标记区域
    LOGI("🎯 初始化Ca段标记区域...");
    for (size_t i = 0; i < sizeof(ca_global_data); i += 4096) {
        ca_global_data[i] = static_cast<char>(0xCA);
        // 强制写入，确保页面被分配
        volatile char read_back = ca_global_data[i];
        (void)read_back;
    }

    // 初始化Cb段标记区域
    LOGI("🎯 初始化Cb段标记区域...");
    for (size_t i = 0; i < sizeof(cb_section_marker); i += 4096) {
        cb_section_marker[i] = static_cast<char>(0xCB);
        // 强制写入，确保页面被分配
        volatile char read_back = cb_section_marker[i];
        (void)read_back;
    }

    // 打印段边界信息 (暂时注释掉，避免链接错误)
    /*
    if (__start_ca_alloc_section && __stop_ca_alloc_section) {
        size_t ca_size = __stop_ca_alloc_section - __start_ca_alloc_section;
        LOGI("✅ Ca段边界: 开始=0x%p, 结束=0x%p, 大小=%zu字节",
             __start_ca_alloc_section, __stop_ca_alloc_section, ca_size);
    }

    if (__start_cb_bss_section && __stop_cb_bss_section) {
        size_t cb_size = __stop_cb_bss_section - __start_cb_bss_section;
        LOGI("✅ Cb段边界: 开始=0x%p, 结束=0x%p, 大小=%zu字节",
             __start_cb_bss_section, __stop_cb_bss_section, cb_size);
    }
    */
    LOGI("✅ ELF段属性初始化完成 (段边界检查已跳过)");

    LOGI("✅ ELF段属性初始化完成");
}

void DpTrap::DpMemoryTrapSystem::StartDetection() {
    LOGI("🚀 启动DpMemoryTrap检测系统...");
    // 这里可以启动检测线程或设置检测机制
    // 目前简化实现，主要依靠陷阱的被动检测
    LOGI("✅ DpMemoryTrap检测系统启动完成");
}

void DpTrap::DpMemoryTrapSystem::StopDetection() {
    LOGI("🛑 停止DpMemoryTrap检测系统...");
    // 停止检测相关的线程或机制
    LOGI("✅ DpMemoryTrap检测系统停止完成");
}

void DpTrap::DpMemoryTrapSystem::Shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (!initialized_) {
        return;
    }

    LOGI("🛑 [dp-MemoryTrapSystem] 关闭dp内存陷阱系统...");

    // 停止持续内存使用线程
    StopMemoryUsageThread();

    // 释放自定义Ca分配器容器
    if (g_ca_vector) {
        delete g_ca_vector;
        g_ca_vector = nullptr;
        LOGI("✅ 释放CaVector");
    }

    if (g_ca_string) {
        delete g_ca_string;
        g_ca_string = nullptr;
        LOGI("✅ 释放CaString");
    }

    if (g_ca_list) {
        delete g_ca_list;
        g_ca_list = nullptr;
        LOGI("✅ 释放CaList");
    }

    // 释放C++标准容器
    if (g_heap_vector) {
        delete g_heap_vector;
        g_heap_vector = nullptr;
        LOGI("✅ 释放std::vector");
    }

    if (g_heap_string) {
        delete g_heap_string;
        g_heap_string = nullptr;
        LOGI("✅ 释放std::string");
    }

    if (g_heap_list) {
        delete g_heap_list;
        g_heap_list = nullptr;
        LOGI("✅ 释放std::list");
    }

    if (g_heap_deque) {
        delete g_heap_deque;
        g_heap_deque = nullptr;
        LOGI("✅ 释放std::deque");
    }

    if (g_heap_map) {
        delete g_heap_map;
        g_heap_map = nullptr;
        LOGI("✅ 释放std::map");
    }

    // 释放Ca陷阱（malloc/calloc分配的内存）
    for (void* ptr : caTraps_) {
        if (ptr) {
            try {
                // 使用free释放malloc/calloc分配的内存
                free(ptr);
            } catch (...) {
                // 忽略释放错误，继续释放其他内存
                LOGI("⚠️ 释放Ca内存时出现异常，继续释放其他内存");
            }
        }
    }
    caTraps_.clear();
    LOGI("✅ 释放Ca区域内存完成");

    // 释放Cb陷阱（malloc分配的模拟代码内存）
    for (void* ptr : cbTraps_) {
        if (ptr) {
            try {
                free(ptr);
            } catch (...) {
                LOGI("⚠️ 释放Cb内存时出现异常，继续释放其他内存");
            }
        }
    }
    cbTraps_.clear();
    LOGI("✅ 释放Cb区域内存完成");

    caMemorySize_ = 0;
    cbMemorySize_ = 0;
    caTrapCount_ = 0;
    cbTrapCount_ = 0;

    initialized_ = false;
    LOGI("✅ dp内存陷阱系统已关闭");
}

// 新增：保护陷阱内存的方法
bool DpTrap::DpMemoryTrapSystem::ProtectTrapMemory(void* addr, size_t size) {
    // 将内存设置为只读，防止被意外修改
    if (mprotect(addr, size, PROT_READ) == -1) {
        LOGW("⚠️  内存保护失败: %s", strerror(errno));
        return false;
    }
    return true;
}

// 新增：分配受保护内存的方法
void* DpTrap::DpMemoryTrapSystem::AllocateProtectedMemory(size_t size) {
    LOGI("🔧 [CA区域修复] 使用mmap创建真正的CA区域内存");

    // 🚀 修复方案1: 使用mmap创建匿名映射，模拟CA区域特征
    void* dataAddr = mmap(nullptr, size,
                         PROT_READ | PROT_WRITE,
                         MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);

    if (dataAddr == MAP_FAILED) {
        LOGE("❌ mmap分配CA区域失败: %s", strerror(errno));
        // 备用方案：使用malloc
        dataAddr = malloc(size);
        if (!dataAddr) {
            LOGE("❌ malloc备用分配也失败");
            return nullptr;
        }
        LOGW("⚠️ 使用malloc备用方案分配CA区域");
    }

    // 🚀 修复方案2: 设置内存建议，让系统识别为特殊区域
    if (dataAddr != MAP_FAILED && madvise(dataAddr, size, MADV_WILLNEED) != 0) {
        LOGW("⚠️ madvise设置失败，但继续执行");
    }

    // 🚀 修复方案3: 填充CA区域特征数据
    uint32_t* data = static_cast<uint32_t*>(dataAddr);
    size_t dataCount = size / sizeof(uint32_t);

    // 使用CA区域特有的数据模式
    for (size_t i = 0; i < dataCount; i++) {
        // CA区域特征：C++ allocator分配的典型数值
        static const uint32_t caValues[] = {
            0xCAFEBABE, 0xDEADBEEF, 0xFEEDFACE, 0xBADDCAFE,
            100, 500, 1000, 5000, 9999, 10000, 50000, 100000, 999999
        };
        data[i] = caValues[i % 13] + static_cast<uint32_t>(i * 0xCA);
    }

    // 填充剩余字节为CA特征值
    size_t remainingBytes = size % sizeof(uint32_t);
    if (remainingBytes > 0) {
        uint8_t* byteData = reinterpret_cast<uint8_t*>(dataAddr) + (dataCount * sizeof(uint32_t));
        memset(byteData, 0xCA, remainingBytes);
    }

    // 🚀 修复方案4: 强制物理页分配和内存预热
    volatile uint8_t* accessPtr = static_cast<volatile uint8_t*>(dataAddr);
    for (size_t i = 0; i < size; i += 4096) {
        accessPtr[i] = 0xCA;
        // 读取操作确保页面被加载
        volatile uint8_t readBack = accessPtr[i];
        (void)readBack;
    }

    uintptr_t addr = reinterpret_cast<uintptr_t>(dataAddr);
    LOGI("✅ CA区域内存分配成功: 地址=0x%lx, 大小=%zu, 特征=CA", addr, size);
    return dataAddr;
}

} // namespace DpTrap

// ===== C接口实现 =====

extern "C" {

void dp_initialize_memory_traps() {
    DpTrap::DpMemoryTrapSystem::GetInstance().Initialize();
}

void dp_deploy_ca_traps(int count) {
    LOGI("🔧 [C接口] dp_deploy_ca_traps被调用，count=%d", count);
    DpTrap::DpMemoryTrapSystem::GetInstance().DeployCaTraps(count);
}

void dp_deploy_cb_traps() {
    DpTrap::DpMemoryTrapSystem::GetInstance().DeployCbTraps();
}

void dp_verify_traps() {
    DpTrap::DpMemoryTrapSystem::GetInstance().VerifyTraps();
}

void dp_dump_memory_info() {
    DpTrap::DpMemoryTrapSystem::GetInstance().DumpMemoryInfo();
}

void dp_shutdown() {
    DpTrap::DpMemoryTrapSystem::GetInstance().Shutdown();
}

size_t dp_get_ca_memory_size() {
    return DpTrap::DpMemoryTrapSystem::GetInstance().GetCaMemorySize();
}

size_t dp_get_cb_memory_size() {
    return DpTrap::DpMemoryTrapSystem::GetInstance().GetCbMemorySize();
}

size_t dp_get_ca_trap_count() {
    return DpTrap::DpMemoryTrapSystem::GetInstance().GetCaTrapCount();
}

size_t dp_get_cb_trap_count() {
    return DpTrap::DpMemoryTrapSystem::GetInstance().GetCbTrapCount();
}

} // extern "C"

// 新增：创建GG修改器能识别的内存模式 - 基于腾讯实现策略
void DpTrap::DpMemoryTrapSystem::CreateGGRecognizableMemory() {
    LOGI("🔧🔧🔧 [GG修复-腾讯策略] 创建GG修改器特定识别的内存模式...");
    LOGI("🚀🚀🚀 [腾讯SO库分析] 基于libtersafe2.so的改进策略开始执行！");

    // 🚀 腾讯策略1: 实现random_trap机制（随机内存陷阱）
    LOGI("🎯🎯🎯 [腾讯策略1] 实现random_trap随机内存陷阱...");
    CreateRandomTraps();

    // 🚀 腾讯策略2: 使用ms_mmap自定义内存管理
    LOGI("🎯🎯🎯 [腾讯策略2] 使用自定义mmap内存管理...");
    CreateCustomMmapRegions();

    // 🚀 腾讯策略3: 创建连续的堆内存块（模拟C++ new/malloc）
    for (int i = 0; i < 50; i++) {
        size_t size = 4096 + i * 1024; // 4KB-54KB的连续块
        void* ptr = malloc(size);
        if (ptr) {
            // 填充特定模式，让GG修改器识别为C++ alloc
            memset(ptr, 0xCA, size);

            // 在开头写入C++对象头部模式
            uint32_t* header = static_cast<uint32_t*>(ptr);
            header[0] = 0xDEADBEEF; // 虚函数表指针模拟
            header[1] = size;       // 对象大小
            header[2] = 0xCAFEBABE; // 魔数

            caTraps_.push_back(ptr);
            caMemorySize_ += size;
            caTrapCount_++;

            LOGI("🎯 [GG修复] 创建C++ alloc模式内存: 地址=0x%lx, 大小=%zu KB",
                 reinterpret_cast<uintptr_t>(ptr), size/1024);
        }
    }

    // 策略2: 创建大量小对象（模拟STL容器）
    for (int i = 0; i < 200; i++) {
        size_t size = 16 + (i % 32) * 8; // 16-264字节的小对象
        void* obj = malloc(size);
        if (obj) {
            // 填充STL容器模式
            uint64_t* data = static_cast<uint64_t*>(obj);
            size_t count = size / sizeof(uint64_t);
            for (size_t j = 0; j < count; j++) {
                data[j] = 0x1234567890ABCDEF + j;
            }

            caTraps_.push_back(obj);
            caMemorySize_ += size;
            caTrapCount_++;
        }
    }

    // 策略3: 使用operator new分配（真正的C++分配）
    try {
        for (int i = 0; i < 30; i++) {
            size_t size = 1024 * (i + 1); // 1KB-30KB
            char* cppObj = new char[size];

            // 填充C++对象模式
            for (size_t j = 0; j < size; j++) {
                cppObj[j] = static_cast<char>(0xAA + (j % 128));
            }

            caTraps_.push_back(cppObj);
            caMemorySize_ += size;
            caTrapCount_++;

            LOGI("🎯 [GG修复] 创建C++ new对象: 大小=%zu KB", size/1024);
        }
    } catch (...) {
        LOGE("❌ [GG修复] C++ new分配失败");
    }

    LOGI("✅ [GG修复-腾讯策略] GG识别模式创建完成");
}

// 🚀 腾讯策略实现：随机内存陷阱机制
void DpTrap::DpMemoryTrapSystem::CreateRandomTraps() {
    LOGI("🎯🎯🎯 [腾讯random_trap] 创建随机内存陷阱...");
    LOGI("🔥🔥🔥 [腾讯分析] 基于libtersafe2.so发现的random_trap机制！");

    // 随机种子
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> size_dist(1024, 65536); // 1KB-64KB随机大小
    std::uniform_int_distribution<> count_dist(20, 100);    // 20-100个随机陷阱

    int trap_count = count_dist(gen);
    LOGI("🎲🎲🎲 [random_trap] 随机生成陷阱数量: %d", trap_count);

    for (int i = 0; i < trap_count; i++) {
        size_t size = size_dist(gen);

        // 随机选择分配方式（模拟腾讯的多样化策略）
        int alloc_method = i % 4;
        void* ptr = nullptr;

        switch (alloc_method) {
            case 0: // malloc方式
                ptr = malloc(size);
                break;
            case 1: // calloc方式
                ptr = calloc(1, size);
                break;
            case 2: // mmap方式
                ptr = mmap(nullptr, size, PROT_READ | PROT_WRITE,
                          MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
                if (ptr == MAP_FAILED) ptr = nullptr;
                break;
            case 3: // new方式
                try {
                    ptr = new char[size];
                } catch (...) {
                    ptr = nullptr;
                }
                break;
        }

        if (ptr) {
            // 填充随机模式数据（模拟腾讯的random_trap特征）
            FillRandomTrapData(ptr, size, i);

            caTraps_.push_back(ptr);
            caMemorySize_ += size;
            caTrapCount_++;

            LOGI("🎯 [random_trap] 陷阱[%d]: 方式=%d, 地址=0x%lx, 大小=%zu",
                 i, alloc_method, reinterpret_cast<uintptr_t>(ptr), size);
        }
    }

    LOGI("✅ [random_trap] 创建完成: %d个随机陷阱", trap_count);
}

// 🚀 腾讯策略实现：自定义mmap内存管理
void DpTrap::DpMemoryTrapSystem::CreateCustomMmapRegions() {
    LOGI("🎯🎯🎯 [腾讯ms_mmap] 创建自定义mmap内存区域...");
    LOGI("🔥🔥🔥 [腾讯分析] 基于libtersafe2.so发现的ms_mmap机制！");

    // 模拟腾讯的ms_mmap策略：创建多个不同大小的mmap区域
    const size_t region_sizes[] = {
        4096,    // 4KB
        8192,    // 8KB
        16384,   // 16KB
        32768,   // 32KB
        65536,   // 64KB
        131072,  // 128KB
        262144,  // 256KB
        524288   // 512KB
    };

    for (size_t i = 0; i < sizeof(region_sizes) / sizeof(region_sizes[0]); i++) {
        size_t size = region_sizes[i];

        // 使用mmap创建匿名映射
        void* region = mmap(nullptr, size,
                           PROT_READ | PROT_WRITE,
                           MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);

        if (region != MAP_FAILED) {
            // 设置内存建议（模拟腾讯的内存管理策略）
            madvise(region, size, MADV_WILLNEED);

            // 填充腾讯特有的数据模式
            FillTencentPattern(region, size);

            caTraps_.push_back(region);
            caMemorySize_ += size;
            caTrapCount_++;

            LOGI("🎯 [ms_mmap] 区域[%zu]: 地址=0x%lx, 大小=%zu KB",
                 i, reinterpret_cast<uintptr_t>(region), size/1024);
        } else {
            LOGE("❌ [ms_mmap] 区域[%zu]创建失败: %s", i, strerror(errno));
        }
    }

    LOGI("✅ [ms_mmap] 自定义mmap区域创建完成");
}

// 填充随机陷阱数据
void DpTrap::DpMemoryTrapSystem::FillRandomTrapData(void* ptr, size_t size, int trap_id) {
    uint32_t* data = static_cast<uint32_t*>(ptr);
    size_t count = size / sizeof(uint32_t);

    // 使用腾讯random_trap的数据模式
    std::random_device rd;
    std::mt19937 gen(rd() + trap_id);
    std::uniform_int_distribution<uint32_t> value_dist(100, 999999);

    for (size_t i = 0; i < count; i++) {
        // 混合固定模式和随机值
        if (i % 4 == 0) {
            data[i] = 0xCAFE0000 + trap_id; // 固定标识
        } else if (i % 4 == 1) {
            data[i] = value_dist(gen);       // 随机游戏数值
        } else if (i % 4 == 2) {
            data[i] = 0xDEAD0000 + i;       // 递增模式
        } else {
            data[i] = 0xBEEF0000 + (i * trap_id); // 组合模式
        }
    }

    // 填充剩余字节
    size_t remaining = size % sizeof(uint32_t);
    if (remaining > 0) {
        uint8_t* byte_data = reinterpret_cast<uint8_t*>(ptr) + (count * sizeof(uint32_t));
        memset(byte_data, 0xCA + trap_id, remaining);
    }
}

// 填充腾讯特有的数据模式
void DpTrap::DpMemoryTrapSystem::FillTencentPattern(void* ptr, size_t size) {
    uint32_t* data = static_cast<uint32_t*>(ptr);
    size_t count = size / sizeof(uint32_t);

    // 模拟腾讯反作弊数据模式
    for (size_t i = 0; i < count; i++) {
        if (i % 8 == 0) {
            data[i] = 0x54535300 + i; // TSS前缀（腾讯安全）
        } else if (i % 8 == 1) {
            data[i] = 0x54503200 + i; // TP2前缀
        } else if (i % 8 == 2) {
            data[i] = 0xA4710000 + i; // ANTI前缀（A471代表ANTI）
        } else {
            data[i] = 0xCAFEBABE + i; // 标准模式
        }
    }
}
