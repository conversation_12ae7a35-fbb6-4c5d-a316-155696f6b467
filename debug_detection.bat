@echo off
echo ========================================
echo 修改器检测调试工具
echo ========================================
echo.

echo [1] 安装最新APK...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %errorlevel% neq 0 (
    echo 安装失败!
    pause
    exit /b 1
)

echo.
echo [2] 启动应用...
adb shell am start -n com.sy.newfwg/.MainActivity

echo.
echo [3] 等待5秒让应用完全启动...
timeout /t 5 /nobreak > nul

echo.
echo [4] 清除日志缓存...
adb logcat -c

echo.
echo [5] 开始监控日志...
echo.
echo 请按以下步骤操作：
echo 1. 在手机上点击"开始修改器检测"按钮
echo 2. 等待看到"权限切换线程开始运行"日志
echo 3. 打开修改器，选择进程com.sy.newfwg
echo 4. 搜索数值100
echo 5. 观察下面的日志输出
echo.
echo 关键日志标识：
echo - "权限切换线程开始运行" - Native层正常启动
echo - "陷阱X设置为可读" - 权限切换正常工作  
echo - "🚨 内存陷阱触发" - 检测到修改器
echo - "🚨🚨🚨 修改器检测到" - Java层确认检测
echo.
echo 按Ctrl+C停止监控
echo ========================================

adb logcat | findstr /i "MainActivity MemoryTrap 权限切换 陷阱触发 修改器检测 🚨"
