package com.sy.newfwg;

import android.util.Log;

/**
 * 内存陷阱检测SDK Java接口
 * 
 * 这是一个专门用于检测内存修改器（如GG修改器）扫描行为的SDK
 * 
 * 主要功能：
 * 1. 在关键内存区域部署陷阱
 * 2. 实时检测修改器扫描行为
 * 3. 提供统计和监控功能
 * 
 * 使用方法：
 * 1. 调用 initialize() 初始化SDK
 * 2. 调用 deployAntiGGTraps() 部署陷阱
 * 3. 调用 startDetection() 启动检测
 * 4. 通过 getStatistics() 查看检测结果
 * 5. 使用完毕后调用 cleanup() 清理资源
 */
public class MemoryTrapSDK {
    private static final String TAG = "MemoryTrapSDK";
    
    // 加载本地库
    static {
        try {
            System.loadLibrary("memorytrap");
            Log.i(TAG, "✅ 内存陷阱SDK本地库加载成功");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "❌ 内存陷阱SDK本地库加载失败", e);
        }
    }
    
    /**
     * 初始化SDK
     * @return 是否成功
     */
    public static boolean initialize() {
        Log.i(TAG, "🚀 初始化内存陷阱SDK...");
        return nativeInitialize();
    }
    
    /**
     * 部署针对GG修改器的陷阱
     * @param targetValue 目标值（默认推荐1111）
     * @return 部署的陷阱数量
     */
    public static int deployAntiGGTraps(int targetValue) {
        Log.i(TAG, "🎯 部署反GG陷阱，目标值=" + targetValue);
        int trapCount = nativeDeployAntiGGTraps(targetValue);
        Log.i(TAG, "✅ 成功部署 " + trapCount + " 个陷阱");
        return trapCount;
    }
    
    /**
     * 部署针对GG修改器的陷阱（使用默认目标值1111）
     * @return 部署的陷阱数量
     */
    public static int deployAntiGGTraps() {
        return deployAntiGGTraps(1111);
    }
    
    /**
     * 启动检测
     * @return 是否成功
     */
    public static boolean startDetection() {
        Log.i(TAG, "🚀 启动内存扫描检测...");
        boolean success = nativeStartDetection();
        if (success) {
            Log.i(TAG, "✅ 检测已启动");
        } else {
            Log.e(TAG, "❌ 检测启动失败");
        }
        return success;
    }
    
    /**
     * 停止检测
     */
    public static void stopDetection() {
        Log.i(TAG, "🛑 停止检测");
        nativeStopDetection();
    }
    
    /**
     * 获取统计信息
     * @return 统计信息字符串
     */
    public static String getStatistics() {
        return nativeGetStatistics();
    }
    
    /**
     * 获取陷阱信息（调试用）
     * @return 陷阱信息字符串
     */
    public static String getTrapInfos() {
        return nativeGetTrapInfos();
    }
    
    /**
     * 清理SDK资源
     */
    public static void cleanup() {
        Log.i(TAG, "🧹 清理SDK资源");
        nativeCleanup();
    }
    
    /**
     * 一键启动：初始化 + 部署陷阱 + 启动检测
     * @param targetValue 目标值
     * @return 是否成功
     */
    public static boolean quickStart(int targetValue) {
        Log.i(TAG, "🚀 一键启动内存陷阱检测系统...");

        // 1. 初始化
        if (!initialize()) {
            Log.e(TAG, "❌ 初始化失败");
            return false;
        }

        // 2. 部署陷阱
        int trapCount = deployAntiGGTraps(targetValue);
        if (trapCount == 0) {
            Log.e(TAG, "❌ 陷阱部署失败");
            return false;
        }

        // 3. 启动检测
        if (!startDetection()) {
            Log.e(TAG, "❌ 检测启动失败");
            return false;
        }

        Log.i(TAG, "🎉 内存陷阱检测系统启动成功！");
        Log.i(TAG, "   - 部署陷阱: " + trapCount + " 个");
        Log.i(TAG, "   - 目标值: " + targetValue);
        Log.i(TAG, "   - 状态: 检测中...");

        return true;
    }
    
    /**
     * 一键启动（使用默认目标值1111）
     * @return 是否成功
     */
    public static boolean quickStart() {
        return quickStart(1111);
    }
    
    /**
     * 打印当前状态
     */
    public static void printStatus() {
        Log.i(TAG, "📊 内存陷阱SDK状态:");
        Log.i(TAG, getStatistics());
    }
    
    // ========== Native方法声明 ==========
    
    private static native boolean nativeInitialize();
    private static native int nativeDeployAntiGGTraps(int targetValue);
    private static native boolean nativeStartDetection();
    private static native void nativeStopDetection();
    private static native String nativeGetStatistics();
    private static native String nativeGetTrapInfos();
    private static native void nativeCleanup();

    // 新增：获取陷阱访问统计
    private static native String nativeGetTrapAccessStats();

    /**
     * 获取陷阱访问统计
     */
    public static String getTrapAccessStatistics() {
        return nativeGetTrapAccessStats();
    }

    // 测试函数
    private static native boolean nativeTestTrapTrigger();
}
