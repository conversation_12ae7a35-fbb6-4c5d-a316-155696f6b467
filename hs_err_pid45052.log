#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=45052, tid=45496
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-705011bafae3b4525f48b14112f8d524-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 11:39:35 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 0.523624 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001d755557cb0):  JavaThread "main"             [_thread_in_vm, id=45496, stack(0x000000c3b2800000,0x000000c3b2900000) (1024K)]

Stack: [0x000000c3b2800000,0x000000c3b2900000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0x8a41ee]
V  [jvm.dll+0x670575]
V  [jvm.dll+0x6705da]
V  [jvm.dll+0x672dc2]
V  [jvm.dll+0x672c92]
V  [jvm.dll+0x670f4e]
V  [jvm.dll+0x26a8d0]
V  [jvm.dll+0x216127]
V  [jvm.dll+0x20bbae]
V  [jvm.dll+0x5ae58c]
V  [jvm.dll+0x21d26a]
V  [jvm.dll+0x820d6c]
V  [jvm.dll+0x821d94]
V  [jvm.dll+0x822362]
V  [jvm.dll+0x821fe8]
V  [jvm.dll+0x26cf1b]
V  [jvm.dll+0x26d14a]
V  [jvm.dll+0x5d1997]
V  [jvm.dll+0x5d497f]
V  [jvm.dll+0x3d8605]
V  [jvm.dll+0x3d7c7d]
C  0x000001d762808080

The last pc belongs to invokestatic (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  sun.security.util.SignatureFileVerifier.<init>(Ljava/util/ArrayList;Lsun/security/util/ManifestDigester;Ljava/lang/String;[B)V+23 java.base@21.0.7
j  java.util.jar.JarVerifier.processEntry(Lsun/security/util/ManifestEntryVerifier;)V+319 java.base@21.0.7
j  java.util.jar.JarVerifier.update(I[BIILsun/security/util/ManifestEntryVerifier;)V+39 java.base@21.0.7
j  java.util.jar.JarFile.initializeVerifier()V+164 java.base@21.0.7
j  java.util.jar.JarFile.ensureInitialization()V+36 java.base@21.0.7
j  java.util.jar.JavaUtilJarAccessImpl.ensureInitialization(Ljava/util/jar/JarFile;)V+1 java.base@21.0.7
j  jdk.internal.loader.URLClassPath$JarLoader$2.getManifest()Ljava/util/jar/Manifest;+10 java.base@21.0.7
j  jdk.internal.loader.BuiltinClassLoader.defineClass(Ljava/lang/String;Ljdk/internal/loader/Resource;)Ljava/lang/Class;+29 java.base@21.0.7
j  jdk.internal.loader.BuiltinClassLoader.findClassOnClassPathOrNull(Ljava/lang/String;)Ljava/lang/Class;+37 java.base@21.0.7
j  jdk.internal.loader.BuiltinClassLoader.loadClassOrNull(Ljava/lang/String;Z)Ljava/lang/Class;+111 java.base@21.0.7
j  jdk.internal.loader.BuiltinClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+3 java.base@21.0.7
j  jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(Ljava/lang/String;Z)Ljava/lang/Class;+36 java.base@21.0.7
j  java.lang.ClassLoader.loadClass(Ljava/lang/String;)Ljava/lang/Class;+3 java.base@21.0.7
v  ~StubRoutines::call_stub 0x000001d7627f100d
j  java.lang.Class.forName0(Ljava/lang/String;ZLjava/lang/ClassLoader;Ljava/lang/Class;)Ljava/lang/Class;+0 java.base@21.0.7
j  java.lang.Class.forName(Ljava/lang/String;ZLjava/lang/ClassLoader;Ljava/lang/Class;)Ljava/lang/Class;+37 java.base@21.0.7
j  java.lang.Class.forName(Ljava/lang/String;ZLjava/lang/ClassLoader;)Ljava/lang/Class;+20 java.base@21.0.7
j  sun.launcher.LauncherHelper.loadMainClass(ILjava/lang/String;)Ljava/lang/Class;+95 java.base@21.0.7
j  sun.launcher.LauncherHelper.checkAndLoadMain(ZILjava/lang/String;)Ljava/lang/Class;+42 java.base@21.0.7
v  ~StubRoutines::call_stub 0x000001d7627f100d
invokestatic  184 invokestatic  [0x000001d762807fe0, 0x000001d7628082a8]  712 bytes
[MachCode]
  0x000001d762807fe0: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x000001d762808000: 4424 0800 | 0000 00eb | 0150 4c89 | 6dc0 410f | b755 0148 | 8b4d d0c1 | e202 8b5c | d138 c1eb 
  0x000001d762808020: 1081 e3ff | 0000 0081 | fbb8 0000 | 000f 84b4 | 0000 00bb | b800 0000 | e805 0000 | 00e9 9900 
  0x000001d762808040: 0000 488b | d348 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 | 8987 9803 
  0x000001d762808060: 0000 4883 | ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 107c | b8ee ff7f | 0000 ffd0 
  0x000001d762808080: 4883 c408 | e90c 0000 | 0048 b810 | 7cb8 eeff | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 
  0x000001d7628080a0: 0000 0000 | 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 | 7749 837f 
  0x000001d7628080c0: 0800 0f84 | 0500 0000 | e933 8efe | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c341 | 0fb7 5501 
  0x000001d7628080e0: 488b 4dd0 | c1e2 0248 | 8b5c d140 | 488b 5b08 | 488b 5b08 | 488b 5b18 | 80bb 2101 | 0000 040f 
  0x000001d762808100: 840d 0000 | 004c 3bbb | 2801 0000 | 0f85 21ff | ffff 488b | 5cd1 408b | 54d1 50c1 | ea1c 49ba 
  0x000001d762808120: c0df 46ef | ff7f 0000 | 498b 14d2 | 5248 8b45 | d848 85c0 | 0f84 1200 | 0000 4883 | 4008 0148 
  0x000001d762808140: 8358 0800 | 4883 c010 | 4889 45d8 | 488b 45d8 | 4885 c00f | 843d 0100 | 0080 78f0 | 0a0f 8533 
  0x000001d762808160: 0100 0048 | 83c0 084c | 8b68 f841 | 83ed 0041 | 83fd 020f | 8c12 0100 | 004c 8b6b | 0845 0fb7 
  0x000001d762808180: 6d2e 4c2b | 2841 83ed | 014e 8b6c | ec08 4d85 | ed75 0ef6 | 4008 0175 | 58f0 4883 | 4808 01eb 
  0x000001d7628081a0: 5045 8b6d | 0849 ba00 | 0000 00d7 | 0100 004d | 03ea 4d8b | d54c 3368 | 0849 f7c5 | fcff ffff 
  0x000001d7628081c0: 742f 41f6 | c502 7529 | 4883 7808 | 0074 1e48 | 8378 0801 | 7417 4d8b | ea4c 3368 | 0849 f7c5 
  0x000001d7628081e0: fcff ffff | 740b 4883 | 4808 02eb | 044c 8968 | 0848 83c0 | 104c 8b68 | e841 83ed | 0241 83fd 
  0x000001d762808200: 020f 8c84 | 0000 004c | 8b6b 0845 | 0fb7 6d2e | 4c2b 2841 | 83ed 014e | 8b6c ec08 | 4d85 ed75 
  0x000001d762808220: 0ef6 4008 | 0175 58f0 | 4883 4808 | 01eb 5045 | 8b6d 0849 | ba00 0000 | 00d7 0100 | 004d 03ea 
  0x000001d762808240: 4d8b d54c | 3368 0849 | f7c5 fcff | ffff 742f | 41f6 c502 | 7529 4883 | 7808 0074 | 1e48 8378 
  0x000001d762808260: 0801 7417 | 4d8b ea4c | 3368 0849 | f7c5 fcff | ffff 740b | 4883 4808 | 02eb 044c | 8968 0848 
  0x000001d762808280: 83c0 104c | 8b68 d841 | 83ed 0441 | c1e5 0349 | 03c5 4889 | 45d8 4c8d | 6c24 084c | 896d f0ff 
  0x000001d7628082a0: 6350 660f | 1f44 0000 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001d76fb35950, length=11, elements={
0x000001d755557cb0, 0x000001d76eb639b0, 0x000001d76f872f30, 0x000001d76f87cf70,
0x000001d76f87df00, 0x000001d76f87fac0, 0x000001d76f880510, 0x000001d76f881750,
0x000001d76f8d6580, 0x000001d76f987fa0, 0x000001d76fb453d0
}

Java Threads: ( => current thread )
=>0x000001d755557cb0 JavaThread "main"                              [_thread_in_vm, id=45496, stack(0x000000c3b2800000,0x000000c3b2900000) (1024K)]
  0x000001d76eb639b0 JavaThread "Reference Handler"          daemon [_thread_blocked, id=46668, stack(0x000000c3b2c00000,0x000000c3b2d00000) (1024K)]
  0x000001d76f872f30 JavaThread "Finalizer"                  daemon [_thread_blocked, id=33688, stack(0x000000c3b2d00000,0x000000c3b2e00000) (1024K)]
  0x000001d76f87cf70 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=17332, stack(0x000000c3b2e00000,0x000000c3b2f00000) (1024K)]
  0x000001d76f87df00 JavaThread "Attach Listener"            daemon [_thread_blocked, id=44368, stack(0x000000c3b2f00000,0x000000c3b3000000) (1024K)]
  0x000001d76f87fac0 JavaThread "Service Thread"             daemon [_thread_blocked, id=35172, stack(0x000000c3b3000000,0x000000c3b3100000) (1024K)]
  0x000001d76f880510 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=46464, stack(0x000000c3b3100000,0x000000c3b3200000) (1024K)]
  0x000001d76f881750 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=36844, stack(0x000000c3b3200000,0x000000c3b3300000) (1024K)]
  0x000001d76f8d6580 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=47056, stack(0x000000c3b3300000,0x000000c3b3400000) (1024K)]
  0x000001d76f987fa0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=34900, stack(0x000000c3b3400000,0x000000c3b3500000) (1024K)]
  0x000001d76fb453d0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=38644, stack(0x000000c3b3500000,0x000000c3b3600000) (1024K)]
Total: 11

Other Threads:
  0x000001d7555449c0 VMThread "VM Thread"                           [id=18548, stack(0x000000c3b2b00000,0x000000c3b2c00000) (1024K)]
  0x000001d76ea8cc70 WatcherThread "VM Periodic Task Thread"        [id=46416, stack(0x000000c3b2a00000,0x000000c3b2b00000) (1024K)]
  0x000001d755544620 WorkerThread "GC Thread#0"                     [id=23104, stack(0x000000c3b2900000,0x000000c3b2a00000) (1024K)]
Total: 3

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45c308] Metaspace_lock - owner thread: 0x000001d755557cb0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001d700000000-0x000001d700ba0000-0x000001d700ba0000), size 12189696, SharedBaseAddress: 0x000001d700000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001d701000000-0x000001d741000000, reserved size: 1073741824
Narrow klass base: 0x000001d700000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 10114K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 39% used [0x00000000d5580000,0x00000000d5f60b00,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 1173K, committed 1344K, reserved 1114112K
  class space    used 104K, committed 192K, reserved 1048576K

Card table byte_map: [0x000001d757a70000,0x000001d757e80000] _byte_map_base: 0x000001d757670000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x000001d76a570000, 0x000001d76c570000)
 End Bits:   [0x000001d76c570000, 0x000001d76e570000)

Polling page: 0x000001d757820000

Metaspace:

Usage:
  Non-class:      1.04 MB used.
      Class:    104.88 KB used.
       Both:      1.15 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       1.12 MB (  2%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     192.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       1.31 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.66 MB
       Class:  15.67 MB
        Both:  27.33 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 74.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 21.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 96.
num_chunk_merges: 0.
num_chunk_splits: 66.
num_chunks_enlarged: 43.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=184Kb max_used=184Kb free=119815Kb
 bounds [0x000001d762d90000, 0x000001d763000000, 0x000001d76a2c0000]
CodeHeap 'profiled nmethods': size=120000Kb used=772Kb max_used=772Kb free=119227Kb
 bounds [0x000001d75b2c0000, 0x000001d75b530000, 0x000001d7627f0000]
CodeHeap 'non-nmethods': size=5760Kb used=1155Kb max_used=1174Kb free=4604Kb
 bounds [0x000001d7627f0000, 0x000001d762a60000, 0x000001d762d90000]
 total_blobs=963 nmethods=559 adapters=311
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.503 Thread 0x000001d76f8d6580  549   !   3       java.lang.String::replace (258 bytes)
Event: 0.505 Thread 0x000001d76f8d6580 nmethod 549 0x000001d75b37b310 code [0x000001d75b37b7e0, 0x000001d75b37da40]
Event: 0.505 Thread 0x000001d76f881750 nmethod 519% 0x000001d762dbba90 code [0x000001d762dbbca0, 0x000001d762dbc9d8]
Event: 0.505 Thread 0x000001d76f8d6580  550       1       lombok.patcher.ScriptManager::access$3 (5 bytes)
Event: 0.505 Thread 0x000001d76f881750  520 %     4       java.lang.StringLatin1::replace @ 79 (198 bytes)
Event: 0.505 Thread 0x000001d76f8d6580 nmethod 550 0x000001d762dbd190 code [0x000001d762dbd320, 0x000001d762dbd3d0]
Event: 0.505 Thread 0x000001d76f8d6580  552       3       lombok.patcher.MethodTarget::typeMatches (14 bytes)
Event: 0.505 Thread 0x000001d76f8d6580 nmethod 552 0x000001d75b37e990 code [0x000001d75b37eb60, 0x000001d75b37ece8]
Event: 0.505 Thread 0x000001d76f8d6580  553       3       java.util.Collections$UnmodifiableCollection::iterator (9 bytes)
Event: 0.505 Thread 0x000001d76f8d6580 nmethod 553 0x000001d75b37ee10 code [0x000001d75b37efe0, 0x000001d75b37f310]
Event: 0.505 Thread 0x000001d76f8d6580  554       3       java.util.Collections$UnmodifiableCollection$1::<init> (26 bytes)
Event: 0.506 Thread 0x000001d76f8d6580 nmethod 554 0x000001d75b37f490 code [0x000001d75b37f640, 0x000001d75b37f8c0]
Event: 0.506 Thread 0x000001d76f8d6580  557       3       lombok.patcher.PatchScript::classMatches (41 bytes)
Event: 0.507 Thread 0x000001d76f8d6580 nmethod 557 0x000001d75b37fa10 code [0x000001d75b37fc40, 0x000001d75b3802a8]
Event: 0.507 Thread 0x000001d76f8d6580  558       3       java.lang.invoke.MemberName::getReferenceKind (12 bytes)
Event: 0.507 Thread 0x000001d76f8d6580 nmethod 558 0x000001d75b380510 code [0x000001d75b3806a0, 0x000001d75b3807b8]
Event: 0.507 Thread 0x000001d76f8d6580  556       3       lombok.patcher.scripts.MethodLevelPatchScript::patch (21 bytes)
Event: 0.507 Thread 0x000001d76f8d6580 nmethod 556 0x000001d75b380890 code [0x000001d75b380a60, 0x000001d75b380ce8]
Event: 0.507 Thread 0x000001d76f8d6580  559       3       jdk.internal.org.objectweb.asm.ByteVector::<init> (13 bytes)
Event: 0.507 Thread 0x000001d76f8d6580 nmethod 559 0x000001d75b380e10 code [0x000001d75b380fc0, 0x000001d75b3811e0]

GC Heap History (0 events):
No events

Dll operation events (8 events):
Event: 0.018 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.050 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.125 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.136 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.138 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.142 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.157 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.333 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (20 events):
Event: 0.145 Thread 0x000001d755557cb0 DEOPT PACKING pc=0x000001d762da0568 sp=0x000000c3b28fe720
Event: 0.145 Thread 0x000001d755557cb0 DEOPT UNPACKING pc=0x000001d762843aa2 sp=0x000000c3b28fe680 mode 2
Event: 0.163 Thread 0x000001d755557cb0 DEOPT PACKING pc=0x000001d75b2f7949 sp=0x000000c3b28fe500
Event: 0.163 Thread 0x000001d755557cb0 DEOPT UNPACKING pc=0x000001d762844242 sp=0x000000c3b28fd988 mode 0
Event: 0.163 Thread 0x000001d755557cb0 DEOPT PACKING pc=0x000001d75b2f7949 sp=0x000000c3b28fe500
Event: 0.163 Thread 0x000001d755557cb0 DEOPT UNPACKING pc=0x000001d762844242 sp=0x000000c3b28fd988 mode 0
Event: 0.164 Thread 0x000001d755557cb0 DEOPT PACKING pc=0x000001d75b2f7949 sp=0x000000c3b28fe500
Event: 0.164 Thread 0x000001d755557cb0 DEOPT UNPACKING pc=0x000001d762844242 sp=0x000000c3b28fd988 mode 0
Event: 0.164 Thread 0x000001d755557cb0 DEOPT PACKING pc=0x000001d75b2f7949 sp=0x000000c3b28fe500
Event: 0.164 Thread 0x000001d755557cb0 DEOPT UNPACKING pc=0x000001d762844242 sp=0x000000c3b28fd988 mode 0
Event: 0.165 Thread 0x000001d755557cb0 DEOPT PACKING pc=0x000001d75b2f7949 sp=0x000000c3b28fe500
Event: 0.165 Thread 0x000001d755557cb0 DEOPT UNPACKING pc=0x000001d762844242 sp=0x000000c3b28fd988 mode 0
Event: 0.504 Thread 0x000001d755557cb0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001d762da0568 relative=0x00000000000007c8
Event: 0.504 Thread 0x000001d755557cb0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d762da0568 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 152 c2
Event: 0.504 Thread 0x000001d755557cb0 DEOPT PACKING pc=0x000001d762da0568 sp=0x000000c3b28fef30
Event: 0.504 Thread 0x000001d755557cb0 DEOPT UNPACKING pc=0x000001d762843aa2 sp=0x000000c3b28fee90 mode 2
Event: 0.504 Thread 0x000001d755557cb0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001d762da0568 relative=0x00000000000007c8
Event: 0.504 Thread 0x000001d755557cb0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001d762da0568 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 152 c2
Event: 0.504 Thread 0x000001d755557cb0 DEOPT PACKING pc=0x000001d762da0568 sp=0x000000c3b28fef30
Event: 0.504 Thread 0x000001d755557cb0 DEOPT UNPACKING pc=0x000001d762843aa2 sp=0x000000c3b28fee90 mode 2

Classes loaded (20 events):
Event: 0.442 Loading class java/util/regex/Pattern$Prolog done
Event: 0.503 Loading class jdk/internal/vm/PostVMInitHook
Event: 0.503 Loading class jdk/internal/vm/PostVMInitHook done
Event: 0.505 Loading class java/util/jar/JarFile$ThreadTrackHolder
Event: 0.505 Loading class java/util/jar/JarFile$ThreadTrackHolder done
Event: 0.505 Loading class jdk/internal/misc/ThreadTracker
Event: 0.505 Loading class jdk/internal/misc/ThreadTracker done
Event: 0.505 Loading class jdk/internal/misc/ThreadTracker$ThreadRef
Event: 0.505 Loading class jdk/internal/misc/ThreadTracker$ThreadRef done
Event: 0.505 Loading class sun/security/util/ManifestEntryVerifier
Event: 0.506 Loading class sun/security/util/ManifestEntryVerifier done
Event: 0.506 Loading class sun/security/util/ManifestDigester
Event: 0.506 Loading class sun/security/util/ManifestDigester done
Event: 0.506 Loading class sun/security/util/ManifestDigester$Position
Event: 0.506 Loading class sun/security/util/ManifestDigester$Position done
Event: 0.506 Loading class sun/security/util/ManifestDigester$Entry
Event: 0.506 Loading class sun/security/util/ManifestDigester$Entry done
Event: 0.506 Loading class sun/security/util/ManifestDigester$Section
Event: 0.506 Loading class sun/security/util/ManifestDigester$Section done
Event: 0.507 Loading class sun/security/jca/Providers

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (11 events):
Event: 0.098 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d57e11f0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d57e11f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.152 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d594a870}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d594a870) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.189 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a03040}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5a03040) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.191 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a15a28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5a15a28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.192 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a20ed0}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a20ed0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.192 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a24908}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5a24908) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.195 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a3d5f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000d5a3d5f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.196 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a41f48}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a41f48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.197 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a45ad8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a45ad8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.197 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a48f48}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a48f48) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.415 Thread 0x000001d755557cb0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5c0ad50}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5c0ad50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (4 events):
Event: 0.100 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.100 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.168 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.168 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (13 events):
Event: 0.034 Thread 0x000001d755557cb0 Thread added: 0x000001d755557cb0
Event: 0.059 Thread 0x000001d755557cb0 Thread added: 0x000001d76eb639b0
Event: 0.059 Thread 0x000001d755557cb0 Thread added: 0x000001d76f872f30
Event: 0.059 Thread 0x000001d755557cb0 Thread added: 0x000001d76f87cf70
Event: 0.060 Thread 0x000001d755557cb0 Thread added: 0x000001d76f87df00
Event: 0.060 Thread 0x000001d755557cb0 Thread added: 0x000001d76f87fac0
Event: 0.060 Thread 0x000001d755557cb0 Thread added: 0x000001d76f880510
Event: 0.060 Thread 0x000001d755557cb0 Thread added: 0x000001d76f881750
Event: 0.061 Thread 0x000001d755557cb0 Thread added: 0x000001d76f8d6580
Event: 0.090 Thread 0x000001d755557cb0 Thread added: 0x000001d76f987fa0
Event: 0.112 Thread 0x000001d76f8d6580 Thread added: 0x000001d76fa5eec0
Event: 0.320 Thread 0x000001d76fa5eec0 Thread exited: 0x000001d76fa5eec0
Event: 0.503 Thread 0x000001d755557cb0 Thread added: 0x000001d76fb453d0


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-705011bafae3b4525f48b14112f8d524-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 18:50 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (5475M free)
TotalPageFile size 61318M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 54M, peak: 54M
current process commit charge ("private bytes"): 228M, peak: 229M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
