import { TssSDK } from "tersafe";

export default class TersafeManager {

  static instance:TersafeManager = new TersafeManager();

  public static getInstance(): TersafeManager {
    return TersafeManager.instance;
  }

  public onCreate(){
    TssSDK.GetInstance().onCreate(globalThis.context);
  }
}

export function RegisterTersafeManager() {
  let register: Record<string, Object> = {};
  register["TersafeManager"] = TersafeManager;
  return register;
}