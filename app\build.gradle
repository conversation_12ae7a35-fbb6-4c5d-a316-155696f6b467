apply plugin: 'com.android.application'

android {
    namespace 'com.sy.newfwg'
    compileSdk 34

    // 增加DEX构建内存
    dexOptions {
        javaMaxHeapSize "4g"
        preDexLibraries = false
    }

    defaultConfig {
        applicationId "com.sy.newfwg"
        minSdk 21
        targetSdk 34
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

        // NDK配置
        ndk {
            abiFilters 'arm64-v8a'
        }

        // CMake配置
        externalNativeBuild {
            cmake {
                cppFlags "-std=c++17 -frtti -fexceptions"
                abiFilters 'arm64-v8a'
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    // 外部Native构建配置
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.18.1"
        }
    }
}

dependencies {
    // 腾讯TP2SDK
    implementation files('tp2.jar')

    implementation 'androidx.appcompat:appcompat:1.4.2'
    implementation 'com.google.android.material:material:1.6.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'


    implementation 'com.google.code.gson:gson:2.8.6'
    //google
    implementation 'com.google.android.gms:play-services-auth:+'
    //Facebook
//    api 'com.facebook.android:facebook-android-sdk:12.2.0'
//    api 'com.facebook.android:facebook-login:latest.release'

    implementation 'com.android.installreferrer:installreferrer:2.2'
    //firebase
    implementation platform('com.google.firebase:firebase-bom:33.15.0')
    implementation 'com.google.firebase:firebase-messaging'
    implementation 'com.google.firebase:firebase-analytics'

    // FCM推送
    implementation 'com.google.firebase:firebase-core:21.1.0'
    implementation 'com.google.firebase:firebase-analytics:21.1.0'
    implementation 'com.google.firebase:firebase-messaging:23.0.7'








}