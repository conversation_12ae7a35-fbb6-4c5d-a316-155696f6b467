<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingBottom="@dimen/activity_vertical_margin"
    android:paddingLeft="@dimen/activity_horizontal_margin"
    android:paddingRight="@dimen/activity_horizontal_margin"
    android:paddingTop="@dimen/activity_vertical_margin"
    android:gravity="center_horizontal"
    tools:context=".MainActivity" >

    <Button
        android:id="@+id/btn_wx_login"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_alignLeft="@+id/btn_qq_login"
        android:layout_below="@+id/btn_qq_login"
        android:layout_marginTop="22dp"
        android:text="@string/wx_login" />

    <Button
        android:id="@+id/btn_qq_login"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="74dp"
        android:text="@string/qq_login" />

</RelativeLayout>