{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-sr\\values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,276,356,450,581,662,728,820,888,951,1054,1120,1176,1247,1307,1361,1473,1530,1591,1645,1721,1846,1932,2015,2123,2204,2287,2375,2442,2508,2582,2660,2749,2824,2900,2975,3046,3136,3209,3301,3397,3469,3545,3641,3694,3761,3848,3935,3997,4061,4124,4229,4333,4429,4536", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "271,351,445,576,657,723,815,883,946,1049,1115,1171,1242,1302,1356,1468,1525,1586,1640,1716,1841,1927,2010,2118,2199,2282,2370,2437,2503,2577,2655,2744,2819,2895,2970,3041,3131,3204,3296,3392,3464,3540,3636,3689,3756,3843,3930,3992,4056,4119,4224,4328,4424,4531,4611"}, "to": {"startLines": "2,34,35,36,37,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3054,3134,3228,3359,5610,5676,5768,5836,5899,6002,6068,6124,6195,6255,6309,6421,6478,6539,6593,6669,6794,6880,6963,7071,7152,7235,7323,7390,7456,7530,7608,7697,7772,7848,7923,7994,8084,8157,8249,8345,8417,8493,8589,8642,8709,8796,8883,8945,9009,9072,9177,9281,9377,9484", "endLines": "6,34,35,36,37,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105", "endColumns": "12,79,93,130,80,65,91,67,62,102,65,55,70,59,53,111,56,60,53,75,124,85,82,107,80,82,87,66,65,73,77,88,74,75,74,70,89,72,91,95,71,75,95,52,66,86,86,61,63,62,104,103,95,106,79", "endOffsets": "321,3129,3223,3354,3435,5671,5763,5831,5894,5997,6063,6119,6190,6250,6304,6416,6473,6534,6588,6664,6789,6875,6958,7066,7147,7230,7318,7385,7451,7525,7603,7692,7767,7843,7918,7989,8079,8152,8244,8340,8412,8488,8584,8637,8704,8791,8878,8940,9004,9067,9172,9276,9372,9479,9559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,106", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "326,433,534,640,726,830,952,1036,1117,1208,1301,1396,1490,1590,1683,1778,1883,1974,2065,2151,2256,2362,2465,2571,2680,2787,2957,9564", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "428,529,635,721,825,947,1031,1112,1203,1296,1391,1485,1585,1678,1773,1878,1969,2060,2146,2251,2357,2460,2566,2675,2782,2952,3049,9646"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "107", "startColumns": "4", "startOffsets": "9651", "endColumns": "100", "endOffsets": "9747"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3440,3546,3702,3828,3938,4092,4219,4331,4563,4712,4819,4979,5106,5255,5397,5465,5530", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "3541,3697,3823,3933,4087,4214,4326,4428,4707,4814,4974,5101,5250,5392,5460,5525,5605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4433", "endColumns": "129", "endOffsets": "4558"}}]}]}