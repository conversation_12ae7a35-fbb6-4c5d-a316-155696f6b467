﻿using UnityEngine;
using System;
using System.Text;
using System.IO;

public class FileLog
{
	public static void Log(string log)
	{
		try
		{
			StringBuilder sb = new StringBuilder();
			sb.AppendFormat("[{0:T}]", System.DateTime.Now);
			sb.Append(log);
			
			DoLog(sb.ToString());
		}
		catch (IOException e)
		{
		}
	}
	public static void CleanLog()
	{
		try
		{
			File.Delete("/data/data/log/c_sharp_log.log");
		} 
		catch (IOException e)
		{
			
		}
	}
	
	public static void Assert(bool result, int line)
    {
        if (!result)
        {
            DoLog(String.Format("assert err, line:{0}", line));
        }
    }
	
	private static void DoLog(string log) 
	{
		FileStream file = new FileStream("/data/data/log/c_sharp_log.log", FileMode.Append);
		if (file == null) return;
		StreamWriter sw = new StreamWriter(file);
		sw.WriteLine(log);
		sw.Close();
		file.Close();
	}
}
