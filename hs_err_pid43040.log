#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 570960 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=43040, tid=38532
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-312d6ff0795baafce2bf9ca828d4e3e4-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 11:23:29 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 0.777455 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000002725f9dcc80):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=38532, stack(0x0000008ba3f00000,0x0000008ba4000000) (1024K)]


Current CompileTask:
C2:777  972   !   4       java.util.jar.Attributes::read (494 bytes)

Stack: [0x0000008ba3f00000,0x0000008ba4000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x3b692c]
V  [jvm.dll+0x382aa5]
V  [jvm.dll+0x381f0a]
V  [jvm.dll+0x247af0]
V  [jvm.dll+0x2470cf]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002725fcf91f0, length=13, elements={
0x00000272046f47d0, 0x000002721db02270, 0x000002725f9d3b00, 0x000002725f9d6a20,
0x000002725f9d9840, 0x000002725f9db400, 0x000002725f9dbe50, 0x000002725f9dcc80,
0x000002725fa405c0, 0x0000027206b1a490, 0x000002725fcfe5a0, 0x000002725fc16d80,
0x00000272650ca9f0
}

Java Threads: ( => current thread )
  0x00000272046f47d0 JavaThread "main"                              [_thread_in_vm, id=31548, stack(0x0000008ba3500000,0x0000008ba3600000) (1024K)]
  0x000002721db02270 JavaThread "Reference Handler"          daemon [_thread_blocked, id=37448, stack(0x0000008ba3900000,0x0000008ba3a00000) (1024K)]
  0x000002725f9d3b00 JavaThread "Finalizer"                  daemon [_thread_blocked, id=32972, stack(0x0000008ba3a00000,0x0000008ba3b00000) (1024K)]
  0x000002725f9d6a20 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=11424, stack(0x0000008ba3b00000,0x0000008ba3c00000) (1024K)]
  0x000002725f9d9840 JavaThread "Attach Listener"            daemon [_thread_blocked, id=43368, stack(0x0000008ba3c00000,0x0000008ba3d00000) (1024K)]
  0x000002725f9db400 JavaThread "Service Thread"             daemon [_thread_blocked, id=46808, stack(0x0000008ba3d00000,0x0000008ba3e00000) (1024K)]
  0x000002725f9dbe50 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=3776, stack(0x0000008ba3e00000,0x0000008ba3f00000) (1024K)]
=>0x000002725f9dcc80 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=38532, stack(0x0000008ba3f00000,0x0000008ba4000000) (1024K)]
  0x000002725fa405c0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=17040, stack(0x0000008ba4000000,0x0000008ba4100000) (1024K)]
  0x0000027206b1a490 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=46244, stack(0x0000008ba4100000,0x0000008ba4200000) (1024K)]
  0x000002725fcfe5a0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=39132, stack(0x0000008ba4200000,0x0000008ba4300000) (1024K)]
  0x000002725fc16d80 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=46916, stack(0x0000008ba4300000,0x0000008ba4400000) (1024K)]
  0x00000272650ca9f0 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=26636, stack(0x0000008ba4400000,0x0000008ba4500000) (1024K)]
Total: 13

Other Threads:
  0x000002725f9d2ab0 VMThread "VM Thread"                           [id=18440, stack(0x0000008ba3800000,0x0000008ba3900000) (1024K)]
  0x0000027206b19050 WatcherThread "VM Periodic Task Thread"        [id=46856, stack(0x0000008ba3700000,0x0000008ba3800000) (1024K)]
  0x0000027206b0b5c0 WorkerThread "GC Thread#0"                     [id=12416, stack(0x0000008ba3600000,0x0000008ba3700000) (1024K)]
Total: 3

Threads with active compile tasks:
C2 CompilerThread0  812  972   !   4       java.util.jar.Attributes::read (494 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45c308] Metaspace_lock - owner thread: 0x00000272046f47d0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002721e000000-0x000002721eba0000-0x000002721eba0000), size 12189696, SharedBaseAddress: 0x000002721e000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002721f000000-0x000002725f000000, reserved size: 1073741824
Narrow klass base: 0x000002721e000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 21462K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 83% used [0x00000000d5580000,0x00000000d6a759f8,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 3862K, committed 4032K, reserved 1114112K
  class space    used 422K, committed 512K, reserved 1048576K

Card table byte_map: [0x0000027218e50000,0x0000027219260000] _byte_map_base: 0x0000027218a50000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x0000027219510000, 0x000002721b510000)
 End Bits:   [0x000002721b510000, 0x000002721d510000)

Polling page: 0x0000027206990000

Metaspace:

Usage:
  Non-class:      3.36 MB used.
      Class:    422.32 KB used.
       Both:      3.77 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       3.44 MB (  5%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     512.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       3.94 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.31 MB
       Class:  15.45 MB
        Both:  26.77 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 178.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 63.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 231.
num_chunk_merges: 0.
num_chunk_splits: 149.
num_chunks_enlarged: 88.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=405Kb max_used=405Kb free=119594Kb
 bounds [0x0000027211830000, 0x0000027211aa0000, 0x0000027218d60000]
CodeHeap 'profiled nmethods': size=120000Kb used=1642Kb max_used=1642Kb free=118357Kb
 bounds [0x0000027209d60000, 0x0000027209fd0000, 0x0000027211290000]
CodeHeap 'non-nmethods': size=5760Kb used=1195Kb max_used=1225Kb free=4564Kb
 bounds [0x0000027211290000, 0x0000027211500000, 0x0000027211830000]
 total_blobs=1481 nmethods=1026 adapters=362
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 0.759 Thread 0x00000272650ca9f0  990       4       jdk.internal.misc.Unsafe::getIntUnaligned (12 bytes)
Event: 0.759 Thread 0x00000272650ca9f0 nmethod 990 0x0000027211891b90 code [0x0000027211891d20, 0x0000027211891dd8]
Event: 0.760 Thread 0x000002725fa405c0  992       3       java.util.Arrays::hashCode (60 bytes)
Event: 0.761 Thread 0x000002725fa405c0 nmethod 992 0x0000027209ee7890 code [0x0000027209ee7a60, 0x0000027209ee7ce8]
Event: 0.765 Thread 0x000002725fa405c0  993  s    3       java.util.Hashtable::put (104 bytes)
Event: 0.765 Thread 0x000002725fa405c0 nmethod 993 0x0000027209ee7e10 code [0x0000027209ee8020, 0x0000027209ee86f8]
Event: 0.765 Thread 0x000002725fa405c0  994       3       java.util.Hashtable::addEntry (87 bytes)
Event: 0.765 Thread 0x000002725fa405c0 nmethod 994 0x0000027209ee8990 code [0x0000027209ee8b80, 0x0000027209ee91b0]
Event: 0.765 Thread 0x000002725fa405c0  995       3       java.util.Hashtable$Entry::<init> (26 bytes)
Event: 0.765 Thread 0x000002725fa405c0 nmethod 995 0x0000027209ee9410 code [0x0000027209ee95c0, 0x0000027209ee9770]
Event: 0.765 Thread 0x000002725fa405c0  996       3       sun.security.util.SignatureFileVerifier::updateSigners (132 bytes)
Event: 0.766 Thread 0x000002725fa405c0 nmethod 996 0x0000027209ee9810 code [0x0000027209ee9ac0, 0x0000027209eea960]
Event: 0.766 Thread 0x000002725fa405c0  997       3       sun.security.util.SignatureFileVerifier::matches (89 bytes)
Event: 0.767 Thread 0x00000272650ca9f0  998       4       sun.security.provider.ByteArrayAccess::b2iBig64 (231 bytes)
Event: 0.767 Thread 0x000002725fa405c0 nmethod 997 0x0000027209eeaf10 code [0x0000027209eeb1c0, 0x0000027209eec010]
Event: 0.771 Thread 0x000002725fc16d80 nmethod 989 0x0000027211891e90 code [0x0000027211892060, 0x0000027211892ab0]
Event: 0.773 Thread 0x000002725fa405c0  999       3       java.lang.ref.WeakReference::<init> (7 bytes)
Event: 0.773 Thread 0x00000272650ca9f0 nmethod 998 0x0000027211892e90 code [0x0000027211893020, 0x0000027211893280]
Event: 0.773 Thread 0x000002725fa405c0 nmethod 999 0x0000027209eec610 code [0x0000027209eec7c0, 0x0000027209eeca10]
Event: 0.774 Thread 0x000002725fa405c0 1000       3       java.util.zip.ZipFile::getZipEntry (322 bytes)

GC Heap History (0 events):
No events

Dll operation events (8 events):
Event: 0.013 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.055 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.182 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.188 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.190 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.194 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.214 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.306 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

Deoptimization events (20 events):
Event: 0.764 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70c5d sp=0x0000008ba35fe540
Event: 0.764 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fd9b8 mode 0
Event: 0.766 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70dfb sp=0x0000008ba35fe820
Event: 0.766 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0
Event: 0.766 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70dfb sp=0x0000008ba35fe820
Event: 0.766 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0
Event: 0.766 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70dfb sp=0x0000008ba35fe820
Event: 0.766 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0
Event: 0.766 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70dfb sp=0x0000008ba35fe820
Event: 0.766 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0
Event: 0.767 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70c5d sp=0x0000008ba35fe820
Event: 0.767 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0
Event: 0.767 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70c5d sp=0x0000008ba35fe820
Event: 0.767 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0
Event: 0.767 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70c5d sp=0x0000008ba35fe820
Event: 0.767 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0
Event: 0.767 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70dfb sp=0x0000008ba35fe820
Event: 0.767 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0
Event: 0.767 Thread 0x00000272046f47d0 DEOPT PACKING pc=0x0000027209e70dfb sp=0x0000008ba35fe820
Event: 0.767 Thread 0x00000272046f47d0 DEOPT UNPACKING pc=0x00000272112e4242 sp=0x0000008ba35fdc98 mode 0

Classes loaded (20 events):
Event: 0.719 Loading class java/nio/file/attribute/FileOwnerAttributeView
Event: 0.719 Loading class java/nio/file/attribute/FileOwnerAttributeView done
Event: 0.719 Loading class java/nio/file/attribute/AclFileAttributeView done
Event: 0.719 Loading class sun/nio/fs/AbstractAclFileAttributeView done
Event: 0.719 Loading class sun/nio/fs/WindowsAclFileAttributeView done
Event: 0.719 Loading class sun/nio/fs/WindowsSecurity
Event: 0.719 Loading class sun/nio/fs/WindowsSecurity done
Event: 0.720 Loading class sun/nio/fs/WindowsFileStore
Event: 0.720 Loading class java/nio/file/FileStore
Event: 0.720 Loading class java/nio/file/FileStore done
Event: 0.720 Loading class sun/nio/fs/WindowsFileStore done
Event: 0.727 Loading class java/net/URLClassLoader$1
Event: 0.727 Loading class java/net/URLClassLoader$1 done
Event: 0.768 Loading class java/io/FilePermission$1
Event: 0.768 Loading class jdk/internal/access/JavaIOFilePermissionAccess
Event: 0.768 Loading class jdk/internal/access/JavaIOFilePermissionAccess done
Event: 0.768 Loading class java/io/FilePermission$1 done
Event: 0.769 Loading class java/io/FilePermissionCollection
Event: 0.769 Loading class java/io/FilePermissionCollection done
Event: 0.774 Loading class java/util/EventListener

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.264 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a15a28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5a15a28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.266 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a20e68}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a20e68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.267 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a248a0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5a248a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.270 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a3d590}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000d5a3d590) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.270 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a41ee0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a41ee0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.271 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a45a70}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a45a70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.271 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a48ee0}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5a48ee0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.342 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5c08e28}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.reinvoke_L(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000000d5c08e28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.488 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6090290}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000d6090290) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.627 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d646f0c0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000d646f0c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.632 Thread 0x00000272046f47d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d6497248}: sun/net/www/protocol/c/Handler> (0x00000000d6497248) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 0.650 Thread 0x00000272046f47d0 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f1360}> (0x00000000d64f1360) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.651 Thread 0x00000272046f47d0 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f2878}> (0x00000000d64f2878) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.651 Thread 0x00000272046f47d0 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f36b8}> (0x00000000d64f36b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.651 Thread 0x00000272046f47d0 Exception <a 'java/io/FileNotFoundException'{0x00000000d64f43b0}> (0x00000000d64f43b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.656 Thread 0x00000272046f47d0 Exception <a 'java/io/FileNotFoundException'{0x00000000d652e6f8}> (0x00000000d652e6f8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 0.672 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d656a460}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d656a460) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.676 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d657df88}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d657df88) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.678 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65951e0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d65951e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.697 Thread 0x00000272046f47d0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6629808}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, int)'> (0x00000000d6629808) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]

ZGC Phase Switch (0 events):
No events

VM Operations (8 events):
Event: 0.154 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.156 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.224 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.224 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.601 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.601 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.622 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.622 Executing VM operation: HandshakeAllThreads (Deoptimize) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (13 events):
Event: 0.030 Thread 0x00000272046f47d0 Thread added: 0x00000272046f47d0
Event: 0.066 Thread 0x00000272046f47d0 Thread added: 0x000002721db02270
Event: 0.066 Thread 0x00000272046f47d0 Thread added: 0x000002725f9d3b00
Event: 0.066 Thread 0x00000272046f47d0 Thread added: 0x000002725f9d6a20
Event: 0.066 Thread 0x00000272046f47d0 Thread added: 0x000002725f9d9840
Event: 0.066 Thread 0x00000272046f47d0 Thread added: 0x000002725f9db400
Event: 0.066 Thread 0x00000272046f47d0 Thread added: 0x000002725f9dbe50
Event: 0.066 Thread 0x00000272046f47d0 Thread added: 0x000002725f9dcc80
Event: 0.109 Thread 0x00000272046f47d0 Thread added: 0x000002725fa405c0
Event: 0.145 Thread 0x00000272046f47d0 Thread added: 0x0000027206b1a490
Event: 0.430 Thread 0x00000272046f47d0 Thread added: 0x000002725fcfe5a0
Event: 0.442 Thread 0x000002725fa405c0 Thread added: 0x000002725fc16d80
Event: 0.540 Thread 0x000002725fa405c0 Thread added: 0x00000272650ca9f0


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_ss_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\ss_ws --pipe=\\.\pipe\lsp-312d6ff0795baafce2bf9ca828d4e3e4-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 18:34 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (3521M free)
TotalPageFile size 61318M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 80M, peak: 80M
current process commit charge ("private bytes"): 241M, peak: 242M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
