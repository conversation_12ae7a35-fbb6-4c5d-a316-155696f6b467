@echo off
echo ========================================
echo 修改器检测专用监控工具
echo ========================================
echo.

echo [1] 安装最新APK...
adb install -r app\build\outputs\apk\debug\app-debug.apk
if %errorlevel% neq 0 (
    echo 安装失败!
    pause
    exit /b 1
)

echo.
echo [2] 启动应用...
adb shell am start -n com.sy.newfwg/.MainActivity

echo.
echo [3] 等待3秒让应用启动...
timeout /t 3 /nobreak > nul

echo.
echo [4] 清除日志缓存...
adb logcat -c

echo.
echo [5] 开始专用监控...
echo.
echo 🎯 统一日志标签: TRAP_DETECT
echo.
echo 操作步骤:
echo 1. 在手机上点击"开始修改器检测"按钮
echo 2. 等待看到系统启动完成的日志
echo 3. 打开修改器，选择进程 com.sy.newfwg
echo 4. 搜索数值 100，类型选择 Dword
echo 5. 观察下面的日志输出
echo.
echo 🔍 关键日志标识:
echo ✅ 系统启动: "检测系统启动成功"
echo ✅ 陷阱设置: "包含1024个Dword值100"  
echo ✅ 权限切换: "权限切换线程开始运行"
echo 🚨 检测成功: "修改器检测成功！"
echo 🎉 Java确认: "Java层确认：修改器检测成功！"
echo.
echo 按Ctrl+C停止监控
echo ========================================

adb logcat -s TRAP_DETECT
