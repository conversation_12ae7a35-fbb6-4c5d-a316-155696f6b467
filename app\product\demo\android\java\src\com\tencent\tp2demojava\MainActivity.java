package com.tencent.tp2demojava;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.Toast;

import com.tencent.tersafe2.TP2Sdk;
import com.tencent.tp.TssInfoPublisher;

public class MainActivity extends Activity {

	private class MyTssInfoReceiver implements TssInfoPublisher.TssInfoReceiver{
		public void onReceive(int tssInfoType,String info){
			if (tssInfoType == TssInfoPublisher.TSS_INFO_TYPE_DETECT_RESULT)
			{
				// 只关注TSS_INFO_TYPE_DETECT_RESULT
				String plain = TP2Sdk.decTssInfo(info);
				if (plain.equals("-1")) return;
				Log.d("MTP","[Java TSS INFO]:" + tssInfoType + "|" + plain);
			}
			else if (tssInfoType == TssInfoPublisher.TSS_INFO_TYPE_HEARTBEAT){
				// 如果不关心心跳信息，可忽略
				String plain = TP2Sdk.decTssInfo(info);
				if (plain.equals("-1")) return;
				Log.d("MTP","[Java TSS INFO]:" + tssInfoType + "|" + plain);
			}
			
		}
	}
	private MyTssInfoReceiver mTssInfoReceiver;
    @Override
	protected void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		setContentView(R.layout.activity_main);
		
		if (mTssInfoReceiver == null)
		{
			mTssInfoReceiver = new MyTssInfoReceiver();
			Log.d("MTP", "Register...");
			TP2Sdk.registTssInfoReceiver(mTssInfoReceiver);
		}

		//登录后第一时间调用init接口, 填写gameId 和 appKey
		TP2Sdk.initEx(19257, "d5ab8dc7ef67ca92e41d730982c5c602");
		
		//在用户成功登录游戏后,调用TP2Sdk.onUserLogin方法
		Button qqLogin = (Button)findViewById(R.id.btn_qq_login);
		qqLogin.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                int worldId = 101;                                  //大区或服务器号
                String openId = "B73B36366565F9E02C7525516DCF31C2"; //用户id
                String roleId = "paladin";                          //角色id
                
                onQQLogin(worldId, openId, roleId);
                Toast.makeText(getApplicationContext(), "QQ登录",Toast.LENGTH_SHORT).show();
                startActivity(new Intent(MainActivity.this, GameActivity.class));

                // 开启Android设备上应用安装列表扫描功能
                // 7.0及之后版本建议游戏在用户登录后调用
                String cmd = "AllowAPKCollect";
                TP2Sdk.ioctl(cmd);
            }
        });
		
		Button wxLogin = (Button)findViewById(R.id.btn_wx_login);
		wxLogin.setOnClickListener(new View.OnClickListener()
        {
            @Override
            public void onClick(View v)
            {
                int worldId = 101;                                  //大区或服务器号
                String openId = "WX3B36366565F9E02C7525516DCF31C2"; //用户id
                String roleId = "paladin";                          //角色id
            
                onWXLogin(worldId, openId, roleId);
                Toast.makeText(getApplicationContext(), "微信登录",Toast.LENGTH_SHORT).show();
                startActivity(new Intent(MainActivity.this, GameActivity.class));
        }
        });
        
        // 7.3版本之后的版本支持上报举报信息至ACE控制台
        String reported_account_id = "8B57B75C79A3E34E718C";   // 被举报人的账号 openid
        int reported_account_type = ENTRYID.ENTRY_ID_QZONE;                 // 被举报人的账号类型
        int report_type = TP2SdkReportType.REPORT_TYPE_CHEAT;                                         // 举报的类型，辱骂、作弊等
        String report_scene = "5v5_mode";                              // 举报发生的场景, 游戏可自定义，会直接展示在控制台
        String report_reason = "肯定开透视了，我蹲草里不动都能看到我";      // 举报的详情信息，游戏可自定义
        // 举报信息最长为1024Byte，否则在上报时会截断
        String cmd = String.format(Locale.ENGLISH, "ReportComplaint:reportedId=%s;reportedType=%d;type=%d;scene=%s;reason=%s",
            reported_account_id,    // 被举报人的账号 openid
            reported_account_type,  // 被举报人的账号类型
            report_type,            // 举报的类型
            report_scene,           // 举报发生的场景
            report_reason           // 举报的详情信息);
        );
        TP2Sdk.ioctl(cmd);
	}
	
    @Override
    protected void onPause()
    {
        
        TP2Sdk.onAppPause();
        super.onPause();
    }

    @Override
    protected void onResume()
    {
        super.onResume();
        TP2Sdk.onAppResume();
    }
	
	public void onQQLogin(int worldId, String openId, String roleId)
	{
	    int accountType = ENTRYID.ENTRY_ID_QZONE;
	    TP2Sdk.onUserLogin(accountType, worldId, openId, roleId);
	}
	
    public void onWXLogin(int worldId, String openId, String roleId)
    {
        int accountType = ENTRYID.ENTRY_ID_MM;
        TP2Sdk.onUserLogin(accountType, worldId, openId, roleId);
    }
}

class ENTRYID
{
    public final static int ENTRY_ID_QZONE = 1;         // QQ
    public final static int ENTRY_ID_MM = 2;            // 微信
    public final static int ENTRT_ID_FACEBOOK = 3;      // facebook
    public final static int ENTRY_ID_TWITTER = 4;       // twitter
    public final static int ENTRY_ID_LINE = 5;          // line
    public final static int ENTRY_ID_WHATSAPP = 6;      // whatsapp
    public final static int ENTRY_ID_OTHERS = 99;       // 其他平台
}

class Tp2SdkReportType
{
    public final static int REPORT_TYPE_CHEAT		    = 1;       	// 作弊
    public final static int REPORT_TYPE_ABUSE_BUGS		= 2;       	// 滥用bug
    public final static int REPORT_TYPE_NEGATIVE_GAME	= 3;		// 消极游戏
    public final static int REPORT_TYPE_SPREAD_ADS	    = 4;		// 散播广告
    public final static int REPORT_TYPE_INSULTING		= 5;		// 辱骂他人
    public final static int REPORT_TYPE_VIOLATING_INFO	= 6;		// 个人信息违规
    public final static int REPORT_TYPE_OTHERS          = 99;		// other
}