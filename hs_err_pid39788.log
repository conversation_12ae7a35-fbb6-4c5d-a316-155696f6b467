#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:113), pid=39788, tid=20124
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-bf4f22037ed7a9b1a2b073b2d79130c3-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:34:46 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 1.739269 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000001c77b8cf8a0):  JavaThread "Start Level: Equinox Container: 957c70f3-6117-4e37-9444-c8534a2eb336" daemon [_thread_in_vm, id=20124, stack(0x0000006af2400000,0x0000006af2500000) (1024K)]

Stack: [0x0000006af2400000,0x0000006af2500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0x8a41ee]
V  [jvm.dll+0x670575]
V  [jvm.dll+0x6705da]
V  [jvm.dll+0x672dc2]
V  [jvm.dll+0x672c92]
V  [jvm.dll+0x670f4e]
V  [jvm.dll+0x67b1f7]
V  [jvm.dll+0x21563d]
V  [jvm.dll+0x215bba]
V  [jvm.dll+0x2165e5]
V  [jvm.dll+0x20bbae]
V  [jvm.dll+0x5ae58c]
V  [jvm.dll+0x21d26a]
V  [jvm.dll+0x820d6c]
V  [jvm.dll+0x821d94]
V  [jvm.dll+0x822362]
V  [jvm.dll+0x821fe8]
V  [jvm.dll+0x26cf1b]
V  [jvm.dll+0x26d14a]
V  [jvm.dll+0x5d1891]
V  [jvm.dll+0x5d3ec4]
V  [jvm.dll+0x3d81d2]
V  [jvm.dll+0x3d7c70]
C  0x000001c72d0d6a71

The last pc belongs to getstatic (printed below).
Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
j  com.sun.org.apache.xerces.internal.parsers.XML11Configuration.<init>(Lcom/sun/org/apache/xerces/internal/util/SymbolTable;Lcom/sun/org/apache/xerces/internal/xni/grammars/XMLGrammarPool;Lcom/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager;)V+592 java.xml@21.0.7
j  com.sun.org.apache.xerces.internal.parsers.XIncludeAwareParserConfiguration.<init>(Lcom/sun/org/apache/xerces/internal/util/SymbolTable;Lcom/sun/org/apache/xerces/internal/xni/grammars/XMLGrammarPool;Lcom/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager;)V+4 java.xml@21.0.7
j  com.sun.org.apache.xerces.internal.parsers.XIncludeAwareParserConfiguration.<init>()V+4 java.xml@21.0.7
j  com.sun.org.apache.xerces.internal.parsers.SAXParser.<init>(Lcom/sun/org/apache/xerces/internal/util/SymbolTable;Lcom/sun/org/apache/xerces/internal/xni/grammars/XMLGrammarPool;)V+5 java.xml@21.0.7
j  com.sun.org.apache.xerces.internal.parsers.SAXParser.<init>()V+3 java.xml@21.0.7
j  com.sun.org.apache.xerces.internal.jaxp.SAXParserImpl$JAXPSAXParser.<init>(Lcom/sun/org/apache/xerces/internal/jaxp/SAXParserImpl;Lcom/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager;Lcom/sun/org/apache/xerces/internal/utils/XMLSecurityManager;)V+1 java.xml@21.0.7
j  com.sun.org.apache.xerces.internal.jaxp.SAXParserImpl.<init>(Lcom/sun/org/apache/xerces/internal/jaxp/SAXParserFactoryImpl;Ljava/util/Map;Z)V+46 java.xml@21.0.7
j  com.sun.org.apache.xerces.internal.jaxp.SAXParserFactoryImpl.newSAXParser()Ljavax/xml/parsers/SAXParser;+13 java.xml@21.0.7
j  org.apache.felix.scr.impl.BundleComponentActivator.loadDescriptor(Ljava/net/URL;)V+64
j  org.apache.felix.scr.impl.BundleComponentActivator.initialize(Ljava/util/List;)V+221
j  org.apache.felix.scr.impl.BundleComponentActivator.<init>(Lorg/apache/felix/scr/impl/logger/ScrLogger;Lorg/apache/felix/scr/impl/ComponentRegistry;Lorg/apache/felix/scr/impl/ComponentActorThread;Lorg/osgi/framework/BundleContext;Lorg/apache/felix/scr/impl/manager/ScrConfiguration;Ljava/util/List;Lorg/osgi/framework/ServiceReference;)V+124
j  org.apache.felix.scr.impl.Activator.loadComponents(Lorg/osgi/framework/Bundle;)V+385
j  org.apache.felix.scr.impl.Activator.access$200(Lorg/apache/felix/scr/impl/Activator;Lorg/osgi/framework/Bundle;)V+2
j  org.apache.felix.scr.impl.Activator$ScrExtension.start()V+72
j  org.apache.felix.scr.impl.AbstractExtender.createExtension(Lorg/osgi/framework/Bundle;)V+71
j  org.apache.felix.scr.impl.AbstractExtender.modifiedBundle(Lorg/osgi/framework/Bundle;Lorg/osgi/framework/BundleEvent;Lorg/osgi/framework/Bundle;)V+115
j  org.apache.felix.scr.impl.AbstractExtender.addingBundle(Lorg/osgi/framework/Bundle;Lorg/osgi/framework/BundleEvent;)Lorg/osgi/framework/Bundle;+4
j  org.apache.felix.scr.impl.AbstractExtender.addingBundle(Lorg/osgi/framework/Bundle;Lorg/osgi/framework/BundleEvent;)Ljava/lang/Object;+3
j  org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(Lorg/osgi/framework/Bundle;Lorg/osgi/framework/BundleEvent;)Ljava/lang/Object;+9
j  org.osgi.util.tracker.BundleTracker$Tracked.customizerAdding(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;+9
j  org.osgi.util.tracker.AbstractTracked.trackAdding(Ljava/lang/Object;Ljava/lang/Object;)V+8
j  org.osgi.util.tracker.AbstractTracked.track(Ljava/lang/Object;Ljava/lang/Object;)V+83
j  org.osgi.util.tracker.BundleTracker$Tracked.bundleChanged(Lorg/osgi/framework/BundleEvent;)V+35
j  org.eclipse.osgi.internal.framework.BundleContextImpl.dispatchEvent(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V+166
j  org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(Ljava/util/Set;Lorg/eclipse/osgi/framework/eventmgr/EventDispatcher;ILjava/lang/Object;)V+48
j  org.eclipse.osgi.framework.eventmgr.ListenerQueue.dispatchEventSynchronous(ILjava/lang/Object;)V+67
j  org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEventPrivileged(Lorg/osgi/framework/BundleEvent;)V+547
j  org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(Lorg/osgi/framework/BundleEvent;)V+8
j  org.eclipse.osgi.internal.framework.EquinoxEventPublisher.publishBundleEvent(ILorg/osgi/framework/Bundle;Lorg/osgi/framework/Bundle;)V+15
j  org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor.publishModuleEvent(Lorg/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent;Lorg/eclipse/osgi/container/Module;Lorg/eclipse/osgi/container/Module;)V+29
j  org.eclipse.osgi.container.Module.publishEvent(Lorg/eclipse/osgi/container/ModuleContainerAdaptor$ModuleEvent;)V+13
j  org.eclipse.osgi.container.Module.start([Lorg/eclipse/osgi/container/Module$StartOptions;)V+588
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel$2.run()V+83
j  org.eclipse.osgi.internal.framework.EquinoxContainerAdaptor$1$1.execute(Ljava/lang/Runnable;)V+1
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ILjava/util/List;Z)V+193
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.incStartLevel(ILjava/util/List;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V+4
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.doContainerStartLevel(Lorg/eclipse/osgi/container/Module;I[Lorg/osgi/framework/FrameworkListener;)V+358
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(Lorg/eclipse/osgi/container/Module;[Lorg/osgi/framework/FrameworkListener;ILjava/lang/Integer;)V+32
j  org.eclipse.osgi.container.ModuleContainer$ContainerStartLevel.dispatchEvent(Ljava/lang/Object;Ljava/lang/Object;ILjava/lang/Object;)V+15
j  org.eclipse.osgi.framework.eventmgr.EventManager.dispatchEvent(Ljava/util/Set;Lorg/eclipse/osgi/framework/eventmgr/EventDispatcher;ILjava/lang/Object;)V+48
j  org.eclipse.osgi.framework.eventmgr.EventManager$EventThread.run()V+26
v  ~StubRoutines::call_stub 0x000001c72d0c100d
getstatic  178 getstatic  [0x000001c72d0d69c0, 0x000001c72d0d6bb8]  504 bytes
[MachCode]
  0x000001c72d0d69c0: 4883 ec08 | c5fa 1104 | 24eb 1f48 | 83ec 10c5 | fb11 0424 | eb14 4883 | ec10 4889 | 0424 48c7 
  0x000001c72d0d69e0: 4424 0800 | 0000 00eb | 0150 410f | b755 0148 | 8b4d d0c1 | e202 8b5c | d138 c1eb | 1081 e3ff 
  0x000001c72d0d6a00: 0000 0081 | fbb2 0000 | 000f 84b4 | 0000 00bb | b200 0000 | e805 0000 | 00e9 9900 | 0000 488b 
  0x000001c72d0d6a20: d348 8d44 | 2408 4c89 | 6dc0 498b | cfc5 f877 | 4989 afa8 | 0300 0049 | 8987 9803 | 0000 4883 
  0x000001c72d0d6a40: ec20 40f6 | c40f 0f84 | 1900 0000 | 4883 ec08 | 48b8 107c | b8ee ff7f | 0000 ffd0 | 4883 c408 
  0x000001c72d0d6a60: e90c 0000 | 0048 b810 | 7cb8 eeff | 7f00 00ff | d048 83c4 | 2049 c787 | 9803 0000 | 0000 0000 
  0x000001c72d0d6a80: 49c7 87a8 | 0300 0000 | 0000 0049 | c787 a003 | 0000 0000 | 0000 c5f8 | 7749 837f | 0800 0f84 
  0x000001c72d0d6aa0: 0500 0000 | e957 a4fe | ff4c 8b6d | c04c 8b75 | c84e 8d74 | f500 c341 | 0fb7 5501 | 488b 4dd0 
  0x000001c72d0d6ac0: c1e2 0248 | 8b5c d148 | 8b44 d150 | 4c8b 4cd1 | 404d 8b49 | 704d 8b09 | c1e8 1c83 | e00f 0f85 
  0x000001c72d0d6ae0: 0b00 0000 | 410f be04 | 1950 e9b1 | 0000 0083 | f801 0f85 | 0b00 0000 | 410f b604 | 1950 e99d 
  0x000001c72d0d6b00: 0000 0083 | f808 0f85 | 0a00 0000 | 418b 0419 | 50e9 8a00 | 0000 83f8 | 040f 850a | 0000 0041 
  0x000001c72d0d6b20: 8b04 1950 | e977 0000 | 0083 f802 | 0f85 0b00 | 0000 410f | b704 1950 | e963 0000 | 0083 f803 
  0x000001c72d0d6b40: 0f85 0b00 | 0000 410f | bf04 1950 | e94f 0000 | 0083 f805 | 0f85 1a00 | 0000 498b | 0419 4883 
  0x000001c72d0d6b60: ec10 4889 | 0424 48c7 | 4424 0800 | 0000 00e9 | 2c00 0000 | 83f8 060f | 8514 0000 | 00c4 c17a 
  0x000001c72d0d6b80: 1004 1948 | 83ec 08c5 | fa11 0424 | e90f 0000 | 00c4 c17b | 1004 1948 | 83ec 10c5 | fb11 0424 
  0x000001c72d0d6ba0: 410f b65d | 0349 83c5 | 0349 bab0 | 2847 efff | 7f00 0041 | ff24 da90 
[/MachCode]

---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001c77d1c2050, length=19, elements={
0x000001c722966250, 0x000001c739a2cf90, 0x000001c739a2e7f0, 0x000001c77b8c6a10,
0x000001c77b8c9ec0, 0x000001c77b8cf210, 0x000001c77b8cd140, 0x000001c77b8d4d40,
0x000001c77b8d7880, 0x000001c77b8ce4f0, 0x000001c77b8cd7d0, 0x000001c77c8c1fe0,
0x000001c77c8540d0, 0x000001c77b8ccab0, 0x000001c77b8cde60, 0x000001c77b8ceb80,
0x000001c77b8cf8a0, 0x000001c77b8cc420, 0x000001c77d865f90
}

Java Threads: ( => current thread )
  0x000001c722966250 JavaThread "main"                              [_thread_blocked, id=35568, stack(0x0000006af0a00000,0x0000006af0b00000) (1024K)]
  0x000001c739a2cf90 JavaThread "Reference Handler"          daemon [_thread_blocked, id=35776, stack(0x0000006af0e00000,0x0000006af0f00000) (1024K)]
  0x000001c739a2e7f0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=36924, stack(0x0000006af0f00000,0x0000006af1000000) (1024K)]
  0x000001c77b8c6a10 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=22292, stack(0x0000006af1000000,0x0000006af1100000) (1024K)]
  0x000001c77b8c9ec0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=9584, stack(0x0000006af1100000,0x0000006af1200000) (1024K)]
  0x000001c77b8cf210 JavaThread "Service Thread"             daemon [_thread_blocked, id=29836, stack(0x0000006af1200000,0x0000006af1300000) (1024K)]
  0x000001c77b8cd140 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=46912, stack(0x0000006af1300000,0x0000006af1400000) (1024K)]
  0x000001c77b8d4d40 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=42036, stack(0x0000006af1400000,0x0000006af1500000) (1024K)]
  0x000001c77b8d7880 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=34500, stack(0x0000006af1500000,0x0000006af1600000) (1024K)]
  0x000001c77b8ce4f0 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=16652, stack(0x0000006af1600000,0x0000006af1700000) (1024K)]
  0x000001c77b8cd7d0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=45780, stack(0x0000006af1700000,0x0000006af1800000) (1024K)]
  0x000001c77c8c1fe0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=14728, stack(0x0000006af1800000,0x0000006af1900000) (1024K)]
  0x000001c77c8540d0 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=36160, stack(0x0000006af1900000,0x0000006af1a00000) (1024K)]
  0x000001c77b8ccab0 JavaThread "Active Thread: Equinox Container: 957c70f3-6117-4e37-9444-c8534a2eb336"        [_thread_blocked, id=17504, stack(0x0000006af2000000,0x0000006af2100000) (1024K)]
  0x000001c77b8cde60 JavaThread "Refresh Thread: Equinox Container: 957c70f3-6117-4e37-9444-c8534a2eb336" daemon [_thread_blocked, id=45948, stack(0x0000006af2200000,0x0000006af2300000) (1024K)]
  0x000001c77b8ceb80 JavaThread "Framework Event Dispatcher: Equinox Container: 957c70f3-6117-4e37-9444-c8534a2eb336" daemon [_thread_blocked, id=38048, stack(0x0000006af2300000,0x0000006af2400000) (1024K)]
=>0x000001c77b8cf8a0 JavaThread "Start Level: Equinox Container: 957c70f3-6117-4e37-9444-c8534a2eb336" daemon [_thread_in_vm, id=20124, stack(0x0000006af2400000,0x0000006af2500000) (1024K)]
  0x000001c77b8cc420 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=34204, stack(0x0000006af2500000,0x0000006af2600000) (1024K)]
  0x000001c77d865f90 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=18056, stack(0x0000006af2600000,0x0000006af2700000) (1024K)]
Total: 19

Other Threads:
  0x000001c77b8c29e0 VMThread "VM Thread"                           [id=46140, stack(0x0000006af0d00000,0x0000006af0e00000) (1024K)]
  0x000001c73994cc70 WatcherThread "VM Periodic Task Thread"        [id=46248, stack(0x0000006af0c00000,0x0000006af0d00000) (1024K)]
  0x000001c7229877c0 WorkerThread "GC Thread#0"                     [id=21844, stack(0x0000006af0b00000,0x0000006af0c00000) (1024K)]
  0x000001c77d0c34f0 WorkerThread "GC Thread#1"                     [id=26612, stack(0x0000006af1a00000,0x0000006af1b00000) (1024K)]
  0x000001c77cf76e90 WorkerThread "GC Thread#2"                     [id=42672, stack(0x0000006af1b00000,0x0000006af1c00000) (1024K)]
  0x000001c77cf77230 WorkerThread "GC Thread#3"                     [id=38644, stack(0x0000006af1c00000,0x0000006af1d00000) (1024K)]
  0x000001c77cf775d0 WorkerThread "GC Thread#4"                     [id=12068, stack(0x0000006af1d00000,0x0000006af1e00000) (1024K)]
  0x000001c77cf77970 WorkerThread "GC Thread#5"                     [id=45940, stack(0x0000006af1e00000,0x0000006af1f00000) (1024K)]
  0x000001c77cf77d10 WorkerThread "GC Thread#6"                     [id=22184, stack(0x0000006af1f00000,0x0000006af2000000) (1024K)]
  0x000001c77cf82250 WorkerThread "GC Thread#7"                     [id=4760, stack(0x0000006af2100000,0x0000006af2200000) (1024K)]
Total: 10

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007fffef45c308] Metaspace_lock - owner thread: 0x000001c77b8cf8a0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001c73a000000-0x000001c73aba0000-0x000001c73aba0000), size 12189696, SharedBaseAddress: 0x000001c73a000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001c73b000000-0x000001c77b000000, reserved size: 1073741824
Narrow klass base: 0x000001c73a000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 29696K, used 19748K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 61% used [0x00000000d5580000,0x00000000d64d0488,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d7278ea8,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 4942K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x00000000804d3b90,0x0000000084300000)
 Metaspace       used 10232K, committed 10624K, reserved 1114112K
  class space    used 1058K, committed 1280K, reserved 1048576K

Card table byte_map: [0x000001c734d70000,0x000001c735180000] _byte_map_base: 0x000001c734970000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x000001c735430000, 0x000001c737430000)
 End Bits:   [0x000001c737430000, 0x000001c739430000)

Polling page: 0x000001c722800000

Metaspace:

Usage:
  Non-class:      8.96 MB used.
      Class:      1.03 MB used.
       Both:      9.99 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       9.12 MB ( 14%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.25 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      10.38 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  6.34 MB
       Class:  14.72 MB
        Both:  21.06 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 262.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 166.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 541.
num_chunk_merges: 0.
num_chunk_splits: 345.
num_chunks_enlarged: 233.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=1159Kb max_used=1159Kb free=118840Kb
 bounds [0x000001c72d660000, 0x000001c72d8d0000, 0x000001c734b90000]
CodeHeap 'profiled nmethods': size=120000Kb used=4610Kb max_used=4610Kb free=115389Kb
 bounds [0x000001c725b90000, 0x000001c726020000, 0x000001c72d0c0000]
CodeHeap 'non-nmethods': size=5760Kb used=1236Kb max_used=1281Kb free=4524Kb
 bounds [0x000001c72d0c0000, 0x000001c72d330000, 0x000001c72d660000]
 total_blobs=2920 nmethods=2422 adapters=404
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.717 Thread 0x000001c77b8d7880 nmethod 2416 0x000001c725ff0c10 code [0x000001c725ff0de0, 0x000001c725ff1018]
Event: 1.717 Thread 0x000001c77b8d7880 2417       3       org.eclipse.osgi.container.ModuleWiring::getProvidedWires (13 bytes)
Event: 1.718 Thread 0x000001c77b8d7880 nmethod 2417 0x000001c725ff1190 code [0x000001c725ff13e0, 0x000001c725ff1d98]
Event: 1.718 Thread 0x000001c77b8d7880 2420       3       java.util.concurrent.locks.AbstractQueuedSynchronizer::releaseShared (19 bytes)
Event: 1.718 Thread 0x000001c77b8d7880 nmethod 2420 0x000001c725ff2090 code [0x000001c725ff22a0, 0x000001c725ff28b8]
Event: 1.718 Thread 0x000001c77b8d7880 2418       3       org.eclipse.osgi.internal.container.InternalUtils::asCopy (32 bytes)
Event: 1.718 Thread 0x000001c77b8d7880 nmethod 2418 0x000001c725ff2a90 code [0x000001c725ff2c80, 0x000001c725ff3230]
Event: 1.718 Thread 0x000001c77b8d7880 2419       3       org.eclipse.osgi.internal.container.InternalUtils$CopyOnFirstWriteList::<init> (18 bytes)
Event: 1.718 Thread 0x000001c77b8d7880 nmethod 2419 0x000001c725ff3410 code [0x000001c725ff35c0, 0x000001c725ff3868]
Event: 1.718 Thread 0x000001c77b8d7880 2422       3       org.apache.aries.spifly.BaseActivator::getAllHeaders (189 bytes)
Event: 1.719 Thread 0x000001c77c8540d0 nmethod 2401% 0x000001c72d780510 code [0x000001c72d780700, 0x000001c72d781530]
Event: 1.720 Thread 0x000001c77b8d7880 nmethod 2422 0x000001c725ff3990 code [0x000001c725ff3e20, 0x000001c725ff6488]
Event: 1.720 Thread 0x000001c77b8d7880 2424   !   3       org.eclipse.osgi.internal.framework.BundleContextImpl::dispatchEvent (577 bytes)
Event: 1.723 Thread 0x000001c77b8d7880 nmethod 2424 0x000001c725ff7310 code [0x000001c725ff7d80, 0x000001c725ffd868]
Event: 1.723 Thread 0x000001c77b8d7880 2423   !   3       org.eclipse.osgi.internal.framework.EquinoxBundle::adapt0 (939 bytes)
Event: 1.729 Thread 0x000001c77b8d7880 nmethod 2423 0x000001c726000c90 code [0x000001c726001ba0, 0x000001c72600b1f8]
Event: 1.729 Thread 0x000001c77b8d7880 2421       3       org.eclipse.osgi.container.Module::unlockStateChange (63 bytes)
Event: 1.729 Thread 0x000001c77b8d7880 nmethod 2421 0x000001c72600ef10 code [0x000001c72600f1e0, 0x000001c72600ff38]
Event: 1.729 Thread 0x000001c77b8d7880 2425       3       java.lang.Thread::getContextClassLoader (30 bytes)
Event: 1.729 Thread 0x000001c77b8d7880 nmethod 2425 0x000001c726010490 code [0x000001c726010640, 0x000001c726010898]

GC Heap History (6 events):
Event: 0.607 GC heap before
{Heap before GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 25600K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 0K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080000000,0x0000000084300000)
 Metaspace       used 4263K, committed 4480K, reserved 1114112K
  class space    used 458K, committed 576K, reserved 1048576K
}
Event: 0.612 GC heap after
{Heap after GC invocations=1 (full 0):
 PSYoungGen      total 29696K, used 3455K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71dfc78,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 4263K, committed 4480K, reserved 1114112K
  class space    used 458K, committed 576K, reserved 1048576K
}
Event: 1.142 GC heap before
{Heap before GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 29055K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 84% used [0x00000000d6e80000,0x00000000d71dfc78,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 16K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 0% used [0x0000000080000000,0x0000000080004000,0x0000000084300000)
 Metaspace       used 7982K, committed 8320K, reserved 1114112K
  class space    used 811K, committed 960K, reserved 1048576K
}
Event: 1.146 GC heap after
{Heap after GC invocations=2 (full 0):
 PSYoungGen      total 29696K, used 4077K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767b5f0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 1851K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 2% used [0x0000000080000000,0x00000000801cef38,0x0000000084300000)
 Metaspace       used 7982K, committed 8320K, reserved 1114112K
  class space    used 811K, committed 960K, reserved 1048576K
}
Event: 1.426 GC heap before
{Heap before GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 29677K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 100% used [0x00000000d5580000,0x00000000d6e80000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d7280000,0x00000000d767b5f0,0x00000000d7680000)
  to   space 4096K, 0% used [0x00000000d6e80000,0x00000000d6e80000,0x00000000d7280000)
 ParOldGen       total 68608K, used 1851K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 2% used [0x0000000080000000,0x00000000801cef38,0x0000000084300000)
 Metaspace       used 9312K, committed 9664K, reserved 1114112K
  class space    used 970K, committed 1152K, reserved 1048576K
}
Event: 1.430 GC heap after
{Heap after GC invocations=3 (full 0):
 PSYoungGen      total 29696K, used 4067K [0x00000000d5580000, 0x00000000d7680000, 0x0000000100000000)
  eden space 25600K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6e80000)
  from space 4096K, 99% used [0x00000000d6e80000,0x00000000d7278ea8,0x00000000d7280000)
  to   space 4096K, 0% used [0x00000000d7280000,0x00000000d7280000,0x00000000d7680000)
 ParOldGen       total 68608K, used 4942K [0x0000000080000000, 0x0000000084300000, 0x00000000d5580000)
  object space 68608K, 7% used [0x0000000080000000,0x00000000804d3b90,0x0000000084300000)
 Metaspace       used 9312K, committed 9664K, reserved 1114112K
  class space    used 970K, committed 1152K, reserved 1048576K
}

Dll operation events (9 events):
Event: 0.011 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.032 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.084 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.088 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.090 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.093 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.107 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.169 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.161 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll

Deoptimization events (20 events):
Event: 1.487 Thread 0x000001c77b8cf8a0 DEOPT PACKING pc=0x000001c725f83149 sp=0x0000006af24fce40
Event: 1.487 Thread 0x000001c77b8cf8a0 DEOPT UNPACKING pc=0x000001c72d114242 sp=0x0000006af24fc2b0 mode 0
Event: 1.488 Thread 0x000001c77b8cf8a0 DEOPT PACKING pc=0x000001c725f83149 sp=0x0000006af24fce40
Event: 1.488 Thread 0x000001c77b8cf8a0 DEOPT UNPACKING pc=0x000001c72d114242 sp=0x0000006af24fc2b0 mode 0
Event: 1.495 Thread 0x000001c77b8cf8a0 DEOPT PACKING pc=0x000001c725f83149 sp=0x0000006af24fcdd0
Event: 1.495 Thread 0x000001c77b8cf8a0 DEOPT UNPACKING pc=0x000001c72d114242 sp=0x0000006af24fc240 mode 0
Event: 1.498 Thread 0x000001c77b8cf8a0 DEOPT PACKING pc=0x000001c725f83149 sp=0x0000006af24fcdd0
Event: 1.498 Thread 0x000001c77b8cf8a0 DEOPT UNPACKING pc=0x000001c72d114242 sp=0x0000006af24fc240 mode 0
Event: 1.505 Thread 0x000001c77b8cf8a0 DEOPT PACKING pc=0x000001c725f83149 sp=0x0000006af24fcdd0
Event: 1.505 Thread 0x000001c77b8cf8a0 DEOPT UNPACKING pc=0x000001c72d114242 sp=0x0000006af24fc240 mode 0
Event: 1.517 Thread 0x000001c77b8cf8a0 DEOPT PACKING pc=0x000001c725f83149 sp=0x0000006af24fce40
Event: 1.517 Thread 0x000001c77b8cf8a0 DEOPT UNPACKING pc=0x000001c72d114242 sp=0x0000006af24fc2b0 mode 0
Event: 1.520 Thread 0x000001c77b8cf8a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001c72d6dc5e8 relative=0x00000000000001a8
Event: 1.520 Thread 0x000001c77b8cf8a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001c72d6dc5e8 method=java.lang.String.startsWith(Ljava/lang/String;I)Z @ 50 c2
Event: 1.520 Thread 0x000001c77b8cf8a0 DEOPT PACKING pc=0x000001c72d6dc5e8 sp=0x0000006af24fde90
Event: 1.520 Thread 0x000001c77b8cf8a0 DEOPT UNPACKING pc=0x000001c72d113aa2 sp=0x0000006af24fddc0 mode 2
Event: 1.675 Thread 0x000001c77b8cf8a0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001c72d774c08 relative=0x00000000000006e8
Event: 1.675 Thread 0x000001c77b8cf8a0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001c72d774c08 method=org.eclipse.osgi.framework.util.CaseInsensitiveDictionaryMap.findCommonKeyIndex(Ljava/lang/String;)Lorg/eclipse/osgi/framework/util/CaseInsensitiveDictionaryMap$CaseInsensit
Event: 1.675 Thread 0x000001c77b8cf8a0 DEOPT PACKING pc=0x000001c72d774c08 sp=0x0000006af24fdf10
Event: 1.675 Thread 0x000001c77b8cf8a0 DEOPT UNPACKING pc=0x000001c72d113aa2 sp=0x0000006af24fdea0 mode 2

Classes loaded (20 events):
Event: 1.731 Loading class com/sun/org/apache/xerces/internal/parsers/XMLParser done
Event: 1.731 Loading class com/sun/org/apache/xerces/internal/parsers/AbstractXMLDocumentParser done
Event: 1.731 Loading class com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser done
Event: 1.731 Loading class com/sun/org/apache/xerces/internal/parsers/SAXParser done
Event: 1.731 Loading class com/sun/org/apache/xerces/internal/jaxp/SAXParserImpl$JAXPSAXParser done
Event: 1.731 Loading class com/sun/org/apache/xerces/internal/parsers/XIncludeAwareParserConfiguration
Event: 1.731 Loading class com/sun/org/apache/xerces/internal/parsers/XML11Configuration
Event: 1.732 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLPullParserConfiguration
Event: 1.732 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLParserConfiguration
Event: 1.732 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager
Event: 1.732 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLComponentManager done
Event: 1.732 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLParserConfiguration done
Event: 1.732 Loading class com/sun/org/apache/xerces/internal/xni/parser/XMLPullParserConfiguration done
Event: 1.732 Loading class com/sun/org/apache/xerces/internal/parsers/XML11Configurable
Event: 1.732 Loading class com/sun/org/apache/xerces/internal/parsers/XML11Configurable done
Event: 1.733 Loading class com/sun/org/apache/xerces/internal/util/ParserConfigurationSettings
Event: 1.733 Loading class com/sun/org/apache/xerces/internal/util/ParserConfigurationSettings done
Event: 1.733 Loading class com/sun/org/apache/xerces/internal/parsers/XML11Configuration done
Event: 1.733 Loading class com/sun/org/apache/xerces/internal/parsers/XIncludeAwareParserConfiguration done
Event: 1.733 Loading class jdk/xml/internal/JdkXmlUtils

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.658 Thread 0x000001c722966250 Implicit null exception at 0x000001c72d6cfa49 to 0x000001c72d6d0568
Event: 0.658 Thread 0x000001c722966250 Implicit null exception at 0x000001c72d6c2320 to 0x000001c72d6c2538
Event: 0.659 Thread 0x000001c722966250 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d579f128}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d579f128) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.660 Thread 0x000001c722966250 Implicit null exception at 0x000001c72d6ca220 to 0x000001c72d6ca43c
Event: 0.660 Thread 0x000001c722966250 Implicit null exception at 0x000001c72d6bfe40 to 0x000001c72d6bfec5
Event: 0.717 Thread 0x000001c722966250 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5a79648}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5a79648) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.926 Thread 0x000001c722966250 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d646c680}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000000d646c680) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.958 Thread 0x000001c722966250 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65e0388}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d65e0388) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.959 Thread 0x000001c722966250 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d65e8810}: Found class java.lang.Object, but interface was expected> (0x00000000d65e8810) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 0.960 Thread 0x000001c722966250 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65eee00}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000d65eee00) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.960 Thread 0x000001c722966250 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d65f7fd8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000d65f7fd8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 0.991 Thread 0x000001c722966250 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d6841f78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object)'> (0x00000000d6841f78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.017 Thread 0x000001c722966250 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d6a4f5a0}: sun/net/www/protocol/plurl/Handler> (0x00000000d6a4f5a0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 1.159 Thread 0x000001c722966250 Exception <a 'java/lang/UnsatisfiedLinkError'{0x00000000d55e9a58}: 'void org.eclipse.equinox.launcher.JNIBridge._update_splash()'> (0x00000000d55e9a58) 
thrown [s\src\hotspot\share\prims\nativeLookup.cpp, line 415]
Event: 1.243 Thread 0x000001c77b8cf8a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5c39460}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5c39460) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.244 Thread 0x000001c77b8cf8a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5c3d2d0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000d5c3d2d0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 1.255 Thread 0x000001c77b8cf8a0 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000d5cddd58}: jakarta/servlet/ServletContainerInitializer> (0x00000000d5cddd58) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 301]
Event: 1.271 Thread 0x000001c77b8cf8a0 Exception <a 'java/lang/ClassNotFoundException'{0x00000000d5e4e930}: sun/util/logging/resources/spi/loggingProvider> (0x00000000d5e4e930) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 312]
Event: 1.713 Thread 0x000001c77b8cf8a0 Exception <a 'java/lang/NullPointerException'{0x00000000d63422e0}> (0x00000000d63422e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1372]
Event: 1.714 Thread 0x000001c77b8cf8a0 Exception <a 'java/lang/NullPointerException'{0x00000000d63425c0}> (0x00000000d63425c0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1372]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 0.983 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.983 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.007 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.007 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.013 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.013 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.064 Executing VM operation: ICBufferFull
Event: 1.064 Executing VM operation: ICBufferFull done
Event: 1.142 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1.146 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 1.264 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.264 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.274 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.274 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.280 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.280 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.298 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.298 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.426 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 1.431 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (19 events):
Event: 0.023 Thread 0x000001c722966250 Thread added: 0x000001c722966250
Event: 0.041 Thread 0x000001c722966250 Thread added: 0x000001c739a2cf90
Event: 0.041 Thread 0x000001c722966250 Thread added: 0x000001c739a2e7f0
Event: 0.041 Thread 0x000001c722966250 Thread added: 0x000001c77b8c6a10
Event: 0.042 Thread 0x000001c722966250 Thread added: 0x000001c77b8c9ec0
Event: 0.042 Thread 0x000001c722966250 Thread added: 0x000001c77b8cf210
Event: 0.042 Thread 0x000001c722966250 Thread added: 0x000001c77b8cd140
Event: 0.042 Thread 0x000001c722966250 Thread added: 0x000001c77b8d4d40
Event: 0.042 Thread 0x000001c722966250 Thread added: 0x000001c77b8d7880
Event: 0.060 Thread 0x000001c722966250 Thread added: 0x000001c77b8ce4f0
Event: 0.269 Thread 0x000001c722966250 Thread added: 0x000001c77b8cd7d0
Event: 0.353 Thread 0x000001c77b8d7880 Thread added: 0x000001c77c8c1fe0
Event: 0.356 Thread 0x000001c77c8c1fe0 Thread added: 0x000001c77c8540d0
Event: 0.967 Thread 0x000001c722966250 Thread added: 0x000001c77b8ccab0
Event: 1.158 Thread 0x000001c722966250 Thread added: 0x000001c77b8cde60
Event: 1.185 Thread 0x000001c77b8cde60 Thread added: 0x000001c77b8ceb80
Event: 1.188 Thread 0x000001c722966250 Thread added: 0x000001c77b8cf8a0
Event: 1.462 Thread 0x000001c77b8cf8a0 Thread added: 0x000001c77b8cc420
Event: 1.676 Thread 0x000001c77b8cf8a0 Thread added: 0x000001c77d865f90


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff83ffb0000 - 0x00007ff83fff5000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-bf4f22037ed7a9b1a2b073b2d79130c3-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:46 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (1245M free)
TotalPageFile size 61318M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 117M, peak: 117M
current process commit charge ("private bytes"): 256M, peak: 257M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
