//
//  AppDelegate.m
//  Tp2DemoC
//
//  Created by <PERSON> on 16/4/20.
//  Copyright (c) 2016年 TX. All rights reserved.
//

#import "AppDelegate.h"
#import "ViewController.h"
#include "tersafe2/tp2_sdk.h"
#include <stdio.h>

class MyTssInfoReceiver:public TssInfoReceiver{
    public:
   void onReceive(int tssInfoType,const char* info){

        char plain[256];
        memset(plain,0x00,sizeof(plain));

        if (tssInfoType == TSS_INFO_TYPE_DETECT_RESULT){
            // 只关注TSS_INFO_TYPE_DETECT_RESULT
            int ret =tp2_dec_tss_info(info,plain,sizeof(plain)); if (ret == -1) return;
            NSLog(@"MTP: [C++ Cheat Info]:%d|%s", tssInfoType,plain);
        }else if (tssInfoType == TSS_INFO_TYPE_HEARTBEAT){
            // // 处理心跳，如果不关心，可以忽略
            int ret =tp2_dec_tss_info(info,plain,sizeof(plain)); if (ret == -1) return;
            NSLog(@"MTP: [C++ Cheat Info]:%d|%s", tssInfoType,plain);
        }
   }
};

@interface AppDelegate ()

@end

@implementation AppDelegate

MyTssInfoReceiver g_tss_info_receiver;

- (void) game_start {
    //游戏启动的第一时间调用
    int gameId = 9000;
    const char *appKey = "d5ab8dc7ef67ca92e41d730982c5c602";
    tp2_regist_tss_info_receiver(&g_tss_info_receiver);
    tp2_sdk_init_ex(gameId, appKey);
    
    //用户登录时调用
    int accountType = ENTRY_ID_QZONE;        /*帐号类型*/
    int worldId = 101;                /*大区id*/
    const char *userId = "B73B36366565F9E02C7525516DCF31C2";    /*与平台相关的用户标识*/
    const char *roleId = "paladin";            /*角色id*/
    tp2_setuserinfo(accountType, worldId, userId, roleId);
    
    // 7.3版本之后的版本支持上报举报信息至ACE控制台
    const char* reported_account_id = "8B57B75C79A3E34E718C";   // 被举报人的账号 openid
    int reported_account_type = ENTRY_ID_QZONE;                 // 被举报人的账号类型
    int report_type = REPORT_TYPE_CHEAT;                                         // 举报的类型，辱骂、作弊等
    const char* report_scene = "5v5_mode";                              // 举报发生的场景, 游戏可自定义，会直接展示在控制台
    const char* report_reason = "肯定开透视了，我蹲草里不动都能看到我";      // 举报的详情信息，游戏可自定义
    // 举报信息最长为1024Byte，否则在上报时会截断
    char complaintMsg[1024] = {0};
    snprintf(complaintMsg, sizeof(complaintMsg), 
        "ReportComplaint:reportedId=%s;reportedType=%d;type=%d;scene=%s;reason=%s",
        reported_account_id,    // 被举报人的账号 openid
        reported_account_type,  // 被举报人的账号类型
        report_type,            // 举报的类型
        report_scene,           // 举报发生的场景
        report_reason           // 举报的详情信息
    );
    TssSdkAntiDataInfo *data = tp2_sdk_ioctl(TssSDKCmd_CommQuery, complaintMsg);
    if (data != NULL)
    {
        tp2_free_anti_data(data);
    }
}

// 游戏切换到前台
- (void) applicationWillEnterForeground:(UIApplication *) applicaton {
    tp2_setgamestatus(TP2_GAME_STATUS_FRONTEND);
}

// 游戏切换到后台
- (void) applicationDidEnterBackground:(UIApplication *) application {
    tp2_setgamestatus(TP2_GAME_STATUS_BACKEND);
}

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    
    UIWindow *window = [[UIWindow alloc] initWithFrame:[[UIScreen mainScreen] bounds]];
    ViewController *vc = [[ViewController alloc] init];
    [window addSubview: vc.view];
    [window makeKeyAndVisible];
    
    self.window = window;
    self.window.rootViewController = vc;
    
    /*
     * 注: 安全SDK默认开启反调试功能，使用XCode调试程序时请关闭SDK或用宏隔离
     */
     
    [self game_start];
    
    [vc release];
    [window release];
    
    return YES;
}

- (void)applicationWillResignActive:(UIApplication *)application {
    // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
    // Use this method to pause ongoing tasks, disable timers, and throttle down OpenGL ES frame rates. Games should use this method to pause the game.
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    // Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
}

- (void)applicationWillTerminate:(UIApplication *)application {
    // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    // Saves changes in the application's managed object context before the application terminates.
    [self saveContext];
}

#pragma mark - Core Data stack

@synthesize managedObjectContext = _managedObjectContext;
@synthesize managedObjectModel = _managedObjectModel;
@synthesize persistentStoreCoordinator = _persistentStoreCoordinator;

- (NSURL *)applicationDocumentsDirectory {
    // The directory the application uses to store the Core Data store file. This code uses a directory named "com.tencent..Tp2DemoC" in the application's documents directory.
    return [[[NSFileManager defaultManager] URLsForDirectory:NSDocumentDirectory inDomains:NSUserDomainMask] lastObject];
}

- (NSManagedObjectModel *)managedObjectModel {
    // The managed object model for the application. It is a fatal error for the application not to be able to find and load its model.
    if (_managedObjectModel != nil) {
        return _managedObjectModel;
    }
    NSURL *modelURL = [[NSBundle mainBundle] URLForResource:@"Tp2DemoC" withExtension:@"momd"];
    _managedObjectModel = [[NSManagedObjectModel alloc] initWithContentsOfURL:modelURL];
    return _managedObjectModel;
}

- (NSPersistentStoreCoordinator *)persistentStoreCoordinator {
    // The persistent store coordinator for the application. This implementation creates and return a coordinator, having added the store for the application to it.
    if (_persistentStoreCoordinator != nil) {
        return _persistentStoreCoordinator;
    }
    
    // Create the coordinator and store
    
    _persistentStoreCoordinator = [[NSPersistentStoreCoordinator alloc] initWithManagedObjectModel:[self managedObjectModel]];
    NSURL *storeURL = [[self applicationDocumentsDirectory] URLByAppendingPathComponent:@"Tp2DemoC.sqlite"];
    NSError *error = nil;
    NSString *failureReason = @"There was an error creating or loading the application's saved data.";
    if (![_persistentStoreCoordinator addPersistentStoreWithType:NSSQLiteStoreType configuration:nil URL:storeURL options:nil error:&error]) {
        // Report any error we got.
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        dict[NSLocalizedDescriptionKey] = @"Failed to initialize the application's saved data";
        dict[NSLocalizedFailureReasonErrorKey] = failureReason;
        dict[NSUnderlyingErrorKey] = error;
        error = [NSError errorWithDomain:@"YOUR_ERROR_DOMAIN" code:9999 userInfo:dict];
        // Replace this with code to handle the error appropriately.
        // abort() causes the application to generate a crash log and terminate. You should not use this function in a shipping application, although it may be useful during development.
        NSLog(@"Unresolved error %@, %@", error, [error userInfo]);
        abort();
    }
    
    return _persistentStoreCoordinator;
}


- (NSManagedObjectContext *)managedObjectContext {
    // Returns the managed object context for the application (which is already bound to the persistent store coordinator for the application.)
    if (_managedObjectContext != nil) {
        return _managedObjectContext;
    }
    
    NSPersistentStoreCoordinator *coordinator = [self persistentStoreCoordinator];
    if (!coordinator) {
        return nil;
    }
    _managedObjectContext = [[NSManagedObjectContext alloc] init];
    [_managedObjectContext setPersistentStoreCoordinator:coordinator];
    return _managedObjectContext;
}

#pragma mark - Core Data Saving support

- (void)saveContext {
    NSManagedObjectContext *managedObjectContext = self.managedObjectContext;
    if (managedObjectContext != nil) {
        NSError *error = nil;
        if ([managedObjectContext hasChanges] && ![managedObjectContext save:&error]) {
            // Replace this implementation with code to handle the error appropriately.
            // abort() causes the application to generate a crash log and terminate. You should not use this function in a shipping application, although it may be useful during development.
            NSLog(@"Unresolved error %@, %@", error, [error userInfo]);
            abort();
        }
    }
}

@end
