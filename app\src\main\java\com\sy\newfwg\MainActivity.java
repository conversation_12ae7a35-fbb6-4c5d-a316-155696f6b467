package com.sy.newfwg;

import androidx.appcompat.app.AppCompatActivity;

import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import com.tencent.tersafe2.TP2Sdk;
import com.tencent.tp.TssInfoPublisher;

import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.os.Handler;
import android.os.Looper;
import java.util.Timer;
import java.util.TimerTask;

import java.util.Map;

public class MainActivity extends AppCompatActivity {
    // 使用统一的标签来输出日志
    private static final String TAG = "ComprehensiveTrap";
    private Handler mainHandler;
    private Timer javaHeapTrapTimer; // 定时器用于定期访问Java堆陷阱

    // 腾讯ACE SDK信息接收器
    private class MyTssInfoReceiver implements TssInfoPublisher.TssInfoReceiver {
        public void onReceive(int tssInfoType, String info) {
            if (tssInfoType == TssInfoPublisher.TSS_INFO_TYPE_DETECT_RESULT) {
                String plain = TP2Sdk.decTssInfo(info);
                if (!plain.equals("-1")) {
                    Log.d("ACE-SDK", "🔍 [腾讯检测] 类型:" + tssInfoType + " 信息:" + plain);
                }
            } else if (tssInfoType == TssInfoPublisher.TSS_INFO_TYPE_HEARTBEAT) {
                String plain = TP2Sdk.decTssInfo(info);
                if (!plain.equals("-1")) {
                    Log.d("ACE-SDK", "💓 [腾讯心跳] 类型:" + tssInfoType + " 信息:" + plain);
                }
            }
        }
    }

    private MyTssInfoReceiver mTssInfoReceiver;

    // UI控件
    private TextView statusText;
    private TextView threatLevelText;
    private TextView statisticsText;
    private Button startButton;
    private Button testButton;
    private Button adjustButton;
    private Button statsButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        mainHandler = new Handler(Looper.getMainLooper());

        // 初始化界面组件
        initializeUI();

        // 🎯 手动对比测试模式
        Log.i("MainActivity", "🎯 [手动测试] 对比测试模式已启用");
        Log.i("MainActivity", "📋 [手动测试] 请点击按钮进行不同的初始化测试");
        Log.i("MainActivity", "🔍 [手动测试] 这样可以更精确地对比Ca/Cb区域变化");

        // 初始化腾讯ACE SDK信息接收器（但不立即初始化SDK）
        if (mTssInfoReceiver == null) {
            mTssInfoReceiver = new MyTssInfoReceiver();
            Log.d("ACE-SDK", "📡 [腾讯ACE] 信息接收器已准备，等待手动初始化");
        }
        
        // 启动定期访问Java堆陷阱的任务
        startAccessingJavaTraps();
    }

    private void initializeUI() {
        statusText = findViewById(R.id.status_text);
        threatLevelText = findViewById(R.id.threat_level_text);
        statisticsText = findViewById(R.id.statistics_text);
        startButton = findViewById(R.id.start_button);
        testButton = findViewById(R.id.test_button);
        adjustButton = findViewById(R.id.adjust_button);
        statsButton = findViewById(R.id.stats_button);
        Button dpTestButton = findViewById(R.id.dp_test_button);
        Button dpVerifyButton = findViewById(R.id.dp_verify_button);

        // 🎯 设置对比测试按钮
        startButton.setText("腾讯ACE SDK");
        testButton.setText("我们的实现");
        adjustButton.setText("清空重置");
        statsButton.setText("查看状态");

        // 设置按钮点击事件
        startButton.setOnClickListener(v -> initializeTencentACESDK());
        testButton.setOnClickListener(v -> initializeOurImplementation());
        adjustButton.setOnClickListener(v -> resetAllSystems());
        statsButton.setOnClickListener(v -> logCurrentStatus());

        // 禁用DP测试按钮
        dpTestButton.setEnabled(false);
        dpVerifyButton.setEnabled(false);

        // 初始状态
        statusText.setText("🎯 对比测试系统 V2.0\n手动控制腾讯ACE SDK vs 我们的实现");
        threatLevelText.setText("状态: 等待手动初始化");
        statisticsText.setText("📋 点击按钮进行不同的初始化测试\n🔍 便于精确对比Ca/Cb区域变化");
    }

    // 🎯 腾讯ACE SDK初始化方法
    private void initializeTencentACESDK() {
        Log.i("MainActivity", "🚀 [手动测试] 用户点击：初始化腾讯ACE SDK");
        statusText.setText("🚀 正在初始化腾讯ACE SDK...");

        try {
            // 注册信息接收器
            TP2Sdk.registTssInfoReceiver(mTssInfoReceiver);

            // 初始化腾讯ACE SDK
            TP2Sdk.initEx(19257, "d5ab8dc7ef67ca92e41d730982c5c602");
            Log.i("MainActivity", "✅ [腾讯ACE] SDK初始化成功");

            // 模拟用户登录
            int worldId = 101;
            String openId = "TEST_OPENID_FOR_MEMORY_ANALYSIS";
            String roleId = "test_role";
            int accountType = 1; // QQ登录
            TP2Sdk.onUserLogin(accountType, worldId, openId, roleId);
            Log.i("MainActivity", "👤 [腾讯ACE] 模拟用户登录完成");

            statusText.setText("✅ 腾讯ACE SDK初始化完成\n📊 请检查GG修改器中的Ca/Cb区域");
            threatLevelText.setText("状态: 腾讯ACE SDK已激活");
            statisticsText.setText("🎯 腾讯官方实现已运行\n📋 请记录Ca/Cb区域大小作为参考");

        } catch (Exception e) {
            Log.e("MainActivity", "❌ [腾讯ACE] 初始化失败: " + e.getMessage());
            statusText.setText("❌ 腾讯ACE SDK初始化失败");
            threatLevelText.setText("状态: 初始化失败");
        }
    }

    // 🎯 我们的实现初始化方法
    private void initializeOurImplementation() {
        Log.i("MainActivity", "🚀 [手动测试] 用户点击：初始化我们的实现");
        statusText.setText("🚀 正在初始化我们的TP2SDK复制版...");

        try {
            // 初始化我们的GG兼容系统
            initializeGGCompatibleTrapSystem();

            statusText.setText("✅ 我们的实现初始化完成\n📊 请检查GG修改器中的Ca/Cb区域变化");
            threatLevelText.setText("状态: 我们的实现已激活");
            statisticsText.setText("🎯 TP2SDK复制版已运行\n📋 请对比Ca/Cb区域大小变化");

        } catch (Exception e) {
            Log.e("MainActivity", "❌ [我们实现] 初始化失败: " + e.getMessage());
            statusText.setText("❌ 我们的实现初始化失败");
            threatLevelText.setText("状态: 初始化失败");
        }
    }

    // 🎯 重置所有系统
    private void resetAllSystems() {
        Log.i("MainActivity", "🔄 [手动测试] 用户点击：重置所有系统");
        statusText.setText("🔄 正在重置所有系统...");

        // 这里可以添加清理代码
        // 注意：某些SDK可能无法完全重置，需要重启应用

        statusText.setText("🎯 对比测试系统 V2.0\n手动控制腾讯ACE SDK vs 我们的实现");
        threatLevelText.setText("状态: 已重置，等待手动初始化");
        statisticsText.setText("📋 点击按钮进行不同的初始化测试\n🔍 便于精确对比Ca/Cb区域变化");

        Log.i("MainActivity", "✅ [手动测试] 系统重置完成");
    }

    // 🎯 查看当前状态
    private void logCurrentStatus() {
        Log.i("MainActivity", "📊 [手动测试] 用户点击：查看当前状态");
        Log.i("MainActivity", "📋 [状态检查] 请查看GG修改器中的当前内存区域状态");
        Log.i("MainActivity", "🔍 [状态检查] 重点关注Ca/Cb区域的大小变化");

        // 如果我们的系统已初始化，输出详细状态
        try {
            GGCompatibleMemoryTrapManager.testGGCompatibleEffect();
        } catch (Exception e) {
            Log.i("MainActivity", "📋 [状态检查] 我们的系统尚未初始化");
        }
    }

    /**
     * 初始化综合陷阱系统
     * 该系统用于检测和防御多种类型的内存修改尝试
     */
    private void initializeComprehensiveTrapSystem() {
        Log.i(TAG, "🔧 开始初始化综合陷阱系统...");
        
        // 初始化综合陷阱系统
        if (ComprehensiveMemoryTrapManager.initialize()) {
            Log.i(TAG, "✅ 综合陷阱系统初始化成功");
            
            // 部署陷阱
            ComprehensiveMemoryTrapManager.deployTraps();
            
            // 设置检测回调
            ComprehensiveMemoryTrapManager.setDetectionCallback(new ComprehensiveMemoryTrapManager.DetectionCallback() {
                @Override
                public void onDetection(long address, String description, boolean isModifierScan, 
                                      int accessFrequency, float stepRatio) {
                    Log.i(TAG, "🔍 检测到内存访问 - 地址: " + address + 
                            ", 描述: " + description + 
                            ", 是否为修改器扫描: " + isModifierScan + 
                            ", 访问频率: " + accessFrequency + 
                            ", 步长比例: " + stepRatio);
                    
                    // 在UI线程更新界面
                    runOnUiThread(() -> {
                        String currentText = statusText.getText().toString();
                        String newText = "🔍 检测到可疑访问!\n地址: 0x" + Long.toHexString(address) + 
                                       "\n描述: " + description + 
                                       "\n" + currentText;
                        statusText.setText(newText);
                    });
                }
            });
            
            // 启动检测
            ComprehensiveMemoryTrapManager.startDetection();
            Log.i(TAG, "✅ 综合陷阱系统启动完成");
            
            statusText.setText("✅ 综合陷阱系统已启动\n" +
                             "🛡️ 多区域防御策略已部署\n" +
                             "📊 内存陷阱监控激活");
            testButton.setEnabled(true);
        } else {
            Log.e(TAG, "❌ 综合陷阱系统初始化失败");
            statusText.setText("❌ 综合陷阱系统初始化失败");
        }
    }

    private void triggerTestTrap() {
        // 这里可以添加触发测试陷阱的逻辑
        statusText.append("\n🧪 触发测试陷阱...");
        Log.i(TAG, "🧪 触发测试陷阱");
        
        // 模拟触发陷阱检测
        Toast.makeText(this, "测试陷阱已触发，请使用修改器扫描内存", Toast.LENGTH_SHORT).show();
    }

    /**
     * 启动定期访问Java堆陷阱的任务
     * 用于保持陷阱活跃，防止被绕过
     */
    private void startAccessingJavaTraps() {
        javaHeapTrapTimer = new Timer();
        javaHeapTrapTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                // 定期访问Java堆陷阱
                ComprehensiveMemoryTrapManager.accessJavaTraps();
            }
        }, 0, 100); // 每100毫秒访问一次
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 停止综合陷阱系统的检测
        ComprehensiveMemoryTrapManager.stopDetection();
        
        // 清理综合陷阱系统的资源
        ComprehensiveMemoryTrapManager.cleanup();
        Log.i(TAG, "🧹 综合陷阱系统资源已清理");
        
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
        
        // 取消Java堆陷阱访问定时器
        if (javaHeapTrapTimer != null) {
            javaHeapTrapTimer.cancel();
            javaHeapTrapTimer = null;
        }
    }

    /**
     * 初始化腾讯风格内存陷阱系统
     */
    private void initializeTencentStyleTrapSystem() {
        Log.i(TAG, "🚀 开始初始化腾讯风格内存陷阱系统...");

        try {
            // 初始化腾讯风格陷阱
            TencentStyleMemoryTrapManager.initTencentStyleTraps();

            // 延迟测试效果
            mainHandler.postDelayed(() -> {
                Log.i(TAG, "🧪 测试腾讯风格内存陷阱效果...");
                TencentStyleMemoryTrapManager.testTencentStyleEffect();
            }, 2000);

            Log.i(TAG, "✅ 腾讯风格内存陷阱系统初始化完成");

        } catch (Exception e) {
            Log.e(TAG, "❌ 腾讯风格内存陷阱系统初始化失败: " + e.getMessage());
        }
    }

    /**
     * 初始化GG兼容内存陷阱系统
     * 基于腾讯SDK成功案例的精确实现
     */
    private void initializeGGCompatibleTrapSystem() {
        Log.i(TAG, "🚀 开始初始化GG兼容内存陷阱系统...");
        Log.i(TAG, "📋 基于腾讯TP2SDK成功案例的精确实现");

        try {
            // 初始化GG兼容陷阱
            GGCompatibleMemoryTrapManager.initializeGGCompatibleTraps();

            // 延迟测试效果
            mainHandler.postDelayed(() -> {
                Log.i(TAG, "🧪 测试GG兼容内存陷阱效果...");
                GGCompatibleMemoryTrapManager.testGGCompatibleEffect();
            }, 2000);

            Log.i(TAG, "✅ GG兼容内存陷阱系统初始化完成");
        } catch (Exception e) {
            Log.e(TAG, "❌ GG兼容内存陷阱系统初始化失败: " + e.getMessage());
        }
    }

    @Override
    protected void onPause() {
        // 腾讯ACE SDK生命周期管理
        TP2Sdk.onAppPause();
        super.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 腾讯ACE SDK生命周期管理
        TP2Sdk.onAppResume();
    }
}