<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="高级反作弊系统 V3.0"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#2196F3"
            android:gravity="center"
            android:layout_marginBottom="16dp" />

        <!-- 系统状态 -->
        <TextView
            android:id="@+id/status_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="系统未启动"
            android:textSize="14sp"
            android:textColor="#333333"
            android:background="#f8f9fa"
            android:padding="12dp"
            android:layout_marginBottom="12dp"
            android:minHeight="120dp"
            android:gravity="top" />

        <!-- 威胁级别 -->
        <TextView
            android:id="@+id/threat_level_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="威胁级别: 未知"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#FF5722"
            android:background="#fff3e0"
            android:padding="8dp"
            android:layout_marginBottom="12dp" />

        <!-- 控制按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/start_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="启动系统"
                android:textSize="14sp"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/test_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="测试陷阱"
                android:textSize="14sp"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/adjust_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="调整陷阱"
                android:textSize="14sp"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/stats_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="更新统计"
                android:textSize="14sp"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- dp方案测试按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/dp_test_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🧪 测试dp方案"
                android:textSize="14sp"
                android:background="@android:color/holo_green_light"
                android:textColor="@android:color/white"
                android:layout_marginEnd="4dp" />

            <Button
                android:id="@+id/dp_verify_button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="🔍 验证dp陷阱"
                android:textSize="14sp"
                android:background="@android:color/holo_blue_light"
                android:textColor="@android:color/white"
                android:layout_marginStart="4dp" />

        </LinearLayout>

        <!-- 统计信息 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="系统统计与行为分析"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="#4CAF50"
            android:layout_marginBottom="8dp" />

        <TextView
            android:id="@+id/statistics_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="统计信息: 系统未启动"
            android:textSize="12sp"
            android:textColor="#666666"
            android:background="#f1f8e9"
            android:padding="12dp"
            android:minHeight="150dp"
            android:gravity="top"
            android:fontFamily="monospace" />

    </LinearLayout>

</ScrollView>
