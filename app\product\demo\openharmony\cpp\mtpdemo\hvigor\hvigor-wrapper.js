"use strict";var e=require("path"),t=require("os"),n=require("fs"),r=require("child_process"),u=require("constants"),o=require("stream"),i=require("util"),s=require("assert"),c=require("process"),a=require("tty"),l=require("url"),f=require("zlib"),d=require("net"),D=require("crypto"),p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},h={},E={},m=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(E,"__esModule",{value:!0}),E.maxPathLength=E.isMac=E.isLinux=E.isWindows=void 0;const y=m(t),C="Windows_NT",F="Darwin";function g(){return y.default.type()===C}function A(){return y.default.type()===F}E.isWindows=g,E.isLinux=function(){return"Linux"===y.default.type()},E.isMac=A,E.maxPathLength=function(){return A()?1016:g()?259:4095},Object.defineProperty(h,"__esModule",{value:!0}),h.LOG_LEVEL=h.ANALYZE=h.PARALLEL=h.INCREMENTAL=h.DAEMON=h.DOT=h.PROPERTIES=h.OHOS_ARK_COMPILE_MAX_SIZE=h.HVIGOR_POOL_CACHE_TTL=h.HVIGOR_POOL_CACHE_CAPACITY=h.HVIGOR_POOL_MAX_CORE_SIZE=h.HVIGOR_POOL_MAX_SIZE=h.BUILD_CACHE_DIR=h.ENABLE_SIGN_TASK_KEY=h.HVIGOR_CACHE_DIR_KEY=h.WORK_SPACE=h.PROJECT_CACHES=h.HVIGOR_USER_HOME_DIR_NAME=h.DEFAULT_PACKAGE_JSON=h.DEFAULT_HVIGOR_CONFIG_JSON_FILE_NAME=h.PNPM=h.HVIGOR=h.NPM_TOOL=h.PNPM_TOOL=h.HVIGOR_ENGINE_PACKAGE_NAME=void 0;const v=E;h.HVIGOR_ENGINE_PACKAGE_NAME="@ohos/hvigor",h.PNPM_TOOL=(0,v.isWindows)()?"pnpm.cmd":"pnpm",h.NPM_TOOL=(0,v.isWindows)()?"npm.cmd":"npm",h.HVIGOR="hvigor",h.PNPM="pnpm",h.DEFAULT_HVIGOR_CONFIG_JSON_FILE_NAME="hvigor-config.json5",h.DEFAULT_PACKAGE_JSON="package.json",h.HVIGOR_USER_HOME_DIR_NAME=".hvigor",h.PROJECT_CACHES="project_caches";var S=h.WORK_SPACE="workspace";h.HVIGOR_CACHE_DIR_KEY="hvigor.cacheDir",h.ENABLE_SIGN_TASK_KEY="enableSignTask",h.BUILD_CACHE_DIR="build-cache-dir",h.HVIGOR_POOL_MAX_SIZE="hvigor.pool.maxSize",h.HVIGOR_POOL_MAX_CORE_SIZE="hvigor.pool.maxCoreSize",h.HVIGOR_POOL_CACHE_CAPACITY="hvigor.pool.cache.capacity",h.HVIGOR_POOL_CACHE_TTL="hvigor.pool.cache.ttl",h.OHOS_ARK_COMPILE_MAX_SIZE="ohos.arkCompile.maxSize",h.PROPERTIES="properties",h.DOT=".",h.DAEMON="daemon",h.INCREMENTAL="incremental",h.PARALLEL="typeCheck",h.ANALYZE="analyze",h.LOG_LEVEL="logLevel";var w={},O={};Object.defineProperty(O,"__esModule",{value:!0}),O.logError=O.logInfo=O.logErrorAndExit=void 0,O.logErrorAndExit=function(e){e instanceof Error?console.error(e.message):console.error(e),process.exit(-1)},O.logInfo=function(e){console.log(e)},O.logError=function(e){console.error(e)};var _=p&&p.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var u=Object.getOwnPropertyDescriptor(t,n);u&&!("get"in u?!t.__esModule:u.writable||u.configurable)||(u={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,u)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),b=p&&p.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),B=p&&p.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&_(t,e,n);return b(t,e),t};Object.defineProperty(w,"__esModule",{value:!0});var P=w.executeBuild=void 0;const I=B(n),k=B(e),x=O,N=r;P=w.executeBuild=function(e){const t=k.resolve(e,"node_modules","@ohos","hvigor","bin","hvigor.js");try{const e=I.realpathSync(t),n=process.argv.slice(2),r=(0,N.spawn)("node",[e,...n],{env:process.env});r.stdout.on("data",(e=>{(0,x.logInfo)(`${e.toString().trim()}`)})),r.stderr.on("data",(e=>{(0,x.logError)(`${e.toString().trim()}`)})),r.on("exit",((e,t)=>{process.exit(null!=e?e:-1)}))}catch(n){(0,x.logErrorAndExit)(`Error: ENOENT: no such file ${t},delete ${e} and retry.`)}};var R={},T={},M={fromCallback:function(e){return Object.defineProperty((function(...t){if("function"!=typeof t[t.length-1])return new Promise(((n,r)=>{t.push(((e,t)=>null!=e?r(e):n(t))),e.apply(this,t)}));e.apply(this,t)}),"name",{value:e.name})},fromPromise:function(e){return Object.defineProperty((function(...t){const n=t[t.length-1];if("function"!=typeof n)return e.apply(this,t);t.pop(),e.apply(this,t).then((e=>n(null,e)),n)}),"name",{value:e.name})}},L=u,j=process.cwd,$=null,H=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return $||($=j.call(process)),$};try{process.cwd()}catch(e){}if("function"==typeof process.chdir){var G=process.chdir;process.chdir=function(e){$=null,G.call(process,e)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,G)}var V=function(e){L.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&function(e){e.lchmod=function(t,n,r){e.open(t,L.O_WRONLY|L.O_SYMLINK,n,(function(t,u){t?r&&r(t):e.fchmod(u,n,(function(t){e.close(u,(function(e){r&&r(t||e)}))}))}))},e.lchmodSync=function(t,n){var r,u=e.openSync(t,L.O_WRONLY|L.O_SYMLINK,n),o=!0;try{r=e.fchmodSync(u,n),o=!1}finally{if(o)try{e.closeSync(u)}catch(e){}else e.closeSync(u)}return r}}(e);e.lutimes||function(e){L.hasOwnProperty("O_SYMLINK")&&e.futimes?(e.lutimes=function(t,n,r,u){e.open(t,L.O_SYMLINK,(function(t,o){t?u&&u(t):e.futimes(o,n,r,(function(t){e.close(o,(function(e){u&&u(t||e)}))}))}))},e.lutimesSync=function(t,n,r){var u,o=e.openSync(t,L.O_SYMLINK),i=!0;try{u=e.futimesSync(o,n,r),i=!1}finally{if(i)try{e.closeSync(o)}catch(e){}else e.closeSync(o)}return u}):e.futimes&&(e.lutimes=function(e,t,n,r){r&&process.nextTick(r)},e.lutimesSync=function(){})}(e);e.chown=r(e.chown),e.fchown=r(e.fchown),e.lchown=r(e.lchown),e.chmod=t(e.chmod),e.fchmod=t(e.fchmod),e.lchmod=t(e.lchmod),e.chownSync=u(e.chownSync),e.fchownSync=u(e.fchownSync),e.lchownSync=u(e.lchownSync),e.chmodSync=n(e.chmodSync),e.fchmodSync=n(e.fchmodSync),e.lchmodSync=n(e.lchmodSync),e.stat=o(e.stat),e.fstat=o(e.fstat),e.lstat=o(e.lstat),e.statSync=i(e.statSync),e.fstatSync=i(e.fstatSync),e.lstatSync=i(e.lstatSync),e.chmod&&!e.lchmod&&(e.lchmod=function(e,t,n){n&&process.nextTick(n)},e.lchmodSync=function(){});e.chown&&!e.lchown&&(e.lchown=function(e,t,n,r){r&&process.nextTick(r)},e.lchownSync=function(){});"win32"===H&&(e.rename="function"!=typeof e.rename?e.rename:function(t){function n(n,r,u){var o=Date.now(),i=0;t(n,r,(function s(c){if(c&&("EACCES"===c.code||"EPERM"===c.code||"EBUSY"===c.code)&&Date.now()-o<6e4)return setTimeout((function(){e.stat(r,(function(e,o){e&&"ENOENT"===e.code?t(n,r,s):u(c)}))}),i),void(i<100&&(i+=10));u&&u(c)}))}return Object.setPrototypeOf&&Object.setPrototypeOf(n,t),n}(e.rename));function t(t){return t?function(n,r,u){return t.call(e,n,r,(function(e){s(e)&&(e=null),u&&u.apply(this,arguments)}))}:t}function n(t){return t?function(n,r){try{return t.call(e,n,r)}catch(e){if(!s(e))throw e}}:t}function r(t){return t?function(n,r,u,o){return t.call(e,n,r,u,(function(e){s(e)&&(e=null),o&&o.apply(this,arguments)}))}:t}function u(t){return t?function(n,r,u){try{return t.call(e,n,r,u)}catch(e){if(!s(e))throw e}}:t}function o(t){return t?function(n,r,u){function o(e,t){t&&(t.uid<0&&(t.uid+=4294967296),t.gid<0&&(t.gid+=4294967296)),u&&u.apply(this,arguments)}return"function"==typeof r&&(u=r,r=null),r?t.call(e,n,r,o):t.call(e,n,o)}:t}function i(t){return t?function(n,r){var u=r?t.call(e,n,r):t.call(e,n);return u&&(u.uid<0&&(u.uid+=4294967296),u.gid<0&&(u.gid+=4294967296)),u}:t}function s(e){return!e||("ENOSYS"===e.code||!(process.getuid&&0===process.getuid()||"EINVAL"!==e.code&&"EPERM"!==e.code))}e.read="function"!=typeof e.read?e.read:function(t){function n(n,r,u,o,i,s){var c;if(s&&"function"==typeof s){var a=0;c=function(l,f,d){if(l&&"EAGAIN"===l.code&&a<10)return a++,t.call(e,n,r,u,o,i,c);s.apply(this,arguments)}}return t.call(e,n,r,u,o,i,c)}return Object.setPrototypeOf&&Object.setPrototypeOf(n,t),n}(e.read),e.readSync="function"!=typeof e.readSync?e.readSync:(c=e.readSync,function(t,n,r,u,o){for(var i=0;;)try{return c.call(e,t,n,r,u,o)}catch(e){if("EAGAIN"===e.code&&i<10){i++;continue}throw e}});var c};var U=o.Stream,J=function(e){return{ReadStream:function t(n,r){if(!(this instanceof t))return new t(n,r);U.call(this);var u=this;this.path=n,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=65536,r=r||{};for(var o=Object.keys(r),i=0,s=o.length;i<s;i++){var c=o[i];this[c]=r[c]}this.encoding&&this.setEncoding(this.encoding);if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(void 0===this.end)this.end=1/0;else if("number"!=typeof this.end)throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}if(null!==this.fd)return void process.nextTick((function(){u._read()}));e.open(this.path,this.flags,this.mode,(function(e,t){if(e)return u.emit("error",e),void(u.readable=!1);u.fd=t,u.emit("open",t),u._read()}))},WriteStream:function t(n,r){if(!(this instanceof t))return new t(n,r);U.call(this),this.path=n,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,r=r||{};for(var u=Object.keys(r),o=0,i=u.length;o<i;o++){var s=u[o];this[s]=r[s]}if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],null===this.fd&&(this._open=e.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}};var W=function(e){if(null===e||"object"!=typeof e)return e;if(e instanceof Object)var t={__proto__:z(e)};else t=Object.create(null);return Object.getOwnPropertyNames(e).forEach((function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})),t},z=Object.getPrototypeOf||function(e){return e.__proto__};var K,q,Y=n,Z=V,X=J,Q=W,ee=i;function te(e,t){Object.defineProperty(e,K,{get:function(){return t}})}"function"==typeof Symbol&&"function"==typeof Symbol.for?(K=Symbol.for("graceful-fs.queue"),q=Symbol.for("graceful-fs.previous")):(K="___graceful-fs.queue",q="___graceful-fs.previous");var ne=function(){};if(ee.debuglog?ne=ee.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(ne=function(){var e=ee.format.apply(ee,arguments);e="GFS4: "+e.split(/\n/).join("\nGFS4: "),console.error(e)}),!Y[K]){var re=p[K]||[];te(Y,re),Y.close=function(e){function t(t,n){return e.call(Y,t,(function(e){e||ce(),"function"==typeof n&&n.apply(this,arguments)}))}return Object.defineProperty(t,q,{value:e}),t}(Y.close),Y.closeSync=function(e){function t(t){e.apply(Y,arguments),ce()}return Object.defineProperty(t,q,{value:e}),t}(Y.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",(function(){ne(Y[K]),s.equal(Y[K].length,0)}))}p[K]||te(p,Y[K]);var ue,oe=ie(Q(Y));function ie(e){Z(e),e.gracefulify=ie,e.createReadStream=function(t,n){return new e.ReadStream(t,n)},e.createWriteStream=function(t,n){return new e.WriteStream(t,n)};var t=e.readFile;e.readFile=function(e,n,r){"function"==typeof n&&(r=n,n=null);return function e(n,r,u,o){return t(n,r,(function(t){!t||"EMFILE"!==t.code&&"ENFILE"!==t.code?"function"==typeof u&&u.apply(this,arguments):se([e,[n,r,u],t,o||Date.now(),Date.now()])}))}(e,n,r)};var n=e.writeFile;e.writeFile=function(e,t,r,u){"function"==typeof r&&(u=r,r=null);return function e(t,r,u,o,i){return n(t,r,u,(function(n){!n||"EMFILE"!==n.code&&"ENFILE"!==n.code?"function"==typeof o&&o.apply(this,arguments):se([e,[t,r,u,o],n,i||Date.now(),Date.now()])}))}(e,t,r,u)};var r=e.appendFile;r&&(e.appendFile=function(e,t,n,u){"function"==typeof n&&(u=n,n=null);return function e(t,n,u,o,i){return r(t,n,u,(function(r){!r||"EMFILE"!==r.code&&"ENFILE"!==r.code?"function"==typeof o&&o.apply(this,arguments):se([e,[t,n,u,o],r,i||Date.now(),Date.now()])}))}(e,t,n,u)});var u=e.copyFile;u&&(e.copyFile=function(e,t,n,r){"function"==typeof n&&(r=n,n=0);return function e(t,n,r,o,i){return u(t,n,r,(function(u){!u||"EMFILE"!==u.code&&"ENFILE"!==u.code?"function"==typeof o&&o.apply(this,arguments):se([e,[t,n,r,o],u,i||Date.now(),Date.now()])}))}(e,t,n,r)});var o=e.readdir;e.readdir=function(e,t,n){"function"==typeof t&&(n=t,t=null);var r=i.test(process.version)?function(e,t,n,r){return o(e,u(e,t,n,r))}:function(e,t,n,r){return o(e,t,u(e,t,n,r))};return r(e,t,n);function u(e,t,n,u){return function(o,i){!o||"EMFILE"!==o.code&&"ENFILE"!==o.code?(i&&i.sort&&i.sort(),"function"==typeof n&&n.call(this,o,i)):se([r,[e,t,n],o,u||Date.now(),Date.now()])}}};var i=/^v[0-5]\./;if("v0.8"===process.version.substr(0,4)){var s=X(e);d=s.ReadStream,D=s.WriteStream}var c=e.ReadStream;c&&(d.prototype=Object.create(c.prototype),d.prototype.open=function(){var e=this;h(e.path,e.flags,e.mode,(function(t,n){t?(e.autoClose&&e.destroy(),e.emit("error",t)):(e.fd=n,e.emit("open",n),e.read())}))});var a=e.WriteStream;a&&(D.prototype=Object.create(a.prototype),D.prototype.open=function(){var e=this;h(e.path,e.flags,e.mode,(function(t,n){t?(e.destroy(),e.emit("error",t)):(e.fd=n,e.emit("open",n))}))}),Object.defineProperty(e,"ReadStream",{get:function(){return d},set:function(e){d=e},enumerable:!0,configurable:!0}),Object.defineProperty(e,"WriteStream",{get:function(){return D},set:function(e){D=e},enumerable:!0,configurable:!0});var l=d;Object.defineProperty(e,"FileReadStream",{get:function(){return l},set:function(e){l=e},enumerable:!0,configurable:!0});var f=D;function d(e,t){return this instanceof d?(c.apply(this,arguments),this):d.apply(Object.create(d.prototype),arguments)}function D(e,t){return this instanceof D?(a.apply(this,arguments),this):D.apply(Object.create(D.prototype),arguments)}Object.defineProperty(e,"FileWriteStream",{get:function(){return f},set:function(e){f=e},enumerable:!0,configurable:!0});var p=e.open;function h(e,t,n,r){return"function"==typeof n&&(r=n,n=null),function e(t,n,r,u,o){return p(t,n,r,(function(i,s){!i||"EMFILE"!==i.code&&"ENFILE"!==i.code?"function"==typeof u&&u.apply(this,arguments):se([e,[t,n,r,u],i,o||Date.now(),Date.now()])}))}(e,t,n,r)}return e.open=h,e}function se(e){ne("ENQUEUE",e[0].name,e[1]),Y[K].push(e),ae()}function ce(){for(var e=Date.now(),t=0;t<Y[K].length;++t)Y[K][t].length>2&&(Y[K][t][3]=e,Y[K][t][4]=e);ae()}function ae(){if(clearTimeout(ue),ue=void 0,0!==Y[K].length){var e=Y[K].shift(),t=e[0],n=e[1],r=e[2],u=e[3],o=e[4];if(void 0===u)ne("RETRY",t.name,n),t.apply(null,n);else if(Date.now()-u>=6e4){ne("TIMEOUT",t.name,n);var i=n.pop();"function"==typeof i&&i.call(null,r)}else{var s=Date.now()-o,c=Math.max(o-u,1);s>=Math.min(1.2*c,100)?(ne("RETRY",t.name,n),t.apply(null,n.concat([u]))):Y[K].push(e)}void 0===ue&&(ue=setTimeout(ae,0))}}process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!Y.__patched&&(oe=ie(Y),Y.__patched=!0),function(e){const t=M.fromCallback,n=oe,r=["access","appendFile","chmod","chown","close","copyFile","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","link","lstat","mkdir","mkdtemp","open","opendir","readdir","readFile","readlink","realpath","rename","rm","rmdir","stat","symlink","truncate","unlink","utimes","writeFile"].filter((e=>"function"==typeof n[e]));Object.assign(e,n),r.forEach((r=>{e[r]=t(n[r])})),e.exists=function(e,t){return"function"==typeof t?n.exists(e,t):new Promise((t=>n.exists(e,t)))},e.read=function(e,t,r,u,o,i){return"function"==typeof i?n.read(e,t,r,u,o,i):new Promise(((i,s)=>{n.read(e,t,r,u,o,((e,t,n)=>{if(e)return s(e);i({bytesRead:t,buffer:n})}))}))},e.write=function(e,t,...r){return"function"==typeof r[r.length-1]?n.write(e,t,...r):new Promise(((u,o)=>{n.write(e,t,...r,((e,t,n)=>{if(e)return o(e);u({bytesWritten:t,buffer:n})}))}))},e.readv=function(e,t,...r){return"function"==typeof r[r.length-1]?n.readv(e,t,...r):new Promise(((u,o)=>{n.readv(e,t,...r,((e,t,n)=>{if(e)return o(e);u({bytesRead:t,buffers:n})}))}))},e.writev=function(e,t,...r){return"function"==typeof r[r.length-1]?n.writev(e,t,...r):new Promise(((u,o)=>{n.writev(e,t,...r,((e,t,n)=>{if(e)return o(e);u({bytesWritten:t,buffers:n})}))}))},"function"==typeof n.realpath.native?e.realpath.native=t(n.realpath.native):process.emitWarning("fs.realpath.native is not a function. Is fs being monkey-patched?","Warning","fs-extra-WARN0003")}(T);var le={},fe={};const de=e;fe.checkPath=function(e){if("win32"===process.platform){if(/[<>:"|?*]/.test(e.replace(de.parse(e).root,""))){const t=new Error(`Path contains invalid characters: ${e}`);throw t.code="EINVAL",t}}};const De=T,{checkPath:pe}=fe,he=e=>"number"==typeof e?e:{mode:511,...e}.mode;le.makeDir=async(e,t)=>(pe(e),De.mkdir(e,{mode:he(t),recursive:!0})),le.makeDirSync=(e,t)=>(pe(e),De.mkdirSync(e,{mode:he(t),recursive:!0}));const Ee=M.fromPromise,{makeDir:me,makeDirSync:ye}=le,Ce=Ee(me);var Fe={mkdirs:Ce,mkdirsSync:ye,mkdirp:Ce,mkdirpSync:ye,ensureDir:Ce,ensureDirSync:ye};const ge=M.fromPromise,Ae=T;var ve={pathExists:ge((function(e){return Ae.access(e).then((()=>!0)).catch((()=>!1))})),pathExistsSync:Ae.existsSync};const Se=oe;var we=function(e,t,n,r){Se.open(e,"r+",((e,u)=>{if(e)return r(e);Se.futimes(u,t,n,(e=>{Se.close(u,(t=>{r&&r(e||t)}))}))}))},Oe=function(e,t,n){const r=Se.openSync(e,"r+");return Se.futimesSync(r,t,n),Se.closeSync(r)};const _e=T,be=e,Be=i;function Pe(e,t,n){const r=n.dereference?e=>_e.stat(e,{bigint:!0}):e=>_e.lstat(e,{bigint:!0});return Promise.all([r(e),r(t).catch((e=>{if("ENOENT"===e.code)return null;throw e}))]).then((([e,t])=>({srcStat:e,destStat:t})))}function Ie(e,t){return t.ino&&t.dev&&t.ino===e.ino&&t.dev===e.dev}function ke(e,t){const n=be.resolve(e).split(be.sep).filter((e=>e)),r=be.resolve(t).split(be.sep).filter((e=>e));return n.reduce(((e,t,n)=>e&&r[n]===t),!0)}function xe(e,t,n){return`Cannot ${n} '${e}' to a subdirectory of itself, '${t}'.`}var Ne={checkPaths:function(e,t,n,r,u){Be.callbackify(Pe)(e,t,r,((r,o)=>{if(r)return u(r);const{srcStat:i,destStat:s}=o;if(s){if(Ie(i,s)){const r=be.basename(e),o=be.basename(t);return"move"===n&&r!==o&&r.toLowerCase()===o.toLowerCase()?u(null,{srcStat:i,destStat:s,isChangingCase:!0}):u(new Error("Source and destination must not be the same."))}if(i.isDirectory()&&!s.isDirectory())return u(new Error(`Cannot overwrite non-directory '${t}' with directory '${e}'.`));if(!i.isDirectory()&&s.isDirectory())return u(new Error(`Cannot overwrite directory '${t}' with non-directory '${e}'.`))}return i.isDirectory()&&ke(e,t)?u(new Error(xe(e,t,n))):u(null,{srcStat:i,destStat:s})}))},checkPathsSync:function(e,t,n,r){const{srcStat:u,destStat:o}=function(e,t,n){let r;const u=n.dereference?e=>_e.statSync(e,{bigint:!0}):e=>_e.lstatSync(e,{bigint:!0}),o=u(e);try{r=u(t)}catch(e){if("ENOENT"===e.code)return{srcStat:o,destStat:null};throw e}return{srcStat:o,destStat:r}}(e,t,r);if(o){if(Ie(u,o)){const r=be.basename(e),i=be.basename(t);if("move"===n&&r!==i&&r.toLowerCase()===i.toLowerCase())return{srcStat:u,destStat:o,isChangingCase:!0};throw new Error("Source and destination must not be the same.")}if(u.isDirectory()&&!o.isDirectory())throw new Error(`Cannot overwrite non-directory '${t}' with directory '${e}'.`);if(!u.isDirectory()&&o.isDirectory())throw new Error(`Cannot overwrite directory '${t}' with non-directory '${e}'.`)}if(u.isDirectory()&&ke(e,t))throw new Error(xe(e,t,n));return{srcStat:u,destStat:o}},checkParentPaths:function e(t,n,r,u,o){const i=be.resolve(be.dirname(t)),s=be.resolve(be.dirname(r));if(s===i||s===be.parse(s).root)return o();_e.stat(s,{bigint:!0},((i,c)=>i?"ENOENT"===i.code?o():o(i):Ie(n,c)?o(new Error(xe(t,r,u))):e(t,n,s,u,o)))},checkParentPathsSync:function e(t,n,r,u){const o=be.resolve(be.dirname(t)),i=be.resolve(be.dirname(r));if(i===o||i===be.parse(i).root)return;let s;try{s=_e.statSync(i,{bigint:!0})}catch(e){if("ENOENT"===e.code)return;throw e}if(Ie(n,s))throw new Error(xe(t,r,u));return e(t,n,i,u)},isSrcSubdir:ke,areIdentical:Ie};const Re=oe,Te=e,Me=Fe.mkdirs,Le=ve.pathExists,je=we,$e=Ne;function He(e,t,n,r){if(!n.filter)return r(null,!0);Promise.resolve(n.filter(e,t)).then((e=>r(null,e)),(e=>r(e)))}function Ge(e,t,n,r,u){(r.dereference?Re.stat:Re.lstat)(t,((o,i)=>o?u(o):i.isDirectory()?function(e,t,n,r,u,o){return t?We(n,r,u,o):function(e,t,n,r,u){Re.mkdir(n,(o=>{if(o)return u(o);We(t,n,r,(t=>t?u(t):Je(n,e,u)))}))}(e.mode,n,r,u,o)}(i,e,t,n,r,u):i.isFile()||i.isCharacterDevice()||i.isBlockDevice()?function(e,t,n,r,u,o){return t?function(e,t,n,r,u){if(!r.overwrite)return r.errorOnExist?u(new Error(`'${n}' already exists`)):u();Re.unlink(n,(o=>o?u(o):Ve(e,t,n,r,u)))}(e,n,r,u,o):Ve(e,n,r,u,o)}(i,e,t,n,r,u):i.isSymbolicLink()?function(e,t,n,r,u){Re.readlink(t,((t,o)=>t?u(t):(r.dereference&&(o=Te.resolve(process.cwd(),o)),e?void Re.readlink(n,((e,t)=>e?"EINVAL"===e.code||"UNKNOWN"===e.code?Re.symlink(o,n,u):u(e):(r.dereference&&(t=Te.resolve(process.cwd(),t)),$e.isSrcSubdir(o,t)?u(new Error(`Cannot copy '${o}' to a subdirectory of itself, '${t}'.`)):$e.isSrcSubdir(t,o)?u(new Error(`Cannot overwrite '${t}' with '${o}'.`)):function(e,t,n){Re.unlink(t,(r=>r?n(r):Re.symlink(e,t,n)))}(o,n,u)))):Re.symlink(o,n,u))))}(e,t,n,r,u):i.isSocket()?u(new Error(`Cannot copy a socket file: ${t}`)):i.isFIFO()?u(new Error(`Cannot copy a FIFO pipe: ${t}`)):u(new Error(`Unknown file: ${t}`))))}function Ve(e,t,n,r,u){Re.copyFile(t,n,(o=>o?u(o):r.preserveTimestamps?function(e,t,n,r){if(function(e){return!(128&e)}(e))return function(e,t,n){return Je(e,128|t,n)}(n,e,(u=>u?r(u):Ue(e,t,n,r)));return Ue(e,t,n,r)}(e.mode,t,n,u):Je(n,e.mode,u)))}function Ue(e,t,n,r){!function(e,t,n){Re.stat(e,((e,r)=>e?n(e):je(t,r.atime,r.mtime,n)))}(t,n,(t=>t?r(t):Je(n,e,r)))}function Je(e,t,n){return Re.chmod(e,t,n)}function We(e,t,n,r){Re.readdir(e,((u,o)=>u?r(u):ze(o,e,t,n,r)))}function ze(e,t,n,r,u){const o=e.pop();return o?function(e,t,n,r,u,o){const i=Te.join(n,t),s=Te.join(r,t);He(i,s,u,((t,c)=>t?o(t):c?void $e.checkPaths(i,s,"copy",u,((t,c)=>{if(t)return o(t);const{destStat:a}=c;Ge(a,i,s,u,(t=>t?o(t):ze(e,n,r,u,o)))})):ze(e,n,r,u,o)))}(e,o,t,n,r,u):u()}var Ke=function(e,t,n,r){"function"!=typeof n||r?"function"==typeof n&&(n={filter:n}):(r=n,n={}),r=r||function(){},(n=n||{}).clobber=!("clobber"in n)||!!n.clobber,n.overwrite="overwrite"in n?!!n.overwrite:n.clobber,n.preserveTimestamps&&"ia32"===process.arch&&process.emitWarning("Using the preserveTimestamps option in 32-bit node is not recommended;\n\n\tsee https://github.com/jprichardson/node-fs-extra/issues/269","Warning","fs-extra-WARN0001"),$e.checkPaths(e,t,"copy",n,((u,o)=>{if(u)return r(u);const{srcStat:i,destStat:s}=o;$e.checkParentPaths(e,i,t,"copy",(u=>{if(u)return r(u);He(e,t,n,((u,o)=>u?r(u):o?void function(e,t,n,r,u){const o=Te.dirname(n);Le(o,((i,s)=>i?u(i):s?Ge(e,t,n,r,u):void Me(o,(o=>o?u(o):Ge(e,t,n,r,u)))))}(s,e,t,n,r):r()))}))}))};const qe=oe,Ye=e,Ze=Fe.mkdirsSync,Xe=Oe,Qe=Ne;function et(e,t,n,r){const u=(r.dereference?qe.statSync:qe.lstatSync)(t);if(u.isDirectory())return function(e,t,n,r,u){return t?rt(n,r,u):function(e,t,n,r){return qe.mkdirSync(n),rt(t,n,r),nt(n,e)}(e.mode,n,r,u)}(u,e,t,n,r);if(u.isFile()||u.isCharacterDevice()||u.isBlockDevice())return function(e,t,n,r,u){return t?function(e,t,n,r){if(r.overwrite)return qe.unlinkSync(n),tt(e,t,n,r);if(r.errorOnExist)throw new Error(`'${n}' already exists`)}(e,n,r,u):tt(e,n,r,u)}(u,e,t,n,r);if(u.isSymbolicLink())return function(e,t,n,r){let u=qe.readlinkSync(t);r.dereference&&(u=Ye.resolve(process.cwd(),u));if(e){let e;try{e=qe.readlinkSync(n)}catch(e){if("EINVAL"===e.code||"UNKNOWN"===e.code)return qe.symlinkSync(u,n);throw e}if(r.dereference&&(e=Ye.resolve(process.cwd(),e)),Qe.isSrcSubdir(u,e))throw new Error(`Cannot copy '${u}' to a subdirectory of itself, '${e}'.`);if(Qe.isSrcSubdir(e,u))throw new Error(`Cannot overwrite '${e}' with '${u}'.`);return function(e,t){return qe.unlinkSync(t),qe.symlinkSync(e,t)}(u,n)}return qe.symlinkSync(u,n)}(e,t,n,r);if(u.isSocket())throw new Error(`Cannot copy a socket file: ${t}`);if(u.isFIFO())throw new Error(`Cannot copy a FIFO pipe: ${t}`);throw new Error(`Unknown file: ${t}`)}function tt(e,t,n,r){return qe.copyFileSync(t,n),r.preserveTimestamps&&function(e,t,n){(function(e){return!(128&e)})(e)&&function(e,t){nt(e,128|t)}(n,e);(function(e,t){const n=qe.statSync(e);Xe(t,n.atime,n.mtime)})(t,n)}(e.mode,t,n),nt(n,e.mode)}function nt(e,t){return qe.chmodSync(e,t)}function rt(e,t,n){qe.readdirSync(e).forEach((r=>function(e,t,n,r){const u=Ye.join(t,e),o=Ye.join(n,e);if(r.filter&&!r.filter(u,o))return;const{destStat:i}=Qe.checkPathsSync(u,o,"copy",r);return et(i,u,o,r)}(r,e,t,n)))}var ut=function(e,t,n){"function"==typeof n&&(n={filter:n}),(n=n||{}).clobber=!("clobber"in n)||!!n.clobber,n.overwrite="overwrite"in n?!!n.overwrite:n.clobber,n.preserveTimestamps&&"ia32"===process.arch&&process.emitWarning("Using the preserveTimestamps option in 32-bit node is not recommended;\n\n\tsee https://github.com/jprichardson/node-fs-extra/issues/269","Warning","fs-extra-WARN0002");const{srcStat:r,destStat:u}=Qe.checkPathsSync(e,t,"copy",n);if(Qe.checkParentPathsSync(e,r,t,"copy"),n.filter&&!n.filter(e,t))return;const o=Ye.dirname(t);return qe.existsSync(o)||Ze(o),et(u,e,t,n)};var ot={copy:(0,M.fromCallback)(Ke),copySync:ut};const it=oe;var st={remove:(0,M.fromCallback)((function(e,t){it.rm(e,{recursive:!0,force:!0},t)})),removeSync:function(e){it.rmSync(e,{recursive:!0,force:!0})}};const ct=M.fromPromise,at=T,lt=e,ft=Fe,dt=st,Dt=ct((async function(e){let t;try{t=await at.readdir(e)}catch{return ft.mkdirs(e)}return Promise.all(t.map((t=>dt.remove(lt.join(e,t)))))}));function pt(e){let t;try{t=at.readdirSync(e)}catch{return ft.mkdirsSync(e)}t.forEach((t=>{t=lt.join(e,t),dt.removeSync(t)}))}var ht={emptyDirSync:pt,emptydirSync:pt,emptyDir:Dt,emptydir:Dt};const Et=M.fromCallback,mt=e,yt=oe,Ct=Fe;var Ft={createFile:Et((function(e,t){function n(){yt.writeFile(e,"",(e=>{if(e)return t(e);t()}))}yt.stat(e,((r,u)=>{if(!r&&u.isFile())return t();const o=mt.dirname(e);yt.stat(o,((e,r)=>{if(e)return"ENOENT"===e.code?Ct.mkdirs(o,(e=>{if(e)return t(e);n()})):t(e);r.isDirectory()?n():yt.readdir(o,(e=>{if(e)return t(e)}))}))}))})),createFileSync:function(e){let t;try{t=yt.statSync(e)}catch{}if(t&&t.isFile())return;const n=mt.dirname(e);try{yt.statSync(n).isDirectory()||yt.readdirSync(n)}catch(e){if(!e||"ENOENT"!==e.code)throw e;Ct.mkdirsSync(n)}yt.writeFileSync(e,"")}};const gt=M.fromCallback,At=e,vt=oe,St=Fe,wt=ve.pathExists,{areIdentical:Ot}=Ne;var _t={createLink:gt((function(e,t,n){function r(e,t){vt.link(e,t,(e=>{if(e)return n(e);n(null)}))}vt.lstat(t,((u,o)=>{vt.lstat(e,((u,i)=>{if(u)return u.message=u.message.replace("lstat","ensureLink"),n(u);if(o&&Ot(i,o))return n(null);const s=At.dirname(t);wt(s,((u,o)=>u?n(u):o?r(e,t):void St.mkdirs(s,(u=>{if(u)return n(u);r(e,t)}))))}))}))})),createLinkSync:function(e,t){let n;try{n=vt.lstatSync(t)}catch{}try{const t=vt.lstatSync(e);if(n&&Ot(t,n))return}catch(e){throw e.message=e.message.replace("lstat","ensureLink"),e}const r=At.dirname(t);return vt.existsSync(r)||St.mkdirsSync(r),vt.linkSync(e,t)}};const bt=e,Bt=oe,Pt=ve.pathExists;var It={symlinkPaths:function(e,t,n){if(bt.isAbsolute(e))return Bt.lstat(e,(t=>t?(t.message=t.message.replace("lstat","ensureSymlink"),n(t)):n(null,{toCwd:e,toDst:e})));{const r=bt.dirname(t),u=bt.join(r,e);return Pt(u,((t,o)=>t?n(t):o?n(null,{toCwd:u,toDst:e}):Bt.lstat(e,(t=>t?(t.message=t.message.replace("lstat","ensureSymlink"),n(t)):n(null,{toCwd:e,toDst:bt.relative(r,e)})))))}},symlinkPathsSync:function(e,t){let n;if(bt.isAbsolute(e)){if(n=Bt.existsSync(e),!n)throw new Error("absolute srcpath does not exist");return{toCwd:e,toDst:e}}{const r=bt.dirname(t),u=bt.join(r,e);if(n=Bt.existsSync(u),n)return{toCwd:u,toDst:e};if(n=Bt.existsSync(e),!n)throw new Error("relative srcpath does not exist");return{toCwd:e,toDst:bt.relative(r,e)}}}};const kt=oe;var xt={symlinkType:function(e,t,n){if(n="function"==typeof t?t:n,t="function"!=typeof t&&t)return n(null,t);kt.lstat(e,((e,r)=>{if(e)return n(null,"file");t=r&&r.isDirectory()?"dir":"file",n(null,t)}))},symlinkTypeSync:function(e,t){let n;if(t)return t;try{n=kt.lstatSync(e)}catch{return"file"}return n&&n.isDirectory()?"dir":"file"}};const Nt=M.fromCallback,Rt=e,Tt=T,Mt=Fe.mkdirs,Lt=Fe.mkdirsSync,jt=It.symlinkPaths,$t=It.symlinkPathsSync,Ht=xt.symlinkType,Gt=xt.symlinkTypeSync,Vt=ve.pathExists,{areIdentical:Ut}=Ne;function Jt(e,t,n,r){jt(e,t,((u,o)=>{if(u)return r(u);e=o.toDst,Ht(o.toCwd,n,((n,u)=>{if(n)return r(n);const o=Rt.dirname(t);Vt(o,((n,i)=>n?r(n):i?Tt.symlink(e,t,u,r):void Mt(o,(n=>{if(n)return r(n);Tt.symlink(e,t,u,r)}))))}))}))}var Wt={createSymlink:Nt((function(e,t,n,r){r="function"==typeof n?n:r,n="function"!=typeof n&&n,Tt.lstat(t,((u,o)=>{!u&&o.isSymbolicLink()?Promise.all([Tt.stat(e),Tt.stat(t)]).then((([u,o])=>{if(Ut(u,o))return r(null);Jt(e,t,n,r)})):Jt(e,t,n,r)}))})),createSymlinkSync:function(e,t,n){let r;try{r=Tt.lstatSync(t)}catch{}if(r&&r.isSymbolicLink()){const n=Tt.statSync(e),r=Tt.statSync(t);if(Ut(n,r))return}const u=$t(e,t);e=u.toDst,n=Gt(u.toCwd,n);const o=Rt.dirname(t);return Tt.existsSync(o)||Lt(o),Tt.symlinkSync(e,t,n)}};const{createFile:zt,createFileSync:Kt}=Ft,{createLink:qt,createLinkSync:Yt}=_t,{createSymlink:Zt,createSymlinkSync:Xt}=Wt;var Qt={createFile:zt,createFileSync:Kt,ensureFile:zt,ensureFileSync:Kt,createLink:qt,createLinkSync:Yt,ensureLink:qt,ensureLinkSync:Yt,createSymlink:Zt,createSymlinkSync:Xt,ensureSymlink:Zt,ensureSymlinkSync:Xt};var en={stringify:function(e,{EOL:t="\n",finalEOL:n=!0,replacer:r=null,spaces:u}={}){const o=n?t:"";return JSON.stringify(e,r,u).replace(/\n/g,t)+o},stripBom:function(e){return Buffer.isBuffer(e)&&(e=e.toString("utf8")),e.replace(/^\uFEFF/,"")}};let tn;try{tn=oe}catch(e){tn=n}const nn=M,{stringify:rn,stripBom:un}=en;const on=nn.fromPromise((async function(e,t={}){"string"==typeof t&&(t={encoding:t});const n=t.fs||tn,r=!("throws"in t)||t.throws;let u,o=await nn.fromCallback(n.readFile)(e,t);o=un(o);try{u=JSON.parse(o,t?t.reviver:null)}catch(t){if(r)throw t.message=`${e}: ${t.message}`,t;return null}return u}));const sn=nn.fromPromise((async function(e,t,n={}){const r=n.fs||tn,u=rn(t,n);await nn.fromCallback(r.writeFile)(e,u,n)}));const cn={readFile:on,readFileSync:function(e,t={}){"string"==typeof t&&(t={encoding:t});const n=t.fs||tn,r=!("throws"in t)||t.throws;try{let r=n.readFileSync(e,t);return r=un(r),JSON.parse(r,t.reviver)}catch(t){if(r)throw t.message=`${e}: ${t.message}`,t;return null}},writeFile:sn,writeFileSync:function(e,t,n={}){const r=n.fs||tn,u=rn(t,n);return r.writeFileSync(e,u,n)}};var an={readJson:cn.readFile,readJsonSync:cn.readFileSync,writeJson:cn.writeFile,writeJsonSync:cn.writeFileSync};const ln=M.fromCallback,fn=oe,dn=e,Dn=Fe,pn=ve.pathExists;var hn={outputFile:ln((function(e,t,n,r){"function"==typeof n&&(r=n,n="utf8");const u=dn.dirname(e);pn(u,((o,i)=>o?r(o):i?fn.writeFile(e,t,n,r):void Dn.mkdirs(u,(u=>{if(u)return r(u);fn.writeFile(e,t,n,r)}))))})),outputFileSync:function(e,...t){const n=dn.dirname(e);if(fn.existsSync(n))return fn.writeFileSync(e,...t);Dn.mkdirsSync(n),fn.writeFileSync(e,...t)}};const{stringify:En}=en,{outputFile:mn}=hn;var yn=async function(e,t,n={}){const r=En(t,n);await mn(e,r,n)};const{stringify:Cn}=en,{outputFileSync:Fn}=hn;var gn=function(e,t,n){const r=Cn(t,n);Fn(e,r,n)};const An=M.fromPromise,vn=an;vn.outputJson=An(yn),vn.outputJsonSync=gn,vn.outputJSON=vn.outputJson,vn.outputJSONSync=vn.outputJsonSync,vn.writeJSON=vn.writeJson,vn.writeJSONSync=vn.writeJsonSync,vn.readJSON=vn.readJson,vn.readJSONSync=vn.readJsonSync;var Sn=vn;const wn=oe,On=e,_n=ot.copy,bn=st.remove,Bn=Fe.mkdirp,Pn=ve.pathExists,In=Ne;function kn(e,t,n,r,u){return r?xn(e,t,n,u):n?bn(t,(r=>r?u(r):xn(e,t,n,u))):void Pn(t,((r,o)=>r?u(r):o?u(new Error("dest already exists.")):xn(e,t,n,u)))}function xn(e,t,n,r){wn.rename(e,t,(u=>u?"EXDEV"!==u.code?r(u):function(e,t,n,r){const u={overwrite:n,errorOnExist:!0};_n(e,t,u,(t=>t?r(t):bn(e,r)))}(e,t,n,r):r()))}var Nn=function(e,t,n,r){"function"==typeof n&&(r=n,n={});const u=(n=n||{}).overwrite||n.clobber||!1;In.checkPaths(e,t,"move",n,((n,o)=>{if(n)return r(n);const{srcStat:i,isChangingCase:s=!1}=o;In.checkParentPaths(e,i,t,"move",(n=>n?r(n):function(e){const t=On.dirname(e);return On.parse(t).root===t}(t)?kn(e,t,u,s,r):void Bn(On.dirname(t),(n=>n?r(n):kn(e,t,u,s,r)))))}))};const Rn=oe,Tn=e,Mn=ot.copySync,Ln=st.removeSync,jn=Fe.mkdirpSync,$n=Ne;function Hn(e,t,n){try{Rn.renameSync(e,t)}catch(r){if("EXDEV"!==r.code)throw r;return function(e,t,n){const r={overwrite:n,errorOnExist:!0};return Mn(e,t,r),Ln(e)}(e,t,n)}}var Gn=function(e,t,n){const r=(n=n||{}).overwrite||n.clobber||!1,{srcStat:u,isChangingCase:o=!1}=$n.checkPathsSync(e,t,"move",n);return $n.checkParentPathsSync(e,u,t,"move"),function(e){const t=Tn.dirname(e);return Tn.parse(t).root===t}(t)||jn(Tn.dirname(t)),function(e,t,n,r){if(r)return Hn(e,t,n);if(n)return Ln(t),Hn(e,t,n);if(Rn.existsSync(t))throw new Error("dest already exists.");return Hn(e,t,n)}(e,t,r,o)};var Vn,Un,Jn,Wn,zn,Kn={move:(0,M.fromCallback)(Nn),moveSync:Gn},qn={...T,...ot,...ht,...Qt,...Sn,...Fe,...Kn,...hn,...ve,...st},Yn={},Zn={exports:{}},Xn={exports:{}};function Qn(){if(Un)return Vn;Un=1;var e=1e3,t=60*e,n=60*t,r=24*n,u=7*r,o=365.25*r;function i(e,t,n,r){var u=t>=1.5*n;return Math.round(e/n)+" "+r+(u?"s":"")}return Vn=function(s,c){c=c||{};var a=typeof s;if("string"===a&&s.length>0)return function(i){if((i=String(i)).length>100)return;var s=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(i);if(!s)return;var c=parseFloat(s[1]);switch((s[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return c*o;case"weeks":case"week":case"w":return c*u;case"days":case"day":case"d":return c*r;case"hours":case"hour":case"hrs":case"hr":case"h":return c*n;case"minutes":case"minute":case"mins":case"min":case"m":return c*t;case"seconds":case"second":case"secs":case"sec":case"s":return c*e;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}(s);if("number"===a&&isFinite(s))return c.long?function(u){var o=Math.abs(u);if(o>=r)return i(u,o,r,"day");if(o>=n)return i(u,o,n,"hour");if(o>=t)return i(u,o,t,"minute");if(o>=e)return i(u,o,e,"second");return u+" ms"}(s):function(u){var o=Math.abs(u);if(o>=r)return Math.round(u/r)+"d";if(o>=n)return Math.round(u/n)+"h";if(o>=t)return Math.round(u/t)+"m";if(o>=e)return Math.round(u/e)+"s";return u+"ms"}(s);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(s))}}function er(){if(Wn)return Jn;return Wn=1,Jn=function(e){function t(e){let r,u,o,i=null;function s(...e){if(!s.enabled)return;const n=s,u=Number(new Date),o=u-(r||u);n.diff=o,n.prev=r,n.curr=u,r=u,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((r,u)=>{if("%%"===r)return"%";i++;const o=t.formatters[u];if("function"==typeof o){const t=e[i];r=o.call(n,t),e.splice(i,1),i--}return r})),t.formatArgs.call(n,e);(n.log||t.log).apply(n,e)}return s.namespace=e,s.useColors=t.useColors(),s.color=t.selectColor(e),s.extend=n,s.destroy=t.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==i?i:(u!==t.namespaces&&(u=t.namespaces,o=t.enabled(e)),o),set:e=>{i=e}}),"function"==typeof t.init&&t.init(s),s}function n(e,n){const r=t(this.namespace+(void 0===n?":":n)+e);return r.log=this.log,r}function r(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){if(e instanceof Error)return e.stack||e.message;return e},t.disable=function(){const e=[...t.names.map(r),...t.skips.map(r).map((e=>"-"+e))].join(",");return t.enable(""),e},t.enable=function(e){let n;t.save(e),t.namespaces=e,t.names=[],t.skips=[];const r=("string"==typeof e?e:"").split(/[\s,]+/),u=r.length;for(n=0;n<u;n++)r[n]&&("-"===(e=r[n].replace(/\*/g,".*?"))[0]?t.skips.push(new RegExp("^"+e.slice(1)+"$")):t.names.push(new RegExp("^"+e+"$")))},t.enabled=function(e){if("*"===e[e.length-1])return!0;let n,r;for(n=0,r=t.skips.length;n<r;n++)if(t.skips[n].test(e))return!1;for(n=0,r=t.names.length;n<r;n++)if(t.names[n].test(e))return!0;return!1},t.humanize=Qn(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{t[n]=e[n]})),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let n=0;for(let t=0;t<e.length;t++)n=(n<<5)-n+e.charCodeAt(t),n|=0;return t.colors[Math.abs(n)%t.colors.length]},t.enable(t.load()),t},Jn}var tr,nr,rr,ur,or,ir,sr={exports:{}};function cr(){return nr||(nr=1,tr=(e,t)=>{t=t||process.argv;const n=e.startsWith("-")?"":1===e.length?"-":"--",r=t.indexOf(n+e),u=t.indexOf("--");return-1!==r&&(-1===u||r<u)}),tr}function ar(){if(ur)return rr;ur=1;const e=t,n=cr(),r=process.env;let u;function o(t){const o=function(t){if(!1===u)return 0;if(n("color=16m")||n("color=full")||n("color=truecolor"))return 3;if(n("color=256"))return 2;if(t&&!t.isTTY&&!0!==u)return 0;const o=u?1:0;if("win32"===process.platform){const t=e.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(t[0])>=10&&Number(t[2])>=10586?Number(t[2])>=14931?3:2:1}if("CI"in r)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some((e=>e in r))||"codeship"===r.CI_NAME?1:o;if("TEAMCITY_VERSION"in r)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(r.TEAMCITY_VERSION)?1:0;if("truecolor"===r.COLORTERM)return 3;if("TERM_PROGRAM"in r){const e=parseInt((r.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(r.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(r.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(r.TERM)||"COLORTERM"in r?1:(r.TERM,o)}(t);return function(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}(o)}return n("no-color")||n("no-colors")||n("color=false")?u=!1:(n("color")||n("colors")||n("color=true")||n("color=always"))&&(u=!0),"FORCE_COLOR"in r&&(u=0===r.FORCE_COLOR.length||0!==parseInt(r.FORCE_COLOR,10)),rr={supportsColor:o,stdout:o(process.stdout),stderr:o(process.stderr)}}function lr(){return or||(or=1,function(e,t){const n=a,r=i;t.init=function(e){e.inspectOpts={};const n=Object.keys(t.inspectOpts);for(let r=0;r<n.length;r++)e.inspectOpts[n[r]]=t.inspectOpts[n[r]]},t.log=function(...e){return process.stderr.write(r.format(...e)+"\n")},t.formatArgs=function(n){const{namespace:r,useColors:u}=this;if(u){const t=this.color,u="[3"+(t<8?t:"8;5;"+t),o=`  ${u};1m${r} [0m`;n[0]=o+n[0].split("\n").join("\n"+o),n.push(u+"m+"+e.exports.humanize(this.diff)+"[0m")}else n[0]=function(){if(t.inspectOpts.hideDate)return"";return(new Date).toISOString()+" "}()+r+" "+n[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?Boolean(t.inspectOpts.colors):n.isatty(process.stderr.fd)},t.destroy=r.deprecate((()=>{}),"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{const e=ar();e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter((e=>/^debug_/i.test(e))).reduce(((e,t)=>{const n=t.substring(6).toLowerCase().replace(/_([a-z])/g,((e,t)=>t.toUpperCase()));let r=process.env[t];return r=!!/^(yes|on|true|enabled)$/i.test(r)||!/^(no|off|false|disabled)$/i.test(r)&&("null"===r?null:Number(r)),e[n]=r,e}),{}),e.exports=er()(t);const{formatters:u}=e.exports;u.o=function(e){return this.inspectOpts.colors=this.useColors,r.inspect(e,this.inspectOpts).split("\n").map((e=>e.trim())).join(" ")},u.O=function(e){return this.inspectOpts.colors=this.useColors,r.inspect(e,this.inspectOpts)}}(sr,sr.exports)),sr.exports}ir=Zn,"undefined"==typeof process||"renderer"===process.type||!0===process.browser||process.__nwjs?ir.exports=(zn||(zn=1,function(e,t){t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let r=0,u=0;t[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(r++,"%c"===e&&(u=r))})),t.splice(u,0,n)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=er()(t);const{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(Xn,Xn.exports)),Xn.exports):ir.exports=lr();var fr=function(e){return(e=e||{}).circles?function(e){var t=[],n=[];return e.proto?function e(u){if("object"!=typeof u||null===u)return u;if(u instanceof Date)return new Date(u);if(Array.isArray(u))return r(u,e);if(u instanceof Map)return new Map(r(Array.from(u),e));if(u instanceof Set)return new Set(r(Array.from(u),e));var o={};for(var i in t.push(u),n.push(o),u){var s=u[i];if("object"!=typeof s||null===s)o[i]=s;else if(s instanceof Date)o[i]=new Date(s);else if(s instanceof Map)o[i]=new Map(r(Array.from(s),e));else if(s instanceof Set)o[i]=new Set(r(Array.from(s),e));else if(ArrayBuffer.isView(s))o[i]=dr(s);else{var c=t.indexOf(s);o[i]=-1!==c?n[c]:e(s)}}return t.pop(),n.pop(),o}:function e(u){if("object"!=typeof u||null===u)return u;if(u instanceof Date)return new Date(u);if(Array.isArray(u))return r(u,e);if(u instanceof Map)return new Map(r(Array.from(u),e));if(u instanceof Set)return new Set(r(Array.from(u),e));var o={};for(var i in t.push(u),n.push(o),u)if(!1!==Object.hasOwnProperty.call(u,i)){var s=u[i];if("object"!=typeof s||null===s)o[i]=s;else if(s instanceof Date)o[i]=new Date(s);else if(s instanceof Map)o[i]=new Map(r(Array.from(s),e));else if(s instanceof Set)o[i]=new Set(r(Array.from(s),e));else if(ArrayBuffer.isView(s))o[i]=dr(s);else{var c=t.indexOf(s);o[i]=-1!==c?n[c]:e(s)}}return t.pop(),n.pop(),o};function r(e,r){for(var u=Object.keys(e),o=new Array(u.length),i=0;i<u.length;i++){var s=u[i],c=e[s];if("object"!=typeof c||null===c)o[s]=c;else if(c instanceof Date)o[s]=new Date(c);else if(ArrayBuffer.isView(c))o[s]=dr(c);else{var a=t.indexOf(c);o[s]=-1!==a?n[a]:r(c)}}return o}}(e):e.proto?function e(n){if("object"!=typeof n||null===n)return n;if(n instanceof Date)return new Date(n);if(Array.isArray(n))return t(n,e);if(n instanceof Map)return new Map(t(Array.from(n),e));if(n instanceof Set)return new Set(t(Array.from(n),e));var r={};for(var u in n){var o=n[u];"object"!=typeof o||null===o?r[u]=o:o instanceof Date?r[u]=new Date(o):o instanceof Map?r[u]=new Map(t(Array.from(o),e)):o instanceof Set?r[u]=new Set(t(Array.from(o),e)):ArrayBuffer.isView(o)?r[u]=dr(o):r[u]=e(o)}return r}:n;function t(e,t){for(var n=Object.keys(e),r=new Array(n.length),u=0;u<n.length;u++){var o=n[u],i=e[o];"object"!=typeof i||null===i?r[o]=i:i instanceof Date?r[o]=new Date(i):ArrayBuffer.isView(i)?r[o]=dr(i):r[o]=t(i)}return r}function n(e){if("object"!=typeof e||null===e)return e;if(e instanceof Date)return new Date(e);if(Array.isArray(e))return t(e,n);if(e instanceof Map)return new Map(t(Array.from(e),n));if(e instanceof Set)return new Set(t(Array.from(e),n));var r={};for(var u in e)if(!1!==Object.hasOwnProperty.call(e,u)){var o=e[u];"object"!=typeof o||null===o?r[u]=o:o instanceof Date?r[u]=new Date(o):o instanceof Map?r[u]=new Map(t(Array.from(o),n)):o instanceof Set?r[u]=new Set(t(Array.from(o),n)):ArrayBuffer.isView(o)?r[u]=dr(o):r[u]=n(o)}return r}};function dr(e){return e instanceof Buffer?Buffer.from(e):new e.constructor(e.buffer.slice(),e.byteOffset,e.length)}const Dr=i,pr=Zn.exports("log4js:configuration"),hr=[],Er=[],mr=e=>!e,yr=e=>e&&"object"==typeof e&&!Array.isArray(e),Cr=(e,t,n)=>{(Array.isArray(t)?t:[t]).forEach((t=>{if(t)throw new Error(`Problem with log4js configuration: (${Dr.inspect(e,{depth:5})}) - ${n}`)}))};var Fr={configure:e=>{pr("New configuration to be validated: ",e),Cr(e,mr(yr(e)),"must be an object."),pr(`Calling pre-processing listeners (${hr.length})`),hr.forEach((t=>t(e))),pr("Configuration pre-processing finished."),pr(`Calling configuration listeners (${Er.length})`),Er.forEach((t=>t(e))),pr("Configuration finished.")},addListener:e=>{Er.push(e),pr(`Added listener, now ${Er.length} listeners`)},addPreProcessingListener:e=>{hr.push(e),pr(`Added pre-processing listener, now ${hr.length} listeners`)},throwExceptionIf:Cr,anObject:yr,anInteger:e=>e&&"number"==typeof e&&Number.isInteger(e),validIdentifier:e=>/^[A-Za-z][A-Za-z0-9_]*$/g.test(e),not:mr},gr={exports:{}};!function(e){function t(e,t){for(var n=e.toString();n.length<t;)n="0"+n;return n}function n(e){return t(e,2)}function r(r,u){"string"!=typeof r&&(u=r,r=e.exports.ISO8601_FORMAT),u||(u=e.exports.now());var o=n(u.getDate()),i=n(u.getMonth()+1),s=n(u.getFullYear()),c=n(s.substring(2,4)),a=r.indexOf("yyyy")>-1?s:c,l=n(u.getHours()),f=n(u.getMinutes()),d=n(u.getSeconds()),D=t(u.getMilliseconds(),3),p=function(e){var t=Math.abs(e),n=String(Math.floor(t/60)),r=String(t%60);return n=("0"+n).slice(-2),r=("0"+r).slice(-2),0===e?"Z":(e<0?"+":"-")+n+":"+r}(u.getTimezoneOffset());return r.replace(/dd/g,o).replace(/MM/g,i).replace(/y{1,4}/g,a).replace(/hh/g,l).replace(/mm/g,f).replace(/ss/g,d).replace(/SSS/g,D).replace(/O/g,p)}function u(e,t,n,r){e["set"+(r?"":"UTC")+t](n)}e.exports=r,e.exports.asString=r,e.exports.parse=function(t,n,r){if(!t)throw new Error("pattern must be supplied");return function(t,n,r){var o=t.indexOf("O")<0,i=!1,s=[{pattern:/y{1,4}/,regexp:"\\d{1,4}",fn:function(e,t){u(e,"FullYear",t,o)}},{pattern:/MM/,regexp:"\\d{1,2}",fn:function(e,t){u(e,"Month",t-1,o),e.getMonth()!==t-1&&(i=!0)}},{pattern:/dd/,regexp:"\\d{1,2}",fn:function(e,t){i&&u(e,"Month",e.getMonth()-1,o),u(e,"Date",t,o)}},{pattern:/hh/,regexp:"\\d{1,2}",fn:function(e,t){u(e,"Hours",t,o)}},{pattern:/mm/,regexp:"\\d\\d",fn:function(e,t){u(e,"Minutes",t,o)}},{pattern:/ss/,regexp:"\\d\\d",fn:function(e,t){u(e,"Seconds",t,o)}},{pattern:/SSS/,regexp:"\\d\\d\\d",fn:function(e,t){u(e,"Milliseconds",t,o)}},{pattern:/O/,regexp:"[+-]\\d{1,2}:?\\d{2}?|Z",fn:function(e,t){t="Z"===t?0:t.replace(":","");var n=Math.abs(t),r=(t>0?-1:1)*(n%100+60*Math.floor(n/100));e.setUTCMinutes(e.getUTCMinutes()+r)}}],c=s.reduce((function(e,t){return t.pattern.test(e.regexp)?(t.index=e.regexp.match(t.pattern).index,e.regexp=e.regexp.replace(t.pattern,"("+t.regexp+")")):t.index=-1,e}),{regexp:t,index:[]}),a=s.filter((function(e){return e.index>-1}));a.sort((function(e,t){return e.index-t.index}));var l=new RegExp(c.regexp).exec(n);if(l){var f=r||e.exports.now();return a.forEach((function(e,t){e.fn(f,l[t+1])})),f}throw new Error("String '"+n+"' could not be parsed as '"+t+"'")}(t,n,r)},e.exports.now=function(){return new Date},e.exports.ISO8601_FORMAT="yyyy-MM-ddThh:mm:ss.SSS",e.exports.ISO8601_WITH_TZ_OFFSET_FORMAT="yyyy-MM-ddThh:mm:ss.SSSO",e.exports.DATETIME_FORMAT="dd MM yyyy hh:mm:ss.SSS",e.exports.ABSOLUTETIME_FORMAT="hh:mm:ss.SSS"}(gr);const Ar=gr.exports,vr=t,Sr=i,wr=e,Or=l,_r=Zn.exports("log4js:layouts"),br={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[90,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[91,39],yellow:[33,39]};function Br(e){return e?`[${br[e][0]}m`:""}function Pr(e){return e?`[${br[e][1]}m`:""}function Ir(e,t){return n=Sr.format("[%s] [%s] %s - ",Ar.asString(e.startTime),e.level.toString(),e.categoryName),Br(r=t)+n+Pr(r);var n,r}function kr(e){return Ir(e)+Sr.format(...e.data)}function xr(e){return Ir(e,e.level.colour)+Sr.format(...e.data)}function Nr(e){return Sr.format(...e.data)}function Rr(e){return e.data[0]}function Tr(e,t){const n=/%(-?[0-9]+)?(\.?-?[0-9]+)?([[\]cdhmnprzxXyflosCMAF%])(\{([^}]+)\})?|([^%]+)/;function r(e){return e&&e.pid?e.pid.toString():process.pid.toString()}e=e||"%r %p %c - %m%n";const u={c:function(e,t){let n=e.categoryName;if(t){const e=parseInt(t,10),r=n.split(".");e<r.length&&(n=r.slice(r.length-e).join("."))}return n},d:function(e,t){let n=Ar.ISO8601_FORMAT;if(t)switch(n=t,n){case"ISO8601":case"ISO8601_FORMAT":n=Ar.ISO8601_FORMAT;break;case"ISO8601_WITH_TZ_OFFSET":case"ISO8601_WITH_TZ_OFFSET_FORMAT":n=Ar.ISO8601_WITH_TZ_OFFSET_FORMAT;break;case"ABSOLUTE":process.emitWarning("Pattern %d{ABSOLUTE} is deprecated in favor of %d{ABSOLUTETIME}. Please use %d{ABSOLUTETIME} instead.","DeprecationWarning","log4js-node-DEP0003"),_r("[log4js-node-DEP0003]","DEPRECATION: Pattern %d{ABSOLUTE} is deprecated and replaced by %d{ABSOLUTETIME}.");case"ABSOLUTETIME":case"ABSOLUTETIME_FORMAT":n=Ar.ABSOLUTETIME_FORMAT;break;case"DATE":process.emitWarning("Pattern %d{DATE} is deprecated due to the confusion it causes when used. Please use %d{DATETIME} instead.","DeprecationWarning","log4js-node-DEP0004"),_r("[log4js-node-DEP0004]","DEPRECATION: Pattern %d{DATE} is deprecated and replaced by %d{DATETIME}.");case"DATETIME":case"DATETIME_FORMAT":n=Ar.DATETIME_FORMAT}return Ar.asString(n,e.startTime)},h:function(){return vr.hostname().toString()},m:function(e){return Sr.format(...e.data)},n:function(){return vr.EOL},p:function(e){return e.level.toString()},r:function(e){return Ar.asString("hh:mm:ss",e.startTime)},"[":function(e){return Br(e.level.colour)},"]":function(e){return Pr(e.level.colour)},y:function(){return r()},z:r,"%":function(){return"%"},x:function(e,n){return void 0!==t[n]?"function"==typeof t[n]?t[n](e):t[n]:null},X:function(e,t){const n=e.context[t];return void 0!==n?"function"==typeof n?n(e):n:null},f:function(e,t){let n=e.fileName||"";if(n=function(e){const t="file://";return e.startsWith(t)&&("function"==typeof Or.fileURLToPath?e=Or.fileURLToPath(e):(e=wr.normalize(e.replace(new RegExp(`^${t}`),"")),"win32"===process.platform&&(e=e.startsWith("\\")?e.slice(1):wr.sep+wr.sep+e))),e}(n),t){const e=parseInt(t,10),r=n.split(wr.sep);r.length>e&&(n=r.slice(-e).join(wr.sep))}return n},l:function(e){return e.lineNumber?`${e.lineNumber}`:""},o:function(e){return e.columnNumber?`${e.columnNumber}`:""},s:function(e){return e.callStack||""},C:function(e){return e.className||""},M:function(e){return e.functionName||""},A:function(e){return e.functionAlias||""},F:function(e){return e.callerName||""}};function o(e,t,n){return u[e](t,n)}function i(e,t,n){let r=e;return r=function(e,t){let n;return e?(n=parseInt(e.slice(1),10),n>0?t.slice(0,n):t.slice(n)):t}(t,r),r=function(e,t){let n;if(e)if("-"===e.charAt(0))for(n=parseInt(e.slice(1),10);t.length<n;)t+=" ";else for(n=parseInt(e,10);t.length<n;)t=` ${t}`;return t}(n,r),r}return function(t){let r,u="",s=e;for(;null!==(r=n.exec(s));){const e=r[1],n=r[2],c=r[3],a=r[5],l=r[6];if(l)u+=l.toString();else{u+=i(o(c,t,a),n,e)}s=s.slice(r.index+r[0].length)}return u}}const Mr={messagePassThrough:()=>Nr,basic:()=>kr,colored:()=>xr,coloured:()=>xr,pattern:e=>Tr(e&&e.pattern,e&&e.tokens),dummy:()=>Rr};var Lr={basicLayout:kr,messagePassThroughLayout:Nr,patternLayout:Tr,colouredLayout:xr,coloredLayout:xr,dummyLayout:Rr,addLayout(e,t){Mr[e]=t},layout:(e,t)=>Mr[e]&&Mr[e](t)};const jr=Fr,$r=["white","grey","black","blue","cyan","green","magenta","red","yellow"];class Hr{constructor(e,t,n){this.level=e,this.levelStr=t,this.colour=n}toString(){return this.levelStr}static getLevel(e,t){return e?e instanceof Hr?e:(e instanceof Object&&e.levelStr&&(e=e.levelStr),Hr[e.toString().toUpperCase()]||t):t}static addLevels(e){if(e){Object.keys(e).forEach((t=>{const n=t.toUpperCase();Hr[n]=new Hr(e[t].value,n,e[t].colour);const r=Hr.levels.findIndex((e=>e.levelStr===n));r>-1?Hr.levels[r]=Hr[n]:Hr.levels.push(Hr[n])})),Hr.levels.sort(((e,t)=>e.level-t.level))}}isLessThanOrEqualTo(e){return"string"==typeof e&&(e=Hr.getLevel(e)),this.level<=e.level}isGreaterThanOrEqualTo(e){return"string"==typeof e&&(e=Hr.getLevel(e)),this.level>=e.level}isEqualTo(e){return"string"==typeof e&&(e=Hr.getLevel(e)),this.level===e.level}}Hr.levels=[],Hr.addLevels({ALL:{value:Number.MIN_VALUE,colour:"grey"},TRACE:{value:5e3,colour:"blue"},DEBUG:{value:1e4,colour:"cyan"},INFO:{value:2e4,colour:"green"},WARN:{value:3e4,colour:"yellow"},ERROR:{value:4e4,colour:"red"},FATAL:{value:5e4,colour:"magenta"},MARK:{value:9007199254740992,colour:"grey"},OFF:{value:Number.MAX_VALUE,colour:"grey"}}),jr.addListener((e=>{const t=e.levels;if(t){jr.throwExceptionIf(e,jr.not(jr.anObject(t)),"levels must be an object");Object.keys(t).forEach((n=>{jr.throwExceptionIf(e,jr.not(jr.validIdentifier(n)),`level name "${n}" is not a valid identifier (must start with a letter, only contain A-Z,a-z,0-9,_)`),jr.throwExceptionIf(e,jr.not(jr.anObject(t[n])),`level "${n}" must be an object`),jr.throwExceptionIf(e,jr.not(t[n].value),`level "${n}" must have a 'value' property`),jr.throwExceptionIf(e,jr.not(jr.anInteger(t[n].value)),`level "${n}".value must have an integer value`),jr.throwExceptionIf(e,jr.not(t[n].colour),`level "${n}" must have a 'colour' property`),jr.throwExceptionIf(e,jr.not($r.indexOf(t[n].colour)>-1),`level "${n}".colour must be one of ${$r.join(", ")}`)}))}})),jr.addListener((e=>{Hr.addLevels(e.levels)}));var Gr=Hr,Vr={exports:{}},Ur={};const{parse:Jr,stringify:Wr}=JSON,{keys:zr}=Object,Kr=String,qr="string",Yr={},Zr="object",Xr=(e,t)=>t,Qr=e=>e instanceof Kr?Kr(e):e,eu=(e,t)=>typeof t===qr?new Kr(t):t,tu=(e,t,n,r)=>{const u=[];for(let o=zr(n),{length:i}=o,s=0;s<i;s++){const i=o[s],c=n[i];if(c instanceof Kr){const o=e[c];typeof o!==Zr||t.has(o)?n[i]=r.call(n,i,o):(t.add(o),n[i]=Yr,u.push({k:i,a:[e,t,o,r]}))}else n[i]!==Yr&&(n[i]=r.call(n,i,c))}for(let{length:e}=u,t=0;t<e;t++){const{k:e,a:o}=u[t];n[e]=r.call(n,e,tu.apply(null,o))}return n},nu=(e,t,n)=>{const r=Kr(t.push(n)-1);return e.set(n,r),r},ru=(e,t)=>{const n=Jr(e,eu).map(Qr),r=n[0],u=t||Xr,o=typeof r===Zr&&r?tu(n,new Set,r,u):r;return u.call({"":o},"",o)};Ur.parse=ru;const uu=(e,t,n)=>{const r=t&&typeof t===Zr?(e,n)=>""===e||-1<t.indexOf(e)?n:void 0:t||Xr,u=new Map,o=[],i=[];let s=+nu(u,o,r.call({"":e},"",e)),c=!s;for(;s<o.length;)c=!0,i[s]=Wr(o[s++],a,n);return"["+i.join(",")+"]";function a(e,t){if(c)return c=!c,t;const n=r.call(this,e,t);switch(typeof n){case Zr:if(null===n)return n;case qr:return u.get(n)||nu(u,o,n)}return n}};Ur.stringify=uu;Ur.toJSON=e=>Jr(uu(e));Ur.fromJSON=e=>ru(Wr(e));const ou=Ur,iu=Gr;const su=new class{constructor(){const e={__LOG4JS_undefined__:void 0,__LOG4JS_NaN__:Number("abc"),__LOG4JS_Infinity__:1/0,"__LOG4JS_-Infinity__":-1/0};this.deMap=e,this.serMap={},Object.keys(this.deMap).forEach((e=>{const t=this.deMap[e];this.serMap[t]=e}))}canSerialise(e){return"string"!=typeof e&&e in this.serMap}serialise(e){return this.canSerialise(e)?this.serMap[e]:e}canDeserialise(e){return e in this.deMap}deserialise(e){return this.canDeserialise(e)?this.deMap[e]:e}};let cu=class{constructor(e,t,n,r,u,o){if(this.startTime=new Date,this.categoryName=e,this.data=n,this.level=t,this.context=Object.assign({},r),this.pid=process.pid,this.error=o,void 0!==u){if(!u||"object"!=typeof u||Array.isArray(u))throw new TypeError("Invalid location type passed to LoggingEvent constructor");this.constructor._getLocationKeys().forEach((e=>{void 0!==u[e]&&(this[e]=u[e])}))}}static _getLocationKeys(){return["fileName","lineNumber","columnNumber","callStack","className","functionName","functionAlias","callerName"]}serialise(){return ou.stringify(this,((e,t)=>(t instanceof Error&&(t=Object.assign({message:t.message,stack:t.stack},t)),su.serialise(t))))}static deserialise(e){let t;try{const n=ou.parse(e,((e,t)=>{if(t&&t.message&&t.stack){const e=new Error(t);Object.keys(t).forEach((n=>{e[n]=t[n]})),t=e}return su.deserialise(t)}));this._getLocationKeys().forEach((e=>{void 0!==n[e]&&(n.location||(n.location={}),n.location[e]=n[e])})),t=new cu(n.categoryName,iu.getLevel(n.level.levelStr),n.data,n.context,n.location,n.error),t.startTime=new Date(n.startTime),t.pid=n.pid,n.cluster&&(t.cluster=n.cluster)}catch(n){t=new cu("log4js",iu.ERROR,["Unable to parse log:",e,"because: ",n])}return t}};var au=cu;const lu=Zn.exports("log4js:clustering"),fu=au,du=Fr;let Du=!1,pu=null;try{pu=require("cluster")}catch(e){lu("cluster module not present"),Du=!0}const hu=[];let Eu=!1,mu="NODE_APP_INSTANCE";const yu=()=>Eu&&"0"===process.env[mu],Cu=()=>Du||pu&&pu.isMaster||yu(),Fu=e=>{hu.forEach((t=>t(e)))},gu=(e,t)=>{if(lu("cluster message received from worker ",e,": ",t),e.topic&&e.data&&(t=e,e=void 0),t&&t.topic&&"log4js:message"===t.topic){lu("received message: ",t.data);const e=fu.deserialise(t.data);Fu(e)}};Du||du.addListener((e=>{hu.length=0,({pm2:Eu,disableClustering:Du,pm2InstanceVar:mu="NODE_APP_INSTANCE"}=e),lu(`clustering disabled ? ${Du}`),lu(`cluster.isMaster ? ${pu&&pu.isMaster}`),lu(`pm2 enabled ? ${Eu}`),lu(`pm2InstanceVar = ${mu}`),lu(`process.env[${mu}] = ${process.env[mu]}`),Eu&&process.removeListener("message",gu),pu&&pu.removeListener&&pu.removeListener("message",gu),Du||e.disableClustering?lu("Not listening for cluster messages, because clustering disabled."):yu()?(lu("listening for PM2 broadcast messages"),process.on("message",gu)):pu&&pu.isMaster?(lu("listening for cluster messages"),pu.on("message",gu)):lu("not listening for messages, because we are not a master process")}));var Au={onlyOnMaster:(e,t)=>Cu()?e():t,isMaster:Cu,send:e=>{Cu()?Fu(e):(Eu||(e.cluster={workerId:pu.worker.id,worker:process.pid}),process.send({topic:"log4js:message",data:e.serialise()}))},onMessage:e=>{hu.push(e)}},vu={};function Su(e){if("number"==typeof e&&Number.isInteger(e))return e;const t={K:1024,M:1048576,G:1073741824},n=Object.keys(t),r=e.slice(-1).toLocaleUpperCase(),u=e.slice(0,-1).trim();if(n.indexOf(r)<0||!Number.isInteger(Number(u)))throw Error(`maxLogSize: "${e}" is invalid`);return u*t[r]}function wu(e){return function(e,t){const n=Object.assign({},t);return Object.keys(e).forEach((r=>{n[r]&&(n[r]=e[r](t[r]))})),n}({maxLogSize:Su},e)}const Ou={dateFile:wu,file:wu,fileSync:wu};vu.modifyConfig=e=>Ou[e.type]?Ou[e.type](e):e;var _u={};const bu=console.log.bind(console);_u.configure=function(e,t){let n=t.colouredLayout;return e.layout&&(n=t.layout(e.layout.type,e.layout)),function(e,t){return n=>{bu(e(n,t))}}(n,e.timezoneOffset)};var Bu={};Bu.configure=function(e,t){let n=t.colouredLayout;return e.layout&&(n=t.layout(e.layout.type,e.layout)),function(e,t){return n=>{process.stdout.write(`${e(n,t)}\n`)}}(n,e.timezoneOffset)};var Pu={};Pu.configure=function(e,t){let n=t.colouredLayout;return e.layout&&(n=t.layout(e.layout.type,e.layout)),function(e,t){return n=>{process.stderr.write(`${e(n,t)}\n`)}}(n,e.timezoneOffset)};var Iu={};Iu.configure=function(e,t,n,r){const u=n(e.appender);return function(e,t,n,r){const u=r.getLevel(e),o=r.getLevel(t,r.FATAL);return e=>{const t=e.level;u.isLessThanOrEqualTo(t)&&o.isGreaterThanOrEqualTo(t)&&n(e)}}(e.level,e.maxLevel,u,r)};var ku={};const xu=Zn.exports("log4js:categoryFilter");ku.configure=function(e,t,n){const r=n(e.appender);return function(e,t){return"string"==typeof e&&(e=[e]),n=>{xu(`Checking ${n.categoryName} against ${e}`),-1===e.indexOf(n.categoryName)&&(xu("Not excluded, sending to appender"),t(n))}}(e.exclude,r)};var Nu={};const Ru=Zn.exports("log4js:noLogFilter");Nu.configure=function(e,t,n){const r=n(e.appender);return function(e,t){return n=>{Ru(`Checking data: ${n.data} against filters: ${e}`),"string"==typeof e&&(e=[e]),e=e.filter((e=>null!=e&&""!==e));const r=new RegExp(e.join("|"),"i");(0===e.length||n.data.findIndex((e=>r.test(e)))<0)&&(Ru("Not excluded, sending to appender"),t(n))}}(e.exclude,r)};var Tu={},Mu={exports:{}},Lu={},ju={fromCallback:function(e){return Object.defineProperty((function(){if("function"!=typeof arguments[arguments.length-1])return new Promise(((t,n)=>{arguments[arguments.length]=(e,r)=>{if(e)return n(e);t(r)},arguments.length++,e.apply(this,arguments)}));e.apply(this,arguments)}),"name",{value:e.name})},fromPromise:function(e){return Object.defineProperty((function(){const t=arguments[arguments.length-1];if("function"!=typeof t)return e.apply(this,arguments);e.apply(this,arguments).then((e=>t(null,e)),t)}),"name",{value:e.name})}};!function(e){const t=ju.fromCallback,n=oe,r=["access","appendFile","chmod","chown","close","copyFile","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchown","lchmod","link","lstat","mkdir","mkdtemp","open","readFile","readdir","readlink","realpath","rename","rmdir","stat","symlink","truncate","unlink","utimes","writeFile"].filter((e=>"function"==typeof n[e]));Object.keys(n).forEach((t=>{"promises"!==t&&(e[t]=n[t])})),r.forEach((r=>{e[r]=t(n[r])})),e.exists=function(e,t){return"function"==typeof t?n.exists(e,t):new Promise((t=>n.exists(e,t)))},e.read=function(e,t,r,u,o,i){return"function"==typeof i?n.read(e,t,r,u,o,i):new Promise(((i,s)=>{n.read(e,t,r,u,o,((e,t,n)=>{if(e)return s(e);i({bytesRead:t,buffer:n})}))}))},e.write=function(e,t,...r){return"function"==typeof r[r.length-1]?n.write(e,t,...r):new Promise(((u,o)=>{n.write(e,t,...r,((e,t,n)=>{if(e)return o(e);u({bytesWritten:t,buffer:n})}))}))},"function"==typeof n.realpath.native&&(e.realpath.native=t(n.realpath.native))}(Lu);const $u=e;function Hu(e){return(e=$u.normalize($u.resolve(e)).split($u.sep)).length>0?e[0]:null}const Gu=/[<>:"|?*]/;var Vu=function(e){const t=Hu(e);return e=e.replace(t,""),Gu.test(e)};const Uu=oe,Ju=e,Wu=Vu,zu=parseInt("0777",8);var Ku=function e(t,n,r,u){if("function"==typeof n?(r=n,n={}):n&&"object"==typeof n||(n={mode:n}),"win32"===process.platform&&Wu(t)){const e=new Error(t+" contains invalid WIN32 path characters.");return e.code="EINVAL",r(e)}let o=n.mode;const i=n.fs||Uu;void 0===o&&(o=zu&~process.umask()),u||(u=null),r=r||function(){},t=Ju.resolve(t),i.mkdir(t,o,(o=>{if(!o)return r(null,u=u||t);if("ENOENT"===o.code){if(Ju.dirname(t)===t)return r(o);e(Ju.dirname(t),n,((u,o)=>{u?r(u,o):e(t,n,r,o)}))}else i.stat(t,((e,t)=>{e||!t.isDirectory()?r(o,u):r(null,u)}))}))};const qu=oe,Yu=e,Zu=Vu,Xu=parseInt("0777",8);var Qu=function e(t,n,r){n&&"object"==typeof n||(n={mode:n});let u=n.mode;const o=n.fs||qu;if("win32"===process.platform&&Zu(t)){const e=new Error(t+" contains invalid WIN32 path characters.");throw e.code="EINVAL",e}void 0===u&&(u=Xu&~process.umask()),r||(r=null),t=Yu.resolve(t);try{o.mkdirSync(t,u),r=r||t}catch(u){if("ENOENT"===u.code){if(Yu.dirname(t)===t)throw u;r=e(Yu.dirname(t),n,r),e(t,n,r)}else{let e;try{e=o.statSync(t)}catch(e){throw u}if(!e.isDirectory())throw u}}return r};const eo=(0,ju.fromCallback)(Ku);var to={mkdirs:eo,mkdirsSync:Qu,mkdirp:eo,mkdirpSync:Qu,ensureDir:eo,ensureDirSync:Qu};const no=oe;var ro=function(e,t,n,r){no.open(e,"r+",((e,u)=>{if(e)return r(e);no.futimes(u,t,n,(e=>{no.close(u,(t=>{r&&r(e||t)}))}))}))},uo=function(e,t,n){const r=no.openSync(e,"r+");return no.futimesSync(r,t,n),no.closeSync(r)};const oo=oe,io=e,so=10,co=5,ao=0,lo=process.versions.node.split("."),fo=Number.parseInt(lo[0],10),Do=Number.parseInt(lo[1],10),po=Number.parseInt(lo[2],10);function ho(){if(fo>so)return!0;if(fo===so){if(Do>co)return!0;if(Do===co&&po>=ao)return!0}return!1}function Eo(e,t){const n=io.resolve(e).split(io.sep).filter((e=>e)),r=io.resolve(t).split(io.sep).filter((e=>e));return n.reduce(((e,t,n)=>e&&r[n]===t),!0)}function mo(e,t,n){return`Cannot ${n} '${e}' to a subdirectory of itself, '${t}'.`}var yo,Co,Fo={checkPaths:function(e,t,n,r){!function(e,t,n){ho()?oo.stat(e,{bigint:!0},((e,r)=>{if(e)return n(e);oo.stat(t,{bigint:!0},((e,t)=>e?"ENOENT"===e.code?n(null,{srcStat:r,destStat:null}):n(e):n(null,{srcStat:r,destStat:t})))})):oo.stat(e,((e,r)=>{if(e)return n(e);oo.stat(t,((e,t)=>e?"ENOENT"===e.code?n(null,{srcStat:r,destStat:null}):n(e):n(null,{srcStat:r,destStat:t})))}))}(e,t,((u,o)=>{if(u)return r(u);const{srcStat:i,destStat:s}=o;return s&&s.ino&&s.dev&&s.ino===i.ino&&s.dev===i.dev?r(new Error("Source and destination must not be the same.")):i.isDirectory()&&Eo(e,t)?r(new Error(mo(e,t,n))):r(null,{srcStat:i,destStat:s})}))},checkPathsSync:function(e,t,n){const{srcStat:r,destStat:u}=function(e,t){let n,r;n=ho()?oo.statSync(e,{bigint:!0}):oo.statSync(e);try{r=ho()?oo.statSync(t,{bigint:!0}):oo.statSync(t)}catch(e){if("ENOENT"===e.code)return{srcStat:n,destStat:null};throw e}return{srcStat:n,destStat:r}}(e,t);if(u&&u.ino&&u.dev&&u.ino===r.ino&&u.dev===r.dev)throw new Error("Source and destination must not be the same.");if(r.isDirectory()&&Eo(e,t))throw new Error(mo(e,t,n));return{srcStat:r,destStat:u}},checkParentPaths:function e(t,n,r,u,o){const i=io.resolve(io.dirname(t)),s=io.resolve(io.dirname(r));if(s===i||s===io.parse(s).root)return o();ho()?oo.stat(s,{bigint:!0},((i,c)=>i?"ENOENT"===i.code?o():o(i):c.ino&&c.dev&&c.ino===n.ino&&c.dev===n.dev?o(new Error(mo(t,r,u))):e(t,n,s,u,o))):oo.stat(s,((i,c)=>i?"ENOENT"===i.code?o():o(i):c.ino&&c.dev&&c.ino===n.ino&&c.dev===n.dev?o(new Error(mo(t,r,u))):e(t,n,s,u,o)))},checkParentPathsSync:function e(t,n,r,u){const o=io.resolve(io.dirname(t)),i=io.resolve(io.dirname(r));if(i===o||i===io.parse(i).root)return;let s;try{s=ho()?oo.statSync(i,{bigint:!0}):oo.statSync(i)}catch(e){if("ENOENT"===e.code)return;throw e}if(s.ino&&s.dev&&s.ino===n.ino&&s.dev===n.dev)throw new Error(mo(t,r,u));return e(t,n,i,u)},isSrcSubdir:Eo};const go=oe,Ao=e,vo=to.mkdirsSync,So=uo,wo=Fo;function Oo(e,t,n,r){if(!r.filter||r.filter(t,n))return function(e,t,n,r){const u=r.dereference?go.statSync:go.lstatSync,o=u(t);if(o.isDirectory())return function(e,t,n,r,u){if(!t)return function(e,t,n,r){return go.mkdirSync(n),bo(t,n,r),go.chmodSync(n,e.mode)}(e,n,r,u);if(t&&!t.isDirectory())throw new Error(`Cannot overwrite non-directory '${r}' with directory '${n}'.`);return bo(n,r,u)}(o,e,t,n,r);if(o.isFile()||o.isCharacterDevice()||o.isBlockDevice())return function(e,t,n,r,u){return t?function(e,t,n,r){if(r.overwrite)return go.unlinkSync(n),_o(e,t,n,r);if(r.errorOnExist)throw new Error(`'${n}' already exists`)}(e,n,r,u):_o(e,n,r,u)}(o,e,t,n,r);if(o.isSymbolicLink())return function(e,t,n,r){let u=go.readlinkSync(t);r.dereference&&(u=Ao.resolve(process.cwd(),u));if(e){let e;try{e=go.readlinkSync(n)}catch(e){if("EINVAL"===e.code||"UNKNOWN"===e.code)return go.symlinkSync(u,n);throw e}if(r.dereference&&(e=Ao.resolve(process.cwd(),e)),wo.isSrcSubdir(u,e))throw new Error(`Cannot copy '${u}' to a subdirectory of itself, '${e}'.`);if(go.statSync(n).isDirectory()&&wo.isSrcSubdir(e,u))throw new Error(`Cannot overwrite '${e}' with '${u}'.`);return function(e,t){return go.unlinkSync(t),go.symlinkSync(e,t)}(u,n)}return go.symlinkSync(u,n)}(e,t,n,r)}(e,t,n,r)}function _o(e,t,n,r){return"function"==typeof go.copyFileSync?(go.copyFileSync(t,n),go.chmodSync(n,e.mode),r.preserveTimestamps?So(n,e.atime,e.mtime):void 0):function(e,t,n,r){const u=65536,o=(Co?yo:(Co=1,yo=function(e){if("function"==typeof Buffer.allocUnsafe)try{return Buffer.allocUnsafe(e)}catch(t){return new Buffer(e)}return new Buffer(e)}))(u),i=go.openSync(t,"r"),s=go.openSync(n,"w",e.mode);let c=0;for(;c<e.size;){const e=go.readSync(i,o,0,u,c);go.writeSync(s,o,0,e),c+=e}r.preserveTimestamps&&go.futimesSync(s,e.atime,e.mtime);go.closeSync(i),go.closeSync(s)}(e,t,n,r)}function bo(e,t,n){go.readdirSync(e).forEach((r=>function(e,t,n,r){const u=Ao.join(t,e),o=Ao.join(n,e),{destStat:i}=wo.checkPathsSync(u,o,"copy");return Oo(i,u,o,r)}(r,e,t,n)))}var Bo=function(e,t,n){"function"==typeof n&&(n={filter:n}),(n=n||{}).clobber=!("clobber"in n)||!!n.clobber,n.overwrite="overwrite"in n?!!n.overwrite:n.clobber,n.preserveTimestamps&&"ia32"===process.arch&&console.warn("fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269");const{srcStat:r,destStat:u}=wo.checkPathsSync(e,t,"copy");return wo.checkParentPathsSync(e,r,t,"copy"),function(e,t,n,r){if(r.filter&&!r.filter(t,n))return;const u=Ao.dirname(n);go.existsSync(u)||vo(u);return Oo(e,t,n,r)}(u,e,t,n)},Po={copySync:Bo};const Io=ju.fromPromise,ko=Lu;var xo={pathExists:Io((function(e){return ko.access(e).then((()=>!0)).catch((()=>!1))})),pathExistsSync:ko.existsSync};const No=oe,Ro=e,To=to.mkdirs,Mo=xo.pathExists,Lo=ro,jo=Fo;function $o(e,t,n,r,u){const o=Ro.dirname(n);Mo(o,((i,s)=>i?u(i):s?Go(e,t,n,r,u):void To(o,(o=>o?u(o):Go(e,t,n,r,u)))))}function Ho(e,t,n,r,u,o){Promise.resolve(u.filter(n,r)).then((i=>i?e(t,n,r,u,o):o()),(e=>o(e)))}function Go(e,t,n,r,u){return r.filter?Ho(Vo,e,t,n,r,u):Vo(e,t,n,r,u)}function Vo(e,t,n,r,u){(r.dereference?No.stat:No.lstat)(t,((o,i)=>o?u(o):i.isDirectory()?function(e,t,n,r,u,o){if(!t)return function(e,t,n,r,u){No.mkdir(n,(o=>{if(o)return u(o);Wo(t,n,r,(t=>t?u(t):No.chmod(n,e.mode,u)))}))}(e,n,r,u,o);if(t&&!t.isDirectory())return o(new Error(`Cannot overwrite non-directory '${r}' with directory '${n}'.`));return Wo(n,r,u,o)}(i,e,t,n,r,u):i.isFile()||i.isCharacterDevice()||i.isBlockDevice()?function(e,t,n,r,u,o){return t?function(e,t,n,r,u){if(!r.overwrite)return r.errorOnExist?u(new Error(`'${n}' already exists`)):u();No.unlink(n,(o=>o?u(o):Uo(e,t,n,r,u)))}(e,n,r,u,o):Uo(e,n,r,u,o)}(i,e,t,n,r,u):i.isSymbolicLink()?function(e,t,n,r,u){No.readlink(t,((t,o)=>t?u(t):(r.dereference&&(o=Ro.resolve(process.cwd(),o)),e?void No.readlink(n,((t,i)=>t?"EINVAL"===t.code||"UNKNOWN"===t.code?No.symlink(o,n,u):u(t):(r.dereference&&(i=Ro.resolve(process.cwd(),i)),jo.isSrcSubdir(o,i)?u(new Error(`Cannot copy '${o}' to a subdirectory of itself, '${i}'.`)):e.isDirectory()&&jo.isSrcSubdir(i,o)?u(new Error(`Cannot overwrite '${i}' with '${o}'.`)):function(e,t,n){No.unlink(t,(r=>r?n(r):No.symlink(e,t,n)))}(o,n,u)))):No.symlink(o,n,u))))}(e,t,n,r,u):void 0))}function Uo(e,t,n,r,u){return"function"==typeof No.copyFile?No.copyFile(t,n,(t=>t?u(t):Jo(e,n,r,u))):function(e,t,n,r,u){const o=No.createReadStream(t);o.on("error",(e=>u(e))).once("open",(()=>{const t=No.createWriteStream(n,{mode:e.mode});t.on("error",(e=>u(e))).on("open",(()=>o.pipe(t))).once("close",(()=>Jo(e,n,r,u)))}))}(e,t,n,r,u)}function Jo(e,t,n,r){No.chmod(t,e.mode,(u=>u?r(u):n.preserveTimestamps?Lo(t,e.atime,e.mtime,r):r()))}function Wo(e,t,n,r){No.readdir(e,((u,o)=>u?r(u):zo(o,e,t,n,r)))}function zo(e,t,n,r,u){const o=e.pop();return o?function(e,t,n,r,u,o){const i=Ro.join(n,t),s=Ro.join(r,t);jo.checkPaths(i,s,"copy",((t,c)=>{if(t)return o(t);const{destStat:a}=c;Go(a,i,s,u,(t=>t?o(t):zo(e,n,r,u,o)))}))}(e,o,t,n,r,u):u()}var Ko=function(e,t,n,r){"function"!=typeof n||r?"function"==typeof n&&(n={filter:n}):(r=n,n={}),r=r||function(){},(n=n||{}).clobber=!("clobber"in n)||!!n.clobber,n.overwrite="overwrite"in n?!!n.overwrite:n.clobber,n.preserveTimestamps&&"ia32"===process.arch&&console.warn("fs-extra: Using the preserveTimestamps option in 32-bit node is not recommended;\n\n    see https://github.com/jprichardson/node-fs-extra/issues/269"),jo.checkPaths(e,t,"copy",((u,o)=>{if(u)return r(u);const{srcStat:i,destStat:s}=o;jo.checkParentPaths(e,i,t,"copy",(u=>u?r(u):n.filter?Ho($o,s,e,t,n,r):$o(s,e,t,n,r)))}))};var qo={copy:(0,ju.fromCallback)(Ko)};const Yo=oe,Zo=e,Xo=s,Qo="win32"===process.platform;function ei(e){["unlink","chmod","stat","lstat","rmdir","readdir"].forEach((t=>{e[t]=e[t]||Yo[t],e[t+="Sync"]=e[t]||Yo[t]})),e.maxBusyTries=e.maxBusyTries||3}function ti(e,t,n){let r=0;"function"==typeof t&&(n=t,t={}),Xo(e,"rimraf: missing path"),Xo.strictEqual(typeof e,"string","rimraf: path should be a string"),Xo.strictEqual(typeof n,"function","rimraf: callback function required"),Xo(t,"rimraf: invalid options argument provided"),Xo.strictEqual(typeof t,"object","rimraf: options should be object"),ei(t),ni(e,t,(function u(o){if(o){if(("EBUSY"===o.code||"ENOTEMPTY"===o.code||"EPERM"===o.code)&&r<t.maxBusyTries){r++;return setTimeout((()=>ni(e,t,u)),100*r)}"ENOENT"===o.code&&(o=null)}n(o)}))}function ni(e,t,n){Xo(e),Xo(t),Xo("function"==typeof n),t.lstat(e,((r,u)=>r&&"ENOENT"===r.code?n(null):r&&"EPERM"===r.code&&Qo?ri(e,t,r,n):u&&u.isDirectory()?oi(e,t,r,n):void t.unlink(e,(r=>{if(r){if("ENOENT"===r.code)return n(null);if("EPERM"===r.code)return Qo?ri(e,t,r,n):oi(e,t,r,n);if("EISDIR"===r.code)return oi(e,t,r,n)}return n(r)}))))}function ri(e,t,n,r){Xo(e),Xo(t),Xo("function"==typeof r),n&&Xo(n instanceof Error),t.chmod(e,438,(u=>{u?r("ENOENT"===u.code?null:n):t.stat(e,((u,o)=>{u?r("ENOENT"===u.code?null:n):o.isDirectory()?oi(e,t,n,r):t.unlink(e,r)}))}))}function ui(e,t,n){let r;Xo(e),Xo(t),n&&Xo(n instanceof Error);try{t.chmodSync(e,438)}catch(e){if("ENOENT"===e.code)return;throw n}try{r=t.statSync(e)}catch(e){if("ENOENT"===e.code)return;throw n}r.isDirectory()?si(e,t,n):t.unlinkSync(e)}function oi(e,t,n,r){Xo(e),Xo(t),n&&Xo(n instanceof Error),Xo("function"==typeof r),t.rmdir(e,(u=>{!u||"ENOTEMPTY"!==u.code&&"EEXIST"!==u.code&&"EPERM"!==u.code?u&&"ENOTDIR"===u.code?r(n):r(u):function(e,t,n){Xo(e),Xo(t),Xo("function"==typeof n),t.readdir(e,((r,u)=>{if(r)return n(r);let o,i=u.length;if(0===i)return t.rmdir(e,n);u.forEach((r=>{ti(Zo.join(e,r),t,(r=>{if(!o)return r?n(o=r):void(0==--i&&t.rmdir(e,n))}))}))}))}(e,t,r)}))}function ii(e,t){let n;ei(t=t||{}),Xo(e,"rimraf: missing path"),Xo.strictEqual(typeof e,"string","rimraf: path should be a string"),Xo(t,"rimraf: missing options"),Xo.strictEqual(typeof t,"object","rimraf: options should be object");try{n=t.lstatSync(e)}catch(n){if("ENOENT"===n.code)return;"EPERM"===n.code&&Qo&&ui(e,t,n)}try{n&&n.isDirectory()?si(e,t,null):t.unlinkSync(e)}catch(n){if("ENOENT"===n.code)return;if("EPERM"===n.code)return Qo?ui(e,t,n):si(e,t,n);if("EISDIR"!==n.code)throw n;si(e,t,n)}}function si(e,t,n){Xo(e),Xo(t),n&&Xo(n instanceof Error);try{t.rmdirSync(e)}catch(r){if("ENOTDIR"===r.code)throw n;if("ENOTEMPTY"===r.code||"EEXIST"===r.code||"EPERM"===r.code)!function(e,t){if(Xo(e),Xo(t),t.readdirSync(e).forEach((n=>ii(Zo.join(e,n),t))),!Qo){return t.rmdirSync(e,t)}{const n=Date.now();do{try{return t.rmdirSync(e,t)}catch(e){}}while(Date.now()-n<500)}}(e,t);else if("ENOENT"!==r.code)throw r}}var ci=ti;ti.sync=ii;const ai=ci;var li={remove:(0,ju.fromCallback)(ai),removeSync:ai.sync};const fi=ju.fromCallback,di=oe,Di=e,pi=to,hi=li,Ei=fi((function(e,t){t=t||function(){},di.readdir(e,((n,r)=>{if(n)return pi.mkdirs(e,t);r=r.map((t=>Di.join(e,t))),function e(){const n=r.pop();if(!n)return t();hi.remove(n,(n=>{if(n)return t(n);e()}))}()}))}));function mi(e){let t;try{t=di.readdirSync(e)}catch(t){return pi.mkdirsSync(e)}t.forEach((t=>{t=Di.join(e,t),hi.removeSync(t)}))}var yi={emptyDirSync:mi,emptydirSync:mi,emptyDir:Ei,emptydir:Ei};const Ci=ju.fromCallback,Fi=e,gi=oe,Ai=to,vi=xo.pathExists;var Si={createFile:Ci((function(e,t){function n(){gi.writeFile(e,"",(e=>{if(e)return t(e);t()}))}gi.stat(e,((r,u)=>{if(!r&&u.isFile())return t();const o=Fi.dirname(e);vi(o,((e,r)=>e?t(e):r?n():void Ai.mkdirs(o,(e=>{if(e)return t(e);n()}))))}))})),createFileSync:function(e){let t;try{t=gi.statSync(e)}catch(e){}if(t&&t.isFile())return;const n=Fi.dirname(e);gi.existsSync(n)||Ai.mkdirsSync(n),gi.writeFileSync(e,"")}};const wi=ju.fromCallback,Oi=e,_i=oe,bi=to,Bi=xo.pathExists;var Pi={createLink:wi((function(e,t,n){function r(e,t){_i.link(e,t,(e=>{if(e)return n(e);n(null)}))}Bi(t,((u,o)=>u?n(u):o?n(null):void _i.lstat(e,(u=>{if(u)return u.message=u.message.replace("lstat","ensureLink"),n(u);const o=Oi.dirname(t);Bi(o,((u,i)=>u?n(u):i?r(e,t):void bi.mkdirs(o,(u=>{if(u)return n(u);r(e,t)}))))}))))})),createLinkSync:function(e,t){if(_i.existsSync(t))return;try{_i.lstatSync(e)}catch(e){throw e.message=e.message.replace("lstat","ensureLink"),e}const n=Oi.dirname(t);return _i.existsSync(n)||bi.mkdirsSync(n),_i.linkSync(e,t)}};const Ii=e,ki=oe,xi=xo.pathExists;var Ni={symlinkPaths:function(e,t,n){if(Ii.isAbsolute(e))return ki.lstat(e,(t=>t?(t.message=t.message.replace("lstat","ensureSymlink"),n(t)):n(null,{toCwd:e,toDst:e})));{const r=Ii.dirname(t),u=Ii.join(r,e);return xi(u,((t,o)=>t?n(t):o?n(null,{toCwd:u,toDst:e}):ki.lstat(e,(t=>t?(t.message=t.message.replace("lstat","ensureSymlink"),n(t)):n(null,{toCwd:e,toDst:Ii.relative(r,e)})))))}},symlinkPathsSync:function(e,t){let n;if(Ii.isAbsolute(e)){if(n=ki.existsSync(e),!n)throw new Error("absolute srcpath does not exist");return{toCwd:e,toDst:e}}{const r=Ii.dirname(t),u=Ii.join(r,e);if(n=ki.existsSync(u),n)return{toCwd:u,toDst:e};if(n=ki.existsSync(e),!n)throw new Error("relative srcpath does not exist");return{toCwd:e,toDst:Ii.relative(r,e)}}}};const Ri=oe;var Ti={symlinkType:function(e,t,n){if(n="function"==typeof t?t:n,t="function"!=typeof t&&t)return n(null,t);Ri.lstat(e,((e,r)=>{if(e)return n(null,"file");t=r&&r.isDirectory()?"dir":"file",n(null,t)}))},symlinkTypeSync:function(e,t){let n;if(t)return t;try{n=Ri.lstatSync(e)}catch(e){return"file"}return n&&n.isDirectory()?"dir":"file"}};const Mi=ju.fromCallback,Li=e,ji=oe,$i=to.mkdirs,Hi=to.mkdirsSync,Gi=Ni.symlinkPaths,Vi=Ni.symlinkPathsSync,Ui=Ti.symlinkType,Ji=Ti.symlinkTypeSync,Wi=xo.pathExists;var zi={createSymlink:Mi((function(e,t,n,r){r="function"==typeof n?n:r,n="function"!=typeof n&&n,Wi(t,((u,o)=>u?r(u):o?r(null):void Gi(e,t,((u,o)=>{if(u)return r(u);e=o.toDst,Ui(o.toCwd,n,((n,u)=>{if(n)return r(n);const o=Li.dirname(t);Wi(o,((n,i)=>n?r(n):i?ji.symlink(e,t,u,r):void $i(o,(n=>{if(n)return r(n);ji.symlink(e,t,u,r)}))))}))}))))})),createSymlinkSync:function(e,t,n){if(ji.existsSync(t))return;const r=Vi(e,t);e=r.toDst,n=Ji(r.toCwd,n);const u=Li.dirname(t);return ji.existsSync(u)||Hi(u),ji.symlinkSync(e,t,n)}};var Ki,qi={createFile:Si.createFile,createFileSync:Si.createFileSync,ensureFile:Si.createFile,ensureFileSync:Si.createFileSync,createLink:Pi.createLink,createLinkSync:Pi.createLinkSync,ensureLink:Pi.createLink,ensureLinkSync:Pi.createLinkSync,createSymlink:zi.createSymlink,createSymlinkSync:zi.createSymlinkSync,ensureSymlink:zi.createSymlink,ensureSymlinkSync:zi.createSymlinkSync};try{Ki=oe}catch(e){Ki=n}function Yi(e,t){var n,r="\n";return"object"==typeof t&&null!==t&&(t.spaces&&(n=t.spaces),t.EOL&&(r=t.EOL)),JSON.stringify(e,t?t.replacer:null,n).replace(/\n/g,r)+r}function Zi(e){return Buffer.isBuffer(e)&&(e=e.toString("utf8")),e=e.replace(/^\uFEFF/,"")}var Xi={readFile:function(e,t,n){null==n&&(n=t,t={}),"string"==typeof t&&(t={encoding:t});var r=(t=t||{}).fs||Ki,u=!0;"throws"in t&&(u=t.throws),r.readFile(e,t,(function(r,o){if(r)return n(r);var i;o=Zi(o);try{i=JSON.parse(o,t?t.reviver:null)}catch(t){return u?(t.message=e+": "+t.message,n(t)):n(null,null)}n(null,i)}))},readFileSync:function(e,t){"string"==typeof(t=t||{})&&(t={encoding:t});var n=t.fs||Ki,r=!0;"throws"in t&&(r=t.throws);try{var u=n.readFileSync(e,t);return u=Zi(u),JSON.parse(u,t.reviver)}catch(t){if(r)throw t.message=e+": "+t.message,t;return null}},writeFile:function(e,t,n,r){null==r&&(r=n,n={});var u=(n=n||{}).fs||Ki,o="";try{o=Yi(t,n)}catch(e){return void(r&&r(e,null))}u.writeFile(e,o,n,r)},writeFileSync:function(e,t,n){var r=(n=n||{}).fs||Ki,u=Yi(t,n);return r.writeFileSync(e,u,n)}},Qi=Xi;const es=ju.fromCallback,ts=Qi;var ns={readJson:es(ts.readFile),readJsonSync:ts.readFileSync,writeJson:es(ts.writeFile),writeJsonSync:ts.writeFileSync};const rs=e,us=to,os=xo.pathExists,is=ns;var ss=function(e,t,n,r){"function"==typeof n&&(r=n,n={});const u=rs.dirname(e);os(u,((o,i)=>o?r(o):i?is.writeJson(e,t,n,r):void us.mkdirs(u,(u=>{if(u)return r(u);is.writeJson(e,t,n,r)}))))};const cs=oe,as=e,ls=to,fs=ns;var ds=function(e,t,n){const r=as.dirname(e);cs.existsSync(r)||ls.mkdirsSync(r),fs.writeJsonSync(e,t,n)};const Ds=ju.fromCallback,ps=ns;ps.outputJson=Ds(ss),ps.outputJsonSync=ds,ps.outputJSON=ps.outputJson,ps.outputJSONSync=ps.outputJsonSync,ps.writeJSON=ps.writeJson,ps.writeJSONSync=ps.writeJsonSync,ps.readJSON=ps.readJson,ps.readJSONSync=ps.readJsonSync;var hs=ps;const Es=oe,ms=e,ys=Po.copySync,Cs=li.removeSync,Fs=to.mkdirpSync,gs=Fo;function As(e,t,n){try{Es.renameSync(e,t)}catch(r){if("EXDEV"!==r.code)throw r;return function(e,t,n){const r={overwrite:n,errorOnExist:!0};return ys(e,t,r),Cs(e)}(e,t,n)}}var vs=function(e,t,n){const r=(n=n||{}).overwrite||n.clobber||!1,{srcStat:u}=gs.checkPathsSync(e,t,"move");return gs.checkParentPathsSync(e,u,t,"move"),Fs(ms.dirname(t)),function(e,t,n){if(n)return Cs(t),As(e,t,n);if(Es.existsSync(t))throw new Error("dest already exists.");return As(e,t,n)}(e,t,r)},Ss={moveSync:vs};const ws=oe,Os=e,_s=qo.copy,bs=li.remove,Bs=to.mkdirp,Ps=xo.pathExists,Is=Fo;function ks(e,t,n,r){ws.rename(e,t,(u=>u?"EXDEV"!==u.code?r(u):function(e,t,n,r){const u={overwrite:n,errorOnExist:!0};_s(e,t,u,(t=>t?r(t):bs(e,r)))}(e,t,n,r):r()))}var xs=function(e,t,n,r){"function"==typeof n&&(r=n,n={});const u=n.overwrite||n.clobber||!1;Is.checkPaths(e,t,"move",((n,o)=>{if(n)return r(n);const{srcStat:i}=o;Is.checkParentPaths(e,i,t,"move",(n=>{if(n)return r(n);Bs(Os.dirname(t),(n=>n?r(n):function(e,t,n,r){if(n)return bs(t,(u=>u?r(u):ks(e,t,n,r)));Ps(t,((u,o)=>u?r(u):o?r(new Error("dest already exists.")):ks(e,t,n,r)))}(e,t,u,r)))}))}))};var Ns={move:(0,ju.fromCallback)(xs)};const Rs=ju.fromCallback,Ts=oe,Ms=e,Ls=to,js=xo.pathExists;var $s={outputFile:Rs((function(e,t,n,r){"function"==typeof n&&(r=n,n="utf8");const u=Ms.dirname(e);js(u,((o,i)=>o?r(o):i?Ts.writeFile(e,t,n,r):void Ls.mkdirs(u,(u=>{if(u)return r(u);Ts.writeFile(e,t,n,r)}))))})),outputFileSync:function(e,...t){const n=Ms.dirname(e);if(Ts.existsSync(n))return Ts.writeFileSync(e,...t);Ls.mkdirsSync(n),Ts.writeFileSync(e,...t)}};!function(e){e.exports=Object.assign({},Lu,Po,qo,yi,qi,hs,to,Ss,Ns,$s,xo,li);const t=n;Object.getOwnPropertyDescriptor(t,"promises")&&Object.defineProperty(e.exports,"promises",{get:()=>t.promises})}(Mu);const Hs=Zn.exports("streamroller:fileNameFormatter"),Gs=e;const Vs=Zn.exports("streamroller:fileNameParser"),Us=gr.exports;const Js=Zn.exports("streamroller:moveAndMaybeCompressFile"),Ws=Mu.exports,zs=f;var Ks=async(e,t,n)=>{if(n=function(e){const t={mode:parseInt("0600",8),compress:!1},n=Object.assign({},t,e);return Js(`_parseOption: moveAndMaybeCompressFile called with option=${JSON.stringify(n)}`),n}(n),e!==t){if(await Ws.pathExists(e))if(Js(`moveAndMaybeCompressFile: moving file from ${e} to ${t} ${n.compress?"with":"without"} compress`),n.compress)await new Promise(((r,u)=>{let o=!1;const i=Ws.createWriteStream(t,{mode:n.mode,flags:"wx"}).on("open",(()=>{o=!0;const t=Ws.createReadStream(e).on("open",(()=>{t.pipe(zs.createGzip()).pipe(i)})).on("error",(t=>{Js(`moveAndMaybeCompressFile: error reading ${e}`,t),i.destroy(t)}))})).on("finish",(()=>{Js(`moveAndMaybeCompressFile: finished compressing ${t}, deleting ${e}`),Ws.unlink(e).then(r).catch((t=>{Js(`moveAndMaybeCompressFile: error deleting ${e}, truncating instead`,t),Ws.truncate(e).then(r).catch((t=>{Js(`moveAndMaybeCompressFile: error truncating ${e}`,t),u(t)}))}))})).on("error",(e=>{o?(Js(`moveAndMaybeCompressFile: error writing ${t}, deleting`,e),Ws.unlink(t).then((()=>{u(e)})).catch((e=>{Js(`moveAndMaybeCompressFile: error deleting ${t}`,e),u(e)}))):(Js(`moveAndMaybeCompressFile: error creating ${t}`,e),u(e))}))})).catch((()=>{}));else{Js(`moveAndMaybeCompressFile: renaming ${e} to ${t}`);try{await Ws.move(e,t,{overwrite:!0})}catch(n){if(Js(`moveAndMaybeCompressFile: error renaming ${e} to ${t}`,n),"ENOENT"!==n.code){Js("moveAndMaybeCompressFile: trying copy+truncate instead");try{await Ws.copy(e,t,{overwrite:!0}),await Ws.truncate(e)}catch(e){Js("moveAndMaybeCompressFile: error copy+truncate",e)}}}}}else Js("moveAndMaybeCompressFile: source and target are the same, not doing anything")};const qs=Zn.exports("streamroller:RollingFileWriteStream"),Ys=Mu.exports,Zs=e,Xs=t,Qs=()=>new Date,ec=gr.exports,{Writable:tc}=o,nc=({file:e,keepFileExt:t,needsIndex:n,alwaysIncludeDate:r,compress:u,fileNameSep:o})=>{let i=o||".";const s=Gs.join(e.dir,e.name),c=t=>t+e.ext,a=(e,t,r)=>!n&&r||!t?e:e+i+t,l=(e,t,n)=>(t>0||r)&&n?e+i+n:e,f=(e,t)=>t&&u?e+".gz":e,d=t?[l,a,c,f]:[c,l,a,f];return({date:e,index:t})=>(Hs(`_formatFileName: date=${e}, index=${t}`),d.reduce(((n,r)=>r(n,t,e)),s))},rc=({file:e,keepFileExt:t,pattern:n,fileNameSep:r})=>{let u=r||".";const o="__NOT_MATCHING__";let i=[(e,t)=>e.endsWith(".gz")?(Vs("it is gzipped"),t.isCompressed=!0,e.slice(0,-3)):e,t?t=>t.startsWith(e.name)&&t.endsWith(e.ext)?(Vs("it starts and ends with the right things"),t.slice(e.name.length+1,-1*e.ext.length)):o:t=>t.startsWith(e.base)?(Vs("it starts with the right things"),t.slice(e.base.length+1)):o,n?(e,t)=>{const r=e.split(u);let o=r[r.length-1];Vs("items: ",r,", indexStr: ",o);let i=e;void 0!==o&&o.match(/^\d+$/)?(i=e.slice(0,-1*(o.length+1)),Vs(`dateStr is ${i}`),n&&!i&&(i=o,o="0")):o="0";try{const r=Us.parse(n,i,new Date(0,0));return Us.asString(n,r)!==i?e:(t.index=parseInt(o,10),t.date=i,t.timestamp=r.getTime(),"")}catch(t){return Vs(`Problem parsing ${i} as ${n}, error was: `,t),e}}:(e,t)=>e.match(/^\d+$/)?(Vs("it has an index"),t.index=parseInt(e,10),""):e];return e=>{let t={filename:e,index:0,isCompressed:!1};return i.reduce(((e,n)=>n(e,t)),e)?null:t}},uc=Ks;var oc=class extends tc{constructor(e,t){if(qs(`constructor: creating RollingFileWriteStream. path=${e}`),"string"!=typeof e||0===e.length)throw new Error(`Invalid filename: ${e}`);if(e.endsWith(Zs.sep))throw new Error(`Filename is a directory: ${e}`);0===e.indexOf(`~${Zs.sep}`)&&(e=e.replace("~",Xs.homedir())),super(t),this.options=this._parseOption(t),this.fileObject=Zs.parse(e),""===this.fileObject.dir&&(this.fileObject=Zs.parse(Zs.join(process.cwd(),e))),this.fileFormatter=nc({file:this.fileObject,alwaysIncludeDate:this.options.alwaysIncludePattern,needsIndex:this.options.maxSize<Number.MAX_SAFE_INTEGER,compress:this.options.compress,keepFileExt:this.options.keepFileExt,fileNameSep:this.options.fileNameSep}),this.fileNameParser=rc({file:this.fileObject,keepFileExt:this.options.keepFileExt,pattern:this.options.pattern,fileNameSep:this.options.fileNameSep}),this.state={currentSize:0},this.options.pattern&&(this.state.currentDate=ec(this.options.pattern,Qs())),this.filename=this.fileFormatter({index:0,date:this.state.currentDate}),["a","a+","as","as+"].includes(this.options.flags)&&this._setExistingSizeAndDate(),qs(`constructor: create new file ${this.filename}, state=${JSON.stringify(this.state)}`),this._renewWriteStream()}_setExistingSizeAndDate(){try{const e=Ys.statSync(this.filename);this.state.currentSize=e.size,this.options.pattern&&(this.state.currentDate=ec(this.options.pattern,e.mtime))}catch(e){return}}_parseOption(e){const t={maxSize:0,numToKeep:Number.MAX_SAFE_INTEGER,encoding:"utf8",mode:parseInt("0600",8),flags:"a",compress:!1,keepFileExt:!1,alwaysIncludePattern:!1},n=Object.assign({},t,e);if(n.maxSize){if(n.maxSize<=0)throw new Error(`options.maxSize (${n.maxSize}) should be > 0`)}else delete n.maxSize;if(n.numBackups||0===n.numBackups){if(n.numBackups<0)throw new Error(`options.numBackups (${n.numBackups}) should be >= 0`);if(n.numBackups>=Number.MAX_SAFE_INTEGER)throw new Error(`options.numBackups (${n.numBackups}) should be < Number.MAX_SAFE_INTEGER`);n.numToKeep=n.numBackups+1}else if(n.numToKeep<=0)throw new Error(`options.numToKeep (${n.numToKeep}) should be > 0`);return qs(`_parseOption: creating stream with option=${JSON.stringify(n)}`),n}_final(e){this.currentFileStream.end("",this.options.encoding,e)}_write(e,t,n){this._shouldRoll().then((()=>{qs(`_write: writing chunk. file=${this.currentFileStream.path} state=${JSON.stringify(this.state)} chunk=${e}`),this.currentFileStream.write(e,t,(t=>{this.state.currentSize+=e.length,n(t)}))}))}async _shouldRoll(){(this._dateChanged()||this._tooBig())&&(qs(`_shouldRoll: rolling because dateChanged? ${this._dateChanged()} or tooBig? ${this._tooBig()}`),await this._roll())}_dateChanged(){return this.state.currentDate&&this.state.currentDate!==ec(this.options.pattern,Qs())}_tooBig(){return this.state.currentSize>=this.options.maxSize}_roll(){return qs("_roll: closing the current stream"),new Promise(((e,t)=>{this.currentFileStream.end("",this.options.encoding,(()=>{this._moveOldFiles().then(e).catch(t)}))}))}async _moveOldFiles(){const e=await this._getExistingFiles();for(let t=(this.state.currentDate?e.filter((e=>e.date===this.state.currentDate)):e).length;t>=0;t--){qs(`_moveOldFiles: i = ${t}`);const e=this.fileFormatter({date:this.state.currentDate,index:t}),n=this.fileFormatter({date:this.state.currentDate,index:t+1}),r={compress:this.options.compress&&0===t,mode:this.options.mode};await uc(e,n,r)}this.state.currentSize=0,this.state.currentDate=this.state.currentDate?ec(this.options.pattern,Qs()):null,qs(`_moveOldFiles: finished rolling files. state=${JSON.stringify(this.state)}`),this._renewWriteStream(),await new Promise(((e,t)=>{this.currentFileStream.write("","utf8",(()=>{this._clean().then(e).catch(t)}))}))}async _getExistingFiles(){const e=await Ys.readdir(this.fileObject.dir).catch((()=>[]));qs(`_getExistingFiles: files=${e}`);const t=e.map((e=>this.fileNameParser(e))).filter((e=>e)),n=e=>(e.timestamp?e.timestamp:Qs().getTime())-e.index;return t.sort(((e,t)=>n(e)-n(t))),t}_renewWriteStream(){const e=this.fileFormatter({date:this.state.currentDate,index:0}),t=e=>{try{return Ys.mkdirSync(e,{recursive:!0})}catch(n){if("ENOENT"===n.code)return t(Zs.dirname(e)),t(e);if("EEXIST"!==n.code&&"EROFS"!==n.code)throw n;try{if(Ys.statSync(e).isDirectory())return e;throw n}catch(e){throw n}}};t(this.fileObject.dir);const n={flags:this.options.flags,encoding:this.options.encoding,mode:this.options.mode};var r,u;Ys.appendFileSync(e,"",(r={...n},u="flags",r["flag"]=r[u],delete r[u],r)),this.currentFileStream=Ys.createWriteStream(e,n),this.currentFileStream.on("error",(e=>{this.emit("error",e)}))}async _clean(){const e=await this._getExistingFiles();if(qs(`_clean: numToKeep = ${this.options.numToKeep}, existingFiles = ${e.length}`),qs("_clean: existing files are: ",e),this._tooManyFiles(e.length)){const n=e.slice(0,e.length-this.options.numToKeep).map((e=>Zs.format({dir:this.fileObject.dir,base:e.filename})));await(t=n,qs(`deleteFiles: files to delete: ${t}`),Promise.all(t.map((e=>Ys.unlink(e).catch((t=>{qs(`deleteFiles: error when unlinking ${e}, ignoring. Error was ${t}`)}))))))}var t}_tooManyFiles(e){return this.options.numToKeep>0&&e>this.options.numToKeep}};const ic=oc;var sc=class extends ic{constructor(e,t,n,r){r||(r={}),t&&(r.maxSize=t),r.numBackups||0===r.numBackups||(n||0===n||(n=1),r.numBackups=n),super(e,r),this.backups=r.numBackups,this.size=this.options.maxSize}get theStream(){return this.currentFileStream}};const cc=oc;var ac={RollingFileWriteStream:oc,RollingFileStream:sc,DateRollingFileStream:class extends cc{constructor(e,t,n){t&&"object"==typeof t&&(n=t,t=null),n||(n={}),t||(t="yyyy-MM-dd"),n.pattern=t,n.numBackups||0===n.numBackups?n.daysToKeep=n.numBackups:(n.daysToKeep||0===n.daysToKeep?process.emitWarning("options.daysToKeep is deprecated due to the confusion it causes when used together with file size rolling. Please use options.numBackups instead.","DeprecationWarning","streamroller-DEP0001"):n.daysToKeep=1,n.numBackups=n.daysToKeep),super(e,n),this.mode=this.options.mode}get theStream(){return this.currentFileStream}}};const lc=Zn.exports("log4js:file"),fc=e,dc=ac,Dc=t,pc=Dc.EOL;let hc=!1;const Ec=new Set;function mc(){Ec.forEach((e=>{e.sighupHandler()}))}Tu.configure=function(e,t){let n=t.basicLayout;return e.layout&&(n=t.layout(e.layout.type,e.layout)),e.mode=e.mode||384,function(e,t,n,r,u,o){if("string"!=typeof e||0===e.length)throw new Error(`Invalid filename: ${e}`);if(e.endsWith(fc.sep))throw new Error(`Filename is a directory: ${e}`);function i(e,t,n,r){const u=new dc.RollingFileStream(e,t,n,r);return u.on("error",(t=>{console.error("log4js.fileAppender - Writing to file %s, error happened ",e,t)})),u.on("drain",(()=>{process.emit("log4js:pause",!1)})),u}e=e.replace(new RegExp(`^~(?=${fc.sep}.+)`),Dc.homedir()),e=fc.normalize(e),lc("Creating file appender (",e,", ",n,", ",r=r||0===r?r:5,", ",u,", ",o,")");let s=i(e,n,r,u);const c=function(e){if(s.writable){if(!0===u.removeColor){const t=/\x1b[[0-9;]*m/g;e.data=e.data.map((e=>"string"==typeof e?e.replace(t,""):e))}s.write(t(e,o)+pc,"utf8")||process.emit("log4js:pause",!0)}};return c.reopen=function(){s.end((()=>{s=i(e,n,r,u)}))},c.sighupHandler=function(){lc("SIGHUP handler called."),c.reopen()},c.shutdown=function(e){Ec.delete(c),0===Ec.size&&hc&&(process.removeListener("SIGHUP",mc),hc=!1),s.end("","utf-8",e)},Ec.add(c),hc||(process.on("SIGHUP",mc),hc=!0),c}(e.filename,n,e.maxLogSize,e.backups,e,e.timezoneOffset)};var yc={};const Cc=ac,Fc=t.EOL;function gc(e,t,n,r,u){r.maxSize=r.maxLogSize;const o=function(e,t,n){const r=new Cc.DateRollingFileStream(e,t,n);return r.on("error",(t=>{console.error("log4js.dateFileAppender - Writing to file %s, error happened ",e,t)})),r.on("drain",(()=>{process.emit("log4js:pause",!1)})),r}(e,t,r),i=function(e){o.writable&&(o.write(n(e,u)+Fc,"utf8")||process.emit("log4js:pause",!0))};return i.shutdown=function(e){o.end("","utf-8",e)},i}yc.configure=function(e,t){let n=t.basicLayout;return e.layout&&(n=t.layout(e.layout.type,e.layout)),e.alwaysIncludePattern||(e.alwaysIncludePattern=!1),e.mode=e.mode||384,gc(e.filename,e.pattern,n,e,e.timezoneOffset)};var Ac={};const vc=Zn.exports("log4js:fileSync"),Sc=e,wc=n,Oc=t,_c=Oc.EOL;function bc(e,t){const n=e=>{try{return wc.mkdirSync(e,{recursive:!0})}catch(t){if("ENOENT"===t.code)return n(Sc.dirname(e)),n(e);if("EEXIST"!==t.code&&"EROFS"!==t.code)throw t;try{if(wc.statSync(e).isDirectory())return e;throw t}catch(e){throw t}}};n(Sc.dirname(e)),wc.appendFileSync(e,"",{mode:t.mode,flag:t.flags})}class Bc{constructor(e,t,n,r){if(vc("In RollingFileStream"),t<0)throw new Error(`maxLogSize (${t}) should be > 0`);this.filename=e,this.size=t,this.backups=n,this.options=r,this.currentSize=0,this.currentSize=function(e){let t=0;try{t=wc.statSync(e).size}catch(t){bc(e,r)}return t}(this.filename)}shouldRoll(){return vc("should roll with current size %d, and max size %d",this.currentSize,this.size),this.currentSize>=this.size}roll(e){const t=this,n=new RegExp(`^${Sc.basename(e)}`);function r(e){return n.test(e)}function u(t){return parseInt(t.slice(`${Sc.basename(e)}.`.length),10)||0}function o(e,t){return u(e)-u(t)}function i(n){const r=u(n);if(vc(`Index of ${n} is ${r}`),0===t.backups)wc.truncateSync(e,0);else if(r<t.backups){try{wc.unlinkSync(`${e}.${r+1}`)}catch(e){}vc(`Renaming ${n} -> ${e}.${r+1}`),wc.renameSync(Sc.join(Sc.dirname(e),n),`${e}.${r+1}`)}}vc("Rolling, rolling, rolling"),vc("Renaming the old files"),wc.readdirSync(Sc.dirname(e)).filter(r).sort(o).reverse().forEach(i)}write(e,t){const n=this;vc("in write"),this.shouldRoll()&&(this.currentSize=0,this.roll(this.filename)),vc("writing the chunk to the file"),n.currentSize+=e.length,wc.appendFileSync(n.filename,e)}}Ac.configure=function(e,t){let n=t.basicLayout;e.layout&&(n=t.layout(e.layout.type,e.layout));const r={flags:e.flags||"a",encoding:e.encoding||"utf8",mode:e.mode||384};return function(e,t,n,r,u,o){if("string"!=typeof e||0===e.length)throw new Error(`Invalid filename: ${e}`);if(e.endsWith(Sc.sep))throw new Error(`Filename is a directory: ${e}`);e=e.replace(new RegExp(`^~(?=${Sc.sep}.+)`),Oc.homedir()),e=Sc.normalize(e),vc("Creating fileSync appender (",e,", ",n,", ",r=r||0===r?r:5,", ",u,", ",o,")");const i=function(e,t,n){let r;var o;return t?r=new Bc(e,t,n,u):(bc(o=e,u),r={write(e){wc.appendFileSync(o,e)}}),r}(e,n,r);return e=>{i.write(t(e,o)+_c)}}(e.filename,n,e.maxLogSize,e.backups,r,e.timezoneOffset)};var Pc={};const Ic=Zn.exports("log4js:tcp"),kc=d;Pc.configure=function(e,t){Ic(`configure with config = ${e}`);let n=function(e){return e.serialise()};return e.layout&&(n=t.layout(e.layout.type,e.layout)),function(e,t){let n=!1;const r=[];let u,o=3,i="__LOG4JS__";function s(e){Ic("Writing log event to socket"),n=u.write(`${t(e)}${i}`,"utf8")}function c(){let e;for(Ic("emptying buffer");e=r.shift();)s(e)}function a(e){n?s(e):(Ic("buffering log event because it cannot write at the moment"),r.push(e))}return function t(){Ic(`appender creating socket to ${e.host||"localhost"}:${e.port||5e3}`),i=`${e.endMsg||"__LOG4JS__"}`,u=kc.createConnection(e.port||5e3,e.host||"localhost"),u.on("connect",(()=>{Ic("socket connected"),c(),n=!0})),u.on("drain",(()=>{Ic("drain event received, emptying buffer"),n=!0,c()})),u.on("timeout",u.end.bind(u)),u.on("error",(e=>{Ic("connection error",e),n=!1,c()})),u.on("close",t)}(),a.shutdown=function(e){Ic("shutdown called"),r.length&&o?(Ic("buffer has items, waiting 100ms to empty"),o-=1,setTimeout((()=>{a.shutdown(e)}),100)):(u.removeAllListeners("close"),u.end(e))},a}(e,n)};const xc=e,Nc=Zn.exports("log4js:appenders"),Rc=Fr,Tc=Au,Mc=Gr,Lc=Lr,jc=vu,$c=new Map;$c.set("console",_u),$c.set("stdout",Bu),$c.set("stderr",Pu),$c.set("logLevelFilter",Iu),$c.set("categoryFilter",ku),$c.set("noLogFilter",Nu),$c.set("file",Tu),$c.set("dateFile",yc),$c.set("fileSync",Ac),$c.set("tcp",Pc);const Hc=new Map,Gc=(e,t)=>{let n;try{const t=`${e}.cjs`;n=require.resolve(t),Nc("Loading module from ",t)}catch(t){n=e,Nc("Loading module from ",e)}try{return require(n)}catch(n){return void Rc.throwExceptionIf(t,"MODULE_NOT_FOUND"!==n.code,`appender "${e}" could not be loaded (error was: ${n})`)}},Vc=new Set,Uc=(e,t)=>{if(Hc.has(e))return Hc.get(e);if(!t.appenders[e])return!1;if(Vc.has(e))throw new Error(`Dependency loop detected for appender ${e}.`);Vc.add(e),Nc(`Creating appender ${e}`);const n=Jc(e,t);return Vc.delete(e),Hc.set(e,n),n},Jc=(e,t)=>{const n=t.appenders[e],r=n.type.configure?n.type:((e,t)=>$c.get(e)||Gc(`./${e}`,t)||Gc(e,t)||require.main&&require.main.filename&&Gc(xc.join(xc.dirname(require.main.filename),e),t)||Gc(xc.join(process.cwd(),e),t))(n.type,t);return Rc.throwExceptionIf(t,Rc.not(r),`appender "${e}" is not valid (type "${n.type}" could not be found)`),r.appender&&(process.emitWarning(`Appender ${n.type} exports an appender function.`,"DeprecationWarning","log4js-node-DEP0001"),Nc("[log4js-node-DEP0001]",`DEPRECATION: Appender ${n.type} exports an appender function.`)),r.shutdown&&(process.emitWarning(`Appender ${n.type} exports a shutdown function.`,"DeprecationWarning","log4js-node-DEP0002"),Nc("[log4js-node-DEP0002]",`DEPRECATION: Appender ${n.type} exports a shutdown function.`)),Nc(`${e}: clustering.isMaster ? ${Tc.isMaster()}`),Nc(`${e}: appenderModule is ${i.inspect(r)}`),Tc.onlyOnMaster((()=>(Nc(`calling appenderModule.configure for ${e} / ${n.type}`),r.configure(jc.modifyConfig(n),Lc,(e=>Uc(e,t)),Mc))),(()=>{}))},Wc=e=>{if(Hc.clear(),Vc.clear(),!e)return;const t=[];Object.values(e.categories).forEach((e=>{t.push(...e.appenders)})),Object.keys(e.appenders).forEach((n=>{(t.includes(n)||"tcp-server"===e.appenders[n].type||"multiprocess"===e.appenders[n].type)&&Uc(n,e)}))},zc=()=>{Wc()};zc(),Rc.addListener((e=>{Rc.throwExceptionIf(e,Rc.not(Rc.anObject(e.appenders)),'must have a property "appenders" of type object.');const t=Object.keys(e.appenders);Rc.throwExceptionIf(e,Rc.not(t.length),"must define at least one appender."),t.forEach((t=>{Rc.throwExceptionIf(e,Rc.not(e.appenders[t].type),`appender "${t}" is not valid (must be an object with property "type")`)}))})),Rc.addListener(Wc),Vr.exports=Hc,Vr.exports.init=zc;var Kc={exports:{}};!function(e){const t=Zn.exports("log4js:categories"),n=Fr,r=Gr,u=Vr.exports,o=new Map;function i(e,t,n){if(!1===t.inherit)return;const r=n.lastIndexOf(".");if(r<0)return;const u=n.slice(0,r);let o=e.categories[u];o||(o={inherit:!0,appenders:[]}),i(e,o,u),!e.categories[u]&&o.appenders&&o.appenders.length&&o.level&&(e.categories[u]=o),t.appenders=t.appenders||[],t.level=t.level||o.level,o.appenders.forEach((e=>{t.appenders.includes(e)||t.appenders.push(e)})),t.parent=o}function s(e){if(!e.categories)return;Object.keys(e.categories).forEach((t=>{const n=e.categories[t];i(e,n,t)}))}n.addPreProcessingListener((e=>s(e))),n.addListener((e=>{n.throwExceptionIf(e,n.not(n.anObject(e.categories)),'must have a property "categories" of type object.');const t=Object.keys(e.categories);n.throwExceptionIf(e,n.not(t.length),"must define at least one category."),t.forEach((t=>{const o=e.categories[t];n.throwExceptionIf(e,[n.not(o.appenders),n.not(o.level)],`category "${t}" is not valid (must be an object with properties "appenders" and "level")`),n.throwExceptionIf(e,n.not(Array.isArray(o.appenders)),`category "${t}" is not valid (appenders must be an array of appender names)`),n.throwExceptionIf(e,n.not(o.appenders.length),`category "${t}" is not valid (appenders must contain at least one appender name)`),Object.prototype.hasOwnProperty.call(o,"enableCallStack")&&n.throwExceptionIf(e,"boolean"!=typeof o.enableCallStack,`category "${t}" is not valid (enableCallStack must be boolean type)`),o.appenders.forEach((r=>{n.throwExceptionIf(e,n.not(u.get(r)),`category "${t}" is not valid (appender "${r}" is not defined)`)})),n.throwExceptionIf(e,n.not(r.getLevel(o.level)),`category "${t}" is not valid (level "${o.level}" not recognised; valid levels are ${r.levels.join(", ")})`)})),n.throwExceptionIf(e,n.not(e.categories.default),'must define a "default" category.')}));const c=e=>{if(o.clear(),!e)return;Object.keys(e.categories).forEach((n=>{const i=e.categories[n],s=[];i.appenders.forEach((e=>{s.push(u.get(e)),t(`Creating category ${n}`),o.set(n,{appenders:s,level:r.getLevel(i.level),enableCallStack:i.enableCallStack||!1})}))}))},a=()=>{c()};a(),n.addListener(c);const l=e=>{if(t(`configForCategory: searching for config for ${e}`),o.has(e))return t(`configForCategory: ${e} exists in config, returning it`),o.get(e);let n;return e.indexOf(".")>0?(t(`configForCategory: ${e} has hierarchy, cloning from parents`),n={...l(e.slice(0,e.lastIndexOf(".")))}):(o.has("default")||c({categories:{default:{appenders:["out"],level:"OFF"}}}),t("configForCategory: cloning default category"),n={...o.get("default")}),o.set(e,n),n};e.exports=o,e.exports=Object.assign(e.exports,{appendersForCategory:e=>l(e).appenders,getLevelForCategory:e=>l(e).level,setLevelForCategory:(e,t)=>{l(e).level=t},getEnableCallStackForCategory:e=>!0===l(e).enableCallStack,setEnableCallStackForCategory:(e,t)=>{l(e).enableCallStack=t},init:a})}(Kc);const qc=Zn.exports("log4js:logger"),Yc=au,Zc=Gr,Xc=Au,Qc=Kc.exports,ea=Fr,ta=/at (?:(.+)\s+\()?(?:(.+?):(\d+)(?::(\d+))?|([^)]+))\)?/;function na(e,t=4){try{const n=e.stack.split("\n").slice(t);if(!n.length)return null;const r=ta.exec(n[0]);if(r&&6===r.length){let e="",t="",u="";return r[1]&&""!==r[1]&&([t,u]=r[1].replace(/[[\]]/g,"").split(" as "),u=u||"",t.includes(".")&&([e,t]=t.split("."))),{fileName:r[2],lineNumber:parseInt(r[3],10),columnNumber:parseInt(r[4],10),callStack:n.join("\n"),className:e,functionName:t,functionAlias:u,callerName:r[1]||""}}console.error("log4js.logger - defaultParseCallStack error")}catch(e){console.error("log4js.logger - defaultParseCallStack error",e)}return null}let ra=class{constructor(e){if(!e)throw new Error("No category provided.");this.category=e,this.context={},this.callStackSkipIndex=0,this.parseCallStack=na,qc(`Logger created (${this.category}, ${this.level})`)}get level(){return Zc.getLevel(Qc.getLevelForCategory(this.category),Zc.OFF)}set level(e){Qc.setLevelForCategory(this.category,Zc.getLevel(e,this.level))}get useCallStack(){return Qc.getEnableCallStackForCategory(this.category)}set useCallStack(e){Qc.setEnableCallStackForCategory(this.category,!0===e)}get callStackLinesToSkip(){return this.callStackSkipIndex}set callStackLinesToSkip(e){if("number"!=typeof e)throw new TypeError("Must be a number");if(e<0)throw new RangeError("Must be >= 0");this.callStackSkipIndex=e}log(e,...t){const n=Zc.getLevel(e);n?this.isLevelEnabled(n)&&this._log(n,t):ea.validIdentifier(e)&&t.length>0?(this.log(Zc.WARN,"log4js:logger.log: valid log-level not found as first parameter given:",e),this.log(Zc.INFO,`[${e}]`,...t)):this.log(Zc.INFO,e,...t)}isLevelEnabled(e){return this.level.isLessThanOrEqualTo(e)}_log(e,t){qc(`sending log data (${e}) to appenders`);const n=t.find((e=>e instanceof Error));let r;if(this.useCallStack){try{n&&(r=this.parseCallStack(n,this.callStackSkipIndex+1))}catch(e){}r=r||this.parseCallStack(new Error,this.callStackSkipIndex+3+1)}const u=new Yc(this.category,e,t,this.context,r,n);Xc.send(u)}addContext(e,t){this.context[e]=t}removeContext(e){delete this.context[e]}clearContext(){this.context={}}setParseCallStackFunction(e){if("function"==typeof e)this.parseCallStack=e;else{if(void 0!==e)throw new TypeError("Invalid type passed to setParseCallStackFunction");this.parseCallStack=na}}};function ua(e){const t=Zc.getLevel(e),n=t.toString().toLowerCase().replace(/_([a-z])/g,(e=>e[1].toUpperCase())),r=n[0].toUpperCase()+n.slice(1);ra.prototype[`is${r}Enabled`]=function(){return this.isLevelEnabled(t)},ra.prototype[n]=function(...e){this.log(t,...e)}}Zc.levels.forEach(ua),ea.addListener((()=>{Zc.levels.forEach(ua)}));var oa=ra;const ia=Gr;function sa(e){return e.originalUrl||e.url}function ca(e,t){for(let n=0;n<t.length;n++)e=e.replace(t[n].token,t[n].replacement);return e}const aa=Zn.exports("log4js:recording"),la=[];function fa(){return la.slice()}function da(){la.length=0}var Da={configure:function(){return function(e){aa(`received logEvent, number of events now ${la.length+1}`),aa("log event was ",e),la.push(e)}},replay:fa,playback:fa,reset:da,erase:da};const pa=Zn.exports("log4js:main"),ha=n,Ea=fr({proto:!0}),ma=Fr,ya=Lr,Ca=Gr,Fa=Vr.exports,ga=Kc.exports,Aa=oa,va=Au,Sa=function(e,t){t="string"==typeof t||"function"==typeof t?{format:t}:t||{};const n=e;let r=ia.getLevel(t.level,ia.INFO);const u=t.format||':remote-addr - - ":method :url HTTP/:http-version" :status :content-length ":referrer" ":user-agent"';return(e,o,i)=>{if(void 0!==e._logging)return i();if("function"!=typeof t.nolog){const n=function(e){let t=null;if(e instanceof RegExp&&(t=e),"string"==typeof e&&(t=new RegExp(e)),Array.isArray(e)){const n=e.map((e=>e.source?e.source:e));t=new RegExp(n.join("|"))}return t}(t.nolog);if(n&&n.test(e.originalUrl))return i()}if(n.isLevelEnabled(r)||"auto"===t.level){const i=new Date,{writeHead:s}=o;e._logging=!0,o.writeHead=(e,t)=>{o.writeHead=s,o.writeHead(e,t),o.__statusCode=e,o.__headers=t||{}};let c=!1;const a=()=>{if(c)return;if(c=!0,"function"==typeof t.nolog&&!0===t.nolog(e,o))return void(e._logging=!1);o.responseTime=new Date-i,o.statusCode&&"auto"===t.level&&(r=ia.INFO,o.statusCode>=300&&(r=ia.WARN),o.statusCode>=400&&(r=ia.ERROR)),r=function(e,t,n){let r=t;if(n){const t=n.find((t=>{let n=!1;return n=t.from&&t.to?e>=t.from&&e<=t.to:-1!==t.codes.indexOf(e),n}));t&&(r=ia.getLevel(t.level,r))}return r}(o.statusCode,r,t.statusRules);const s=function(e,t,n){const r=[];return r.push({token:":url",replacement:sa(e)}),r.push({token:":protocol",replacement:e.protocol}),r.push({token:":hostname",replacement:e.hostname}),r.push({token:":method",replacement:e.method}),r.push({token:":status",replacement:t.__statusCode||t.statusCode}),r.push({token:":response-time",replacement:t.responseTime}),r.push({token:":date",replacement:(new Date).toUTCString()}),r.push({token:":referrer",replacement:e.headers.referer||e.headers.referrer||""}),r.push({token:":http-version",replacement:`${e.httpVersionMajor}.${e.httpVersionMinor}`}),r.push({token:":remote-addr",replacement:e.headers["x-forwarded-for"]||e.ip||e._remoteAddress||e.socket&&(e.socket.remoteAddress||e.socket.socket&&e.socket.socket.remoteAddress)}),r.push({token:":user-agent",replacement:e.headers["user-agent"]}),r.push({token:":content-length",replacement:t.getHeader("content-length")||t.__headers&&t.__headers["Content-Length"]||"-"}),r.push({token:/:req\[([^\]]+)]/g,replacement:(t,n)=>e.headers[n.toLowerCase()]}),r.push({token:/:res\[([^\]]+)]/g,replacement:(e,n)=>t.getHeader(n.toLowerCase())||t.__headers&&t.__headers[n]}),(e=>{const t=e.concat();for(let e=0;e<t.length;++e)for(let n=e+1;n<t.length;++n)t[e].token==t[n].token&&t.splice(n--,1);return t})(n.concat(r))}(e,o,t.tokens||[]);if(t.context&&n.addContext("res",o),"function"==typeof u){const t=u(e,o,(e=>ca(e,s)));t&&n.log(r,t)}else n.log(r,ca(u,s));t.context&&n.removeContext("res")};o.on("end",a),o.on("finish",a),o.on("error",a),o.on("close",a)}return i()}},wa=Da;let Oa=!1;function _a(e){if(!Oa)return;pa("Received log event ",e);ga.appendersForCategory(e.categoryName).forEach((t=>{t(e)}))}function ba(e){Oa&&Ba();let t=e;return"string"==typeof t&&(t=function(e){pa(`Loading configuration from ${e}`);try{return JSON.parse(ha.readFileSync(e,"utf8"))}catch(t){throw new Error(`Problem reading config from file "${e}". Error was ${t.message}`,t)}}(e)),pa(`Configuration is ${t}`),ma.configure(Ea(t)),va.onMessage(_a),Oa=!0,Pa}function Ba(e=(()=>{})){if("function"!=typeof e)throw new TypeError("Invalid callback passed to shutdown");pa("Shutdown called. Disabling all log writing."),Oa=!1;const t=Array.from(Fa.values());Fa.init(),ga.init();const n=t.reduce(((e,t)=>t.shutdown?e+1:e),0);0===n&&(pa("No appenders with shutdown functions found."),e());let r,u=0;function o(t){r=r||t,u+=1,pa(`Appender shutdowns complete: ${u} / ${n}`),u>=n&&(pa("All shutdown functions completed."),e(r))}pa(`Found ${n} appenders with shutdown functions.`),t.filter((e=>e.shutdown)).forEach((e=>e.shutdown(o)))}const Pa={getLogger:function(e){return Oa||ba(process.env.LOG4JS_CONFIG||{appenders:{out:{type:"stdout"}},categories:{default:{appenders:["out"],level:"OFF"}}}),new Aa(e||"default")},configure:ba,shutdown:Ba,connectLogger:Sa,levels:Ca,addLayout:ya.addLayout,recording:function(){return wa}};var Ia=Pa;!function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.addCustomPlugin=e.addCustomTask=e.hvigorTrace=void 0;const t=Ia;e.hvigorTrace={totalTime:0,moduleNum:0,taskTime:{},isIncremental:!0,hasIncremental:!1,isParallel:!0,IS_DAEMON:!0,LOG_LEVEL:t.levels.INFO.levelStr,IS_HVIGORFILE_TYPE_CHECK:!1},e.addCustomTask=function(t){var n;let r=null!==(n=e.hvigorTrace.CUSTOM_TASKS)&&void 0!==n?n:[];r.length>0&&(r=r.filter((e=>e.NAME!==t.NAME))),r.push(t),e.hvigorTrace.CUSTOM_TASKS=r},e.addCustomPlugin=function(t){var n;let r=null!==(n=e.hvigorTrace.CUSTOM_PLUGINS)&&void 0!==n?n:[];r.length>0&&(r=r.filter((e=>e.PLUGIN_ID!==t.PLUGIN_ID))),r.push({PLUGIN_ID:t.PLUGIN_ID}),e.hvigorTrace.CUSTOM_PLUGINS=r}}(Yn);var ka,xa={};ka=xa,Object.defineProperty(ka,"__esModule",{value:!0}),ka.isCI=void 0,ka.isCI=function(){return!("false"===process.env.CI||!(process.env.BUILD_ID||process.env.BUILD_NUMBER||process.env.CI||process.env.CI_APP_ID||process.env.CI_BUILD_ID||process.env.CI_BUILD_NUMBER||process.env.CI_NAME||process.env.CONTINUOUS_INTEGRATION||process.env.RUN_ID||ka.name))};var Na={};!function(e){var t=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(e,"__esModule",{value:!0}),e.hashFile=e.hash=e.createHash=void 0;const r=t(D),u=t(n);e.createHash=(e="MD5")=>r.default.createHash(e);e.hash=(t,n)=>(0,e.createHash)(n).update(t).digest("hex");e.hashFile=(t,n)=>{if(u.default.existsSync(t))return(0,e.hash)(u.default.readFileSync(t,"utf-8"),n)}}(Na);var Ra={},Ta={},Ma={};Object.defineProperty(Ma,"__esModule",{value:!0}),Ma.Unicode=void 0;class La{}Ma.Unicode=La,La.SPACE_SEPARATOR=/[\u1680\u2000-\u200A\u202F\u205F\u3000]/,La.ID_START=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE83\uDE86-\uDE89\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]/,La.ID_CONTINUE=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u0860-\u086A\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u09FC\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9-\u0AFF\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D00-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF9\u1D00-\u1DF9\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312E\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FEA\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF2D-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDE00-\uDE3E\uDE47\uDE50-\uDE83\uDE86-\uDE99\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD36\uDD3A\uDD3C\uDD3D\uDD3F-\uDD47\uDD50-\uDD59]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0\uDFE1]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00-\uDD1E\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/,Object.defineProperty(Ta,"__esModule",{value:!0}),Ta.JudgeUtil=void 0;const ja=Ma;Ta.JudgeUtil=class{static isIgnoreChar(e){return"string"==typeof e&&("\t"===e||"\v"===e||"\f"===e||" "===e||" "===e||"\ufeff"===e||"\n"===e||"\r"===e||"\u2028"===e||"\u2029"===e)}static isSpaceSeparator(e){return"string"==typeof e&&ja.Unicode.SPACE_SEPARATOR.test(e)}static isIdStartChar(e){return"string"==typeof e&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||"$"===e||"_"===e||ja.Unicode.ID_START.test(e))}static isIdContinueChar(e){return"string"==typeof e&&(e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||"$"===e||"_"===e||"‌"===e||"‍"===e||ja.Unicode.ID_CONTINUE.test(e))}static isDigitWithoutZero(e){return/[1-9]/.test(e)}static isDigit(e){return"string"==typeof e&&/[0-9]/.test(e)}static isHexDigit(e){return"string"==typeof e&&/[0-9A-Fa-f]/.test(e)}};var $a=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ra,"__esModule",{value:!0}),Ra.parseJsonText=Ra.parseJsonFile=void 0;const Ha=$a(n),Ga=$a(t),Va=$a(e),Ua=Ta;var Ja;!function(e){e[e.Char=0]="Char",e[e.EOF=1]="EOF",e[e.Identifier=2]="Identifier"}(Ja||(Ja={}));let Wa,za,Ka,qa,Ya,Za,Xa="start",Qa=[],el=0,tl=1,nl=0,rl=!1,ul="default",ol="'",il=1;function sl(e,t=!1){za=String(e),Xa="start",Qa=[],el=0,tl=1,nl=0,qa=void 0,rl=t;do{Wa=cl(),hl[Xa]()}while("eof"!==Wa.type);return qa}function cl(){for(ul="default",Ya="",ol="'",il=1;;){Za=al();const e=fl[ul]();if(e)return e}}function al(){if(za[el])return String.fromCodePoint(za.codePointAt(el))}function ll(){const e=al();return"\n"===e?(tl++,nl=0):e?nl+=e.length:nl++,e&&(el+=e.length),e}Ra.parseJsonFile=function(e,t=!1,n="utf-8"){const r=Ha.default.readFileSync(Va.default.resolve(e),{encoding:n});try{return sl(r,t)}catch(t){if(t instanceof SyntaxError){const n=t.message.split("at");if(2===n.length)throw new Error(`${n[0].trim()}${Ga.default.EOL}\t at ${e}:${n[1].trim()}`)}throw new Error(`${e} is not in valid JSON/JSON5 format.`)}},Ra.parseJsonText=sl;const fl={default(){switch(Za){case"/":return ll(),void(ul="comment");case void 0:return ll(),dl("eof")}if(!Ua.JudgeUtil.isIgnoreChar(Za)&&!Ua.JudgeUtil.isSpaceSeparator(Za))return fl[Xa]();ll()},start(){ul="value"},beforePropertyName(){switch(Za){case"$":case"_":return Ya=ll(),void(ul="identifierName");case"\\":return ll(),void(ul="identifierNameStartEscape");case"}":return dl("punctuator",ll());case'"':case"'":return ol=Za,ll(),void(ul="string")}if(Ua.JudgeUtil.isIdStartChar(Za))return Ya+=ll(),void(ul="identifierName");throw Cl(Ja.Char,ll())},afterPropertyName(){if(":"===Za)return dl("punctuator",ll());throw Cl(Ja.Char,ll())},beforePropertyValue(){ul="value"},afterPropertyValue(){switch(Za){case",":case"}":return dl("punctuator",ll())}throw Cl(Ja.Char,ll())},beforeArrayValue(){if("]"===Za)return dl("punctuator",ll());ul="value"},afterArrayValue(){switch(Za){case",":case"]":return dl("punctuator",ll())}throw Cl(Ja.Char,ll())},end(){throw Cl(Ja.Char,ll())},comment(){switch(Za){case"*":return ll(),void(ul="multiLineComment");case"/":return ll(),void(ul="singleLineComment")}throw Cl(Ja.Char,ll())},multiLineComment(){switch(Za){case"*":return ll(),void(ul="multiLineCommentAsterisk");case void 0:throw Cl(Ja.Char,ll())}ll()},multiLineCommentAsterisk(){switch(Za){case"*":return void ll();case"/":return ll(),void(ul="default");case void 0:throw Cl(Ja.Char,ll())}ll(),ul="multiLineComment"},singleLineComment(){switch(Za){case"\n":case"\r":case"\u2028":case"\u2029":return ll(),void(ul="default");case void 0:return ll(),dl("eof")}ll()},value(){switch(Za){case"{":case"[":return dl("punctuator",ll());case"n":return ll(),Dl("ull"),dl("null",null);case"t":return ll(),Dl("rue"),dl("boolean",!0);case"f":return ll(),Dl("alse"),dl("boolean",!1);case"-":case"+":return"-"===ll()&&(il=-1),void(ul="numerical");case".":case"0":case"I":case"N":return void(ul="numerical");case'"':case"'":return ol=Za,ll(),Ya="",void(ul="string")}if(void 0===Za||!Ua.JudgeUtil.isDigitWithoutZero(Za))throw Cl(Ja.Char,ll());ul="numerical"},numerical(){switch(Za){case".":return Ya=ll(),void(ul="decimalPointLeading");case"0":return Ya=ll(),void(ul="zero");case"I":return ll(),Dl("nfinity"),dl("numeric",il*(1/0));case"N":return ll(),Dl("aN"),dl("numeric",NaN)}if(void 0!==Za&&Ua.JudgeUtil.isDigitWithoutZero(Za))return Ya=ll(),void(ul="decimalInteger");throw Cl(Ja.Char,ll())},zero(){switch(Za){case".":case"e":case"E":return void(ul="decimal");case"x":case"X":return Ya+=ll(),void(ul="hexadecimal")}return dl("numeric",0)},decimalInteger(){switch(Za){case".":case"e":case"E":return void(ul="decimal")}if(!Ua.JudgeUtil.isDigit(Za))return dl("numeric",il*Number(Ya));Ya+=ll()},decimal(){switch(Za){case".":Ya+=ll(),ul="decimalFraction";break;case"e":case"E":Ya+=ll(),ul="decimalExponent"}},decimalPointLeading(){if(Ua.JudgeUtil.isDigit(Za))return Ya+=ll(),void(ul="decimalFraction");throw Cl(Ja.Char,ll())},decimalFraction(){switch(Za){case"e":case"E":return Ya+=ll(),void(ul="decimalExponent")}if(!Ua.JudgeUtil.isDigit(Za))return dl("numeric",il*Number(Ya));Ya+=ll()},decimalExponent(){switch(Za){case"+":case"-":return Ya+=ll(),void(ul="decimalExponentSign")}if(Ua.JudgeUtil.isDigit(Za))return Ya+=ll(),void(ul="decimalExponentInteger");throw Cl(Ja.Char,ll())},decimalExponentSign(){if(Ua.JudgeUtil.isDigit(Za))return Ya+=ll(),void(ul="decimalExponentInteger");throw Cl(Ja.Char,ll())},decimalExponentInteger(){if(!Ua.JudgeUtil.isDigit(Za))return dl("numeric",il*Number(Ya));Ya+=ll()},hexadecimal(){if(Ua.JudgeUtil.isHexDigit(Za))return Ya+=ll(),void(ul="hexadecimalInteger");throw Cl(Ja.Char,ll())},hexadecimalInteger(){if(!Ua.JudgeUtil.isHexDigit(Za))return dl("numeric",il*Number(Ya));Ya+=ll()},identifierNameStartEscape(){if("u"!==Za)throw Cl(Ja.Char,ll());ll();const e=pl();switch(e){case"$":case"_":break;default:if(!Ua.JudgeUtil.isIdStartChar(e))throw Cl(Ja.Identifier)}Ya+=e,ul="identifierName"},identifierName(){switch(Za){case"$":case"_":case"‌":case"‍":return void(Ya+=ll());case"\\":return ll(),void(ul="identifierNameEscape")}if(!Ua.JudgeUtil.isIdContinueChar(Za))return dl("identifier",Ya);Ya+=ll()},identifierNameEscape(){if("u"!==Za)throw Cl(Ja.Char,ll());ll();const e=pl();switch(e){case"$":case"_":case"‌":case"‍":break;default:if(!Ua.JudgeUtil.isIdContinueChar(e))throw Cl(Ja.Identifier)}Ya+=e,ul="identifierName"},string(){switch(Za){case"\\":return ll(),void(Ya+=function(){const e=al(),t=function(){switch(al()){case"b":return ll(),"\b";case"f":return ll(),"\f";case"n":return ll(),"\n";case"r":return ll(),"\r";case"t":return ll(),"\t";case"v":return ll(),"\v"}return}();if(t)return t;switch(e){case"0":if(ll(),Ua.JudgeUtil.isDigit(al()))throw Cl(Ja.Char,ll());return"\0";case"x":return ll(),function(){let e="",t=al();if(!Ua.JudgeUtil.isHexDigit(t))throw Cl(Ja.Char,ll());if(e+=ll(),t=al(),!Ua.JudgeUtil.isHexDigit(t))throw Cl(Ja.Char,ll());return e+=ll(),String.fromCodePoint(parseInt(e,16))}();case"u":return ll(),pl();case"\n":case"\u2028":case"\u2029":return ll(),"";case"\r":return ll(),"\n"===al()&&ll(),""}if(void 0===e||Ua.JudgeUtil.isDigitWithoutZero(e))throw Cl(Ja.Char,ll());return ll()}());case'"':case"'":if(Za===ol){const e=dl("string",Ya);return ll(),e}return void(Ya+=ll());case"\n":case"\r":case void 0:throw Cl(Ja.Char,ll());case"\u2028":case"\u2029":!function(e){console.warn(`JSON5: '${yl(e)}' in strings is not valid ECMAScript; consider escaping.`)}(Za)}Ya+=ll()}};function dl(e,t){return{type:e,value:t,line:tl,column:nl}}function Dl(e){for(const t of e){if(al()!==t)throw Cl(Ja.Char,ll());ll()}}function pl(){let e="",t=4;for(;t-- >0;){const t=al();if(!Ua.JudgeUtil.isHexDigit(t))throw Cl(Ja.Char,ll());e+=ll()}return String.fromCodePoint(parseInt(e,16))}const hl={start(){if("eof"===Wa.type)throw Cl(Ja.EOF);El()},beforePropertyName(){switch(Wa.type){case"identifier":case"string":return Ka=Wa.value,void(Xa="afterPropertyName");case"punctuator":return void ml();case"eof":throw Cl(Ja.EOF)}},afterPropertyName(){if("eof"===Wa.type)throw Cl(Ja.EOF);Xa="beforePropertyValue"},beforePropertyValue(){if("eof"===Wa.type)throw Cl(Ja.EOF);El()},afterPropertyValue(){if("eof"===Wa.type)throw Cl(Ja.EOF);switch(Wa.value){case",":return void(Xa="beforePropertyName");case"}":ml()}},beforeArrayValue(){if("eof"===Wa.type)throw Cl(Ja.EOF);"punctuator"!==Wa.type||"]"!==Wa.value?El():ml()},afterArrayValue(){if("eof"===Wa.type)throw Cl(Ja.EOF);switch(Wa.value){case",":return void(Xa="beforeArrayValue");case"]":ml()}},end(){}};function El(){const e=function(){let e;switch(Wa.type){case"punctuator":switch(Wa.value){case"{":e={};break;case"[":e=[]}break;case"null":case"boolean":case"numeric":case"string":e=Wa.value}return e}();if(rl&&"object"==typeof e&&(e._line=tl,e._column=nl),void 0===qa)qa=e;else{const t=Qa[Qa.length-1];Array.isArray(t)?rl&&"object"!=typeof e?t.push({value:e,_line:tl,_column:nl}):t.push(e):t[Ka]=rl&&"object"!=typeof e?{value:e,_line:tl,_column:nl}:e}!function(e){if(e&&"object"==typeof e)Qa.push(e),Xa=Array.isArray(e)?"beforeArrayValue":"beforePropertyName";else{const e=Qa[Qa.length-1];Xa=e?Array.isArray(e)?"afterArrayValue":"afterPropertyValue":"end"}}(e)}function ml(){Qa.pop();const e=Qa[Qa.length-1];Xa=e?Array.isArray(e)?"afterArrayValue":"afterPropertyValue":"end"}function yl(e){const t={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(t[e])return t[e];if(e<" "){const t=e.charCodeAt(0).toString(16);return`\\x${`00${t}`.substring(t.length)}`}return e}function Cl(e,t){let n="";switch(e){case Ja.Char:n=void 0===t?`JSON5: invalid end of input at ${tl}:${nl}`:`JSON5: invalid character '${yl(t)}' at ${tl}:${nl}`;break;case Ja.EOF:n=`JSON5: invalid end of input at ${tl}:${nl}`;break;case Ja.Identifier:nl-=5,n=`JSON5: invalid identifier character at ${tl}:${nl}`}const r=new Fl(n);return r.lineNumber=tl,r.columnNumber=nl,r}class Fl extends SyntaxError{}var gl={},Al={},vl={},Sl=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(vl,"__esModule",{value:!0}),vl.getHvigorUserHomeCacheDir=void 0;const wl=Sl(qn),Ol=Sl(t),_l=Sl(e),bl=h;vl.getHvigorUserHomeCacheDir=function(){const e=_l.default.resolve(Ol.default.homedir(),bl.HVIGOR_USER_HOME_DIR_NAME),t=process.env.HVIGOR_USER_HOME;return void 0!==t&&_l.default.isAbsolute(t)?(wl.default.ensureDirSync(t),t):e},function(t){var n=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.HVIGOR_PROJECT_WRAPPER_HOME=t.HVIGOR_PROJECT_ROOT_DIR=t.HVIGOR_PROJECT_CACHES_HOME=t.HVIGOR_PNPM_STORE_PATH=t.HVIGOR_WRAPPER_PNPM_SCRIPT_PATH=t.HVIGOR_WRAPPER_TOOLS_HOME=t.HVIGOR_USER_HOME=void 0;const r=n(e),u=vl,o=h;t.HVIGOR_USER_HOME=(0,u.getHvigorUserHomeCacheDir)(),t.HVIGOR_WRAPPER_TOOLS_HOME=r.default.resolve(t.HVIGOR_USER_HOME,"wrapper","tools"),t.HVIGOR_WRAPPER_PNPM_SCRIPT_PATH=r.default.resolve(t.HVIGOR_WRAPPER_TOOLS_HOME,"node_modules",".bin",o.PNPM_TOOL),t.HVIGOR_PNPM_STORE_PATH=r.default.resolve(t.HVIGOR_USER_HOME,"caches"),t.HVIGOR_PROJECT_CACHES_HOME=r.default.resolve(t.HVIGOR_USER_HOME,o.PROJECT_CACHES),t.HVIGOR_PROJECT_ROOT_DIR=process.cwd(),t.HVIGOR_PROJECT_WRAPPER_HOME=r.default.resolve(t.HVIGOR_PROJECT_ROOT_DIR,o.HVIGOR)}(Al);var Bl={},Pl=p&&p.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var u=Object.getOwnPropertyDescriptor(t,n);u&&!("get"in u?!t.__esModule:u.writable||u.configurable)||(u={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,u)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),Il=p&&p.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),kl=p&&p.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&Pl(t,e,n);return Il(t,e),t},xl=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Bl,"__esModule",{value:!0}),Bl.isFileExists=Bl.offlinePluginConversion=Bl.executeCommand=Bl.getNpmPath=Bl.hasNpmPackInPaths=void 0;const Nl=r,Rl=xl(n),Tl=kl(e),Ml=h,Ll=O;Bl.hasNpmPackInPaths=function(e,t){try{return require.resolve(e,{paths:[...t]}),!0}catch(e){return!1}},Bl.getNpmPath=function(){const e=process.execPath;return Tl.join(Tl.dirname(e),Ml.NPM_TOOL)},Bl.executeCommand=function(e,t,n){0!==(0,Nl.spawnSync)(e,t,n).status&&(0,Ll.logErrorAndExit)(`Error: ${e} ${t} execute failed.See above for details.`)},Bl.offlinePluginConversion=function(e,t){return t.startsWith("file:")||t.endsWith(".tgz")?Tl.resolve(e,Ml.HVIGOR,t.replace("file:","")):t},Bl.isFileExists=function(e){return Rl.default.existsSync(e)&&Rl.default.statSync(e).isFile()},function(u){var o=p&&p.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var u=Object.getOwnPropertyDescriptor(t,n);u&&!("get"in u?!t.__esModule:u.writable||u.configurable)||(u={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,u)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=p&&p.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=p&&p.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&o(t,e,n);return i(t,e),t},c=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(u,"__esModule",{value:!0}),u.executeInstallPnpm=u.isPnpmInstalled=u.environmentHandler=u.checkNpmConifg=u.PNPM_VERSION=void 0;const a=r,l=s(n),f=c(t),d=s(e),D=h,E=Al,m=Al,y=O,C=Bl;u.PNPM_VERSION="7.30.0",u.checkNpmConifg=function(){const e=d.resolve(E.HVIGOR_PROJECT_ROOT_DIR,".npmrc"),t=d.resolve(f.default.homedir(),".npmrc");if((0,C.isFileExists)(e)||(0,C.isFileExists)(t))return;const n=(0,C.getNpmPath)(),r=(0,a.spawnSync)(n,["config","get","prefix"],{cwd:E.HVIGOR_PROJECT_ROOT_DIR});if(0!==r.status||!r.stdout)return void(0,y.logErrorAndExit)("Error: The hvigor depends on the npmrc file. Configure the npmrc file first.");const u=d.resolve(`${r.stdout}`.replace(/[\r\n]/gi,""),".npmrc");(0,C.isFileExists)(u)||(0,y.logErrorAndExit)("Error: The hvigor depends on the npmrc file. Configure the npmrc file first.")},u.environmentHandler=function(){process.env["npm_config_update-notifier"]="false"},u.isPnpmInstalled=function(){return!!l.existsSync(m.HVIGOR_WRAPPER_PNPM_SCRIPT_PATH)&&(0,C.hasNpmPackInPaths)("pnpm",[m.HVIGOR_WRAPPER_TOOLS_HOME])},u.executeInstallPnpm=function(){(0,y.logInfo)(`Installing pnpm@${u.PNPM_VERSION}...`);const e=(0,C.getNpmPath)();!function(){const e=d.resolve(m.HVIGOR_WRAPPER_TOOLS_HOME,D.DEFAULT_PACKAGE_JSON);try{l.existsSync(m.HVIGOR_WRAPPER_TOOLS_HOME)||l.mkdirSync(m.HVIGOR_WRAPPER_TOOLS_HOME,{recursive:!0});const t={dependencies:{}};t.dependencies[D.PNPM]=u.PNPM_VERSION,l.writeFileSync(e,JSON.stringify(t))}catch(t){(0,y.logErrorAndExit)(`Error: EPERM: operation not permitted,create ${e} failed.`)}}(),(0,C.executeCommand)(e,["install","pnpm"],{cwd:m.HVIGOR_WRAPPER_TOOLS_HOME,stdio:["inherit","inherit","inherit"],env:process.env}),(0,y.logInfo)("Pnpm install success.")}}(gl);var jl={},$l={},Hl=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty($l,"__esModule",{value:!0}),$l.getHvigorUserHomeCacheDir=void 0;const Gl=Hl(qn),Vl=Hl(t),Ul=Hl(e),Jl=h,Wl=O;let zl=!1;$l.getHvigorUserHomeCacheDir=function(){const e=Ul.default.resolve(Vl.default.homedir(),Jl.HVIGOR_USER_HOME_DIR_NAME),t=process.env.HVIGOR_USER_HOME;return void 0===t?e:Ul.default.isAbsolute(t)?Gl.default.existsSync(t)&&Gl.default.statSync(t).isFile()?((0,Wl.logInfo)(`File already exists: ${t}`),e):(Gl.default.ensureDirSync(t),t):(zl||((0,Wl.logInfo)(`Invalid custom userhome hvigor data dir:${t}`),zl=!0),e)},function(t){var n=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.HVIGOR_PROJECT_WRAPPER_HOME=t.HVIGOR_PROJECT_ROOT_DIR=t.HVIGOR_PNPM_STORE_PATH=t.HVIGOR_WRAPPER_PNPM_SCRIPT_PATH=t.HVIGOR_WRAPPER_TOOLS_HOME=t.HVIGOR_USER_HOME=void 0;const r=n(e),u=h,o=$l;t.HVIGOR_USER_HOME=(0,o.getHvigorUserHomeCacheDir)(),t.HVIGOR_WRAPPER_TOOLS_HOME=r.default.resolve(t.HVIGOR_USER_HOME,"wrapper","tools"),t.HVIGOR_WRAPPER_PNPM_SCRIPT_PATH=r.default.resolve(t.HVIGOR_WRAPPER_TOOLS_HOME,"node_modules",".bin",u.PNPM_TOOL),t.HVIGOR_PNPM_STORE_PATH=r.default.resolve(t.HVIGOR_USER_HOME,"caches"),t.HVIGOR_PROJECT_ROOT_DIR=process.cwd(),t.HVIGOR_PROJECT_WRAPPER_HOME=r.default.resolve(t.HVIGOR_PROJECT_ROOT_DIR,u.HVIGOR)}(jl);var Kl=p&&p.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var u=Object.getOwnPropertyDescriptor(t,n);u&&!("get"in u?!t.__esModule:u.writable||u.configurable)||(u={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,u)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),ql=p&&p.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),Yl=p&&p.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&Kl(t,e,n);return ql(t,e),t},Zl=p&&p.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(R,"__esModule",{value:!0});var Xl=R.initProjectWorkSpace=void 0;const Ql=Yl(n),ef=Zl(qn),tf=Zl(t),nf=Yl(e),rf=Zl(c),uf=Yn,of=h,sf=xa,cf=Na,af=Ra,lf=O,ff=gl,df=Bl,Df=jl;let pf,hf,Ef;function mf(e,t,n){return void 0!==n.dependencies&&(0,df.offlinePluginConversion)(Df.HVIGOR_PROJECT_ROOT_DIR,t.dependencies[e])===nf.normalize(n.dependencies[e])}Xl=R.initProjectWorkSpace=function(){if(pf=function(){const e=nf.resolve(Df.HVIGOR_PROJECT_WRAPPER_HOME,of.DEFAULT_HVIGOR_CONFIG_JSON_FILE_NAME);Ql.existsSync(e)||(0,lf.logErrorAndExit)(`Error: Hvigor config file ${e} does not exist.`);return(0,af.parseJsonFile)(e)}(),Ef=function(e){let t;t=!(0,sf.isCI)()&&function(e){const t=e.hvigorVersion;if(t.startsWith("file:")||t.endsWith(".tgz"))return!1;const n=e.dependencies,r=Object.getOwnPropertyNames(n);for(const e of r){const t=n[e];if(t.startsWith("file:")||t.endsWith(".tgz"))return!1}if(1===r.length&&"@ohos/hvigor-ohos-plugin"===r[0])return t>"2.5.0";return!1}(e)?function(e){let t=`${of.HVIGOR_ENGINE_PACKAGE_NAME}@${e.hvigorVersion}`;const n=e.dependencies;if(n){Object.getOwnPropertyNames(n).sort().forEach((e=>{t+=`,${e}@${n[e]}`}))}return(0,cf.hash)(t)}(e):(0,cf.hash)(rf.default.cwd());return nf.resolve(Df.HVIGOR_USER_HOME,"project_caches",t)}(pf),hf=function(){const e=nf.resolve(Ef,of.WORK_SPACE,of.DEFAULT_PACKAGE_JSON);return Ql.existsSync(e)?(0,af.parseJsonFile)(e):{dependencies:{}}}(),function(){const e=nf.resolve(Df.HVIGOR_USER_HOME,of.DEFAULT_HVIGOR_CONFIG_JSON_FILE_NAME);if(Ql.existsSync(e))(0,af.parseJsonFile)(e)}(),!(0,df.hasNpmPackInPaths)(of.HVIGOR_ENGINE_PACKAGE_NAME,[nf.join(Ef,of.WORK_SPACE)])||(0,df.offlinePluginConversion)(Df.HVIGOR_PROJECT_ROOT_DIR,pf.hvigorVersion)!==hf.dependencies[of.HVIGOR_ENGINE_PACKAGE_NAME]||!function(){function e(e){const t=null==e?void 0:e.dependencies;return void 0===t?0:Object.getOwnPropertyNames(t).length}const t=e(pf),n=e(hf);if(t+1!==n)return!1;for(const e in null==pf?void 0:pf.dependencies)if(!(0,df.hasNpmPackInPaths)(e,[nf.join(Ef,of.WORK_SPACE)])||!mf(e,pf,hf))return!1;return!0}())try{const e=rf.default.hrtime();(0,ff.checkNpmConifg)(),function(){(0,lf.logInfo)("Hvigor installing...");for(const e in pf.dependencies)pf.dependencies[e]&&(pf.dependencies[e]=(0,df.offlinePluginConversion)(Df.HVIGOR_PROJECT_ROOT_DIR,pf.dependencies[e]));const e={dependencies:{...pf.dependencies}};e.dependencies[of.HVIGOR_ENGINE_PACKAGE_NAME]=(0,df.offlinePluginConversion)(Df.HVIGOR_PROJECT_ROOT_DIR,pf.hvigorVersion);const t=nf.join(Ef,of.WORK_SPACE);try{Ql.mkdirSync(t,{recursive:!0});const n=nf.resolve(t,of.DEFAULT_PACKAGE_JSON);Ql.writeFileSync(n,JSON.stringify(e))}catch(e){(0,lf.logErrorAndExit)(e)}(function(){const e=["config","set","store-dir",Df.HVIGOR_PNPM_STORE_PATH],t={cwd:nf.join(Ef,of.WORK_SPACE),stdio:["inherit","inherit","inherit"]};(0,df.executeCommand)(Df.HVIGOR_WRAPPER_PNPM_SCRIPT_PATH,e,t)})(),function(){const e=["install"];(0,sf.isCI)()&&e.push("--no-frozen-lockfile");ef.default.existsSync(nf.resolve(Df.HVIGOR_PROJECT_ROOT_DIR,".npmrc"))&&(rf.default.env.npm_config_userconfig=function(e){const t=nf.resolve(Df.HVIGOR_USER_HOME,"project_caches",(0,cf.hash)(rf.default.cwd()),".npmrc");try{const n=ef.default.readFileSync(nf.resolve(tf.default.homedir(),".npmrc"),"utf-8"),r=`${n}\n${ef.default.readFileSync(e,"utf-8")}`;ef.default.ensureFileSync(t),ef.default.writeFileSync(t,r)}catch(e){(0,lf.logErrorAndExit)(e)}return t}(nf.resolve(Df.HVIGOR_PROJECT_ROOT_DIR,".npmrc")));const t={cwd:nf.join(Ef,of.WORK_SPACE),stdio:["inherit","inherit","inherit"],env:rf.default.env};(0,df.executeCommand)(Df.HVIGOR_WRAPPER_PNPM_SCRIPT_PATH,e,t)}(),(0,lf.logInfo)("Hvigor install success.")}();const t=rf.default.hrtime(e);uf.hvigorTrace.HVIGOR_INSTALL_TIME=1e9*t[0]+t[1]}catch(e){!function(){const e=nf.join(Ef,of.WORK_SPACE);if((0,lf.logInfo)("Hvigor cleaning..."),!Ql.existsSync(e))return;const t=Ql.readdirSync(e);if(!t||0===t.length)return;const n=nf.resolve(Ef,"node_modules","@ohos","hvigor","bin","hvigor.js");Ql.existsSync(n)&&(0,df.executeCommand)(rf.default.argv[0],[n,"--stop-daemon"],{});try{t.forEach((t=>{Ql.rmSync(nf.resolve(e,t),{recursive:!0})}))}catch(t){(0,lf.logErrorAndExit)(`The hvigor build tool cannot be installed. Please manually clear the workspace directory and synchronize the project again.\n\n      Workspace Path: ${e}.`)}}()}return Ef},function(){gl.environmentHandler(),gl.isPnpmInstalled()||(gl.checkNpmConifg(),gl.executeInstallPnpm());const t=Xl();P(e.join(t,S))}();