{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-ur\\values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9604", "endColumns": "100", "endOffsets": "9700"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,896,988,1082,1177,1271,1372,1466,1562,1656,1748,1840,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "214,320,429,515,619,739,816,891,983,1077,1172,1266,1367,1461,1557,1651,1743,1835,1920,2028,2134,2236,2347,2448,2564,2729,2827,2913"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "264,378,484,593,679,783,903,980,1055,1147,1241,1336,1430,1531,1625,1721,1815,1907,1999,2084,2192,2298,2400,2511,2612,2728,2893,9518", "endColumns": "113,105,108,85,103,119,76,74,91,93,94,93,100,93,95,93,91,91,84,107,105,101,110,100,115,164,97,85", "endOffsets": "373,479,588,674,778,898,975,1050,1142,1236,1331,1425,1526,1620,1716,1810,1902,1994,2079,2187,2293,2395,2506,2607,2723,2888,2986,9599"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ur\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,457,579,686,822,945,1053,1153,1301,1407,1575,1699,1841,2006,2065,2128", "endColumns": "103,159,121,106,135,122,107,99,147,105,167,123,141,164,58,62,83", "endOffsets": "296,456,578,685,821,944,1052,1152,1300,1406,1574,1698,1840,2005,2064,2127,2211"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3371,3479,3643,3769,3880,4020,4147,4259,4515,4667,4777,4949,5077,5223,5392,5455,5522", "endColumns": "107,163,125,110,139,126,111,103,151,109,171,127,145,168,62,66,87", "endOffsets": "3474,3638,3764,3875,4015,4142,4254,4358,4662,4772,4944,5072,5218,5387,5450,5517,5605"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4363", "endColumns": "151", "endOffsets": "4510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,214,292,392,511,594,659,752,822,881,971,1040,1098,1167,1227,1291,1403,1462,1521,1576,1651,1774,1854,1938,2041,2123,2204,2291,2358,2424,2499,2579,2664,2731,2806,2883,2947,3041,3111,3200,3293,3367,3442,3532,3588,3655,3739,3823,3885,3949,4012,4112,4219,4313,4422", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,77,99,118,82,64,92,69,58,89,68,57,68,59,63,111,58,58,54,74,122,79,83,102,81,80,86,66,65,74,79,84,66,74,76,63,93,69,88,92,73,74,89,55,66,83,83,61,63,62,99,106,93,108,79", "endOffsets": "209,287,387,506,589,654,747,817,876,966,1035,1093,1162,1222,1286,1398,1457,1516,1571,1646,1769,1849,1933,2036,2118,2199,2286,2353,2419,2494,2574,2659,2726,2801,2878,2942,3036,3106,3195,3288,3362,3437,3527,3583,3650,3734,3818,3880,3944,4007,4107,4214,4308,4417,4497"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2991,3069,3169,3288,5610,5675,5768,5838,5897,5987,6056,6114,6183,6243,6307,6419,6478,6537,6592,6667,6790,6870,6954,7057,7139,7220,7307,7374,7440,7515,7595,7680,7747,7822,7899,7963,8057,8127,8216,8309,8383,8458,8548,8604,8671,8755,8839,8901,8965,9028,9128,9235,9329,9438", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,77,99,118,82,64,92,69,58,89,68,57,68,59,63,111,58,58,54,74,122,79,83,102,81,80,86,66,65,74,79,84,66,74,76,63,93,69,88,92,73,74,89,55,66,83,83,61,63,62,99,106,93,108,79", "endOffsets": "259,3064,3164,3283,3366,5670,5763,5833,5892,5982,6051,6109,6178,6238,6302,6414,6473,6532,6587,6662,6785,6865,6949,7052,7134,7215,7302,7369,7435,7510,7590,7675,7742,7817,7894,7958,8052,8122,8211,8304,8378,8453,8543,8599,8666,8750,8834,8896,8960,9023,9123,9230,9324,9433,9513"}}]}]}