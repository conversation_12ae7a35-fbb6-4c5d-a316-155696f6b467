{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeReleaseResources\\merged.dir\\values-tl\\values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "285,396,504,617,705,811,926,1006,1083,1174,1267,1362,1456,1556,1649,1744,1838,1929,2020,2104,2213,2323,2424,2534,2652,2760,2923,9887", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "391,499,612,700,806,921,1001,1078,1169,1262,1357,1451,1551,1644,1739,1833,1924,2015,2099,2208,2318,2419,2529,2647,2755,2918,3020,9967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,235,321,426,562,647,712,811,879,938,1027,1094,1157,1232,1300,1354,1474,1532,1594,1648,1723,1865,1955,2040,2155,2239,2322,2418,2485,2551,2625,2703,2794,2868,2947,3020,3092,3196,3269,3368,3468,3542,3617,3724,3776,3843,3934,4028,4090,4154,4217,4336,4438,4547,4650", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,104,135,84,64,98,67,58,88,66,62,74,67,53,119,57,61,53,74,141,89,84,114,83,82,95,66,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,84", "endOffsets": "230,316,421,557,642,707,806,874,933,1022,1089,1152,1227,1295,1349,1469,1527,1589,1643,1718,1860,1950,2035,2150,2234,2317,2413,2480,2546,2620,2698,2789,2863,2942,3015,3087,3191,3264,3363,3463,3537,3612,3719,3771,3838,3929,4023,4085,4149,4212,4331,4433,4542,4645,4730"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,3025,3111,3216,3352,5799,5864,5963,6031,6090,6179,6246,6309,6384,6452,6506,6626,6684,6746,6800,6875,7017,7107,7192,7307,7391,7474,7570,7637,7703,7777,7855,7946,8020,8099,8172,8244,8348,8421,8520,8620,8694,8769,8876,8928,8995,9086,9180,9242,9306,9369,9488,9590,9699,9802", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,85,104,135,84,64,98,67,58,88,66,62,74,67,53,119,57,61,53,74,141,89,84,114,83,82,95,66,65,73,77,90,73,78,72,71,103,72,98,99,73,74,106,51,66,90,93,61,63,62,118,101,108,102,84", "endOffsets": "280,3106,3211,3347,3432,5859,5958,6026,6085,6174,6241,6304,6379,6447,6501,6621,6679,6741,6795,6870,7012,7102,7187,7302,7386,7469,7565,7632,7698,7772,7850,7941,8015,8094,8167,8239,8343,8416,8515,8615,8689,8764,8871,8923,8990,9081,9175,9237,9301,9364,9483,9585,9694,9797,9882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9972", "endColumns": "100", "endOffsets": "10068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3437,3544,3720,3858,3967,4125,4261,4383,4641,4820,4927,5105,5243,5405,5584,5652,5718", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "3539,3715,3853,3962,4120,4256,4378,4491,4815,4922,5100,5238,5400,5579,5647,5713,5794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4496", "endColumns": "144", "endOffsets": "4636"}}]}]}