#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯SO库分析工具
分析libtersafe2.so中的内存陷阱实现方法
"""

import re
import struct
import sys

def analyze_elf_header(data):
    """分析ELF头部信息"""
    print("=== ELF Header Analysis ===")
    if len(data) < 64:
        print("文件太小，不是有效的ELF文件")
        return False
    
    # ELF魔数检查
    if data[:4] != b'\x7fELF':
        print("不是有效的ELF文件")
        return False
    
    # 架构信息
    arch = "32位" if data[4] == 1 else "64位"
    endian = "小端" if data[5] == 1 else "大端"
    print(f"架构: {arch}, 字节序: {endian}")
    
    # 机器类型
    machine = struct.unpack('<H', data[18:20])[0]
    machine_types = {
        0x28: "ARM",
        0xB7: "AArch64", 
        0x3E: "x86_64",
        0x03: "x86"
    }
    print(f"目标架构: {machine_types.get(machine, f'未知({machine})')}")
    
    return True

def find_strings(data, min_length=4):
    """查找二进制文件中的字符串"""
    print("\n=== String Analysis ===")
    
    # 查找可打印字符串
    strings = []
    current_string = b""
    
    for byte in data:
        if 32 <= byte <= 126:  # 可打印ASCII字符
            current_string += bytes([byte])
        else:
            if len(current_string) >= min_length:
                try:
                    strings.append(current_string.decode('ascii'))
                except:
                    pass
            current_string = b""
    
    # 最后一个字符串
    if len(current_string) >= min_length:
        try:
            strings.append(current_string.decode('ascii'))
        except:
            pass
    
    return strings

def analyze_memory_patterns(strings):
    """分析内存相关的模式"""
    print("\n=== Memory Pattern Analysis ===")
    
    # 关键词模式
    memory_keywords = [
        r'malloc', r'calloc', r'realloc', r'free',
        r'mmap', r'munmap', r'mprotect',
        r'\.data', r'\.bss', r'\.text', r'\.rodata',
        r'alloc', r'memory', r'heap', r'stack',
        r'trap', r'hook', r'detect', r'anti',
        r'ca_', r'cb_', r'segment', r'region'
    ]
    
    found_patterns = {}
    for keyword in memory_keywords:
        pattern = re.compile(keyword, re.IGNORECASE)
        matches = []
        for s in strings:
            if pattern.search(s):
                matches.append(s)
        if matches:
            found_patterns[keyword] = matches[:10]  # 限制显示前10个
    
    # 显示结果
    for keyword, matches in found_patterns.items():
        print(f"\n关键词 '{keyword}' 匹配:")
        for match in matches:
            print(f"  - {match}")
    
    return found_patterns

def analyze_function_names(strings):
    """分析函数名模式"""
    print("\n=== Function Name Analysis ===")
    
    # C++函数名模式 (mangled names)
    cpp_functions = []
    for s in strings:
        if s.startswith('_Z') and len(s) > 5:  # C++ mangled names
            cpp_functions.append(s)
    
    print(f"发现 {len(cpp_functions)} 个C++函数名:")
    for func in cpp_functions[:20]:  # 显示前20个
        print(f"  - {func}")
    
    # JNI函数模式
    jni_functions = []
    for s in strings:
        if 'Java_' in s or 'JNI_' in s:
            jni_functions.append(s)
    
    if jni_functions:
        print(f"\n发现 {len(jni_functions)} 个JNI函数:")
        for func in jni_functions:
            print(f"  - {func}")
    
    return cpp_functions, jni_functions

def analyze_section_info(data):
    """分析段信息"""
    print("\n=== Section Analysis ===")
    
    # 查找段名字符串
    section_patterns = [
        b'.text\x00', b'.data\x00', b'.bss\x00', b'.rodata\x00',
        b'.init\x00', b'.fini\x00', b'.plt\x00', b'.got\x00'
    ]
    
    found_sections = []
    for pattern in section_patterns:
        if pattern in data:
            section_name = pattern.decode('ascii').rstrip('\x00')
            found_sections.append(section_name)
            print(f"发现段: {section_name}")
    
    return found_sections

def analyze_tencent_specific(strings):
    """分析腾讯特有的实现模式"""
    print("\n=== 腾讯特有模式分析 ===")

    # 腾讯相关的关键词
    tencent_patterns = [
        r'tss', r'tp2', r'TssSdk', r'tersafe',
        r'anti_data', r'hook', r'inline',
        r'random_trap', r'gen_random'
    ]

    tencent_findings = {}
    for pattern in tencent_patterns:
        regex = re.compile(pattern, re.IGNORECASE)
        matches = []
        for s in strings:
            if regex.search(s):
                matches.append(s)
        if matches:
            tencent_findings[pattern] = matches

    # 显示腾讯特有的实现
    for pattern, matches in tencent_findings.items():
        print(f"\n腾讯模式 '{pattern}':")
        for match in matches[:15]:  # 显示前15个
            print(f"  - {match}")

    return tencent_findings

def analyze_memory_allocation_strategy(strings):
    """分析内存分配策略"""
    print("\n=== 内存分配策略分析 ===")

    # 查找内存分配相关的字符串
    allocation_strings = []
    for s in strings:
        if any(keyword in s.lower() for keyword in ['malloc', 'mmap', 'alloc', 'heap', 'memory']):
            allocation_strings.append(s)

    print(f"发现 {len(allocation_strings)} 个内存分配相关字符串:")
    for s in allocation_strings[:20]:  # 显示前20个
        print(f"  - {s}")

    # 分析可能的GG识别策略
    print(f"\n=== GG修改器识别策略推测 ===")

    # 查找可能与GG相关的模式
    gg_related = []
    for s in strings:
        if any(keyword in s.lower() for keyword in ['ca_', 'cb_', 'segment', 'region', 'section']):
            gg_related.append(s)

    if gg_related:
        print("可能的GG识别相关字符串:")
        for s in gg_related:
            print(f"  - {s}")
    else:
        print("未发现明显的GG识别相关字符串")

    return allocation_strings, gg_related

def main():
    filename = "app/libtersafe2.so"

    try:
        with open(filename, 'rb') as f:
            data = f.read()

        print(f"分析文件: {filename}")
        print(f"文件大小: {len(data)} 字节 ({len(data)/1024/1024:.2f} MB)")

        # 分析ELF头部
        if not analyze_elf_header(data):
            return

        # 查找字符串
        print("\n正在提取字符串...")
        strings = find_strings(data, min_length=4)
        print(f"发现 {len(strings)} 个字符串")

        # 分析内存模式
        memory_patterns = analyze_memory_patterns(strings)

        # 分析函数名
        cpp_funcs, jni_funcs = analyze_function_names(strings)

        # 分析段信息
        sections = analyze_section_info(data)

        # 腾讯特有模式分析
        tencent_patterns = analyze_tencent_specific(strings)

        # 内存分配策略分析
        alloc_strings, gg_strings = analyze_memory_allocation_strategy(strings)

        # 总结分析结果
        print("\n=== 分析总结 ===")
        print(f"- 发现内存相关关键词: {len(memory_patterns)} 类")
        print(f"- 发现C++函数: {len(cpp_funcs)} 个")
        print(f"- 发现JNI函数: {len(jni_funcs)} 个")
        print(f"- 发现ELF段: {len(sections)} 个")
        print(f"- 发现腾讯特有模式: {len(tencent_patterns)} 类")
        print(f"- 发现内存分配字符串: {len(alloc_strings)} 个")
        print(f"- 发现GG相关字符串: {len(gg_strings)} 个")

        # 重点关注的模式
        important_patterns = ['malloc', 'mmap', 'alloc', 'trap', 'hook']
        print(f"\n重点关注的内存管理模式:")
        for pattern in important_patterns:
            if pattern in memory_patterns:
                print(f"  ✓ {pattern}: {len(memory_patterns[pattern])} 个匹配")
            else:
                print(f"  ✗ {pattern}: 未发现")

        # 腾讯实现的关键发现
        print(f"\n=== 腾讯实现关键发现 ===")
        key_findings = [
            "1. 使用 'random_trap' 机制 - 可能是随机内存陷阱",
            "2. 有 'inline_hook' 相关实现 - 内联钩子技术",
            "3. 'tss_sdk_rcv_anti_data' - 反作弊数据接收",
            "4. 'tp2_free_anti_data' - TP2反作弊数据释放",
            "5. 使用标准ELF段(.data, .bss) - 符合GG识别模式",
            "6. 'ms_mmap' 和 'ms_set_inlie_hook' - 自定义内存管理"
        ]

        for finding in key_findings:
            print(f"  {finding}")

    except FileNotFoundError:
        print(f"错误: 找不到文件 {filename}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
