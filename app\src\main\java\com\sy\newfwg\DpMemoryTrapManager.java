package com.sy.newfwg;

import android.util.Log;

/**
 * dp方案内存陷阱管理器
 * 基于腾讯ACE SDK的纯净实现
 */
public class DpMemoryTrapManager {
    private static final String TAG = "DpMemoryTrapManager";
    
    // 单例实例
    private static DpMemoryTrapManager instance;
    private boolean initialized = false;
    
    // 私有构造函数
    private DpMemoryTrapManager() {}
    
    // 获取单例实例
    public static synchronized DpMemoryTrapManager getInstance() {
        if (instance == null) {
            instance = new DpMemoryTrapManager();
        }
        return instance;
    }
    
    /**
     * 初始化dp内存陷阱系统
     */
    public boolean initialize() {
        if (initialized) {
            Log.w(TAG, "dp内存陷阱系统已经初始化");
            return true;
        }
        
        try {
            Log.i(TAG, "🚀 初始化dp内存陷阱系统...");
            nativeInitialize();
            initialized = true;
            
            // 输出统计信息
            dumpMemoryInfo();
            
            Log.i(TAG, "✅ dp内存陷阱系统初始化成功");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "❌ dp内存陷阱系统初始化失败", e);
            return false;
        }
    }
    
    /**
     * 部署Ca区域陷阱
     */
    public void deployCaTraps(int count) {
        if (!initialized) {
            Log.w(TAG, "系统未初始化，无法部署Ca陷阱");
            return;
        }
        
        Log.i(TAG, "🔧 部署Ca区域陷阱: " + count + "个");
        nativeDeployCaTraps(count);
    }
    
    /**
     * 部署Cb区域陷阱
     */
    public void deployCbTraps() {
        if (!initialized) {
            Log.w(TAG, "系统未初始化，无法部署Cb陷阱");
            return;
        }
        
        Log.i(TAG, "🔧 部署Cb区域陷阱");
        nativeDeployCbTraps();
    }
    
    /**
     * 验证陷阱完整性
     */
    public void verifyTraps() {
        if (!initialized) {
            Log.w(TAG, "系统未初始化，无法验证陷阱");
            return;
        }
        
        Log.i(TAG, "🔍 验证陷阱完整性");
        nativeVerifyTraps();
    }
    
    /**
     * 输出内存信息
     */
    public void dumpMemoryInfo() {
        if (!initialized) {
            Log.w(TAG, "系统未初始化，无法输出内存信息");
            return;
        }
        
        nativeDumpMemoryInfo();
        
        // 输出Java端统计
        long caSize = getCaMemorySize();
        long cbSize = getCbMemorySize();
        int caCount = getCaTrapCount();
        int cbCount = getCbTrapCount();
        
        Log.i(TAG, "📊 Java端统计:");
        Log.i(TAG, "   Ca区域: " + caCount + "个陷阱, " + (caSize / 1024.0 / 1024.0) + "MB");
        Log.i(TAG, "   Cb区域: " + cbCount + "个陷阱, " + (cbSize / 1024.0) + "KB");
        Log.i(TAG, "   总计: " + (caCount + cbCount) + "个陷阱, " + 
              ((caSize + cbSize) / 1024.0 / 1024.0) + "MB");
    }
    
    /**
     * 关闭系统
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        Log.i(TAG, "🛑 关闭dp内存陷阱系统");
        nativeShutdown();
        initialized = false;
    }
    
    /**
     * 获取Ca区域内存大小
     */
    public long getCaMemorySize() {
        return initialized ? nativeGetCaMemorySize() : 0;
    }
    
    /**
     * 获取Cb区域内存大小
     */
    public long getCbMemorySize() {
        return initialized ? nativeGetCbMemorySize() : 0;
    }
    
    /**
     * 获取Ca陷阱数量
     */
    public int getCaTrapCount() {
        return initialized ? nativeGetCaTrapCount() : 0;
    }
    
    /**
     * 获取Cb陷阱数量
     */
    public int getCbTrapCount() {
        return initialized ? nativeGetCbTrapCount() : 0;
    }
    
    /**
     * 检查系统是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    // Native方法声明
    private native void nativeInitialize();
    private native void nativeDeployCaTraps(int count);
    private native void nativeDeployCbTraps();
    private native void nativeVerifyTraps();
    private native void nativeDumpMemoryInfo();
    private native void nativeShutdown();
    private native long nativeGetCaMemorySize();
    private native long nativeGetCbMemorySize();
    private native int nativeGetCaTrapCount();
    private native int nativeGetCbTrapCount();
    
    // 静态加载库
    static {
        try {
            System.loadLibrary("memorytrap");
            Log.i(TAG, "✅ dp内存陷阱库加载成功");
        } catch (UnsatisfiedLinkError e) {
            Log.e(TAG, "❌ dp内存陷阱库加载失败", e);
        }
    }
}
