#include "memory_trap_sdk.h"
#include <android/log.h>
#include <sys/mman.h>
#include <unistd.h>
#include <cstring>
#include <signal.h>
#include <vector>
#include <mutex>
#include <chrono>
#include <thread>
#include <errno.h>
#include <map>
#include <atomic>
#include <new>

#define TAG "MemTrapDetector"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, TAG, "[V3.0] " __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, TAG, "[V3.0] " __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, TAG, "[V3.0] " __VA_ARGS__)

namespace MemoryTrapSDK {

// 增强的陷阱结构 - 参考腾讯ACE
struct EnhancedTrap {
    void* address;
    size_t size;
    uint32_t target_value;
    bool is_active;

    // 统计信息 - 类似腾讯的tick:20,ac:2,rc:3
    int access_count;      // ac: 访问计数
    int read_count;        // rc: 读取计数
    int tick_count;        // tick: 时间戳计数

    std::chrono::steady_clock::time_point create_time;
    std::chrono::steady_clock::time_point last_hit;

    // 访问模式分析
    std::vector<std::chrono::steady_clock::time_point> access_times;
    std::vector<void*> access_addresses;
};

// 全局变量
static std::vector<EnhancedTrap> g_traps;
static std::mutex g_trap_mutex;
static DetectionCallback g_callback = nullptr;
static struct sigaction g_old_sigsegv;
static bool g_handler_installed = false;

// 新增：主动检测变量
static std::atomic<bool> g_active_monitoring{false};

// 函数声明
bool createUnitySpecificTraps(uint32_t target_value);
static std::thread g_monitor_thread;

// 新增：内存访问统计 - 按照专业建议
static std::atomic<int> g_memory_access_count{0};
static std::chrono::steady_clock::time_point g_last_reset_time;
static std::map<pid_t, int> g_process_access_count;
static std::mutex g_access_mutex;

// 监控阈值：10秒内超过50次跨进程内存访问
#define SCAN_THRESHOLD 50
#define TIME_WINDOW_SECONDS 10

// 新增：蜜罐内存区域
struct HoneyPot {
    void* address;
    size_t size;
    uint32_t original_value;
    uint32_t current_value;
    std::chrono::steady_clock::time_point create_time;
    int check_count;
};

static std::vector<HoneyPot> g_honeypots;
static std::mutex g_honeypot_mutex;

// 函数声明
void activeMemoryMonitor();
bool startActiveMonitoring();
void stopActiveMonitoring();

// 增强的信号处理器 - 参考腾讯ACE的详细统计
void memTrapSignalHandler(int sig, siginfo_t* info, void* context) {
    void* fault_addr = info->si_addr;
    auto now = std::chrono::steady_clock::now();

    LOGW("🚨 内存陷阱触发！");
    LOGW("   信号: %d", sig);
    LOGW("   故障地址: %p", fault_addr);
    LOGW("   错误码: %d", info->si_code);

    // 查找匹配的陷阱
    std::lock_guard<std::mutex> lock(g_trap_mutex);
    for (auto& trap : g_traps) {
        if (fault_addr >= trap.address &&
            fault_addr < (char*)trap.address + trap.size) {

            // 更新统计信息 - 类似腾讯的格式
            trap.access_count++;
            trap.read_count++;
            trap.tick_count = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - trap.create_time).count();
            trap.last_hit = now;

            // 记录访问历史
            trap.access_times.push_back(now);
            trap.access_addresses.push_back(fault_addr);

            // 只保留最近10次访问记录
            if (trap.access_times.size() > 10) {
                trap.access_times.erase(trap.access_times.begin());
                trap.access_addresses.erase(trap.access_addresses.begin());
            }

            LOGW("🎯 命中陷阱！");
            LOGW("   陷阱地址: %p", trap.address);
            LOGW("   陷阱大小: %zu", trap.size);
            LOGW("   目标值: %u", trap.target_value);

            // 增加内存访问计数 - 关键检测点
            g_memory_access_count.fetch_add(1);
            LOGW("   当前访问计数: %d", g_memory_access_count.load());

            // 记录详细的访问信息
            auto now = std::chrono::steady_clock::now();
            auto time_since_create = std::chrono::duration_cast<std::chrono::seconds>(
                now - trap.create_time).count();

            LOGW("🚨🚨🚨 [V2.0] 陷阱访问确认！");
            LOGW("   陷阱ID: #%zu", &trap - &g_traps[0] + 1);
            LOGW("   目标值: %u", trap.target_value);
            LOGW("   访问次数: %d", trap.access_count);
            LOGW("   创建后经过: %lds", time_since_create);
            LOGW("   这证明修改器正在扫描我们的陷阱！");

            // 类似腾讯ACE的统计格式: ptr:0x7f6c684c000,tick:20,ac:2,rc:3
            LOGW("   统计信息: ptr:%p,tick:%d,ac:%d,rc:%d",
                 trap.address, trap.tick_count, trap.access_count, trap.read_count);

            // 访问模式分析 - 类似腾讯的风险评估
            std::string risk_level = "🟡 可疑行为";  // 默认黄色警告
            std::string behavior_type = "内存扫描";

            if (trap.access_count >= 3) {
                risk_level = "🔴 高危行为";  // 红色高危
                behavior_type = "修改器扫描";
            }

            // 分析访问频率
            if (trap.access_times.size() >= 2) {
                auto time_span = std::chrono::duration_cast<std::chrono::milliseconds>(
                    trap.access_times.back() - trap.access_times.front()).count();

                if (time_span < 1000 && trap.access_count >= 2) {
                    risk_level = "🔴 高危行为";
                    behavior_type = "快速扫描";
                }

                LOGW("   访问频率: %d次/%dms", (int)trap.access_times.size(), (int)time_span);
            }

            LOGW("   风险等级: %s", risk_level.c_str());
            LOGW("   行为类型: %s", behavior_type.c_str());
            
            // 临时解除保护，让修改器能读取到值
            if (mprotect(trap.address, trap.size, PROT_READ | PROT_WRITE) == 0) {
                LOGW("✅ 临时解除陷阱保护");
                
                // 确保陷阱中有目标值
                uint32_t* trap_data = (uint32_t*)trap.address;
                for (size_t i = 0; i < trap.size / sizeof(uint32_t); i++) {
                    trap_data[i] = trap.target_value;
                }
                
                LOGW("✅ 重新设置陷阱数据为: %u", trap.target_value);
            }
            
            // 立即触发检测回调
            if (g_callback) {
                DetectionEvent event;
                event.fault_address = fault_addr;
                event.trap_info = nullptr; // 简化版不需要
                event.timestamp = std::chrono::steady_clock::now();
                event.description = "内存陷阱命中检测";
                event.analysis.is_modifier_scan = true;
                event.analysis.total_accesses = trap.access_count;
                event.analysis.reason = "内存陷阱被访问";
                
                LOGW("🚨🚨🚨 触发检测回调！🚨🚨🚨");
                g_callback(event);
            }
            
            // 重新保护陷阱（延迟一点，让修改器先读取）
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            mprotect(trap.address, trap.size, PROT_NONE);
            LOGW("🔒 重新保护陷阱");
            
            return;
        }
    }
    
    LOGE("❌ 未找到匹配的陷阱，故障地址: %p", fault_addr);
    
    // 如果不是我们的陷阱，恢复默认处理
    sigaction(SIGSEGV, &g_old_sigsegv, nullptr);
    raise(SIGSEGV);
}

// 创建内存陷阱
bool createMemTrap(uint32_t target_value, size_t count) {
    LOGI("🔧 创建内存陷阱...");
    LOGI("   目标值: %u", target_value);
    LOGI("   数量: %zu", count);
    
    std::lock_guard<std::mutex> lock(g_trap_mutex);
    
    for (size_t i = 0; i < count; i++) {
        // 分配内存页
        size_t page_size = getpagesize();
        void* addr = mmap(nullptr, page_size, PROT_READ | PROT_WRITE, 
                         MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
        
        if (addr == MAP_FAILED) {
            LOGE("❌ 分配内存失败: %s", strerror(errno));
            continue;
        }
        
        LOGI("✅ [V2.0] 分配Anonymous内存页: %p (大小: %zu)", addr, page_size);

        // 填充目标值
        uint32_t* data = (uint32_t*)addr;
        size_t value_count = page_size / sizeof(uint32_t);

        for (size_t j = 0; j < value_count; j++) {
            data[j] = target_value;
        }

        LOGI("✅ [V2.0] 填充Dword值: %u, 数量: %zu个", target_value, value_count);
        LOGI("   地址范围: %p - %p", addr, (char*)addr + page_size - 1);
        
        // 设置为不可访问（陷阱状态）
        if (mprotect(addr, page_size, PROT_NONE) != 0) {
            LOGE("❌ 设置内存保护失败: %s", strerror(errno));
            munmap(addr, page_size);
            continue;
        }
        
        LOGI("🔒 设置内存保护: PROT_NONE");
        
        // 添加到陷阱列表
        EnhancedTrap trap;
        trap.address = addr;
        trap.size = page_size;
        trap.target_value = target_value;
        trap.is_active = true;

        // 初始化统计信息
        trap.access_count = 0;
        trap.read_count = 0;
        trap.tick_count = 0;
        trap.create_time = std::chrono::steady_clock::now();
        
        g_traps.push_back(trap);
        
        LOGI("🎯 陷阱[%zu]创建成功: %p", i, addr);
    }
    
    LOGI("🎯 [V2.0] 陷阱创建完成统计:");
    LOGI("   目标值: %u (Dword类型)", target_value);
    LOGI("   陷阱数量: %zu 个", g_traps.size());
    LOGI("   每个陷阱大小: 4096 字节");
    LOGI("   每个陷阱包含Dword数: %zu 个", 4096 / sizeof(uint32_t));
    LOGI("   总Dword数量: %zu 个", g_traps.size() * (4096 / sizeof(uint32_t)));
    LOGI("   内存类型: Anonymous (匿名内存)");
    LOGI("   保护状态: PROT_NONE (完全保护)");

    return !g_traps.empty();
}

// 安装信号处理器
bool installMemTrapHandler() {
    if (g_handler_installed) {
        LOGW("信号处理器已安装");
        return true;
    }
    
    LOGI("🔧 安装内存陷阱信号处理器...");
    
    struct sigaction sa;
    memset(&sa, 0, sizeof(sa));
    sa.sa_sigaction = memTrapSignalHandler;
    sa.sa_flags = SA_SIGINFO | SA_RESTART;
    sigemptyset(&sa.sa_mask);
    
    if (sigaction(SIGSEGV, &sa, &g_old_sigsegv) == -1) {
        LOGE("❌ 安装SIGSEGV处理器失败: %s", strerror(errno));
        return false;
    }
    
    g_handler_installed = true;
    LOGI("✅ 信号处理器安装成功");
    return true;
}

// 卸载信号处理器
void uninstallMemTrapHandler() {
    if (!g_handler_installed) {
        return;
    }
    
    LOGI("🔧 卸载信号处理器...");
    sigaction(SIGSEGV, &g_old_sigsegv, nullptr);
    g_handler_installed = false;
    LOGI("✅ 信号处理器已卸载");
}

// 清理所有陷阱
void cleanupMemTraps() {
    LOGI("🧹 清理内存陷阱...");
    
    std::lock_guard<std::mutex> lock(g_trap_mutex);
    for (auto& trap : g_traps) {
        if (trap.address) {
            LOGI("   清理陷阱: %p", trap.address);
            munmap(trap.address, trap.size);
        }
    }
    
    g_traps.clear();
    LOGI("✅ 所有陷阱已清理");
}

// 获取陷阱统计信息 - 类似腾讯ACE格式
void printTrapStats() {
    LOGI("📊 陷阱统计信息 (类似腾讯ACE格式):");

    std::lock_guard<std::mutex> lock(g_trap_mutex);
    for (size_t i = 0; i < g_traps.size(); i++) {
        const auto& trap = g_traps[i];

        auto now = std::chrono::steady_clock::now();
        int current_tick = std::chrono::duration_cast<std::chrono::milliseconds>(
            now - trap.create_time).count();

        LOGI("   陷阱[%zu]: ptr:%p,tick:%d,ac:%d,rc:%d,活跃:%s",
             i, trap.address, current_tick, trap.access_count, trap.read_count,
             trap.is_active ? "是" : "否");

        if (trap.access_count > 0) {
            LOGI("     最后访问: %d次访问记录", (int)trap.access_times.size());
        }
    }
}

// 设置回调
void setMemTrapCallback(DetectionCallback callback) {
    g_callback = callback;
    LOGI("✅ 设置检测回调");
}

// 创建蜜罐内存 - 不受保护，用于诱导修改器
bool createHoneyPots(uint32_t target_value, size_t count) {
    LOGI("🍯 创建蜜罐内存区域...");
    LOGI("   目标值: %u, 数量: %zu", target_value, count);

    std::lock_guard<std::mutex> lock(g_honeypot_mutex);

    for (size_t i = 0; i < count; i++) {
        // 分配普通内存（不保护）
        size_t size = 4096; // 1页
        void* addr = malloc(size);

        if (!addr) {
            LOGE("❌ 分配蜜罐内存失败");
            continue;
        }

        // 填充目标值
        uint32_t* data = static_cast<uint32_t*>(addr);
        size_t value_count = size / sizeof(uint32_t);

        for (size_t j = 0; j < value_count; j++) {
            data[j] = target_value;
        }

        // 创建蜜罐记录
        HoneyPot honeypot;
        honeypot.address = addr;
        honeypot.size = size;
        honeypot.original_value = target_value;
        honeypot.current_value = target_value;
        honeypot.create_time = std::chrono::steady_clock::now();
        honeypot.check_count = 0;

        g_honeypots.push_back(honeypot);

        LOGI("🍯 创建蜜罐 #%zu: %p (大小: %zu)", i+1, addr, size);
    }

    LOGI("🍯 [V2.0] 蜜罐创建完成统计:");
    LOGI("   目标值: %u (Dword类型)", target_value);
    LOGI("   蜜罐数量: %zu 个", g_honeypots.size());
    LOGI("   每个蜜罐大小: 4096 字节");
    LOGI("   每个蜜罐包含Dword数: %zu 个", 4096 / sizeof(uint32_t));
    LOGI("   总Dword数量: %zu 个", g_honeypots.size() * (4096 / sizeof(uint32_t)));
    LOGI("   内存类型: Anonymous (匿名内存)");
    LOGI("   保护状态: 无保护 (可读写)");

    return true;
}

// 检测可疑进程 - V2.0增强版本，过滤系统模块
bool detectSuspiciousModules() {
    LOGI("🔍 [V2.0] 检测可疑进程，扫描用户空间模块...");

    // 检查/proc/self/maps中的可疑模块
    FILE* maps = fopen("/proc/self/maps", "r");
    if (!maps) {
        LOGE("❌ 无法打开/proc/self/maps");
        return false;
    }

    char line[512];
    bool found_suspicious = false;
    int total_count = 0;
    int filtered_count = 0;

    LOGI("📋 [V2.0] 用户空间模块列表 (已过滤系统模块):");

    while (fgets(line, sizeof(line), maps)) {
        total_count++;

        // 移除换行符
        line[strcspn(line, "\n")] = 0;

        // 过滤掉系统模块，只关注用户空间
        if (strstr(line, "/apex/") ||
            strstr(line, "/system/") ||
            strstr(line, "/vendor/") ||
            strstr(line, "/product/") ||
            strstr(line, "[anon:") ||
            strstr(line, "[stack") ||
            strstr(line, "[heap]") ||
            strstr(line, "[vdso]") ||
            strstr(line, "[vsyscall]")) {
            continue; // 跳过系统模块
        }

        // 只显示包含.so文件或用户数据目录的行
        if (strstr(line, ".so") || strstr(line, "/data/")) {
            filtered_count++;
            LOGI("   [%d] %s", filtered_count, line);

            // 检查修改器相关模块 - 扩展检测列表
            if (strstr(line, "libgg.so") ||
                strstr(line, "com.speedygamer") ||
                strstr(line, "gameguardian") ||
                strstr(line, "libcheat") ||
                strstr(line, "libhook") ||
                strstr(line, "cheat") ||
                strstr(line, "hack") ||
                strstr(line, "mod") ||
                strstr(line, "xposed") ||
                strstr(line, "substrate") ||
                strstr(line, "frida") ||
                strstr(line, "magisk") ||
                strstr(line, "riru") ||
                strstr(line, "zygisk") ||
                strstr(line, "lsposed")) {

                LOGW("🚨🚨🚨 [V2.0] 发现可疑模块: %s", line);
                found_suspicious = true;
            }
        }
    }

    fclose(maps);

    LOGI("📊 [V2.0] 模块扫描完成: 总计%d个，用户空间%d个", total_count, filtered_count);

    if (found_suspicious) {
        LOGW("🔴 [V2.0] 检测到修改器相关模块！");
        return true;
    }

    LOGI("✅ [V2.0] 未发现可疑模块");
    return false;
}

// 检测内存访问频率 - 核心检测逻辑
void checkMemoryAccessFrequency() {
    std::lock_guard<std::mutex> lock(g_access_mutex);

    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
        now - g_last_reset_time).count();

    if (elapsed >= TIME_WINDOW_SECONDS) {
        // 重置计数器
        if (g_memory_access_count.load() > SCAN_THRESHOLD) {
            LOGW("🚨🚨🚨 检测到高频内存访问！🚨🚨🚨");
            LOGW("   时间窗口: %ld秒", elapsed);
            LOGW("   访问次数: %d", g_memory_access_count.load());
            LOGW("   阈值: %d", SCAN_THRESHOLD);
            LOGW("   疑似修改器扫描行为！");
        }

        g_memory_access_count.store(0);
        g_last_reset_time = now;
    }
}

// 检查蜜罐是否被修改
void checkHoneyPots() {
    std::lock_guard<std::mutex> lock(g_honeypot_mutex);

    LOGI("🍯 检查蜜罐状态，当前蜜罐数量: %zu", g_honeypots.size());

    if (g_honeypots.empty()) {
        LOGW("⚠️ 没有蜜罐可检查！");
        return;
    }

    for (auto& honeypot : g_honeypots) {
        honeypot.check_count++;

        // 检查内存内容是否被修改
        uint32_t* data = static_cast<uint32_t*>(honeypot.address);
        size_t value_count = honeypot.size / sizeof(uint32_t);

        int modified_count = 0;
        uint32_t first_modified_value = 0;

        for (size_t i = 0; i < value_count; i++) {
            if (data[i] != honeypot.original_value) {
                if (modified_count == 0) {
                    first_modified_value = data[i];
                }
                modified_count++;
            }
        }

        if (modified_count > 0) {
            LOGW("🚨🚨🚨 蜜罐被修改！检测到修改器！🚨🚨🚨");
            LOGW("   蜜罐地址: %p", honeypot.address);
            LOGW("   原始值: %u", honeypot.original_value);
            LOGW("   修改后值: %u", first_modified_value);
            LOGW("   修改数量: %d/%zu", modified_count, value_count);
            LOGW("   检查次数: %d", honeypot.check_count);

            auto now = std::chrono::steady_clock::now();
            auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(
                now - honeypot.create_time).count();
            LOGW("   存活时间: %lds", elapsed);

            if (g_callback) {
                DetectionEvent event;
                event.fault_address = honeypot.address;
                event.description = "蜜罐被修改 - 确认检测到修改器";
                event.timestamp = now;
                g_callback(event);
            }

            // 重置蜜罐值，继续监控
            for (size_t i = 0; i < value_count; i++) {
                data[i] = honeypot.original_value;
            }

            LOGI("🔄 蜜罐已重置，继续监控");
        }
    }
}

// 启动内存陷阱检测 - V3.0 Unity专用版本
bool startMemTrapDetection(uint32_t target_value, size_t trap_count) {
    LOGI("🚀 [V3.0] 启动Unity内存陷阱检测系统");
    LOGI("   版本: V3.0 - Unity内存架构专用");
    LOGI("   主目标值: %u", target_value);
    LOGI("   陷阱数量: %zu", trap_count);
    LOGI("   策略: Ch(C++堆) + Jh(C#堆) + A(匿名) + Cd(.data)");

    // 1. 创建Unity专用陷阱 - 重点改进！
    LOGI("🎮 优先创建Unity专用内存陷阱...");

    if (!createUnitySpecificTraps(target_value)) {
        LOGE("❌ 创建Unity专用陷阱失败");
        return false;
    }

    // 2. 创建传统Anonymous陷阱作为补充
    LOGI("🔧 创建补充Anonymous陷阱...");
    if (!createMemTrap(target_value, trap_count)) {
        LOGE("❌ 创建Anonymous陷阱失败");
        return false;
    }

    // 2. 创建更多诱饵陷阱 - 常见的搜索值
    uint32_t decoy_values[] = {999, 1000, 1234, 9999, 12345, 100, 200, 500};
    for (uint32_t decoy : decoy_values) {
        createMemTrap(decoy, 5);  // 每个诱饵值创建5个陷阱
        LOGI("   创建诱饵陷阱: %u (5个)", decoy);
    }

    LOGI("🎯 总陷阱数量: %zu + %zu = %zu",
         trap_count, sizeof(decoy_values)/sizeof(decoy_values[0]) * 5,
         trap_count + sizeof(decoy_values)/sizeof(decoy_values[0]) * 5);

    // 3. 创建蜜罐内存 - 关键创新！
    LOGI("🍯 创建蜜罐内存系统...");

    LOGI("🍯 创建主目标蜜罐: %u", target_value);
    bool result1 = createHoneyPots(target_value, 5);  // 创建5个蜜罐
    LOGI("🍯 主目标蜜罐创建结果: %s", result1 ? "成功" : "失败");

    for (uint32_t decoy : decoy_values) {
        LOGI("🍯 创建诱饵蜜罐: %u", decoy);
        bool result2 = createHoneyPots(decoy, 2);  // 每个诱饵值创建2个蜜罐
        LOGI("🍯 诱饵蜜罐创建结果: %s", result2 ? "成功" : "失败");
    }

    LOGI("🍯 蜜罐系统部署完成，总蜜罐数量应为: %d", 5 + 8*2);

    // 4. V3.0 Unity专用系统总体统计
    size_t total_traps = g_traps.size();
    size_t total_honeypots = g_honeypots.size();
    size_t total_dwords_protected = total_traps * (4096 / sizeof(uint32_t));
    size_t total_dwords_honeypot = total_honeypots * (4096 / sizeof(uint32_t));
    size_t target_1111_dwords = 0;

    LOGI("🎮 === V3.0 Unity内存陷阱系统部署汇总 ===");

    // 计算包含1111值的Dword数量
    for (const auto& trap : g_traps) {
        if (trap.target_value == 1111) {
            target_1111_dwords += 4096 / sizeof(uint32_t);
        }
    }
    for (const auto& honeypot : g_honeypots) {
        if (honeypot.original_value == 1111) {
            target_1111_dwords += 4096 / sizeof(uint32_t);
        }
    }

    LOGI("📊 === V3.0 Unity内存陷阱系统最终统计 ===");
    LOGI("🎮 Unity专用陷阱:");
    LOGI("   Ch（C++非托管堆）: ~20个");
    LOGI("   Cd（C++ .data段）: ~1个");
    LOGI("🔧 传统Anonymous陷阱:");
    LOGI("   保护陷阱: %zu个", total_traps - 21);
    LOGI("   蜜罐陷阱: %zu个", total_honeypots);
    LOGI("📈 系统总计:");
    LOGI("   总陷阱数: %zu个", total_traps + total_honeypots);
    LOGI("   保护的Dword数: %zu个", total_dwords_protected);
    LOGI("   蜜罐的Dword数: %zu个", total_dwords_honeypot);
    LOGI("   包含1111值的Dword: %zu个", target_1111_dwords);
    LOGI("   总内存占用: %zu KB", (total_traps + total_honeypots) * 4);
    LOGI("==========================================");

    // 2. 安装信号处理器
    if (!installMemTrapHandler()) {
        LOGE("❌ 安装信号处理器失败");
        cleanupMemTraps();
        return false;
    }
    
    // 3. 启动主动监控
    if (!startActiveMonitoring()) {
        LOGE("❌ 启动主动监控失败");
        cleanupMemTraps();
        return false;
    }

    // 4. 打印统计信息
    printTrapStats();

    LOGI("🎯 内存陷阱检测系统启动成功！（信号+主动监控）");
    LOGI("💡 现在可以用GG搜索值 %u 来测试检测", target_value);

    return true;
}

// 停止检测
void stopMemTrapDetection() {
    LOGI("🛑 停止内存陷阱检测");

    // 停止主动监控
    stopActiveMonitoring();

    uninstallMemTrapHandler();
    cleanupMemTraps();
    g_callback = nullptr;

    LOGI("✅ 内存陷阱检测已停止");
}

// 新增：主动监控函数 - V2.0增强版本
void activeMemoryMonitor() {
    LOGI("🔍 [V2.0] 启动主动内存监控线程");
    LOGI("🧵 [V2.0] 线程开始运行，监控状态: %s", g_active_monitoring.load() ? "true" : "false");

    int loop_count = 0;
    while (g_active_monitoring.load()) {
        try {
            loop_count++;
            LOGI("🔄 监控循环 #%d", loop_count);

            // 使用try_lock避免死锁
            std::unique_lock<std::mutex> lock(g_trap_mutex, std::try_to_lock);
            if (!lock.owns_lock()) {
                LOGW("⚠️ 无法获取陷阱锁，跳过本轮检查");
                std::this_thread::sleep_for(std::chrono::milliseconds(500));
                continue;
            }

            LOGI("🔒 获得陷阱锁，当前陷阱数量: %zu", g_traps.size());

            if (g_traps.empty()) {
                LOGW("⚠️ 没有陷阱可检查");
                lock.unlock();
                std::this_thread::sleep_for(std::chrono::seconds(2));
                continue;
            }

            int checked_traps = 0;
            for (auto& trap : g_traps) {
                if (!trap.is_active) continue;

                checked_traps++;

                // 新的检测策略：不直接访问受保护内存，而是检查访问统计
                try {
                    // 检查这个陷阱是否被访问过
                    if (trap.access_count > 0) {
                        auto now = std::chrono::steady_clock::now();
                        auto time_since_last = std::chrono::duration_cast<std::chrono::milliseconds>(
                            now - trap.last_hit).count();

                        LOGW("🚨 检测到陷阱被访问！");
                        LOGW("   陷阱地址: %p", trap.address);
                        LOGW("   目标值: %u", trap.target_value);
                        LOGW("   访问次数: %d", trap.access_count);
                        LOGW("   距离上次访问: %ldms", time_since_last);

                        // 分析访问模式
                        if (trap.access_count >= 3) {
                            LOGW("🔴 高危：多次访问，疑似修改器扫描！");

                            if (g_callback) {
                                DetectionEvent event;
                                event.fault_address = trap.address;
                                event.description = "疑似修改器扫描";
                                event.timestamp = now;
                                g_callback(event);
                            }
                        } else if (trap.access_count >= 1) {
                            LOGW("🟡 可疑：检测到内存访问");
                        }
                    }
                } catch (...) {
                    LOGE("❌ 检查陷阱统计时发生异常: %p", trap.address);
                }
            }

            LOGI("✅ 检查完成，共检查 %d 个活跃陷阱", checked_traps);
            lock.unlock();

            // 检查蜜罐内存
            LOGI("🍯 开始检查蜜罐...");
            checkHoneyPots();
            LOGI("🍯 蜜罐检查完成");

            // 检测可疑进程 - 新增
            if (loop_count % 5 == 0) { // 每15秒检查一次
                LOGI("🔍 执行可疑进程检测...");
                if (detectSuspiciousModules()) {
                    LOGW("🚨 发现可疑进程！");
                }
            }

            // 检查内存访问频率 - 核心检测
            checkMemoryAccessFrequency();

            // 每3秒检查一次
            std::this_thread::sleep_for(std::chrono::seconds(3));

        } catch (const std::exception& e) {
            LOGE("❌ 监控循环异常: %s", e.what());
            std::this_thread::sleep_for(std::chrono::seconds(1));
        } catch (...) {
            LOGE("❌ 监控循环未知异常");
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
    }

    LOGI("🔍 主动内存监控线程结束");
}

// 新增：获取陷阱访问统计
std::string getTrapAccessStatistics() {
    std::lock_guard<std::mutex> lock(g_trap_mutex);

    std::string result = "[V2.0] === 陷阱访问统计 ===\n";

    int total_traps = g_traps.size();
    int accessed_traps = 0;
    int total_access_count = 0;

    result += "保护陷阱统计:\n";
    for (size_t i = 0; i < g_traps.size(); i++) {
        const auto& trap = g_traps[i];
        if (trap.access_count > 0) {
            accessed_traps++;
            total_access_count += trap.access_count;

            result += "  陷阱#" + std::to_string(i+1) + ": ";
            result += "值=" + std::to_string(trap.target_value);
            result += ", 访问=" + std::to_string(trap.access_count) + "次\n";
        }
    }

    // 蜜罐统计
    std::lock_guard<std::mutex> honeypot_lock(g_honeypot_mutex);
    int modified_honeypots = 0;

    result += "\n蜜罐统计:\n";
    for (size_t i = 0; i < g_honeypots.size(); i++) {
        const auto& honeypot = g_honeypots[i];

        // 检查蜜罐是否被修改
        uint32_t* data = static_cast<uint32_t*>(honeypot.address);
        bool is_modified = false;

        for (size_t j = 0; j < honeypot.size / sizeof(uint32_t); j++) {
            if (data[j] != honeypot.original_value) {
                is_modified = true;
                break;
            }
        }

        if (is_modified) {
            modified_honeypots++;
            result += "  蜜罐#" + std::to_string(i+1) + ": 被修改！\n";
        }
    }

    result += "\n总结:\n";
    result += "- 总陷阱数: " + std::to_string(total_traps) + "\n";
    result += "- 被访问陷阱: " + std::to_string(accessed_traps) + "\n";
    result += "- 总访问次数: " + std::to_string(total_access_count) + "\n";
    result += "- 被修改蜜罐: " + std::to_string(modified_honeypots) + "/" + std::to_string(g_honeypots.size()) + "\n";
    result += "- 内存访问计数: " + std::to_string(g_memory_access_count.load()) + "\n";

    if (accessed_traps > 0 || modified_honeypots > 0) {
        result += "\n🚨 结论: 检测到修改器活动！\n";
    } else {
        result += "\n✅ 结论: 未检测到修改器活动\n";
    }

    return result;
}

// Unity引擎数据结构 - 移到全局
struct UnityEngineData {
    int health;
    float cooldown;
    int maxHealth;
    int experience;
    float speed;
    int level;

    UnityEngineData(uint32_t target_val) {
        health = target_val;
        cooldown = 5.0f;
        maxHealth = target_val * 10;
        experience = target_val * 2;
        speed = 100.0f;
        level = target_val / 100;
    }
};

// 新增：Unity专用陷阱创建
bool createUnitySpecificTraps(uint32_t target_value) {
    LOGI("🎮 [V3.0] 创建Unity专用内存陷阱...");

    // 1. Ch（C++非托管堆）陷阱 - 最高优先级
    LOGI("🔧 创建Ch（C++非托管堆）陷阱...");

    // 在真正的C++堆上分配多个伪装数据块
    for (int i = 0; i < 20; i++) {
        // 使用new分配大块内存，确保进入C++ heap
        size_t large_size = sizeof(UnityEngineData) * 10;  // 分配更大的块
        UnityEngineData* data = (UnityEngineData*)new char[large_size];

        // 在大块中设置我们的数据
        data->health = target_value + i;
        data->cooldown = 5.0f;
        data->level = 1;
        data->experience = 0;

        // 不使用mprotect保护，避免崩溃
        // 依靠其他方式检测访问

        EnhancedTrap trap;
        trap.address = data;
        trap.size = large_size;
        trap.target_value = target_value + i;
        trap.is_active = true;
        trap.access_count = 0;
        trap.create_time = std::chrono::steady_clock::now();

        std::lock_guard<std::mutex> lock(g_trap_mutex);
        g_traps.push_back(trap);

        LOGI("✅ Ch陷阱 #%d: %p (血量=%d)", i+1, data, data->health);
    }

    LOGI("🎮 Ch（C++非托管堆）陷阱创建完成: 20个");

    // 1.5. Ca（C++ alloc）陷阱 - 使用malloc分配
    LOGI("🔧 创建Ca（C++ alloc）陷阱...");

    for (int i = 0; i < 10; i++) {
        // 使用malloc分配，这应该进入C++ alloc区域
        UnityEngineData* data = (UnityEngineData*)malloc(sizeof(UnityEngineData));
        if (data) {
            data->health = target_value + i + 100;  // 1211, 1212, 1213...
            data->cooldown = 3.0f;
            data->level = 1;
            data->experience = 0;

            EnhancedTrap trap;
            trap.address = data;
            trap.size = sizeof(UnityEngineData);
            trap.target_value = target_value + i + 100;
            trap.is_active = true;
            trap.access_count = 0;
            trap.create_time = std::chrono::steady_clock::now();

            std::lock_guard<std::mutex> lock(g_trap_mutex);
            g_traps.push_back(trap);

            LOGI("✅ Ca陷阱 #%d: %p (血量=%d)", i+1, data, data->health);
        }
    }

    LOGI("🎮 Ca（C++ alloc）陷阱创建完成: 10个");

    // 2. Cd（C++ .data段）陷阱 - 全局静态变量
    LOGI("📊 创建Cd（C++ .data段）陷阱...");

    // 创建全局静态变量（模拟游戏配置）
    static int s_maxHealthLimit = target_value * 100;    // 血量上限
    static float s_baseCooldown = 1.0f;                  // 基础CD系数
    static int s_gameVersion = target_value;             // 游戏版本号
    static float s_damageMultiplier = 1.5f;              // 伤害倍数

    // 暂时不保护静态变量，避免崩溃
    // 只记录这些变量作为监控目标
    EnhancedTrap trap;
    trap.address = &s_maxHealthLimit;
    trap.size = sizeof(int) * 4; // 4个静态变量
    trap.target_value = target_value;
    trap.is_active = true;
    trap.access_count = 0;
    trap.create_time = std::chrono::steady_clock::now();

    std::lock_guard<std::mutex> lock(g_trap_mutex);
    g_traps.push_back(trap);

    LOGI("✅ Cd陷阱: %p (配置值=%d)", &s_maxHealthLimit, s_maxHealthLimit);

    // 统计各区域陷阱数量
    int ch_traps = 0;
    int cd_traps = 0;

    {
        std::lock_guard<std::mutex> lock(g_trap_mutex);
        for (const auto& trap : g_traps) {
            // 简单判断：如果地址在堆区域，认为是Ch陷阱
            if ((uintptr_t)trap.address > 0x10000000) {
                ch_traps++;
            } else {
                cd_traps++;
            }
        }
    }

    LOGI("🎮 === Unity专用陷阱创建汇总 ===");
    LOGI("   Ch（C++非托管堆）陷阱: %d个", ch_traps);
    LOGI("   Cd（C++ .data段）陷阱: %d个", cd_traps);
    LOGI("   总Unity专用陷阱: %d个", ch_traps + cd_traps);
    LOGI("🎮 Unity专用陷阱创建完成");
    return true;
}

// 启动主动监控
bool startActiveMonitoring() {
    LOGI("🔧 尝试启动主动监控...");

    if (g_active_monitoring.load()) {
        LOGW("⚠️ 主动监控已经在运行");
        return true;
    }

    try {
        g_active_monitoring.store(true);
        g_monitor_thread = std::thread(activeMemoryMonitor);

        LOGI("✅ 主动监控启动成功");
        LOGI("🧵 监控线程ID: %p", &g_monitor_thread);
        return true;
    } catch (const std::exception& e) {
        LOGE("❌ 启动主动监控失败: %s", e.what());
        g_active_monitoring.store(false);
        return false;
    }
}

// 停止主动监控
void stopActiveMonitoring() {
    if (!g_active_monitoring.load()) {
        return;
    }

    g_active_monitoring.store(false);
    if (g_monitor_thread.joinable()) {
        g_monitor_thread.join();
    }

    LOGI("🛑 主动监控已停止");
}

} // namespace MemoryTrapSDK
