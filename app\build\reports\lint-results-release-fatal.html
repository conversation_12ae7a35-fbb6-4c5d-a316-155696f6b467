<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<title>Lint Report</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
 <link rel="stylesheet" href="https://code.getmdl.io/1.2.1/material.blue-indigo.min.css" />
<link rel="stylesheet" href="http://fonts.googleapis.com/css?family=Roboto:300,400,500,700" type="text/css">
<script defer src="https://code.getmdl.io/1.2.0/material.min.js"></script>
<style>
section.section--center {
    max-width: 860px;
}
.mdl-card__supporting-text + .mdl-card__actions {
    border-top: 1px solid rgba(0, 0, 0, 0.12);
}
main > .mdl-layout__tab-panel {
  padding: 8px;
  padding-top: 48px;
}

.mdl-card__actions {
    margin: 0;
    padding: 4px 40px;
    color: inherit;
}
.mdl-card > * {
    height: auto;
}
.mdl-card__actions a {
    color: #00BCD4;
    margin: 0;
}
.error-icon {
    color: #bb7777;
    vertical-align: bottom;
}
.warning-icon {
    vertical-align: bottom;
}
.mdl-layout__content section:not(:last-of-type) {
  position: relative;
  margin-bottom: 48px;
}

.mdl-card .mdl-card__supporting-text {
  margin: 40px;
  -webkit-flex-grow: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  padding: 0;
  color: inherit;
  width: calc(100% - 80px);
}
div.mdl-layout__drawer-button .material-icons {
    line-height: 48px;
}
.mdl-card .mdl-card__supporting-text {
    margin-top: 0px;
}
.chips {
    float: right;
    vertical-align: middle;
}

pre.errorlines {
    background-color: white;
    font-family: monospace;
    border: 1px solid #e0e0e0;
    line-height: 0.9rem;
    font-size: 0.9rem;    padding: 1px 0px 1px; 1px;
    overflow: scroll;
}
.prefix {
    color: #660e7a;
    font-weight: bold;
}
.attribute {
    color: #0000ff;
    font-weight: bold;
}
.value {
    color: #008000;
    font-weight: bold;
}
.tag {
    color: #000080;
    font-weight: bold;
}
.comment {
    color: #808080;
    font-style: italic;
}
.javadoc {
    color: #808080;
    font-style: italic;
}
.annotation {
    color: #808000;
}
.string {
    color: #008000;
    font-weight: bold;
}
.number {
    color: #0000ff;
}
.keyword {
    color: #000080;
    font-weight: bold;
}
.caretline {
    background-color: #fffae3;
}
.lineno {
    color: #999999;
    background-color: #f0f0f0;
}
.error {
    display: inline-block;
    position:relative;
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH4AwCFR4T/3uLMgAAADxJREFUCNdNyLERQEAABMCjL4lQwIzcjErpguAL+C9AvgKJDbeD/PRpLdm35Hm+MU+cB+tCKaJW4L4YBy+CAiLJrFs9mgAAAABJRU5ErkJggg==) bottom repeat-x;
}
.warning {
    text-decoration: none;
    background-color: #f6ebbc;
}
.overview {
    padding: 10pt;
    width: 100%;
    overflow: auto;
    border-collapse:collapse;
}
.overview tr {
    border-bottom: solid 1px #eeeeee;
}
.categoryColumn a {
     text-decoration: none;
     color: inherit;
}
.countColumn {
    text-align: right;
    padding-right: 20px;
    width: 50px;
}
.issueColumn {
   padding-left: 16px;
}
.categoryColumn {
   position: relative;
   left: -50px;
   padding-top: 20px;
   padding-bottom: 5px;
}
</style>
<script language="javascript" type="text/javascript">
<!--
function reveal(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'block';
document.getElementById(id+'Link').style.display = 'none';
}
}
function hideid(id) {
if (document.getElementById) {
document.getElementById(id).style.display = 'none';
}
}
//-->
</script>
</head>
<body class="mdl-color--grey-100 mdl-color-text--grey-700 mdl-base">
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">
  <header class="mdl-layout__header">
    <div class="mdl-layout__header-row">
      <span class="mdl-layout-title">Lint Report: 1 error</span>
      <div class="mdl-layout-spacer"></div>
      <nav class="mdl-navigation mdl-layout--large-screen-only">
Check performed at Fri Aug 01 09:43:07 CST 2025      </nav>
    </div>
  </header>
  <div class="mdl-layout__drawer">
    <span class="mdl-layout-title">Issue Types</span>
    <nav class="mdl-navigation">
      <a class="mdl-navigation__link" href="#overview"><i class="material-icons">dashboard</i>Overview</a>
      <a class="mdl-navigation__link" href="#HardcodedDebugMode"><i class="material-icons error-icon">error</i>Hardcoded value of <code>android:debuggable</code> in the manifest (1)</a>
    </nav>
  </div>
  <main class="mdl-layout__content">
    <div class="mdl-layout__tab-panel is-active">
<a name="overview"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="OverviewCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Overview</h2>
  </div>
              <div class="mdl-card__supporting-text">
<table class="overview">
<tr><td class="countColumn"></td><td class="categoryColumn"><a href="#Security">Security</a>
</td></tr>
<tr>
<td class="countColumn">1</td><td class="issueColumn"><i class="material-icons error-icon">error</i>
<a href="#HardcodedDebugMode">HardcodedDebugMode</a>: Hardcoded value of <code>android:debuggable</code> in the manifest</td></tr>
<tr><td></td><td class="categoryColumn"><a href="#MissingIssues">Disabled Checks (349)</a>
</td></tr></table>
<br/>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="OverviewCardLink" onclick="hideid('OverviewCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="Security"></a>
<a name="HardcodedDebugMode"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="HardcodedDebugModeCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Hardcoded value of android:debuggable in the manifest</h2>
  </div>
              <div class="mdl-card__supporting-text">
<div class="issue">
<div class="warningslist">
<span class="location"><a href="../../src/main/AndroidManifest.xml">../../src/main/AndroidManifest.xml</a>:23</span>: <span class="message">Avoid hardcoding the debug mode; leaving it out allows debug and release builds to automatically assign one</span><br /><pre class="errorlines">
<span class="lineno"> 20 </span>        <span class="prefix">android:</span><span class="attribute">roundIcon</span>=<span class="value">"@mipmap/ic_launcher_round"</span>
<span class="lineno"> 21 </span>        <span class="prefix">android:</span><span class="attribute">supportsRtl</span>=<span class="value">"true"</span>
<span class="lineno"> 22 </span>        <span class="prefix">android:</span><span class="attribute">theme</span>=<span class="value">"@style/Theme.NewFWG"</span>
<span class="caretline"><span class="lineno"> 23 </span>        <span class="error"><span class="prefix">android:</span><span class="attribute">debuggable</span>=<span class="value">"false"</span></span>
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
<span class="lineno"> 24 </span>        <span class="prefix">android:</span><span class="attribute">extractNativeLibs</span>=<span class="value">"true"</span>
<span class="lineno"> 25 </span>        <span class="prefix">android:</span><span class="attribute">hardwareAccelerated</span>=<span class="value">"true"</span>
<span class="lineno"> 26 </span>        <span class="prefix">tools:</span><span class="attribute">targetApi</span>=<span class="value">"31"</span>>
</pre>

</div>
<div class="metadata"><div class="explanation" id="explanationHardcodedDebugMode" style="display: none;">
It's best to leave out the <code>android:debuggable</code> attribute from the manifest. If you do, then the tools will automatically insert <code>android:debuggable=true</code> when building an APK to debug on an emulator or device. And when you perform a release build, such as Exporting APK, it will automatically set it to <code>false</code>.<br/>
<br/>
If on the other hand you specify a specific value in the manifest file, then the tools will always use it. This can lead to accidentally publishing your app with debug information.<br/>To suppress this error, use the issue id "HardcodedDebugMode" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="chips">
<span class="mdl-chip">
    <span class="mdl-chip__text">HardcodedDebugMode</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Security</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Fatal</span>
</span>
<span class="mdl-chip">
    <span class="mdl-chip__text">Priority 5/10</span>
</span>
</div>
              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="explanationHardcodedDebugModeLink" onclick="reveal('explanationHardcodedDebugMode');">
Explain</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="HardcodedDebugModeCardLink" onclick="hideid('HardcodedDebugModeCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="MissingIssues"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="MissingIssuesCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Disabled Checks</h2>
  </div>
              <div class="mdl-card__supporting-text">
One or more issues were not run by lint, either
because the check is not enabled by default, or because
it was disabled with a command line flag or via one or
more <code>lint.xml</code> configuration files in the project directories.
<div id="SuppressedIssues" style="display: none;"><br/><br/><div class="issue">
<div class="id">AcceptsUserCertificates<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Allowing user certificates could allow eavesdroppers to intercept data sent by your app, 'which could impact the privacy of your users. Consider nesting your app's <code>trust-anchors</code> inside a <code>&lt;debug-overrides></code> element to make sure they are only available when <code>android:debuggable</code> is set to <code>"true"</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/articles/security-config#TrustingDebugCa">https://developer.android.com/training/articles/security-config#TrustingDebugCa</a>
</div>To suppress this error, use the issue id "AcceptsUserCertificates" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AccidentalOctal<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In Groovy, an integer literal that starts with a leading 0 will be interpreted as an octal number. That is usually (always?) an accident and can lead to subtle bugs, for example when used in the <code>versionCode</code> of an app.<br/>To suppress this error, use the issue id "AccidentalOctal" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AdapterViewChildren<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
An <code>AdapterView</code> such as a `ListView`s must be configured with data from Java code, such as a <code>ListAdapter</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/AdapterView.html">https://developer.android.com/reference/android/widget/AdapterView.html</a>
</div>To suppress this error, use the issue id "AdapterViewChildren" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AddJavascriptInterface<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
For applications built for API levels below 17, <code>WebView#addJavascriptInterface</code> presents a security hazard as JavaScript on the target web page has the ability to use reflection to access the injected object's public fields and thus manipulate the host application in unintended ways.<br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/reference/android/webkit/WebView.html#addJavascriptInterface(java.lang.Object,%20java.lang.String)">https://developer.android.com/reference/android/webkit/WebView.html#addJavascriptInterface(java.lang.Object,%20java.lang.String)</a>
<li><a href="https://support.google.com/faqs/answer/9095419?hl=en">https://support.google.com/faqs/answer/9095419?hl=en</a>
</ul></div>To suppress this error, use the issue id "AddJavascriptInterface" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AllCaps<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The textAllCaps text transform will end up calling <code>toString</code> on the <code>CharSequence</code>, which has the net effect of removing any markup such as <code>&lt;b></code>. This check looks for usages of strings containing markup that also specify <code>textAllCaps=true</code>.<br/>To suppress this error, use the issue id "AllCaps" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AllowAllHostnameVerifier<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check looks for use of HostnameVerifier implementations whose <code>verify</code> method always returns true (thus trusting any hostname) which could result in insecure network traffic caused by trusting arbitrary hostnames in TLS/SSL certificates presented by peers.<br/>To suppress this error, use the issue id "AllowAllHostnameVerifier" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AllowBackup<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>allowBackup</code> attribute determines if an application's data can be backed up and restored. It is documented at <a href="https://developer.android.com/reference/android/R.attr.html#allowBackup">https://developer.android.com/reference/android/R.attr.html#allowBackup</a><br/>
<br/>
By default, this flag is set to <code>true</code> which means application data can be backed up and restored by the OS. Setting <code>allowBackup="false"</code> opts the application out of being backed up and so users can't restore data related to it when they go through the device setup wizard.<br/>
<br/>
Allowing backups may have security consequences for an application. Currently <code>adb backup</code> allows users who have enabled USB debugging to copy application data off of the device. Once backed up, all application data can be read by the user. <code>adb restore</code> allows creation of application data from a source specified by the user. Following a restore, applications should not assume that the data, file permissions, and directory permissions were created by the application itself.<br/>
<br/>
To fix this warning, decide whether your application should support backup, and explicitly set <code>android:allowBackup=(true|false)"</code>.<br/>
<br/>
If not set to false, and if targeting API 23 or later, lint will also warn that you should set <code>android:fullBackupContent</code> to configure auto backup.<br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/training/backup/autosyncapi.html">https://developer.android.com/training/backup/autosyncapi.html</a>
<li><a href="https://developer.android.com/reference/android/R.attr.html#allowBackup">https://developer.android.com/reference/android/R.attr.html#allowBackup</a>
</ul></div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AllowBackup" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AlwaysShowAction<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using <code>showAsAction="always"</code> in menu XML, or <code>MenuItem.SHOW_AS_ACTION_ALWAYS</code> in Java code is usually a deviation from the user interface style guide.Use <code>ifRoom</code> or the corresponding <code>MenuItem.SHOW_AS_ACTION_IF_ROOM</code> instead.<br/>
<br/>
If <code>always</code> is used sparingly there are usually no problems and behavior is roughly equivalent to <code>ifRoom</code> but with preference over other <code>ifRoom</code> items. Using it more than twice in the same menu is a bad idea.<br/>
<br/>
This check looks for menu XML files that contain more than two <code>always</code> actions, or some <code>always</code> actions and no <code>ifRoom</code> actions. In Java code, it looks for projects that contain references to <code>MenuItem.SHOW_AS_ACTION_ALWAYS</code> and no references to <code>MenuItem.SHOW_AS_ACTION_IF_ROOM</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AlwaysShowAction" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AndroidGradlePluginVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This detector looks for usage of the Android Gradle Plugin where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>To suppress this error, use the issue id "AndroidGradlePluginVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AnimatorKeep<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When you use property animators, properties can be accessed via reflection. Those methods should be annotated with @Keep to ensure that during release builds, the methods are not potentially treated as unused and removed, or treated as internal only and get renamed to something shorter.<br/>
<br/>
This check will also flag other potential reflection problems it encounters, such as a missing property, wrong argument types, etc.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AnimatorKeep" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AnnotationProcessorOnCompilePath<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This dependency is identified as an annotation processor. Consider adding it to the processor path using <code>annotationProcessor</code> instead of including it to the<br/>
compile path.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AnnotationProcessorOnCompilePath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppCompatCustomView<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In order to support features such as tinting, the appcompat library will automatically load special appcompat replacements for the builtin widgets. However, this does not work for your own custom views.<br/>
<br/>
Instead of extending the <code>android.widget</code> classes directly, you should instead extend one of the delegate classes in<br/>
<code>androidx.appcompat.widget.AppCompatTextView</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AppCompatCustomView" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppCompatMethod<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When using the appcompat library, there are some methods you should be calling instead of the normal ones; for example, <code>getSupportActionBar()</code> instead of <code>getActionBar()</code>. This lint check looks for calls to the wrong method.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/libraries/support-library/">https://developer.android.com/topic/libraries/support-library/</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AppCompatMethod" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppCompatResource<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When using the appcompat library, menu resources should refer to the <code>showAsAction</code> (or <code>actionViewClass</code>, or <code>actionProviderClass</code>) in the <code>app:</code> namespace, not the <code>android:</code> namespace.<br/>
<br/>
Similarly, when <b>not</b> using the appcompat library, you should be using the <code>android:showAsAction</code> (or <code>actionViewClass</code>, or <code>actionProviderClass</code>) attribute.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AppCompatResource" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppIndexingService<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Apps targeting Android 8.0 or higher can no longer rely on background services while listening for updates to the on-device index. Use a <code>BroadcastReceiver</code> for the <code>UPDATE_INDEX</code> intent to continue supporting indexing in your app.<br/><div class="moreinfo">More info: <a href="https://firebase.google.com/docs/app-indexing/android/personal-content#add-a-broadcast-receiver-to-your-app">https://firebase.google.com/docs/app-indexing/android/personal-content#add-a-broadcast-receiver-to-your-app</a>
</div>To suppress this error, use the issue id "AppIndexingService" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinkUrlError<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Ensure the URL is supported by your app, to get installs and traffic to your app from Google Search.<br/><div class="moreinfo">More info: <a href="https://g.co/AppIndexing/AndroidStudio">https://g.co/AppIndexing/AndroidStudio</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "AppLinkUrlError" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerifyError<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div>To suppress this error, use the issue id "AppLinksAutoVerifyError" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AppLinksAutoVerifyWarning<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that app links are correctly set and associated with website.<br/><div class="moreinfo">More info: <a href="https://g.co/appindexing/applinks">https://g.co/appindexing/applinks</a>
</div>To suppress this error, use the issue id "AppLinksAutoVerifyWarning" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ApplySharedPref<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Consider using <code>apply()</code> instead of <code>commit</code> on shared preferences. Whereas <code>commit</code> blocks and writes its data to persistent storage immediately, <code>apply</code> will handle it in the background.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ApplySharedPref" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Assert<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Assertions will never be turned on in Android. (It was possible to enable it in Dalvik with <code>adb shell setprop debug.assert 1</code>, but it is not implemented in ART, the runtime for Android 5.0 and later.)<br/>
<br/>
This means that the assertion will never catch mistakes, and you should not use assertions from Java or Kotlin for debug build checking.<br/>
<br/>
Instead, perform conditional checking inside <code>if (BuildConfig.DEBUG) { }</code> blocks. That constant is a static final boolean which will be true only in debug builds, and false in release builds, and the compiler will completely remove all code inside the <code>if</code>-body from the app.<br/>
<br/>
For example, you can replace
<pre>
    assert(speed > 0, { "Message" })    // Kotlin
    assert speed > 0 : "Message"        // Java
</pre>
with
<pre>
    if (BuildConfig.DEBUG &amp;&amp; !(speed > 0)) {
        throw new AssertionError("Message")
    }
</pre>
(Note: This lint check does not flag assertions purely asserting nullness or non-nullness in Java code; these are typically more intended for tools usage than runtime checks.)<br/>To suppress this error, use the issue id "Assert" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">AuthLeak<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Strings in java apps can be discovered by decompiling apps, this lint check looks for code which looks like it may contain an url with a username and password<br/>To suppress this error, use the issue id "AuthLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Autofill<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Specify an <code>autofillHints</code> attribute when targeting SDK version 26 or higher or explicitly specify that the view is not important for autofill. Your app can help an autofill service classify the data correctly by providing the meaning of each view that could be autofillable, such as views representing usernames, passwords, credit card fields, email addresses, etc.<br/>
<br/>
The hints can have any value, but it is recommended to use predefined values like 'username' for a username or 'creditCardNumber' for a credit card number. For a list of all predefined autofill hint constants, see the <code>AUTOFILL_HINT_</code> constants in the <code>View</code> reference at <a href="https://developer.android.com/reference/android/view/View.html">https://developer.android.com/reference/android/view/View.html</a>.<br/>
<br/>
You can mark a view unimportant for autofill by specifying an <code>importantForAutofill</code> attribute on that view or a parent view. See <a href="https://developer.android.com/reference/android/view/View.html#setImportantForAutofill">https://developer.android.com/reference/android/view/View.html#setImportantForAutofill</a>(int).<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/text/autofill.html">https://developer.android.com/guide/topics/text/autofill.html</a>
</div>To suppress this error, use the issue id "Autofill" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BackButton<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Other platforms use an explicit back button with label to allow the user to navigate up the application's hierarchy. Instead, Android uses the main action bar's app icon for hierarchical navigation and the navigation bar's back button for temporal navigation."<br/>
This check is not very sophisticated (it just looks for buttons with the label "Back"), so it is disabled by default to not trigger on common scenarios like pairs of Back/Next buttons to paginate through screens.<br/><div class="moreinfo">More info: <a href="https://material.io/design/">https://material.io/design/</a>
</div>To suppress this error, use the issue id "BackButton" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BadHostnameVerifier<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check looks for implementations of <code>HostnameVerifier</code> whose <code>verify</code> method always returns true (thus trusting any hostname) which could result in insecure network traffic caused by trusting arbitrary hostnames in TLS/SSL certificates presented by peers.<br/>To suppress this error, use the issue id "BadHostnameVerifier" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BatteryLife<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This issue flags code that either<br/>
* negatively affects battery life, or<br/>
* uses APIs that have recently changed behavior to prevent background tasks from consuming memory and battery excessively.<br/>
<br/>
Generally, you should be using <code>WorkManager</code> instead.<br/>
<br/>
For more details on how to update your code, please see <a href="https://developer.android.com/topic/performance/background-optimization">https://developer.android.com/topic/performance/background-optimization</a><br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/performance/background-optimization">https://developer.android.com/topic/performance/background-optimization</a>
</div>To suppress this error, use the issue id "BatteryLife" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BottomAppBar<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>BottomAppBar</code> widget must be placed within a <code>CoordinatorLayout</code>.<br/>To suppress this error, use the issue id "BottomAppBar" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">BrokenIterator<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
<b>For LinkedHashMap:</b><br/>
<br/>
The spliterators returned by <code>LinkedHashMap</code> in Android Nougat (API levels 24 and 25) use the wrong order (inconsistent with the iterators, which use the correct order), despite reporting <code>Spliterator.ORDERED</code>. You may use the following code fragments to obtain a correctly ordered <code>Spliterator</code> on API level 24 and 25:<br/>
<br/>
For a Collection view <code>c = lhm.entrySet()</code>, <code>c = lhm.keySet()</code> or <code>c = lhm.values()</code>, use <code>java.util.Spliterators.spliterator(c, c.spliterator().characteristics())</code> instead of <code>c.spliterator()</code>.<br/>
<br/>
Instead of <code>c.stream()</code> or <code>c.parallelStream()</code>, use <code>java.util.stream.StreamSupport.stream(spliterator, false)</code> to construct a<br/>
(nonparallel) Stream from such a <code>Spliterator</code>.<br/>
<br/>
<b>For Vector:</b><br/>
<br/>
The <code>listIterator()</code> returned for a <code>Vector</code> has a broken <code>add()</code> implementation on Android N (API level 24). Consider switching to <code>ArrayList</code> and if necessary adding synchronization.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/LinkedHashMap">https://developer.android.com/reference/java/util/LinkedHashMap</a>
</div>To suppress this error, use the issue id "BrokenIterator" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ButtonCase<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The standard capitalization for OK/Cancel dialogs is "OK" and "Cancel". To ensure that your dialogs use the standard strings, you can use the resource strings @android:string/ok and @android:string/cancel.<br/>To suppress this error, use the issue id "ButtonCase" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ButtonOrder<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
According to the Android Design Guide,<br/>
<br/>
"Action buttons are typically Cancel and/or OK, with OK indicating the preferred or most likely action. However, if the options consist of specific actions such as Close or Wait rather than a confirmation or cancellation of the action described in the content, then all the buttons should be active verbs. As a rule, the dismissive action of a dialog is always on the left whereas the affirmative actions are on the right."<br/>
<br/>
This check looks for button bars and buttons which look like cancel buttons, and makes sure that these are on the left.<br/><div class="moreinfo">More info: <a href="https://material.io/components/dialogs/">https://material.io/components/dialogs/</a>
</div>To suppress this error, use the issue id "ButtonOrder" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ButtonStyle<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Button bars typically use a borderless style for the buttons. Set the <code>style="?android:attr/buttonBarButtonStyle"</code> attribute on each of the buttons, and set <code>style="?android:attr/buttonBarStyle"</code> on the parent layout<br/><div class="moreinfo">More info: <a href="https://material.io/components/dialogs/">https://material.io/components/dialogs/</a>
</div>To suppress this error, use the issue id "ButtonStyle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ByteOrderMark<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Lint will flag any byte-order-mark (BOM) characters it finds in the middle of a file. Since we expect files to be encoded with UTF-8 (see the EnforceUTF8 issue), the BOM characters are not necessary, and they are not handled correctly by all tools. For example, if you have a BOM as part of a resource name in one particular translation, that name will not be considered identical to the base resource's name and the translation will not be used.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Byte_order_mark">https://en.wikipedia.org/wiki/Byte_order_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ByteOrderMark" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CanvasSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In a custom view's draw implementation, you should normally call <code>getWidth</code> and <code>getHeight</code> on the custom view itself, not on the <code>canvas</code> instance.<br/>
<br/>
Canvas width and height are the width and height of the <code>Canvas</code>, which is not always the same as size of the view.<br/>
<br/>
In the hardware accelerated path the width and height of the canvas typically always match that of the <code>View</code> because every view goes to its own recorded <code>DisplayList</code>. But in software rendering there's just one canvas that is clipped and transformed as it makes its way through the <code>View</code> tree, and otherwise remains the same <code>Canvas</code> object for every View's draw method.<br/>
<br/>
You should only use Canvas state to adjust how much you draw, such as a quick-reject for early work avoidance if it's going to be clipped away, but not what you draw.<br/>To suppress this error, use the issue id "CanvasSize" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CheckResult<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Some methods have no side effects, and calling them without doing something without the result is suspicious.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "CheckResult" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ClickableViewAccessibility<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If a <code>View</code> that overrides <code>onTouchEvent</code> or uses an <code>OnTouchListener</code> does not also implement <code>performClick</code> and call it when clicks are detected, the <code>View</code> may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in <code>View#performClick</code> as some accessibility services invoke <code>performClick</code> when a click action should occur.<br/>To suppress this error, use the issue id "ClickableViewAccessibility" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CommitPrefEdits<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
After calling <code>edit()</code> on a <code>SharedPreference</code>, you must call <code>commit()</code> or <code>apply()</code> on the editor to save the results.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "CommitPrefEdits" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CommitTransaction<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
After creating a <code>FragmentTransaction</code>, you typically need to commit it as well<br/>To suppress this error, use the issue id "CommitTransaction" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConstantLocale<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Assigning <code>Locale.getDefault()</code> to a constant is suspicious, because the locale can change while the app is running.<br/>To suppress this error, use the issue id "ConstantLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ContentDescription<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Non-textual widgets like ImageViews and ImageButtons should use the <code>contentDescription</code> attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.<br/>
<br/>
Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, just suppress the lint warning with a tools:ignore="ContentDescription" attribute.<br/>
<br/>
Note that for text fields, you should not set both the <code>hint</code> and the <code>contentDescription</code> attributes since the hint will never be shown. Just set the <code>hint</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases">https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ContentDescription" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ConvertToWebp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The WebP format is typically more compact than PNG and JPEG. As of Android 4.2.1 it supports transparency and lossless conversion as well. Note that there is a quickfix in the IDE which lets you perform conversion.<br/>
<br/>
Launcher icons must be in the PNG format.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ConvertToWebp" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CustomViewStyleable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The convention for custom views is to use a <code>declare-styleable</code> whose name matches the custom view class name. The IDE relies on this convention such that for example code completion can be offered for attributes in a custom view in layout XML resource files.<br/>
<br/>
(Similarly, layout parameter classes should use the suffix <code>_Layout</code>.)<br/>To suppress this error, use the issue id "CustomViewStyleable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">CutPasteId<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This lint check looks for cases where you have cut &amp; pasted calls to <code>findViewById</code> but have forgotten to update the R.id field. It's possible that your code is simply (redundantly) looking up the field repeatedly, but lint cannot distinguish that from a case where you for example want to initialize fields <code>prev</code> and <code>next</code> and you cut &amp; pasted <code>findViewById(R.id.prev)</code> and forgot to update the second initialization to <code>R.id.next</code>.<br/>To suppress this error, use the issue id "CutPasteId" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DalvikOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The Dalvik virtual machine will treat a package private method in one class as overriding a package private method in its super class, even if they are in separate packages.<br/>
<br/>
If you really did intend for this method to override the other, make the method <code>protected</code> instead.<br/>
<br/>
If you did <b>not</b> intend the override, consider making the method private, or changing its name or signature.<br/>
<br/>
Note that this check is disabled be default, because ART (the successor to Dalvik) no longer has this behavior.<br/>To suppress this error, use the issue id "DalvikOverride" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DataBindingWithoutKapt<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Apps that use Kotlin and data binding should also apply the kotlin-kapt plugin. <br/><div class="moreinfo">More info: <a href="https://kotlinlang.org/docs/reference/kapt.html">https://kotlinlang.org/docs/reference/kapt.html</a>
</div>To suppress this error, use the issue id "DataBindingWithoutKapt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DefaultLocale<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Calling <code>String#toLowerCase()</code> or <code>#toUpperCase()</code> <b>without specifying an explicit locale</b> is a common source of bugs. The reason for that is that those methods will use the current locale on the user's device, and even though the code appears to work correctly when you are developing the app, it will fail in some locales. For example, in the Turkish locale, the uppercase replacement for <code>i</code> is <b>not</b> <code>I</code>.<br/>
<br/>
If you want the methods to just perform ASCII replacement, for example to convert an enum name, call <code>String#toUpperCase(Locale.US)</code> instead. If you really want to use the current locale, call <code>String#toUpperCase(Locale.getDefault())</code> instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html#default_locale">https://developer.android.com/reference/java/util/Locale.html#default_locale</a>
</div>To suppress this error, use the issue id "DefaultLocale" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DeletedProvider<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>Crypto</code> provider has been completely removed in Android P (and was deprecated in an earlier release). This means that the code will throw a <code>NoSuchProviderException</code> and the app will crash. Even if the code catches that exception at a higher level, this is not secure and should not be used.<br/><div class="moreinfo">More info: <a href="https://android-developers.googleblog.com/2018/03/cryptography-changes-in-android-p.html">https://android-developers.googleblog.com/2018/03/cryptography-changes-in-android-p.html</a>
</div>To suppress this error, use the issue id "DeletedProvider" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Deprecated<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Deprecated views, attributes and so on are deprecated because there is a better way to do something. Do it that new way. You've been warned.<br/>To suppress this error, use the issue id "Deprecated" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DeprecatedProvider<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>BC</code> provider has been deprecated and will not be provided when <code>targetSdkVersion</code> is P or higher.<br/><div class="moreinfo">More info: <a href="https://android-developers.googleblog.com/2018/03/cryptography-changes-in-android-p.html">https://android-developers.googleblog.com/2018/03/cryptography-changes-in-android-p.html</a>
</div>To suppress this error, use the issue id "DeprecatedProvider" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DevModeObsolete<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In the past, our documentation recommended creating a <code>dev</code> product flavor with has a minSdkVersion of 21, in order to enable multidexing to speed up builds significantly during development.<br/>
<br/>
That workaround is no longer necessary, and it has some serious downsides, such as breaking API access checking (since the true <code>minSdkVersion</code> is no longer known).<br/>
<br/>
In recent versions of the IDE and the Gradle plugin, the IDE automatically passes the API level of the connected device used for deployment, and if that device is at least API 21, then multidexing is automatically turned on, meaning that you get the same speed benefits as the <code>dev</code> product flavor but without the downsides.<br/>To suppress this error, use the issue id "DevModeObsolete" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DeviceAdmin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If you register a broadcast receiver which acts as a device admin, you must also register an <code>&lt;intent-filter></code> for the action <code>android.app.action.DEVICE_ADMIN_ENABLED</code>, without any <code>&lt;data></code>, such that the device admin can be activated/deactivated.<br/>
<br/>
To do this, add
<pre>
`&lt;intent-filter>`
    `&lt;action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />`
`&lt;/intent-filter>`
</pre>
to your <code>&lt;receiver></code>.<br/>To suppress this error, use the issue id "DeviceAdmin" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DiffUtilEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
<code>areContentsTheSame</code> is used by <code>DiffUtil</code> to produce diffs. If the method is implemented incorrectly, such as using identity equals instead of equals, or calling equals on a class that has not implemented it, weird visual artifacts can occur.<br/><div class="moreinfo">More info: <a href="https://issuetracker.google.com/116789824">https://issuetracker.google.com/116789824</a>
</div>To suppress this error, use the issue id "DiffUtilEquals" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DisableBaselineAlignment<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When a <code>LinearLayout</code> is used to distribute the space proportionally between nested layouts, the baseline alignment property should be turned off to make the layout computation faster.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "DisableBaselineAlignment" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DiscouragedPrivateApi<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Usage of restricted non-SDK interface may throw an exception at runtime. Accessing non-SDK methods or fields through reflection has a high likelihood to break your app between versions, and is being restricted to facilitate future app compatibility.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/restrictions-non-sdk-interfaces">https://developer.android.com/preview/restrictions-non-sdk-interfaces</a>
</div>To suppress this error, use the issue id "DiscouragedPrivateApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DrawAllocation<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You should avoid allocating objects during a drawing or layout operation. These are called frequently, so a smooth UI can be interrupted by garbage collection pauses caused by the object allocations.<br/>
<br/>
The way this is generally handled is to allocate the needed objects up front and to reuse them for each drawing operation.<br/>
<br/>
Some methods allocate memory on your behalf (such as <code>Bitmap.create</code>), and these should be handled in the same way.<br/>To suppress this error, use the issue id "DrawAllocation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateDefinition<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You can define a resource multiple times in different resource folders; that's how string translations are done, for example. However, defining the same resource more than once in the same resource folder is likely an error, for example attempting to add a new resource without realizing that the name is already used, and so on.<br/>To suppress this error, use the issue id "DuplicateDefinition" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateDivider<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Older versions of the RecyclerView library did not include a divider decorator, but one was provided as a sample in the support demos. This divider class has been widely copy/pasted into various projects.<br/>
<br/>
In recent versions of the support library, the divider decorator is now included, so you can replace custom copies with the "built-in" version, <code>android.support.v7.widget.DividerItemDecoration</code>.<br/>To suppress this error, use the issue id "DuplicateDivider" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateIncludedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
It's okay for two independent layouts to use the same ids. However, if layouts are combined with include tags, then the id's need to be unique within any chain of included layouts, or <code>Activity#findViewById()</code> can return an unexpected view.<br/>To suppress this error, use the issue id "DuplicateIncludedIds" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateStrings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Duplicate strings can make applications larger unnecessarily.<br/>
<br/>
This lint check looks for duplicate strings, including differences for strings where the only difference is in capitalization. Title casing and all uppercase can all be adjusted in the layout or in code.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType">https://developer.android.com/reference/android/widget/TextView.html#attr_android:inputType</a>
</div>To suppress this error, use the issue id "DuplicateStrings" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">DuplicateUsesFeature<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A given feature should only be declared once in the manifest.<br/>To suppress this error, use the issue id "DuplicateUsesFeature" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EasterEgg<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
An "easter egg" is code deliberately hidden in the code, both from potential users and even from other developers. This lint check looks for code which looks like it may be hidden from sight.<br/>To suppress this error, use the issue id "EasterEgg" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EllipsizeMaxLines<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Combining <code>ellipsize</code> and <code>maxLines=1</code> can lead to crashes on some devices. Earlier versions of lint recommended replacing <code>singleLine=true</code> with <code>maxLines=1</code> but that should not be done when using <code>ellipsize</code>.<br/><div class="moreinfo">More info: <a href="https://issuetracker.google.com/issues/36950033">https://issuetracker.google.com/issues/36950033</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "EllipsizeMaxLines" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">EnqueueWork<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
<code>WorkContinuations</code> cannot be enqueued automatically.  You must call <code>enqueue()</code> on a <code>WorkContinuation</code> to have it and its parent continuations enqueued inside <code>WorkManager</code>.<br/>To suppress this error, use the issue id "EnqueueWork" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExifInterface<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>android.media.ExifInterface</code> implementation has some known security bugs in older versions of Android. There is a new implementation available of this library in the support library, which is preferable.<br/>To suppress this error, use the issue id "ExifInterface" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpensiveAssertion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
In Kotlin, assertions are not handled the same way as from the Java programming language. In particular, they're just implemented as a library call, and inside the library call the error is only thrown if assertions are enabled.<br/>
<br/>
This means that the arguments to the <code>assert</code> call will <b>always</b> be evaluated. If you're doing any computation in the expression being asserted, that computation will unconditionally be performed whether or not assertions are turned on. This typically turns into wasted work in release builds.<br/>
<br/>
This check looks for cases where the assertion condition is nontrivial, e.g. it is performing method calls or doing more work than simple comparisons on local variables or fields.<br/>
<br/>
You can work around this by writing your own inline assert method instead:<br/>

<pre>
@Suppress("INVISIBLE_REFERENCE", "INVISIBLE_MEMBER")
inline fun assert(condition: () -> Boolean) {
    if (_Assertions.ENABLED &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>
In Android, because assertions are not enforced at runtime, instead use this:<br/>

<pre>
inline fun assert(condition: () -> Boolean) {
    if (BuildConfig.DEBUG &amp;&amp; !condition()) {
        throw AssertionError()
    }
}
</pre>
<br/>To suppress this error, use the issue id "ExpensiveAssertion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExpiringTargetSdkVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In the second half of 2018, Google Play will require that new apps and app updates target API level 26 or higher. This will be required for new apps in August 2018, and for updates to existing apps in November 2018.<br/>
<br/>
Configuring your app to target a recent API level ensures that users benefit from significant security and performance improvements, while still allowing your app to run on older Android versions (down to the <code>minSdkVersion</code>).<br/>
<br/>
This lint check starts warning you some months <b>before</b> these changes go into effect if your <code>targetSdkVersion</code> is 25 or lower. This is intended to give you a heads up to update your app, since depending on your current <code>targetSdkVersion</code> the work can be nontrivial.<br/>
<br/>
To update your <code>targetSdkVersion</code>, follow the steps from "Meeting Google Play requirements for target API level",<br/>
<a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a><br/><div class="moreinfo">More info: <ul><li><a href="https://support.google.com/googleplay/android-developer/answer/113469#targetsdk">https://support.google.com/googleplay/android-developer/answer/113469#targetsdk</a>
<li><a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a>
</ul></div>To suppress this error, use the issue id "ExpiringTargetSdkVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExportedContentProvider<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Content providers are exported by default and any application on the system can potentially use them to read and write data. If the content provider provides access to sensitive data, it should be protected by specifying <code>export=false</code> in the manifest or by protecting it with a permission that can be granted to other applications.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ExportedContentProvider" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExportedPreferenceActivity<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Fragment injection gives anyone who can send your <code>PreferenceActivity</code> an intent the ability to load any fragment, with any arguments, in your process.<br/><div class="moreinfo">More info: <a href="http://securityintelligence.com/new-vulnerability-android-framework-fragment-injection">http://securityintelligence.com/new-vulnerability-android-framework-fragment-injection</a>
</div>To suppress this error, use the issue id "ExportedPreferenceActivity" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExportedReceiver<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Exported receivers (receivers which either set <code>exported=true</code> or contain an intent-filter and do not specify <code>exported=false</code>) should define a permission that an entity must have in order to launch the receiver or bind to it. Without this, any application can use this receiver.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ExportedReceiver" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExportedService<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Exported services (services which either set <code>exported=true</code> or contain an intent-filter and do not specify <code>exported=false</code>) should define a permission that an entity must have in order to launch the service or bind to it. Without this, any application can use this service.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ExportedService" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ExtraText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Layout resource files should only contain elements and attributes. Any XML text content found in the file is likely accidental (and potentially dangerous if the text resembles XML and the developer believes the text to be functional)<br/>To suppress this error, use the issue id "ExtraText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FieldGetter<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Accessing a field within the class that defines a getter for that field is at least 3 times faster than calling the getter. For simple getters that do nothing other than return the field, you might want to just reference the local field directly instead.<br/>
<br/>
<b>NOTE</b>: As of Android 2.3 (Gingerbread), this optimization is performed automatically by Dalvik, so there is no need to change your code; this is only relevant if you are targeting older versions of Android.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/articles/perf-tips#internal_get_set">https://developer.android.com/training/articles/perf-tips#internal_get_set</a>
</div>To suppress this error, use the issue id "FieldGetter" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FindViewByIdCast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In Android O, the <code>findViewById</code> signature switched to using generics, which means that most of the time you can leave out explicit casts and just assign the result of the <code>findViewById</code> call to variables of specific view classes.<br/>
<br/>
However, due to language changes between Java 7 and 8, this change may cause code to not compile without explicit casts. This lint check looks for these scenarios and suggests casts to be added now such that the code will continue to compile if the language level is updated to 1.8.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "FindViewByIdCast" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FontValidationError<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Look for problems in various font files.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/text/downloadable-fonts.html">https://developer.android.com/guide/topics/text/downloadable-fonts.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "FontValidationError" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">FontValidationWarning<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Look for problems in various font files.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/text/downloadable-fonts.html">https://developer.android.com/guide/topics/text/downloadable-fonts.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "FontValidationWarning" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GetContentDescriptionOverride<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Overriding <code>getContentDescription()</code> may prevent some accessibility services from properly navigating content exposed by your view. Instead, call <code>setContentDescription()</code> when the content description needs to be changed.<br/>To suppress this error, use the issue id "GetContentDescriptionOverride" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GetInstance<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
<code>Cipher#getInstance</code> should not be called with ECB as the cipher mode or without setting the cipher mode because the default mode on android is ECB, which is insecure.<br/>To suppress this error, use the issue id "GetInstance" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GetLocales<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check looks for usage of Lollipop-style locale folders (e.g. 3 letter language codes, or BCP 47 qualifiers) combined with an <code>AssetManager#getLocales()</code> call. This leads to crashes<br/>To suppress this error, use the issue id "GetLocales" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GifUsage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>.gif</code> file format is discouraged. Consider using <code>.png</code> (preferred) or <code>.jpg</code> (acceptable) instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/drawable-resource.html#Bitmap">https://developer.android.com/guide/topics/resources/drawable-resource.html#Bitmap</a>
</div>To suppress this error, use the issue id "GifUsage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GoogleAppIndexingApiWarning<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Adds URLs to get your app into the Google index, to get installs and traffic to your app from Google Search.<br/><div class="moreinfo">More info: <a href="https://g.co/AppIndexing/AndroidStudio">https://g.co/AppIndexing/AndroidStudio</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GoogleAppIndexingApiWarning" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GoogleAppIndexingWarning<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Adds URLs to get your app into the Google index, to get installs and traffic to your app from Google Search.<br/><div class="moreinfo">More info: <a href="https://g.co/AppIndexing/AndroidStudio">https://g.co/AppIndexing/AndroidStudio</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GoogleAppIndexingWarning" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradleDependency<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDependency" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradleDeprecated<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This detector looks for deprecated Gradle constructs which currently work but will likely stop working in a future update.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDeprecated" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradleDeprecatedConfiguration<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Some Gradle configurations have been deprecated since Android Gradle Plugin 3.0.0 and will be removed in a future version of the Android Gradle Plugin.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/tools/update-dependency-configurations">https://d.android.com/r/tools/update-dependency-configurations</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDeprecatedConfiguration" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradleDynamicVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using <code>+</code> in dependencies lets you automatically pick up the latest available version rather than a specific, named version. However, this is not recommended; your builds are not repeatable; you may have tested with a slightly different version than what the build server used. (Using a dynamic version as the major version number is more problematic than using it in the minor version position.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "GradleDynamicVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradleGetter<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Gradle will let you replace specific constants in your build scripts with method calls, so you can for example dynamically compute a version string based on your current version control revision number, rather than hardcoding a number.<br/>
<br/>
When computing a version name, it's tempting to for example call the method to do that <code>getVersionName</code>. However, when you put that method call inside the <code>defaultConfig</code> block, you will actually be calling the Groovy getter for the <code>versionName</code> property instead. Therefore, you need to name your method something which does not conflict with the existing implicit getters. Consider using <code>compute</code> as a prefix instead of <code>get</code>.<br/>To suppress this error, use the issue id "GradleGetter" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradleIdeError<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Gradle is highly flexible, and there are things you can do in Gradle files which can make it hard or impossible for IDEs to properly handle the project. This lint check looks for constructs that potentially break IDE support.<br/>To suppress this error, use the issue id "GradleIdeError" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradleOverrides<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The value of (for example) <code>minSdkVersion</code> is only used if it is not specified in the <code>build.gradle</code> build scripts. When specified in the Gradle build scripts, the manifest value is ignored and can be misleading, so should be removed to avoid ambiguity.<br/>To suppress this error, use the issue id "GradleOverrides" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradlePath<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Gradle build scripts are meant to be cross platform, so file paths use Unix-style path separators (a forward slash) rather than Windows path separators (a backslash). Similarly, to keep projects portable and repeatable, avoid using absolute paths on the system; keep files within the project instead. To share code between projects, consider creating an android-library and an AAR dependency<br/>To suppress this error, use the issue id "GradlePath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GradlePluginVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Not all versions of the Android Gradle plugin are compatible with all versions of the SDK. If you update your tools, or if you are trying to open a project that was built with an old version of the tools, you may need to update your plugin version number.<br/>To suppress this error, use the issue id "GradlePluginVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">GrantAllUris<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>&lt;grant-uri-permission></code> element allows specific paths to be shared. This detector checks for a path URL of just '/' (everything), which is probably not what you want; you should limit access to a subset.<br/>To suppress this error, use the issue id "GrantAllUris" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">HalfFloat<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Half-precision floating point are stored in a short data type, and should be manipulated using the <code>android.util.Half</code> class. This check flags usages where it appears that these values are used incorrectly.<br/>To suppress this error, use the issue id "HalfFloat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">HandlerLeak<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Since this Handler is declared as an inner class, it may prevent the outer class from being garbage collected. If the Handler is using a <code>Looper</code> or <code>MessageQueue</code> for a thread other than the main thread, then there is no issue. If the <code>Handler</code> is using the <code>Looper</code> or <code>MessageQueue</code> of the main thread, you need to fix your <code>Handler</code> declaration, as follows: Declare the <code>Handler</code> as a static class; In the outer class, instantiate a <code>WeakReference</code> to the outer class and pass this object to your <code>Handler</code> when you instantiate the <code>Handler</code>; Make all references to members of the outer class using the <code>WeakReference</code> object.<br/>To suppress this error, use the issue id "HandlerLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">HardcodedText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Hardcoding text attributes directly in layout files is bad for several reasons:<br/>
<br/>
* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)<br/>
<br/>
* The application cannot be translated to other languages by just adding new translations for existing string resources.<br/>
<br/>
There are quickfixes to automatically extract this hardcoded string into a resource lookup.<br/>To suppress this error, use the issue id "HardcodedText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">HardwareIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using these device identifiers is not recommended other than for high value fraud prevention and advanced telephony use-cases. For advertising use-cases, use <code>AdvertisingIdClient$Info#getId</code> and for analytics, use <code>InstanceId#getId</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/articles/user-data-ids.html">https://developer.android.com/training/articles/user-data-ids.html</a>
</div>To suppress this error, use the issue id "HardwareIds" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">HighAppVersionCode<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The declared <code>versionCode</code> is an Integer. Ensure that the version number is not close to the limit. It is recommended to monotonically increase this number each minor or major release of the app. Note that updating an app with a versionCode over <code>Integer.MAX_VALUE</code> is not possible.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/studio/publish/versioning.html">https://developer.android.com/studio/publish/versioning.html</a>
</div>To suppress this error, use the issue id "HighAppVersionCode" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconColors<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Notification icons and Action Bar icons should only white and shades of gray. See the Android Design Guide for more details. Note that the way Lint decides whether an icon is an action bar icon or a notification icon is based on the filename prefix: <code>ic_menu_</code> for action bar icons, <code>ic_stat_</code> for notification icons etc. These correspond to the naming conventions documented in <a href="https://material.io/design/iconography/">https://material.io/design/iconography/</a><br/>To suppress this error, use the issue id "IconColors" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconDensities<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Icons will look best if a custom version is provided for each of the major screen density classes (low, medium, high, extra high). This lint check identifies icons which do not have complete coverage across the densities.<br/>
<br/>
Low density is not really used much anymore, so this check ignores the ldpi density. To force lint to include it, set the environment variable <code>ANDROID_LINT_INCLUDE_LDPI=true</code>. For more information on current density usage, see <a href="https://developer.android.com/about/dashboards">https://developer.android.com/about/dashboards</a><br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html">https://developer.android.com/guide/practices/screens_support.html</a>
</div>To suppress this error, use the issue id "IconDensities" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconDipSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Checks the all icons which are provided in multiple densities, all compute to roughly the same density-independent pixel (<code>dip</code>) size. This catches errors where images are either placed in the wrong folder, or icons are changed to new sizes but some folders are forgotten.<br/>To suppress this error, use the issue id "IconDipSize" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconDuplicates<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If an icon is repeated under different names, you can consolidate and just use one of the icons and delete the others to make your application smaller. However, duplicated icons usually are not intentional and can sometimes point to icons that were accidentally overwritten or accidentally not updated.<br/>To suppress this error, use the issue id "IconDuplicates" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconDuplicatesConfig<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If an icon is provided under different configuration parameters such as <code>drawable-hdpi</code> or <code>-v11</code>, they should typically be different. This detector catches cases where the same icon is provided in different configuration folder which is usually not intentional.<br/>To suppress this error, use the issue id "IconDuplicatesConfig" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExpectedSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
There are predefined sizes (for each density) for launcher icons. You should follow these conventions to make sure your icons fit in with the overall look of the platform.<br/><div class="moreinfo">More info: <a href="https://material.io/design/iconography/">https://material.io/design/iconography/</a>
</div>To suppress this error, use the issue id "IconExpectedSize" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconExtension<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Ensures that icons have the correct file extension (e.g. a <code>.png</code> file is really in the PNG format and not for example a GIF file named <code>.png</code>).<br/>To suppress this error, use the issue id "IconExtension" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconLauncherShape<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
According to the Android Design Guide (<a href="https://material.io/design/iconography/">https://material.io/design/iconography/</a>) your launcher icons should "use a distinct silhouette", a "three-dimensional, front view, with a slight perspective as if viewed from above, so that users perceive some depth."<br/>
<br/>
The unique silhouette implies that your launcher icon should not be a filled square.<br/>To suppress this error, use the issue id "IconLauncherShape" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconLocation<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The res/drawable folder is intended for density-independent graphics such as shapes defined in XML. For bitmaps, move it to <code>drawable-mdpi</code> and consider providing higher and lower resolution versions in <code>drawable-ldpi</code>, <code>drawable-hdpi</code> and <code>drawable-xhdpi</code>. If the icon <b>really</b> is density independent (for example a solid color) you can place it in <code>drawable-nodpi</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html">https://developer.android.com/guide/practices/screens_support.html</a>
</div>To suppress this error, use the issue id "IconLocation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconMissingDensityFolder<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Icons will look best if a custom version is provided for each of the major screen density classes (low, medium, high, extra-high, extra-extra-high). This lint check identifies folders which are missing, such as <code>drawable-hdpi</code>.<br/>
<br/>
Low density is not really used much anymore, so this check ignores the ldpi density. To force lint to include it, set the environment variable <code>ANDROID_LINT_INCLUDE_LDPI=true</code>. For more information on current density usage, see <a href="https://developer.android.com/about/dashboards">https://developer.android.com/about/dashboards</a><br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html">https://developer.android.com/guide/practices/screens_support.html</a>
</div>To suppress this error, use the issue id "IconMissingDensityFolder" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconMixedNinePatch<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If you accidentally name two separate resources <code>file.png</code> and <code>file.9.png</code>, the image file and the nine patch file will both map to the same drawable resource, <code>@drawable/file</code>, which is probably not what was intended.<br/>To suppress this error, use the issue id "IconMixedNinePatch" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconNoDpi<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Bitmaps that appear in <code>drawable-nodpi</code> folders will not be scaled by the Android framework. If a drawable resource of the same name appears <b>both</b> in a <code>-nodpi</code> folder as well as a dpi folder such as <code>drawable-hdpi</code>, then the behavior is ambiguous and probably not intentional. Delete one or the other, or use different names for the icons.<br/>To suppress this error, use the issue id "IconNoDpi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IconXmlAndPng<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If a drawable resource appears as an <code>.xml</code> file in the <code>drawable/</code> folder, it's usually not intentional for it to also appear as a bitmap using the same name; generally you expect the drawable XML file to define states and each state has a corresponding drawable bitmap.<br/>To suppress this error, use the issue id "IconXmlAndPng" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IgnoreWithoutReason<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Ignoring a test without a reason makes it difficult to figure out the problem later.<br/>
Please define an explicit reason why it is ignored, and when it can be resolved.<br/>To suppress this error, use the issue id "IgnoreWithoutReason" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IllegalResourceRef<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
For the <code>versionCode</code> attribute, you have to specify an actual integer literal; you cannot use an indirection with a <code>@dimen/name</code> resource. Similarly, the <code>versionName</code> attribute should be an actual string, not a string resource url.<br/>To suppress this error, use the issue id "IllegalResourceRef" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ImplicitSamInstance<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Kotlin's support for SAM (single accessor method) interfaces lets you pass a lambda to the interface. This will create a new instance on the fly even though there is no explicit constructor call. If you pass one of these lambdas or method references into a method which (for example) stores or compares the object identity, unexpected results may happen.<br/>To suppress this error, use the issue id "ImplicitSamInstance" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ImpliedQuantity<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Plural strings should generally include a <code>%s</code> or <code>%d</code> formatting argument. In locales like English, the <code>one</code> quantity only applies to a single value, 1, but that's not true everywhere. For example, in Slovene, the <code>one</code> quantity will apply to 1, 101, 201, 301, and so on. Similarly, there are locales where multiple values match the <code>zero</code> and <code>two</code> quantities.<br/>
<br/>
In these locales, it is usually an error to have a message which does not include a formatting argument (such as '%d'), since it will not be clear from the grammar what quantity the quantity string is describing.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/string-resource.html#Plurals">https://developer.android.com/guide/topics/resources/string-resource.html#Plurals</a>
</div>To suppress this error, use the issue id "ImpliedQuantity" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ImpliedTouchscreenHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Apps require the <code>android.hardware.touchscreen</code> feature by default. If you want your app to be available on TV, you must also explicitly declare that a touchscreen is not required as follows:<br/>
<code>&lt;uses-feature android:name="android.hardware.touchscreen" android:required="false"/></code><br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/uses-feature-element.html">https://developer.android.com/guide/topics/manifest/uses-feature-element.html</a>
</div>To suppress this error, use the issue id "ImpliedTouchscreenHardware" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InOrMmUsage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Avoid using <code>mm</code> (millimeters) or <code>in</code> (inches) as the unit for dimensions.<br/>
<br/>
While it should work in principle, unfortunately many devices do not report the correct true physical density, which means that the dimension calculations won't work correctly. You are better off using <code>dp</code> (and for font sizes, <code>sp</code>).<br/>To suppress this error, use the issue id "InOrMmUsage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IncludeLayoutParam<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Layout parameters specified on an <code>&lt;include></code> tag will only be used if you also override <code>layout_width</code> and <code>layout_height</code> on the <code>&lt;include></code> tag; otherwise they will be ignored.<br/><div class="moreinfo">More info: <a href="https://stackoverflow.com/questions/2631614/does-android-xml-layouts-include-tag-really-work">https://stackoverflow.com/questions/2631614/does-android-xml-layouts-include-tag-really-work</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "IncludeLayoutParam" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IncompatibleMediaBrowserServiceCompatVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
<code>MediaBrowserServiceCompat</code> from version 23.2.0 to 23.4.0 of the Support v4 Library used private APIs and will not be compatible with future versions of Android beyond Android N. Please upgrade to version 24.0.0 or higher of the Support Library.<br/>To suppress this error, use the issue id "IncompatibleMediaBrowserServiceCompatVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InconsistentArrays<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When an array is translated in a different locale, it should normally have the same number of elements as the original array. When adding or removing elements to an array, it is easy to forget to update all the locales, and this lint warning finds inconsistencies like these.<br/>
<br/>
Note however that there may be cases where you really want to declare a different number of array items in each configuration (for example where the array represents available options, and those options differ for different layout orientations and so on), so use your own judgement to decide if this is really an error.<br/>
<br/>
You can suppress this error type if it finds false errors in your project.<br/>To suppress this error, use the issue id "InconsistentArrays" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InconsistentLayout<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check ensures that a layout resource which is defined in multiple resource folders, specifies the same set of widgets.<br/>
<br/>
This finds cases where you have accidentally forgotten to add a widget to all variations of the layout, which could result in a runtime crash for some resource configurations when a <code>findViewById()</code> fails.<br/>
<br/>
There <b>are</b> cases where this is intentional. For example, you may have a dedicated large tablet layout which adds some extra widgets that are not present in the phone version of the layout. As long as the code accessing the layout resource is careful to handle this properly, it is valid. In that case, you can suppress this lint check for the given extra or missing views, or the whole layout<br/>To suppress this error, use the issue id "InconsistentLayout" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InefficientWeight<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When only a single widget in a <code>LinearLayout</code> defines a weight, it is more efficient to assign a width/height of <code>0dp</code> to it since it will absorb all the remaining space anyway. With a declared width/height of <code>0dp</code> it does not have to measure its own size first.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InefficientWeight" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InflateParams<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When inflating a layout, avoid passing in null as the parent view, since otherwise any layout parameters on the root of the inflated layout will be ignored.<br/><div class="moreinfo">More info: <a href="https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/">https://www.bignerdranch.com/blog/understanding-androids-layoutinflater-inflate/</a>
</div>To suppress this error, use the issue id "InflateParams" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InlinedApi<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check scans through all the Android API field references in the application and flags certain constants, such as static final integers and Strings, which were introduced in later versions. These will actually be copied into the class files rather than being referenced, which means that the value is available even when running on older devices. In some cases that's fine, and in other cases it can result in a runtime crash or incorrect behavior. It depends on the context, so consider the code carefully and decide whether it's safe and can be suppressed or whether the code needs to be guarded.<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InlinedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InnerclassSeparator<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When you reference an inner class in a manifest file, you must use '$' instead of '.' as the separator character, i.e. Outer$Inner instead of Outer.Inner.<br/>
<br/>
(If you get this warning for a class which is not actually an inner class, it's because you are using uppercase characters in your package name, which is not conventional.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InnerclassSeparator" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InsecureBaseConfiguration<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Permitting cleartext traffic could allow eavesdroppers to intercept data sent by your app, which impacts the privacy of your users. Consider only allowing encrypted traffic by setting the <code>cleartextTrafficPermitted</code> tag to <code>"false"</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/features/security-config.html">https://developer.android.com/preview/features/security-config.html</a>
</div>To suppress this error, use the issue id "InsecureBaseConfiguration" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InstantApps<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This issue flags code that will not work correctly in Instant Apps<br/>To suppress this error, use the issue id "InstantApps" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">IntentReset<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Intent provides the following APIs: <code>setData(Uri)</code> and <code>setType(String)</code>. Unfortunately, setting one clears the other. If you want to set both, you should call <code>setDataAndType(Uri, String)</code> instead.<br/>To suppress this error, use the issue id "IntentReset" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidAnalyticsName<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Event names and parameters must follow the naming conventions defined in the`FirebaseAnalytics#logEvent()` documentation.<br/><div class="moreinfo">More info: <a href="https://firebase.google.com/docs/reference/android/com/google/firebase/analytics/FirebaseAnalytics#logEvent(java.lang.String,%20android.os.Bundle)">https://firebase.google.com/docs/reference/android/com/google/firebase/analytics/FirebaseAnalytics#logEvent(java.lang.String,%20android.os.Bundle)</a>
</div>To suppress this error, use the issue id "InvalidAnalyticsName" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidImeActionId<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
<code>android:imeActionId</code> should not be a resource ID such as <code>@+id/resName</code>. It must be an integer constant, or an integer resource reference, as defined in <code>EditorInfo</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/view/inputmethod/EditorInfo.html">https://developer.android.com/reference/android/view/inputmethod/EditorInfo.html</a>
</div>To suppress this error, use the issue id "InvalidImeActionId" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidNavigation<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
All <code>&lt;navigation></code> elements must have a start destination specified, and it must be a direct child of that <code>&lt;navigation></code>.<br/>To suppress this error, use the issue id "InvalidNavigation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPackage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This check scans through libraries looking for calls to APIs that are not included in Android.<br/>
<br/>
When you create Android projects, the classpath is set up such that you can only access classes in the API packages that are included in Android. However, if you add other projects to your libs/ folder, there is no guarantee that those .jar files were built with an Android specific classpath, and in particular, they could be accessing unsupported APIs such as java.applet.<br/>
<br/>
This check scans through library jars and looks for references to API packages that are not included in Android and flags these. This is only an error if your code calls one of the library classes which wind up referencing the unsupported package.<br/>To suppress this error, use the issue id "InvalidPackage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidPermission<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Not all elements support the permission attribute. If a permission is set on an invalid element, it is a no-op and ignored. Ensure that this permission attribute was set on the correct element to protect the correct component.<br/>To suppress this error, use the issue id "InvalidPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidResourceFolder<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This lint check looks for a folder name that is not a valid resource folder name; these will be ignored and not packaged by the Android Gradle build plugin.<br/>
<br/>
Note that the order of resources is very important; for example, you can't specify a language before a network code.<br/>
<br/>
Similarly, note that to use 3 letter region codes, you have to use a special BCP 47 syntax: the prefix b+ followed by the BCP 47 language tag but with <code>+</code> as the individual separators instead of <code>-</code>. Therefore, for the BCP 47 language tag <code>nl-ABW</code> you have to use <code>b+nl+ABW</code>.<br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/guide/topics/resources/providing-resources.html">https://developer.android.com/guide/topics/resources/providing-resources.html</a>
<li><a href="https://tools.ietf.org/html/bcp47">https://tools.ietf.org/html/bcp47</a>
</ul></div>To suppress this error, use the issue id "InvalidResourceFolder" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidUsesTagAttribute<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The &lt;uses> element in <code>&lt;automotiveApp></code> should contain a valid value for the <code>name</code> attribute.<br/>
Valid values are <code>media</code>, <code>notification</code>, or <code>sms</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/auto/start/index.html#auto-metadata">https://developer.android.com/training/auto/start/index.html#auto-metadata</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InvalidUsesTagAttribute" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidVectorPath<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check ensures that vector paths are valid. For example, it makes sure that the numbers are not using scientific notation (such as 1.0e3) which can lead to runtime crashes on older devices. As another example, it flags numbers like <code>.5</code> which should be written as <code>0.5</code> instead to avoid crashes on some pre-Marshmallow devices.<br/><div class="moreinfo">More info: <a href="https://issuetracker.google.com/37008268">https://issuetracker.google.com/37008268</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "InvalidVectorPath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidWakeLockTag<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Wake Lock tags must follow the naming conventions defined in the`PowerManager` documentation.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/os/PowerManager.html">https://developer.android.com/reference/android/os/PowerManager.html</a>
</div>To suppress this error, use the issue id "InvalidWakeLockTag" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">InvalidWearFeatureAttribute<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
For the <code>android.hardware.type.watch</code> uses-feature, android:required="false" is disallowed. A single APK for Wear and non-Wear devices is not supported.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/wearables/apps/packaging.html">https://developer.android.com/training/wearables/apps/packaging.html</a>
</div>To suppress this error, use the issue id "InvalidWearFeatureAttribute" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">JavaPluginLanguageLevel<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In modules using plugins deriving from the Gradle <code>java</code> plugin (e.g. <code>java-library</code> or <code>application</code>), the java source and target compatibility default to the version of the JDK being used to run Gradle, which may cause compatibility problems with Android (or other) modules.<br/>
<br/>
You can specify an explicit sourceCompatibility and targetCompatibility in this module to maintain compatibility no matter which JDK is used to run Gradle.<br/>To suppress this error, use the issue id "JavaPluginLanguageLevel" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">JavascriptInterface<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
As of API 17, you must annotate methods in objects registered with the <code>addJavascriptInterface</code> method with a <code>@JavascriptInterface</code> annotation.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/webkit/WebView.html#addJavascriptInterface(java.lang.Object, java.lang.String)">https://developer.android.com/reference/android/webkit/WebView.html#addJavascriptInterface(java.lang.Object, java.lang.String)</a>
</div>To suppress this error, use the issue id "JavascriptInterface" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">JcenterRepositoryObsolete<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The JCenter Maven repository is no longer accepting submissions of Maven artifacts since 31st March 2021.  Ensure that the project is configured to search in repositories with the latest versions of its dependencies.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/r/tools/jcenter-end-of-service">https://developer.android.com/r/tools/jcenter-end-of-service</a>
</div>To suppress this error, use the issue id "JcenterRepositoryObsolete" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">JobSchedulerService<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check looks for various common mistakes in using the JobScheduler API: the service class must extend <code>JobService</code>, the service must be registered in the manifest and the registration must require the permission <code>android.permission.BIND_JOB_SERVICE</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/performance/scheduling.html">https://developer.android.com/topic/performance/scheduling.html</a>
</div>To suppress this error, use the issue id "JobSchedulerService" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KeyboardInaccessibleWidget<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A widget that is declared to be clickable but not declared to be focusable is not accessible via the keyboard. Please add the <code>focusable</code> attribute as well.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "KeyboardInaccessibleWidget" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KotlinPropertyAccess<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
For a method to be represented as a property in Kotlin, strict &#8220;bean&#8221;-style prefixing must be used.<br/>
<br/>
Accessor methods require a &#8216;get&#8217; prefix or for boolean-returning methods an &#8216;is&#8217; prefix can be used.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#property-prefixes">https://android.github.io/kotlin-guides/interop.html#property-prefixes</a>
</div>To suppress this error, use the issue id "KotlinPropertyAccess" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">KtxExtensionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Android KTX extensions augment some libraries with support for modern Kotlin language features like extension functions, extension properties, lambdas, named parameters, coroutines, and more.<br/>
<br/>
In Kotlin projects, use the KTX version of a library by replacing the dependency in your <code>build.gradle</code> file. For example, you can replace <code>androidx.fragment:fragment</code> with <code>androidx.fragment:fragment-ktx</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/kotlin/ktx">https://developer.android.com/kotlin/ktx</a>
</div>To suppress this error, use the issue id "KtxExtensionAvailable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LabelFor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Editable text fields should provide an <code>android:hint</code> or, provided your <code>minSdkVersion</code> is at least 17, they may be referenced by a view with a <code>android:labelFor</code> attribute.<br/>
<br/>
When using <code>android:labelFor</code>, be sure to provide an <code>android:text</code> or an <code>android:contentDescription</code>.<br/>
<br/>
If your view is labeled but by a label in a different layout which includes this one, just suppress this warning from lint.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "LabelFor" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LambdaLast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve calling this code from Kotlin,<br/>
parameter types eligible for SAM conversion should be last.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last">https://android.github.io/kotlin-guides/interop.html#lambda-parameters-last</a>
</div>To suppress this error, use the issue id "LambdaLast" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LifecycleAnnotationProcessorWithJava8<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
For faster incremental build, switch to the Lifecycle Java 8 API with these steps:<br/>
<br/>
First replace
<pre>
annotationProcessor "androidx.lifecycle:lifecycle-compiler:*version*"
kapt "androidx.lifecycle:lifecycle-compiler:*version*"
</pre>
with
<pre>
implementation "androidx.lifecycle:lifecycle-common-java8:*version*"
</pre>
Then remove any <code>OnLifecycleEvent</code> annotations from <code>Observer</code> classes and make them implement the <code>DefaultLifecycleObserver</code> interface.<br/><div class="moreinfo">More info: <a href="https://d.android.com/r/studio-ui/lifecycle-release-notes">https://d.android.com/r/studio-ui/lifecycle-release-notes</a>
</div>To suppress this error, use the issue id "LifecycleAnnotationProcessorWithJava8" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplBadUrl<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
More Info URLs let a link check point to additional resources about the problem and solution it's checking for.<br/>
<br/>
This check validates the URLs in various ways, such as making sure that issue tracker links look correct. It may also at some point touch the network to make sure that the URLs are actually still reachable.<br/>To suppress this error, use the issue id "LintImplBadUrl" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplDollarEscapes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Instead of putting ${"$"} in your Kotlin raw string literals you can simply use &#65284;. This looks like the dollar sign but is instead the full width dollar sign, U+FF04. And this character does not need to be escaped in Kotlin raw strings, since it does not start a string template.<br/>
<br/>
Lint will automatically convert references to &#65284; in unit test files into a real dollar sign, and when pulling results and error messages out of lint, the dollar sign back into the full width dollar sign.<br/>
<br/>
That means you can use &#65284; everywhere instead of ${"$"}, which makes the test strings more readable -- especially $-heavy code such as references to inner classes.<br/>To suppress this error, use the issue id "LintImplDollarEscapes" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplIdFormat<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check looks at lint issue id registrations and makes sure the id follows the expected conventions: capitalized, camel case, no spaces, and not too long.<br/>
<br/>
Note: You shouldn't change id's for lint checks that are already widely used, since the id can already appear in <code>@SuppressLint</code> annotations, <code>tools:ignore=</code> attributes, lint baselines, Gradle <code>lintOptions</code> blocks, <code>lint.xml</code> files, and so on. In these cases, just explicitly suppress this warning instead using something like<br/>

<pre>
    @JvmField
    val ISSUE = Issue.create(
        // ID string is too long, but we can't change this now since this
        // id is already used in user suppress configurations
        //noinspection LintImplIdFormat
        id = "IncompatibleMediaBrowserServiceCompatVersion",
        ...
</pre>
<br/>To suppress this error, use the issue id "LintImplIdFormat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplPsiEquals<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
You should never compare two PSI elements for equality with <code>equals</code>;<br/>
use <code>isEquivalentTo(PsiElement)</code> instead.<br/>To suppress this error, use the issue id "LintImplPsiEquals" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplTextFormat<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Lint supports various markdown like formatting directives in all of its strings (issue explanations, reported error messages, etc).<br/>
<br/>
This lint check looks for strings that look like they may benefit from additional formatting. For example, if a snippet looks like code it should be surrounded with backticks.<br/>
<br/>
Note: Be careful changing <b>existing</b> strings; this may stop baseline file matching from working, so consider suppressing existing violations of this check if this is an error many users may be filtering in baselines. (This is only an issue for strings used in <code>report</code> calls; for issue registration strings like summaries and explanations there's no risk changing the text contents.)<br/>To suppress this error, use the issue id "LintImplTextFormat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplTrimIndent<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Lint implicitly calls <code>.trimIndent()</code> (lazily, at the last minute) in a number of places:<br/>
* Issue explanations<br/>
* Error messages<br/>
* Lint test file descriptions<br/>
* etc<br/>
<br/>
That means you don't need to put <code>.trimIndent()</code> in your source code to handle this.<br/>
<br/>
There are advantages to <b>not</b> putting <code>.trimIndent()</code> in the code. For test files, if you call for example <code>kotlin(""\"source code"\""\")</code> then IntelliJ/Android Studio will syntax highlight the source code as Kotlin. The second you add in a .trimIndent() on the string, the syntax highlighting goes away. For test files you can instead call ".indented()" on the test file builder to get it to indent the string.<br/>To suppress this error, use the issue id "LintImplTrimIndent" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUnexpectedDomain<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This checks flags URLs to domains that have not been explicitly allowed for use as a documentation source.<br/>To suppress this error, use the issue id "LintImplUnexpectedDomain" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUseKotlin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
New lint checks should be written in Kotlin; the Lint API is written in Kotlin and uses a number of language features that makes it beneficial to also write the lint checks in Kotlin. Examples include many extension functions (as well as in UAST), default and named parameters (for the Issue registration methods for example where there are methods with 12+ parameters with only a couple of required ones), and so on.<br/>To suppress this error, use the issue id "LintImplUseKotlin" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LintImplUseUast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
UAST is a library that sits on top of PSI, and in many cases PSI is part of the UAST API; for example, UResolvable#resolve returns a PsiElement.<br/>
<br/>
Also, for convenience, a UClass is a PsiClass, a UMethod is a PsiMethod, and so on.<br/>
<br/>
However, there are some parts of the PSI API that does not work correctly when used in this way. For example, if you call <code>PsiMethod#getBody</code> or <code>PsiVariable#getInitializer</code>, this will only work in Java, not for Kotlin (or potentially other languages).<br/>
<br/>
There are UAST specific methods you need to call instead and lint will flag these.<br/>To suppress this error, use the issue id "LintImplUseUast" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LocalSuppress<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>@SuppressAnnotation</code> is used to suppress Lint warnings in Java files. However, while many lint checks analyzes the Java source code, where they can find annotations on (for example) local variables, some checks are analyzing the <code>.class</code> files. And in class files, annotations only appear on classes, fields and methods. Annotations placed on local variables disappear. If you attempt to suppress a lint error for a class-file based lint check, the suppress annotation not work. You must move the annotation out to the surrounding method.<br/>To suppress this error, use the issue id "LocalSuppress" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LocaleFolder<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
From the <code>java.util.Locale</code> documentation:<br/>
"Note that Java uses several deprecated two-letter codes. The Hebrew ("he") language code is rewritten as "iw", Indonesian ("id") as "in", and Yiddish ("yi") as "ji". This rewriting happens even if you construct your own Locale object, not just for instances returned by the various lookup methods.<br/>
<br/>
Because of this, if you add your localized resources in for example <code>values-he</code> they will not be used, since the system will look for <code>values-iw</code> instead.<br/>
<br/>
To work around this, place your resources in a <code>values</code> folder using the deprecated language code instead.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/util/Locale.html">https://developer.android.com/reference/java/util/Locale.html</a>
</div>To suppress this error, use the issue id "LocaleFolder" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LockedOrientationActivity<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>&lt;activity></code> element should not be locked to any orientation so that users can take advantage of the multi-window environments and larger screens available on Android. To fix the issue, consider declaring the corresponding activity element with `screenOrientation="unspecified"<code>or </code>"fullSensor"` attribute.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/window-management">https://developer.android.com/topic/arc/window-management</a>
</div>To suppress this error, use the issue id "LockedOrientationActivity" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogConditional<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>BuildConfig</code> class provides a constant, <code>DEBUG</code>, which indicates whether the code is being built in release mode or in debug mode. In release mode, you typically want to strip out all the logging calls. Since the compiler will automatically remove all code which is inside a <code>if (false)</code> check, surrounding your logging calls with a check for <code>BuildConfig.DEBUG</code> is a good idea.<br/>
<br/>
If you <b>really</b> intend for the logging to be present in release mode, you can suppress this warning with a <code>@SuppressLint</code> annotation for the intentional logging calls.<br/>To suppress this error, use the issue id "LogConditional" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LogTagMismatch<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When guarding a <code>Log.v(tag, ...)</code> call with <code>Log.isLoggable(tag)</code>, the tag passed to both calls should be the same. Similarly, the level passed in to <code>Log.isLoggable</code> should typically match the type of <code>Log</code> call, e.g. if checking level <code>Log.DEBUG</code>, the corresponding <code>Log</code> call should be <code>Log.d</code>, not <code>Log.i</code>.<br/>To suppress this error, use the issue id "LogTagMismatch" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">LongLogTag<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Log tags are only allowed to be at most 23 tag characters long.<br/>To suppress this error, use the issue id "LongLogTag" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MangledCRLF<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
On Windows, line endings are typically recorded as carriage return plus newline: \r\n.<br/>
<br/>
This detector looks for invalid line endings with repeated carriage return characters (without newlines). Previous versions of the ADT plugin could accidentally introduce these into the file, and when editing the file, the editor could produce confusing visual artifacts.<br/><div class="moreinfo">More info: <a href="https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421">https://bugs.eclipse.org/bugs/show_bug.cgi?id=375421</a>
</div>To suppress this error, use the issue id "MangledCRLF" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ManifestOrder<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The &lt;application> tag should appear after the elements which declare which version you need, which features you need, which libraries you need, and so on. In the past there have been subtle bugs (such as themes not getting applied correctly) when the <code>&lt;application></code> tag appears before some of these other elements, so it's best to order your manifest in the logical dependency order.<br/>To suppress this error, use the issue id "ManifestOrder" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MenuTitle<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
From the action bar documentation:<br/>
"It's important that you always define android:title for each menu item &#8212; even if you don't declare that the title appear with the action item &#8212; for three reasons:<br/>
<br/>
* If there's not enough room in the action bar for the action item, the menu item appears in the overflow menu and only the title appears.<br/>
* Screen readers for sight-impaired users read the menu item's title.<br/>
* If the action item appears with only the icon, a user can long-press the item to reveal a tool-tip that displays the action item's title.<br/>
The android:icon is always optional, but recommended.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/appbar">https://developer.android.com/training/appbar</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MenuTitle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MergeMarker<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Many version control systems leave unmerged files with markers such as &lt;&lt;&lt; in the source code. This check looks for these markers, which are sometimes accidentally left in, particularly in resource files where they don't break compilation.<br/>To suppress this error, use the issue id "MergeMarker" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MergeRootFrame<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If a <code>&lt;FrameLayout></code> is the root of a layout and does not provide background or padding etc, it can often be replaced with a <code>&lt;merge></code> tag which is slightly more efficient. Note that this depends on context, so make sure you understand how the <code>&lt;merge></code> tag works before proceeding.<br/><div class="moreinfo">More info: <a href="https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html">https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html</a>
</div>To suppress this error, use the issue id "MergeRootFrame" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MinSdkTooLow<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The value of the <code>minSdkVersion</code> property is too low and can be incremented without noticeably reducing the number of supported devices.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MinSdkTooLow" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MipmapIcons<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Launcher icons should be provided in the <code>mipmap</code> resource directory. This is the same as the <code>drawable</code> resource directory, except resources in the <code>mipmap</code> directory will not get stripped out when creating density-specific APKs.<br/>
<br/>
In certain cases, the Launcher app may use a higher resolution asset (than would normally be computed for the device) to display large app shortcuts. If drawables for densities other than the device's resolution have been stripped out, then the app shortcut could appear blurry.<br/>
<br/>
To fix this, move your launcher icons from `drawable-`dpi to `mipmap-`dpi and change references from @drawable/ and R.drawable to @mipmap/ and R.mipmap.<br/>
In Android Studio this lint warning has a quickfix to perform this automatically.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MipmapIcons" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingApplicationIcon<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You should set an icon for the application as whole because there is no default. This attribute must be set as a reference to a drawable resource containing the image (for example <code>@drawable/icon</code>).<br/><div class="moreinfo">More info: <a href="https://developer.android.com/studio/publish/preparing#publishing-configure">https://developer.android.com/studio/publish/preparing#publishing-configure</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingApplicationIcon" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingBackupPin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
It is highly recommended to declare a backup <code>&lt;pin></code> element. Not having a second pin defined can cause connection failures when the particular site certificate is rotated and the app has not yet been updated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/features/security-config.html">https://developer.android.com/preview/features/security-config.html</a>
</div>To suppress this error, use the issue id "MissingBackupPin" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingClass<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If a class is referenced in the manifest or in a layout file, it must also exist in the project (or in one of the libraries included by the project. This check helps uncover typos in registration names, or attempts to rename or move classes without updating the XML references<br/>
properly.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div>To suppress this error, use the issue id "MissingClass" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingConstraints<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The layout editor allows you to place widgets anywhere on the canvas, and it records the current position with designtime attributes (such as <code>layout_editor_absoluteX</code>). These attributes are <b>not</b> applied at runtime, so if you push your layout on a device, the widgets may appear in a different location than shown in the editor. To fix this, make sure a widget has both horizontal and vertical constraints by dragging from the edge connections.<br/>To suppress this error, use the issue id "MissingConstraints" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingFirebaseInstanceTokenRefresh<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Apps that use Firebase Cloud Messaging should implement the <code>FirebaseMessagingService#onNewToken()</code> callback in order to observe token changes.<br/><div class="moreinfo">More info: <a href="https://firebase.google.com/docs/cloud-messaging/android/client#monitor-token-generation">https://firebase.google.com/docs/cloud-messaging/android/client#monitor-token-generation</a>
</div>To suppress this error, use the issue id "MissingFirebaseInstanceTokenRefresh" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingId<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If you do not specify an <code>android:id</code> or an <code>android:tag</code> attribute on a <code>&lt;fragment></code> element, then if the activity is restarted (for example for an orientation rotation) you may lose state. From the fragment documentation:<br/>
<br/>
"Each fragment requires a unique identifier that the system can use to restore the fragment if the activity is restarted (and which you can use to capture the fragment to perform transactions, such as remove it).<br/>
<br/>
* Supply the <code>android:id</code> attribute with a unique ID.<br/>
* Supply the <code>android:tag</code> attribute with a unique string.<br/>
If you provide neither of the previous two, the system uses the ID of the container view.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/fragments.html">https://developer.android.com/guide/components/fragments.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingId" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingIntentFilterForMediaSearch<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
To support voice searches on Android Auto, you should also register an <code>intent-filter</code> for the action <code>android.media.action.MEDIA_PLAY_FROM_SEARCH</code>.<br/>
To do this, add
<pre>
`&lt;intent-filter>`
    `&lt;action android:name="android.media.action.MEDIA_PLAY_FROM_SEARCH" />`
`&lt;/intent-filter>`
</pre>
to your <code>&lt;activity></code> or <code>&lt;service></code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/auto/audio/index.html#support_voice">https://developer.android.com/training/auto/audio/index.html#support_voice</a>
</div>To suppress this error, use the issue id "MissingIntentFilterForMediaSearch" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingLeanbackLauncher<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
An application intended to run on TV devices must declare a launcher activity for TV in its manifest using a <code>android.intent.category.LEANBACK_LAUNCHER</code> intent filter.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/tv/start/start.html#tv-activity">https://developer.android.com/training/tv/start/start.html#tv-activity</a>
</div>To suppress this error, use the issue id "MissingLeanbackLauncher" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingLeanbackSupport<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The manifest should declare the use of the Leanback user interface required by Android TV.<br/>
To fix this, add
<pre>
`&lt;uses-feature android:name="android.software.leanback"   android:required="false" />`
</pre>
to your manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/tv/start/start.html#leanback-req">https://developer.android.com/training/tv/start/start.html#leanback-req</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingLeanbackSupport" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingMediaBrowserServiceIntentFilter<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
An Automotive Media App requires an exported service that extends <code>android.service.media.MediaBrowserService</code> with an <code>intent-filter</code> for the action <code>android.media.browse.MediaBrowserService</code> to be able to browse and play media.<br/>
To do this, add
<pre>
`&lt;intent-filter>`
    `&lt;action android:name="android.media.browse.MediaBrowserService" />`
`&lt;/intent-filter>`
</pre>
to the service that extends <code>android.service.media.MediaBrowserService</code><br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/auto/audio/index.html#config_manifest">https://developer.android.com/training/auto/audio/index.html#config_manifest</a>
</div>To suppress this error, use the issue id "MissingMediaBrowserServiceIntentFilter" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingOnPlayFromSearch<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
To support voice searches on Android Auto, in addition to adding an <code>intent-filter</code> for the action <code>onPlayFromSearch</code>, you also need to override and implement <code>onPlayFromSearch(String query, Bundle bundle)</code><br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/auto/audio/index.html#support_voice">https://developer.android.com/training/auto/audio/index.html#support_voice</a>
</div>To suppress this error, use the issue id "MissingOnPlayFromSearch" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingPermission<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check scans through your code and libraries and looks at the APIs being used, and checks this against the set of permissions required to access those APIs. If the code using those APIs is called at runtime, then the program will crash.<br/>
<br/>
Furthermore, for permissions that are revocable (with <code>targetSdkVersion</code> 23), client code must also be prepared to handle the calls throwing an exception if the user rejects the request for permission at runtime.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingPrefix<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Most Android views have attributes in the Android namespace. When referencing these attributes you <b>must</b> include the namespace prefix, or your attribute will be interpreted by <code>aapt</code> as just a custom attribute.<br/>
<br/>
Similarly, in manifest files, nearly all attributes should be in the <code>android:</code> namespace.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingPrefix" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingQuantity<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Different languages have different rules for grammatical agreement with quantity. In English, for example, the quantity 1 is a special case. We write "1 book", but for any other quantity we'd write "n books". This distinction between singular and plural is very common, but other languages make finer distinctions.<br/>
<br/>
This lint check looks at each translation of a <code>&lt;plural></code> and makes sure that all the quantity strings considered by the given language are provided by this translation.<br/>
<br/>
For example, an English translation must provide a string for <code>quantity="one"</code>. Similarly, a Czech translation must provide a string for <code>quantity="few"</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/string-resource.html#Plurals">https://developer.android.com/guide/topics/resources/string-resource.html#Plurals</a>
</div>To suppress this error, use the issue id "MissingQuantity" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingSuperCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Some methods, such as <code>View#onDetachedFromWindow</code>, require that you also call the super implementation as part of your method.<br/>To suppress this error, use the issue id "MissingSuperCall" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingTranslation<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If an application has more than one locale, then all the strings declared in one language should also be translated in all other languages.<br/>
<br/>
If the string should <b>not</b> be translated, you can add the attribute <code>translatable="false"</code> on the <code>&lt;string></code> element, or you can define all your non-translatable strings in a resource file called <code>donottranslate.xml</code>. Or, you can ignore the issue with a <code>tools:ignore="MissingTranslation"</code> attribute.<br/>
<br/>
You can tell lint (and other tools) which language is the default language in your <code>res/values/</code> folder by specifying <code>tools:locale="languageCode"</code> for the root <code>&lt;resources></code> element in your resource file. (The <code>tools</code> prefix refers to the namespace declaration <code>http://schemas.android.com/tools</code>.)<br/>To suppress this error, use the issue id "MissingTranslation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingTvBanner<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A TV application must provide a home screen banner for each localization if it includes a Leanback launcher intent filter. The banner is the app launch point that appears on the home screen in the apps and games rows.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/tv/start/start.html#banner">https://developer.android.com/training/tv/start/start.html#banner</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingTvBanner" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MissingVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You should define the version information for your application.<br/>
<code>android:versionCode</code>: An integer value that represents the version of the application code, relative to other versions.<br/>
<br/>
<code>android:versionName</code>: A string value that represents the release version of the application code, as it should be shown to users.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/studio/publish/versioning#appversioning">https://developer.android.com/studio/publish/versioning#appversioning</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "MissingVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MotionLayoutInvalidSceneFileReference<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A motion scene file specifies the animations used in a <code>MotionLayout</code>. The <code>layoutDescription</code> is required to specify a valid motion scene file.<br/>To suppress this error, use the issue id "MotionLayoutInvalidSceneFileReference" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MotionSceneFileValidationError<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A motion scene file specifies the animations used in a <code>MotionLayout</code>. This check performs various serious correctness checks in a motion scene file.<br/>To suppress this error, use the issue id "MotionSceneFileValidationError" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">MutatingSharedPrefs<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
As stated in the docs for <code>SharedPreferences.getStringSet</code>, you must not modify the set returned by <code>getStringSet</code>:<br/>
<br/>
  "Note that you &lt;em>must not&lt;/em> modify the set instance returned    by this call.  The consistency of the stored data is not guaranteed    if you do, nor is your ability to modify the instance at all."<br/>To suppress this error, use the issue id "MutatingSharedPrefs" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NegativeMargin<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Margin values should be positive. Negative values are generally a sign that you are making assumptions about views surrounding the current one, or may be tempted to turn off child clipping to allow a view to escape its parent. Turning off child clipping to do this not only leads to poor graphical performance, it also results in wrong touch event handling since touch events are based strictly on a chain of parent-rect hit tests. Finally, making assumptions about the size of strings can lead to localization problems.<br/>To suppress this error, use the issue id "NegativeMargin" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NestedScrolling<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A scrolling widget such as a <code>ScrollView</code> should not contain any nested scrolling widgets since this has various usability issues<br/>To suppress this error, use the issue id "NestedScrolling" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NestedWeights<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Layout weights require a widget to be measured twice. When a <code>LinearLayout</code> with non-zero weights is nested inside another <code>LinearLayout</code> with non-zero weights, then the number of measurements increase exponentially.<br/>To suppress this error, use the issue id "NestedWeights" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewApi<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check scans through all the Android API calls in the application and warns about any calls that are not available on <b>all</b> versions targeted by this application (according to its minimum SDK attribute in the manifest).<br/>
<br/>
If you really want to use this API and don't need to support older devices just set the <code>minSdkVersion</code> in your <code>build.gradle</code> or <code>AndroidManifest.xml</code> files.<br/>
<br/>
If your code is <b>deliberately</b> accessing newer APIs, and you have ensured (e.g. with conditional execution) that this code will only ever be called on a supported platform, then you can annotate your class or method with the <code>@TargetApi</code> annotation specifying the local minimum SDK to apply, such as <code>@TargetApi(11)</code>, such that this check considers 11 rather than your manifest file's minimum SDK as the required API level.<br/>
<br/>
If you are deliberately setting <code>android:</code> attributes in style definitions, make sure you place this in a <code>values-v</code><i>NN</i> folder in order to avoid running into runtime conflicts on certain devices where manufacturers have added custom attributes whose ids conflict with the new ones on later platforms.<br/>
<br/>
Similarly, you can use tools:targetApi="11" in an XML file to indicate that the element will only be inflated in an adequate context.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "NewApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NewerVersionAvailable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This detector checks with a central repository to see if there are newer versions available for the dependencies used by this project. This is similar to the <code>GradleDependency</code> check, which checks for newer versions available in the Android SDK tools and libraries, but this works with any MavenCentral dependency, and connects to the library every time, which makes it more flexible but also <b>much</b> slower.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "NewerVersionAvailable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NoHardKeywords<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Do not use Kotlin&#8217;s hard keywords as the name of methods or fields.<br/>
These require the use of backticks to escape when calling from Kotlin.<br/>
Soft keywords, modifier keywords, and special identifiers are allowed.<br/>
<br/>
For example, Mockito&#8217;s <code>when</code> function requires backticks when used from Kotlin:<br/>
<br/>
    val callable = Mockito.mock(Callable::class.java)<br/>
    Mockito.`when`(callable.call()).thenReturn(/* &#8230; */)<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#no-hard-keywords">https://android.github.io/kotlin-guides/interop.html#no-hard-keywords</a>
</div>To suppress this error, use the issue id "NoHardKeywords" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NonConstantResourceId<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Avoid the usage of resource IDs where constant expressions are required.<br/>
<br/>
A future version of the Android Gradle Plugin will generate R classes with non-constant IDs in order to improve the performance of incremental compilation.<br/>To suppress this error, use the issue id "NonConstantResourceId" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NonResizeableActivity<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>&lt;activity></code> element should be allowed to be resized to allow users to take advantage of the multi-window environments available on larger screen Android devices.To fix the issue, consider declaring the corresponding activity element with <code>resizableActivity="true"</code> attribute.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/window-management">https://developer.android.com/topic/arc/window-management</a>
</div>To suppress this error, use the issue id "NonResizeableActivity" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NotInterpolated<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
To insert the value of a variable, you can use <code>${variable}</code> inside a string literal, but <b>only</b> if you are using double quotes!<br/><div class="moreinfo">More info: <a href="https://www.groovy-lang.org/syntax.html#_string_interpolation">https://www.groovy-lang.org/syntax.html#_string_interpolation</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "NotInterpolated" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">NotificationIconCompatibility<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Notification icons should define a raster image to support Android versions below 5.0 (API 21). Note that the way Lint decides whether an icon is a notification icon is based on the filename prefix <code>ic_stat_</code>. This corresponds to the naming convention documented in <a href="https://material.io/design/iconography/">https://material.io/design/iconography/</a><br/>To suppress this error, use the issue id "NotificationIconCompatibility" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ObjectAnimatorBinding<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check cross references properties referenced by String from <code>ObjectAnimator</code> and <code>PropertyValuesHolder</code> method calls and ensures that the corresponding setter methods exist and have the right signatures.<br/>To suppress this error, use the issue id "ObjectAnimatorBinding" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ObsoleteLayoutParam<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The given layout_param is not defined for the given layout, meaning it has no effect. This usually happens when you change the parent layout or move view code around without updating the layout params. This will cause useless attribute processing at runtime, and is misleading for others reading the layout so the parameter should be removed.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteLayoutParam" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ObsoleteSdkInt<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check flags version checks that are not necessary, because the <code>minSdkVersion</code> (or surrounding known API level) is already at least as high as the version checked for.<br/>
<br/>
Similarly, it also looks for resources in <code>-vNN</code> folders, such as <code>values-v14</code> where the version qualifier is less than or equal to the <code>minSdkVersion</code>, where the contents should be merged into the best folder.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ObsoleteSdkInt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">OldTargetApi<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When your application runs on a version of Android that is more recent than your <code>targetSdkVersion</code> specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the <code>targetSdkVersion</code> is less than 14, your app may get an option button in the UI.<br/>
<br/>
To fix this issue, set the <code>targetSdkVersion</code> to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: <a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a> as well as follow this guide:<br/>
<a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a><br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/distribute/best-practices/develop/target-sdk.html">https://developer.android.com/distribute/best-practices/develop/target-sdk.html</a>
<li><a href="https://developer.android.com/reference/android/os/Build.VERSION_CODES.html">https://developer.android.com/reference/android/os/Build.VERSION_CODES.html</a>
</ul></div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "OldTargetApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">OnClick<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>onClick</code> attribute value should be the name of a method in this View's context to invoke when the view is clicked. This name must correspond to a public method that takes exactly one parameter of type <code>View</code>.<br/>
<br/>
Must be a string value, using '\;' to escape characters such as '\n' or '\uxxxx' for a unicode character.<br/>To suppress this error, use the issue id "OnClick" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Orientation<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The default orientation of a <code>LinearLayout</code> is horizontal. It's pretty easy to believe that the layout is vertical, add multiple children to it, and wonder why only the first child is visible (when the subsequent children are off screen to the right). This lint rule helps pinpoint this issue by warning whenever a <code>LinearLayout</code> is used with an implicit orientation and multiple children.<br/>
<br/>
It also checks for empty LinearLayouts without an <code>orientation</code> attribute that also defines an <code>id</code> attribute. This catches the scenarios where children will be added to the <code>LinearLayout</code> dynamically. <br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "Orientation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">OutdatedLibrary<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Your app is using an outdated version of a library. This may cause violations of Google Play policies (see <a href="https://play.google.com/about/monetization-ads/ads/">https://play.google.com/about/monetization-ads/ads/</a>) and/or may affect your app&#8217;s visibility on the Play Store.<br/>
<br/>
Please try updating your app with an updated version of this library, or remove it from your app.<br/>To suppress this error, use the issue id "OutdatedLibrary" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Overdraw<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If you set a background drawable on a root view, then you should use a custom theme where the theme background is null. Otherwise, the theme background will be painted first, only to have your custom background completely cover it; this is called "overdraw".<br/>
<br/>
NOTE: This detector relies on figuring out which layouts are associated with which activities based on scanning the Java code, and it's currently doing that using an inexact pattern matching algorithm. Therefore, it can incorrectly conclude which activity the layout is associated with and then wrongly complain that a background-theme is hidden.<br/>
<br/>
If you want your custom background on multiple pages, then you should consider making a custom theme with your custom background and just using that theme instead of a root element background.<br/>
<br/>
Of course it's possible that your custom drawable is translucent and you want it to be mixed with the background. However, you will get better performance if you pre-mix the background with your drawable and use that resulting image or color as a custom theme background instead.<br/>To suppress this error, use the issue id "Overdraw" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Override<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Suppose you are building against Android API 8, and you've subclassed Activity. In your subclass you add a new method called <code>isDestroyed</code>(). At some later point, a method of the same name and signature is added to Android. Your method will now override the Android method, and possibly break its contract. Your method is not calling <code>super.isDestroyed()</code>, since your compilation target doesn't know about the method.<br/>
<br/>
The above scenario is what this lint detector looks for. The above example is real, since <code>isDestroyed()</code> was added in API 17, but it will be true for <b>any</b> method you have added to a subclass of an Android class where your build target is lower than the version the method was introduced in.<br/>
<br/>
To fix this, either rename your method, or if you are really trying to augment the builtin method if available, switch to a higher build target where you can deliberately add <code>@Override</code> on your overriding method, and call <code>super</code> if appropriate etc.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "Override" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">OverrideAbstract<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
To improve the usability of some APIs, some methods that used to be <code>abstract</code> have been made concrete by adding default implementations. This means that when compiling with new versions of the SDK, your code does not have to override these methods.<br/>
<br/>
However, if your code is also targeting older versions of the platform where these methods were still <code>abstract</code>, the code will crash. You must override all methods that used to be abstract in any versions targeted by your application's <code>minSdkVersion</code>.<br/>To suppress this error, use the issue id "OverrideAbstract" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PackageManagerGetSignatures<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Improper validation of app signatures could lead to issues where a malicious app submits itself to the Play Store with both its real certificate and a fake certificate and gains access to functionality or information it shouldn't have due to another application only checking for the fake certificate and ignoring the rest. Please make sure to validate all signatures returned by this method.<br/><div class="moreinfo">More info: <a href="https://bluebox.com/technical/android-fake-id-vulnerability/">https://bluebox.com/technical/android-fake-id-vulnerability/</a>
</div>To suppress this error, use the issue id "PackageManagerGetSignatures" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ParcelClassLoader<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The documentation for <code>Parcel#readParcelable(ClassLoader)</code> (and its variations) says that you can pass in <code>null</code> to pick up the default class loader. However, that ClassLoader is a system class loader and is not able to find classes in your own application.<br/>
<br/>
If you are writing your own classes into the <code>Parcel</code> (not just SDK classes like <code>String</code> and so on), then you should supply a <code>ClassLoader</code> for your application instead; a simple way to obtain one is to just call <code>getClass().getClassLoader()</code> from your own class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/os/Parcel.html">https://developer.android.com/reference/android/os/Parcel.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ParcelClassLoader" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ParcelCreator<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
According to the <code>Parcelable</code> interface documentation, "Classes implementing the Parcelable interface must also have a static field called <code>CREATOR</code>, which is an object implementing the <code>Parcelable.Creator</code> interface."<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/os/Parcelable.html">https://developer.android.com/reference/android/os/Parcelable.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ParcelCreator" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PendingBindings<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When using a <code>ViewDataBinding</code> in a <code>onBindViewHolder</code> method, you <b>must</b> call <code>executePendingBindings()</code> before the method exits; otherwise the data binding runtime will update the UI in the next animation frame causing a delayed update and potential jumps if the item resizes.<br/>To suppress this error, use the issue id "PendingBindings" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionImpliesUnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-permission></code> element should not require a permission that implies an unsupported Chrome OS hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding uses-feature element with <code>required="false"</code> attribute.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#implied-features">https://developer.android.com/topic/arc/manifest.html#implied-features</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "PermissionImpliesUnsupportedChromeOsHardware" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PermissionImpliesUnsupportedHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>&lt;uses-permission></code> element should not require a permission that implies an unsupported TV hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding <code>uses-feature</code> element with <code>required="false"</code> attribute.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/uses-feature-element.html#permissions">https://developer.android.com/guide/topics/manifest/uses-feature-element.html#permissions</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "PermissionImpliesUnsupportedHardware" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PinSetExpiry<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Ensures that the <code>expiration</code> attribute of the <code>&lt;pin-set></code> element is valid and has not already expired or is expiring soon<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/features/security-config.html">https://developer.android.com/preview/features/security-config.html</a>
</div>To suppress this error, use the issue id "PinSetExpiry" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PluralsCandidate<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This lint check looks for potential errors in internationalization where you have translated a message which involves a quantity and it looks like other parts of the string may need grammatical changes.<br/>
<br/>
For example, rather than something like this:
<pre>
  &lt;string name="try_again">Try again in %d seconds.&lt;/string>
</pre>
you should be using a plural:
<pre>
   &lt;plurals name="try_again">
        &lt;item quantity="one">Try again in %d second&lt;/item>
        &lt;item quantity="other">Try again in %d seconds&lt;/item>
    &lt;/plurals>
</pre>
This will ensure that in other languages the right set of translations are provided for the different quantity classes.<br/>
<br/>
(This check depends on some heuristics, so it may not accurately determine whether a string really should be a quantity. You can use tools:ignore to filter out false positives.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/string-resource.html#Plurals">https://developer.android.com/guide/topics/resources/string-resource.html#Plurals</a>
</div>To suppress this error, use the issue id "PluralsCandidate" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivateApi<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using reflection to access hidden/private Android APIs is not safe; it will often not work on devices from other vendors, and it may suddenly stop working (if the API is removed) or crash spectacularly (if the API behavior changes, since there are no guarantees for compatibility).<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/restrictions-non-sdk-interfaces">https://developer.android.com/preview/restrictions-non-sdk-interfaces</a>
</div>To suppress this error, use the issue id "PrivateApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PrivateResource<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Private resources should not be referenced; the may not be present everywhere, and even where they are they may disappear without notice.<br/>
<br/>
To fix this, copy the resource into your own project instead.<br/>To suppress this error, use the issue id "PrivateResource" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ProguardSplit<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Earlier versions of the Android tools bundled a single <code>proguard.cfg</code> file containing a ProGuard configuration file suitable for Android shrinking and obfuscation. However, that version was copied into new projects, which means that it does not continue to get updated as we improve the default ProGuard rules for Android.<br/>
<br/>
In the new version of the tools, we have split the ProGuard configuration into two halves:<br/>
* A simple configuration file containing only project-specific flags, in your project<br/>
* A generic configuration file containing the recommended set of ProGuard options for Android projects. This generic file lives in the SDK install directory which means that it gets updated along with the tools.<br/>
<br/>
In order for this to work, the proguard.config property in the <code>project.properties</code> file now refers to a path, so you can reference both the generic file as well as your own (and any additional files too).<br/>
<br/>
To migrate your project to the new setup, create a new <code>proguard-project.txt</code> file in your project containing any project specific ProGuard flags as well as any customizations you have made, then update your project.properties file to contain:<br/>
<code>proguard.config=${sdk.dir}/tools/proguard/proguard-android.txt:proguard-project.txt</code><br/>To suppress this error, use the issue id "ProguardSplit" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PropertyEscape<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
All backslashes and colons in .property files must be escaped with a backslash (). This means that when writing a Windows path, you must escape the file separators, so the path MyFiles should be written as <code>key=\\My\\Files.</code><br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "PropertyEscape" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ProtectedPermissions<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Permissions with the protection level <code>signature</code>, <code>privileged</code> or <code>signatureOrSystem</code> are only granted to system apps. If an app is a regular non-system app, it will never be able to use these permissions.<br/>To suppress this error, use the issue id "ProtectedPermissions" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ProxyPassword<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Storing proxy server passwords in clear text is dangerous if this file is shared via version control. If this is deliberate or this is a truly private project, suppress this warning.<br/>To suppress this error, use the issue id "ProxyPassword" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">PxUsage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
For performance reasons and to keep the code simpler, the Android system uses pixels as the standard unit for expressing dimension or coordinate values. That means that the dimensions of a view are always expressed in the code using pixels, but always based on the current screen density. For instance, if <code>myView.getWidth()</code> returns 10, the view is 10 pixels wide on the current screen, but on a device with a higher density screen, the value returned might be 15. If you use pixel values in your application code to work with bitmaps that are not pre-scaled for the current screen density, you might need to scale the pixel values that you use in your code to match the un-scaled bitmap source.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/practices/screens_support.html#screen-independence">https://developer.android.com/guide/practices/screens_support.html#screen-independence</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "PxUsage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">QueryAllPackagesPermission<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If you need to query or interact with other installed apps, you should be using a <code>&lt;queries></code> declaration in your manifest. Using the QUERY_ALL_PACKAGES permission in order to see all installed apps is rarely necessary, and most apps on Google Play are not allowed to have this permission.<br/><div class="moreinfo">More info: <a href="https://g.co/dev/packagevisibility">https://g.co/dev/packagevisibility</a>
</div>To suppress this error, use the issue id "QueryAllPackagesPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">QueryPermissionsNeeded<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Apps that target Android 11 cannot query or interact with other installed apps by default. If you need to query or interact with other installed apps, you may need to add a <code>&lt;queries></code> declaration in your manifest.<br/>
<br/>
As a corollary, the methods <code>PackageManager#getInstalledPackages</code> and <code>PackageManager#getInstalledApplications</code> will no longer return information about all installed apps. To query specific apps or types of apps, you can use methods like <code>PackageManager#getPackageInfo</code> or <code>PackageManager#queryIntentActivities</code>.<br/><div class="moreinfo">More info: <a href="https://g.co/dev/packagevisibility">https://g.co/dev/packagevisibility</a>
</div>To suppress this error, use the issue id "QueryPermissionsNeeded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Range<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Some parameters are required to in a particular numerical range; this check makes sure that arguments passed fall within the range. For arrays, Strings and collections this refers to the size or length.<br/>To suppress this error, use the issue id "Range" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Recycle<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Many resources, such as TypedArrays, VelocityTrackers, etc., should be recycled (with a <code>recycle()</code> call) after use. This lint check looks for missing <code>recycle()</code> calls.<br/>To suppress this error, use the issue id "Recycle" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RecyclerView<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
<code>RecyclerView</code> will <b>not</b> call <code>onBindViewHolder</code> again when the position of the item changes in the data set unless the item itself is invalidated or the new position cannot be determined.<br/>
<br/>
For this reason, you should <b>only</b> use the position parameter while acquiring the related data item inside this method, and should <b>not</b> keep a copy of it.<br/>
<br/>
If you need the position of an item later on (e.g. in a click listener), use <code>getAdapterPosition()</code> which will have the updated adapter position.<br/>To suppress this error, use the issue id "RecyclerView" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RedundantNamespace<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In Android XML documents, only specify the namespace on the root/document element. Namespace declarations elsewhere in the document are typically accidental leftovers from copy/pasting XML from other files or documentation.<br/>To suppress this error, use the issue id "RedundantNamespace" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Registered<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Activities, services and content providers should be registered in the <code>AndroidManifest.xml</code> file using <code>&lt;activity></code>, <code>&lt;service></code> and <code>&lt;provider></code> tags.<br/>
<br/>
If your activity is simply a parent class intended to be subclassed by other "real" activities, make it an abstract class.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/manifest-intro.html">https://developer.android.com/guide/topics/manifest/manifest-intro.html</a>
</div>To suppress this error, use the issue id "Registered" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RelativeOverlap<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If relative layout has text or button items aligned to left and right sides they can overlap each other due to localized text expansion unless they have mutual constraints like <code>toEndOf</code>/<code>toStartOf</code>.<br/>To suppress this error, use the issue id "RelativeOverlap" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RemoteViewLayout<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In a <code>RemoteView</code>, only some layouts and views are allowed.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/widget/RemoteViews">https://developer.android.com/reference/android/widget/RemoteViews</a>
</div>To suppress this error, use the issue id "RemoteViewLayout" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiredSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
All views must specify an explicit <code>layout_width</code> and <code>layout_height</code> attribute. There is a runtime check for this, so if you fail to specify a size, an exception is thrown at runtime.<br/>
<br/>
It's possible to specify these widths via styles as well. GridLayout, as a special case, does not require you to specify a size.<br/>To suppress this error, use the issue id "RequiredSize" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RequiresFeature<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Some APIs require optional features to be present. This check makes sure that calls to these APIs are surrounded by a check which enforces this.<br/>To suppress this error, use the issue id "RequiresFeature" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ResourceAsColor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Methods that take a color in the form of an integer should be passed an RGB triple, not the actual color resource id. You must call <code>getResources().getColor(resource)</code> to resolve the actual color value first.<br/>
<br/>
Similarly, methods that take a dimension integer should be passed an actual dimension (call <code>getResources().getDimension(resource)</code><br/>To suppress this error, use the issue id "ResourceAsColor" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ResourceType<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Ensures that resource id's passed to APIs are of the right type; for example, calling <code>Resources.getColor(R.string.name)</code> is wrong.<br/>To suppress this error, use the issue id "ResourceType" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RestrictedApi<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This API has been flagged with a restriction that has not been met.<br/>
<br/>
Examples of API restrictions:<br/>
* Method can only be invoked by a subclass<br/>
* Method can only be accessed from within the same library (defined by the Gradle library group id)<br/>
* Method can only be accessed from tests.<br/>
<br/>
You can add your own API restrictions with the <code>@RestrictTo</code> annotation.<br/>To suppress this error, use the issue id "RestrictedApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RiskyLibrary<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Your app is using a version of a library that has been identified by the library developer as a potential source of privacy and/or security risks. This may be a violation of Google Play policies (see <a href="https://play.google.com/about/monetization-ads/ads/">https://play.google.com/about/monetization-ads/ads/</a>) and/or affect your app&#8217;s visibility on the Play Store.<br/>
<br/>
When available, the individual error messages from lint will include details about the reasons for this advisory.<br/>
<br/>
Please try updating your app with an updated version of this library, or remove it from your app.<br/>To suppress this error, use the issue id "RiskyLibrary" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RtlCompat<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
API 17 adds a <code>textAlignment</code> attribute to specify text alignment. However, if you are supporting older versions than API 17, you must <b>also</b> specify a gravity or layout_gravity attribute, since older platforms will ignore the <code>textAlignment</code> attribute.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RtlCompat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RtlEnabled<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
To enable right-to-left support, when running on API 17 and higher, you must set the <code>android:supportsRtl</code> attribute in the manifest <code>&lt;application></code> element.<br/>
<br/>
If you have started adding RTL attributes, but have not yet finished the migration, you can set the attribute to false to satisfy this lint check.<br/>To suppress this error, use the issue id "RtlEnabled" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RtlHardcoded<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using <code>Gravity#LEFT</code> and <code>Gravity#RIGHT</code> can lead to problems when a layout is rendered in locales where text flows from right to left. Use <code>Gravity#START</code> and <code>Gravity#END</code> instead. Similarly, in XML <code>gravity</code> and <code>layout_gravity</code> attributes, use <code>start</code> rather than <code>left</code>.<br/>
<br/>
For XML attributes such as paddingLeft and <code>layout_marginLeft</code>, use <code>paddingStart</code> and <code>layout_marginStart</code>. <b>NOTE</b>: If your <code>minSdkVersion</code> is less than 17, you should add <b>both</b> the older left/right attributes <b>as well as</b> the new start/end attributes. On older platforms, where RTL is not supported and the start/end attributes are unknown and therefore ignored, you need the older left/right attributes. There is a separate lint check which catches that type of error.<br/>
<br/>
(Note: For <code>Gravity#LEFT</code> and <code>Gravity#START</code>, you can use these constants even when targeting older platforms, because the <code>start</code> bitmask is a superset of the <code>left</code> bitmask. Therefore, you can use <code>gravity="start"</code> rather than <code>gravity="left|start"</code>.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "RtlHardcoded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">RtlSymmetry<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If you specify padding or margin on the left side of a layout, you should probably also specify padding on the right side (and vice versa) for right-to-left layout symmetry.<br/>To suppress this error, use the issue id "RtlSymmetry" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SQLiteString<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In SQLite, any column can store any data type; the declared type for a column is more of a hint as to what the data should be cast to when stored.<br/>
<br/>
There are many ways to store a string. <code>TEXT</code>, <code>VARCHAR</code>, <code>CHARACTER</code> and <code>CLOB</code> are string types, <b>but `STRING` is not</b>. Columns defined as STRING are actually numeric.<br/>
<br/>
If you try to store a value in a numeric column, SQLite will try to cast it to a float or an integer before storing. If it can't, it will just store it as a string.<br/>
<br/>
This can lead to some subtle bugs. For example, when SQLite encounters a string like <code>1234567e1234</code>, it will parse it as a float, but the result will be out of range for floating point numbers, so <code>Inf</code> will be stored! Similarly, strings that look like integers will lose leading zeroes.<br/>
<br/>
To fix this, you can change your schema to use a <code>TEXT</code> type instead.<br/><div class="moreinfo">More info: <a href="https://www.sqlite.org/datatype3.html">https://www.sqlite.org/datatype3.html</a>
</div>To suppress this error, use the issue id "SQLiteString" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SSLCertificateSocketFactoryCreateSocket<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When <code>SSLCertificateSocketFactory.createSocket()</code> is called with an <code>InetAddress</code> as the first parameter, TLS/SSL hostname verification is not performed, which could result in insecure network traffic caused by trusting arbitrary hostnames in TLS/SSL certificates presented by peers. In this case, developers must ensure that the <code>InetAddress</code> is explicitly verified against the certificate through other means, such as by calling `SSLCertificateSocketFactory.getDefaultHostnameVerifier() to get a <code>HostnameVerifier</code> and calling <code>HostnameVerifier.verify()</code>.<br/>To suppress this error, use the issue id "SSLCertificateSocketFactoryCreateSocket" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SSLCertificateSocketFactoryGetInsecure<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>SSLCertificateSocketFactory.getInsecure()</code> method returns an SSLSocketFactory with all TLS/SSL security checks disabled, which could result in insecure network traffic caused by trusting arbitrary TLS/SSL certificates presented by peers. This method should be avoided unless needed for a special circumstance such as debugging. Instead, <code>SSLCertificateSocketFactory.getDefault()</code> should be used.<br/>To suppress this error, use the issue id "SSLCertificateSocketFactoryGetInsecure" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ScopedStorage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Scoped storage is enforced on Android 10+ (or Android 11+ if using <code>requestLegacyExternalStorage</code>). In particular, <code>WRITE_EXTERNAL_STORAGE</code> will no longer provide write access to all files; it will provide the equivalent of <code>READ_EXTERNAL_STORAGE</code> instead.<br/>
<br/>
The <code>MANAGE_EXTERNAL_STORAGE</code> permission can be used to manage all files, but it is rarely necessary and most apps on Google Play are not allowed to use it. Most apps should instead migrate to use scoped storage. To modify or delete files, apps should request write access from the user as described at <a href="https://developer.android.com/reference/android/provider/MediaStore#createWriteRequest">https://developer.android.com/reference/android/provider/MediaStore#createWriteRequest</a>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/privacy/storage#scoped-storage">https://developer.android.com/preview/privacy/storage#scoped-storage</a>
</div>To suppress this error, use the issue id "ScopedStorage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ScrollViewCount<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A <code>ScrollView</code> can only have one child widget. If you want more children, wrap them in a container layout.<br/>To suppress this error, use the issue id "ScrollViewCount" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ScrollViewSize<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
ScrollView children must set their <code>layout_width</code> or <code>layout_height</code> attributes to <code>wrap_content</code> rather than <code>fill_parent</code> or <code>match_parent</code> in the scrolling dimension<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ScrollViewSize" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SdCardPath<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Your code should not reference the <code>/sdcard</code> path directly; instead use <code>Environment.getExternalStorageDirectory().getPath()</code>.<br/>
<br/>
Similarly, do not reference the <code>/data/data/</code> path directly; it can vary in multi-user scenarios. Instead, use <code>Context.getFilesDir().getPath()</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/data-storage#filesExternal">https://developer.android.com/training/data-storage#filesExternal</a>
</div>To suppress this error, use the issue id "SdCardPath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SecureRandom<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Specifying a fixed seed will cause the instance to return a predictable sequence of numbers. This may be useful for testing but it is not appropriate for secure use.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/security/SecureRandom.html">https://developer.android.com/reference/java/security/SecureRandom.html</a>
</div>To suppress this error, use the issue id "SecureRandom" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SelectableText<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
If a <code>&lt;TextView></code> is used to display data, the user might want to copy that data and paste it elsewhere. To allow this, the <code>&lt;TextView></code> should specify <code>android:textIsSelectable="true"</code>.<br/>
<br/>
This lint check looks for TextViews which are likely to be displaying data: views whose text is set dynamically. This value will be ignored on platforms older than API 11, so it is okay to set it regardless of your <code>minSdkVersion</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "SelectableText" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ServiceCast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When you call <code>Context#getSystemService()</code>, the result is typically cast to a specific interface. This lint check ensures that the cast is compatible with the expected type of the return value.<br/>To suppress this error, use the issue id "ServiceCast" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SetJavaScriptEnabled<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Your code should not invoke <code>setJavaScriptEnabled</code> if you are not sure that your app really requires JavaScript support.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/articles/security-tips">https://developer.android.com/training/articles/security-tips</a>
</div>To suppress this error, use the issue id "SetJavaScriptEnabled" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SetTextI18n<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When calling <code>TextView#setText</code><br/>
* Never call <code>Number#toString()</code> to format numbers; it will not handle fraction separators and locale-specific digits properly. Consider using <code>String#format</code> with proper format specifications (<code>%d</code> or <code>%f</code>) instead.<br/>
* Do not pass a string literal (e.g. "Hello") to display text. Hardcoded text can not be properly translated to other languages. Consider using Android resource strings instead.<br/>
* Do not build messages by concatenating text chunks. Such messages can not be properly translated.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/localization.html">https://developer.android.com/guide/topics/resources/localization.html</a>
</div>To suppress this error, use the issue id "SetTextI18n" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SetWorldReadable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Setting files world-readable is very dangerous, and likely to cause security holes in applications. It is strongly discouraged; instead, applications should use more formal mechanisms for interactions such as <code>ContentProvider</code>, <code>BroadcastReceiver</code>, and <code>Service</code>.<br/>To suppress this error, use the issue id "SetWorldReadable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SetWorldWritable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Setting files world-writable is very dangerous, and likely to cause security holes in applications. It is strongly discouraged; instead, applications should use more formal mechanisms for interactions such as <code>ContentProvider</code>, <code>BroadcastReceiver</code>, and <code>Service</code>.<br/>To suppress this error, use the issue id "SetWorldWritable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ShiftFlags<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When defining multiple constants for use in flags, the recommended style is to use the form <code>1 &lt;&lt; 2</code>, <code>1 &lt;&lt; 3</code>, <code>1 &lt;&lt; 4</code> and so on to ensure that the constants are unique and non-overlapping.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "ShiftFlags" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ShortAlarm<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Frequent alarms are bad for battery life. As of API 22, the <code>AlarmManager</code> will override near-future and high-frequency alarm requests, delaying the alarm at least 5 seconds into the future and ensuring that the repeat interval is at least 60 seconds.<br/>
<br/>
If you really need to do work sooner than 5 seconds, post a delayed message or runnable to a Handler.<br/>To suppress this error, use the issue id "ShortAlarm" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ShowToast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
<code>Toast.makeText()</code> creates a <code>Toast</code> but does <b>not</b> show it. You must call <code>show()</code> on the resulting object to actually make the <code>Toast</code> appear.<br/>To suppress this error, use the issue id "ShowToast" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SignatureOrSystemPermissions<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>signature</code> protection level should probably be sufficient for most needs and works regardless of where applications are installed. The <code>signatureOrSystem</code> level is used for certain situations where multiple vendors have applications built into a system image and need to share specific features explicitly because they are being built together.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "SignatureOrSystemPermissions" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SimpleDateFormat<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Almost all callers should use <code>getDateInstance()</code>, <code>getDateTimeInstance()</code>, or <code>getTimeInstance()</code> to get a ready-made instance of SimpleDateFormat suitable for the user's locale. The main reason you'd create an instance this class directly is because you need to format/parse a specific machine-readable format, in which case you almost certainly want to explicitly ask for US to ensure that you get ASCII digits (rather than, say, Arabic digits).<br/>
<br/>
Therefore, you should either use the form of the SimpleDateFormat constructor where you pass in an explicit locale, such as Locale.US, or use one of the get instance methods, or suppress this error if really know what you are doing.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/java/text/SimpleDateFormat.html">https://developer.android.com/reference/java/text/SimpleDateFormat.html</a>
</div>To suppress this error, use the issue id "SimpleDateFormat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Slices<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check analyzes usages of the Slices API and offers suggestions based<br/>
on best practices.<br/>To suppress this error, use the issue id "Slices" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SmallSp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Avoid using sizes smaller than 12sp.<br/>To suppress this error, use the issue id "SmallSp" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SoonBlockedPrivateApi<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Usage of restricted non-SDK interface will throw an exception at runtime. Accessing non-SDK methods or fields through reflection has a high likelihood to break your app between versions, and is being restricted to facilitate future app compatibility.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/preview/restrictions-non-sdk-interfaces">https://developer.android.com/preview/restrictions-non-sdk-interfaces</a>
</div>To suppress this error, use the issue id "SoonBlockedPrivateApi" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SourceLockedOrientationActivity<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>Activity</code> should not be locked to a portrait orientation so that users<br/>
can take advantage of the multi-window environments and larger landscape-first screens<br/>
that Android runs on such as Chrome OS. To fix the issue, consider calling<br/>
<code>setRequestedOrientation</code> with the <code>ActivityInfo.SCREEN_ORIENTATION_FULL_SENSOR</code> or<br/>
<code>ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED</code> options or removing the call<br/>
all together.<br/>To suppress this error, use the issue id "SourceLockedOrientationActivity" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SpUsage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When setting text sizes, you should normally use <code>sp</code>, or "scale-independent pixels". This is like the <code>dp</code> unit, but it is also scaled by the user's font size preference. It is recommend you use this unit when specifying font sizes, so they will be adjusted for both the screen density and the user's preference.<br/>
<br/>
There <b>are</b> cases where you might need to use <code>dp</code>; typically this happens when the text is in a container with a specific dp-size. This will prevent the text from spilling outside the container. Note however that this means that the user's font size settings are not respected, so consider adjusting the layout itself to be more flexible.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/multiscreen/screendensities.html">https://developer.android.com/training/multiscreen/screendensities.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "SpUsage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StateListReachable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In a selector, only the last child in the state list should omit a state qualifier. If not, all subsequent items in the list will be ignored since the given item will match all.<br/>To suppress this error, use the issue id "StateListReachable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StaticFieldLeak<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A static field will leak contexts.<br/>
<br/>
Non-static inner classes have an implicit reference to their outer class. If that outer class is for example a <code>Fragment</code> or <code>Activity</code>, then this reference means that the long-running handler/loader/task will hold a reference to the activity which prevents it from getting garbage collected.<br/>
<br/>
Similarly, direct field references to activities and fragments from these longer running instances can cause leaks.<br/>
<br/>
ViewModel classes should never point to Views or non-application Contexts.<br/>To suppress this error, use the issue id "StaticFieldLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StopShip<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Using the comment <code>// STOPSHIP</code> can be used to flag code that is incomplete but checked in. This comment marker can be used to indicate that the code should not be shipped until the issue is addressed, and lint will look for these. In Gradle projects, this is only checked for non-debug (release) builds.<br/>
<br/>
In Kotlin, the <code>TODO()</code> method is also treated as a stop ship marker; you can use it to make incomplete code compile, but it will throw an exception at runtime and therefore should be fixed before shipping releases.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "StopShip" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringEscaping<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Apostrophes (') must always be escaped (with a \), unless they appear in a string which is itself escaped in double quotes (").<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "StringEscaping" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatCount<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When a formatted string takes arguments, it usually needs to reference the same arguments in all translations (or all arguments if there are no translations.<br/>
<br/>
There are cases where this is not the case, so this issue is a warning rather than an error by default. However, this usually happens when a language is not translated or updated correctly.<br/>To suppress this error, use the issue id "StringFormatCount" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatInvalid<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
If a string contains a '%' character, then the string may be a formatting string which will be passed to <code>String.format</code> from Java code to replace each '%' occurrence with specific values.<br/>
<br/>
This lint warning checks for two related problems:<br/>
(1) Formatting strings that are invalid, meaning that <code>String.format</code> will throw exceptions at runtime when attempting to use the format string.<br/>
(2) Strings containing '%' that are not formatting strings getting passed to a <code>String.format</code> call. In this case the '%' will need to be escaped as '%%'.<br/>
<br/>
NOTE: Not all Strings which look like formatting strings are intended for use by <code>String.format</code>; for example, they may contain date formats intended for <code>android.text.format.Time#format()</code>. Lint cannot always figure out that a String is a date format, so you may get false warnings in those scenarios. See the suppress help topic for information on how to suppress errors in that case.<br/>To suppress this error, use the issue id "StringFormatInvalid" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringFormatMatches<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This lint check ensures the following:<br/>
(1) If there are multiple translations of the format string, then all translations use the same type for the same numbered arguments<br/>
(2) The usage of the format string in Java is consistent with the format string, meaning that the parameter types passed to String.format matches those in the format string.<br/>To suppress this error, use the issue id "StringFormatMatches" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">StringShouldBeInt<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The properties <code>compileSdkVersion</code>, <code>minSdkVersion</code> and <code>targetSdkVersion</code> are usually numbers, but can be strings when you are using an add-on (in the case of <code>compileSdkVersion</code>) or a preview platform (for the other two properties).<br/>
<br/>
However, you can not use a number as a string (e.g. "19" instead of 19); that will result in a platform not found error message at build/sync time.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "StringShouldBeInt" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SupportAnnotationUsage<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This lint check makes sure that the support annotations (such as <code>@IntDef</code> and <code>@ColorInt</code>) are used correctly. For example, it's an error to specify an <code>@IntRange</code> where the <code>from</code> value is higher than the <code>to</code> value.<br/>To suppress this error, use the issue id "SupportAnnotationUsage" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Suspicious0dp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using 0dp as the width in a horizontal <code>LinearLayout</code> with weights is a useful trick to ensure that only the weights (and not the intrinsic sizes) are used when sizing the children.<br/>
<br/>
However, if you use 0dp for the opposite dimension, the view will be invisible. This can happen if you change the orientation of a layout without also flipping the <code>0dp</code> dimension in all the children.<br/>To suppress this error, use the issue id "Suspicious0dp" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SuspiciousImport<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Importing <code>android.R</code> is usually not intentional; it sometimes happens when you use an IDE and ask it to automatically add imports at a time when your project's R class it not present.<br/>
<br/>
Once the import is there you might get a lot of "confusing" error messages because of course the fields available on <code>android.R</code> are not the ones you'd expect from just looking at your own <code>R</code> class.<br/>To suppress this error, use the issue id "SuspiciousImport" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SwitchIntDef<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check warns if a <code>switch</code> statement does not explicitly include all the values declared by the typedef <code>@IntDef</code> declaration.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "SwitchIntDef" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">SyntheticAccessor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
A private inner class which is accessed from the outer class will force the compiler to insert a synthetic accessor; this means that you are causing extra overhead. This is not important in small projects, but is important for large apps running up against the 64K method handle limit, and especially for <b>libraries</b> where you want to make sure your library is as small as possible for the cases where your library is used in an app running up against the 64K limit.<br/>To suppress this error, use the issue id "SyntheticAccessor" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TextFields<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Providing an <code>inputType</code> attribute on a text field improves usability because depending on the data to be input, optimized keyboards can be shown to the user (such as just digits and parentheses for a phone number). <br/>
<br/>
The lint detector also looks at the <code>id</code> of the view, and if the id offers a hint of the purpose of the field (for example, the <code>id</code> contains the phrase <code>phone</code> or <code>email</code>), then lint will also ensure that the <code>inputType</code> contains the corresponding type attributes.<br/>
<br/>
If you really want to keep the text field generic, you can suppress this warning by setting <code>inputType="text"</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TextFields" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TextViewEdits<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using a <code>&lt;TextView></code> to input text is generally an error, you should be using <code>&lt;EditText></code> instead.  <code>EditText</code> is a subclass of <code>TextView</code>, and some of the editing support is provided by <code>TextView</code>, so it's possible to set some input-related properties on a <code>TextView</code>. However, using a <code>TextView</code> along with input attributes is usually a cut &amp; paste error. To input text you should be using <code>&lt;EditText></code>.<br/>
<br/>
This check also checks subclasses of <code>TextView</code>, such as <code>Button</code> and <code>CheckBox</code>, since these have the same issue: they should not be used with editable attributes.<br/>To suppress this error, use the issue id "TextViewEdits" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TooDeepLayout<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Layouts with too much nesting is bad for performance. Consider using a flatter layout (such as <code>RelativeLayout</code> or <code>GridLayout</code>).The default maximum depth is 10 but can be configured with the environment variable <code>ANDROID_LINT_MAX_DEPTH</code>.<br/>To suppress this error, use the issue id "TooDeepLayout" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TooManyViews<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using too many views in a single layout is bad for performance. Consider using compound drawables or other tricks for reducing the number of views in this layout.<br/>
<br/>
The maximum view count defaults to 80 but can be configured with the environment variable <code>ANDROID_LINT_MAX_VIEW_COUNT</code>.<br/>To suppress this error, use the issue id "TooManyViews" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TranslucentOrientation<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Specifying a fixed screen orientation with a translucent theme isn't supported on apps with <code>targetSdkVersion</code> O or greater since there can be an another activity visible behind your activity with a conflicting request.<br/>
<br/>
For example, your activity requests landscape and the visible activity behind your translucent activity request portrait. In this case the system can only honor one of the requests and currently prefers to honor the request from non-translucent activities since there is nothing visible behind them.<br/>
<br/>
Devices running platform version O or greater will throw an exception in your app if this state is detected.<br/>To suppress this error, use the issue id "TranslucentOrientation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TrulyRandom<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Key generation, signing, encryption, and random number generation may not receive cryptographically strong values due to improper initialization of the underlying PRNG on Android 4.3 and below.<br/>
<br/>
If your application relies on cryptographically secure random number generation you should apply the workaround described in <a href="https://android-developers.blogspot.com/2013/08/some-securerandom-thoughts.html">https://android-developers.blogspot.com/2013/08/some-securerandom-thoughts.html</a> .<br/>
<br/>
This lint rule is mostly informational; it does not accurately detect whether cryptographically secure RNG is required, or whether the workaround has already been applied. After reading the blog entry and updating your code if necessary, you can disable this lint issue.<br/><div class="moreinfo">More info: <a href="https://android-developers.blogspot.com/2013/08/some-securerandom-thoughts.html">https://android-developers.blogspot.com/2013/08/some-securerandom-thoughts.html</a>
</div>To suppress this error, use the issue id "TrulyRandom" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TrustAllX509TrustManager<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check looks for X509TrustManager implementations whose <code>checkServerTrusted</code> or <code>checkClientTrusted</code> methods do nothing (thus trusting any certificate chain) which could result in insecure network traffic caused by trusting arbitrary TLS/SSL certificates presented by peers.<br/>To suppress this error, use the issue id "TrustAllX509TrustManager" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyDashes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The "n dash" (&#8211;, &amp;#8211;) and the "m dash" (&#8212;, &amp;#8212;) characters are used for ranges (n dash) and breaks (m dash). Using these instead of plain hyphens can make text easier to read and your application will look more polished.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Dash">https://en.wikipedia.org/wiki/Dash</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TypographyDashes" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyEllipsis<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You can replace the string "..." with a dedicated ellipsis character, ellipsis character (&#8230;, &amp;#8230;). This can help make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Ellipsis">https://en.wikipedia.org/wiki/Ellipsis</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TypographyEllipsis" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyFractions<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You can replace certain strings, such as 1/2, and 1/4, with dedicated characters for these, such as ½ (&amp;#189;) and ¼ (&amp;#188;). This can help make the text more readable.<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Number_Forms">https://en.wikipedia.org/wiki/Number_Forms</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TypographyFractions" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyOther<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check looks for miscellaneous typographical problems and offers replacement sequences that will make the text easier to read and your application more polished.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TypographyOther" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">TypographyQuotes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Straight single quotes and double quotes, when used as a pair, can be replaced by "curvy quotes" (or directional quotes). This can make the text more readable.<br/>
<br/>
Note that you should never use grave accents and apostrophes to quote, `like this'.<br/>
<br/>
(Also note that you should not use curvy quotes for code fragments.)<br/><div class="moreinfo">More info: <a href="https://en.wikipedia.org/wiki/Quotation_mark">https://en.wikipedia.org/wiki/Quotation_mark</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "TypographyQuotes" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Typos<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check looks through the string definitions, and if it finds any words that look like likely misspellings, they are flagged.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "Typos" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UniqueConstants<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>@IntDef</code> annotation allows you to create a light-weight "enum" or type definition. However, it's possible to accidentally specify the same value for two or more of the values, which can lead to hard-to-detect bugs. This check looks for this scenario and flags any repeated constants.<br/>
<br/>
In some cases, the repeated constant is intentional (for example, renaming a constant to a more intuitive name, and leaving the old name in place for compatibility purposes).  In that case, simply suppress this check by adding a <code>@SuppressLint("UniqueConstants")</code> annotation.<br/>To suppress this error, use the issue id "UniqueConstants" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownIdInLayout<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>@+id/</code> syntax refers to an existing id, or creates a new one if it has not already been defined elsewhere. However, this means that if you have a typo in your reference, or if the referred view no longer exists, you do not get a warning since the id will be created on demand.<br/>
<br/>
This is sometimes intentional, for example where you are referring to a view which is provided in a different layout via an include. However, it is usually an accident where you have a typo or you have renamed a view without updating all the references to it.<br/>To suppress this error, use the issue id "UnknownIdInLayout" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnknownNullness<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
To improve referencing this code from Kotlin, consider adding<br/>
explicit nullness information here with either <code>@NonNull</code> or <code>@Nullable</code>.<br/>
<br/>
You can set the environment variable<br/>
    <code>ANDROID_LINT_NULLNESS_IGNORE_DEPRECATED=true</code><br/>
if you want lint to ignore classes and members that have been annotated with<br/>
<code>@Deprecated</code>.<br/><div class="moreinfo">More info: <a href="https://android.github.io/kotlin-guides/interop.html#nullability-annotations">https://android.github.io/kotlin-guides/interop.html#nullability-annotations</a>
</div>To suppress this error, use the issue id "UnknownNullness" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnlocalizedSms<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
SMS destination numbers must start with a country code or the application code must ensure that the SMS is only sent when the user is in the same country as the receiver.<br/>To suppress this error, use the issue id "UnlocalizedSms" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnpackedNativeCode<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This app loads native libraries using <code>System.loadLibrary()</code>.<br/>
<br/>
Consider adding <code>android:extractNativeLibs="false"</code> to the <code>&lt;application></code> tag in AndroidManifest.xml. Starting with Android 6.0, this will make installation faster, the app will take up less space on the device and updates will have smaller download sizes.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnpackedNativeCode" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnprotectedSMSBroadcastReceiver<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
BroadcastReceivers that declare an intent-filter for <code>SMS_DELIVER</code> or <code>SMS_RECEIVED</code> must ensure that the caller has the <code>BROADCAST_SMS</code> permission, otherwise it is possible for malicious actors to spoof intents.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnprotectedSMSBroadcastReceiver" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeDynamicallyLoadedCode<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Dynamically loading code from locations other than the application's library directory or the Android platform's built-in library directories is dangerous, as there is an increased risk that the code could have been tampered with. Applications should use <code>loadLibrary</code> when possible, which provides increased assurance that libraries are loaded from one of these safer locations. Application developers should use the features of their development environment to place application native libraries into the lib directory of their compiled APKs.<br/>To suppress this error, use the issue id "UnsafeDynamicallyLoadedCode" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeNativeCodeLocation<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
In general, application native code should only be placed in the application's library directory, not in other locations such as the res or assets directories. Placing the code in the library directory provides increased assurance that the code will not be tampered with after application installation. Application developers should use the features of their development environment to place application native libraries into the lib directory of their compiled APKs. Embedding non-shared library native executables into applications should be avoided when possible.<br/>To suppress this error, use the issue id "UnsafeNativeCodeLocation" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsafeProtectedBroadcastReceiver<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
`BroadcastReceiver`s that declare an intent-filter for a protected-broadcast action string must check that the received intent's action string matches the expected value, otherwise it is possible for malicious actors to spoof intents.<br/>To suppress this error, use the issue id "UnsafeProtectedBroadcastReceiver" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsCameraSystemFeature<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You should look for the <code>FEATURE_CAMERA_ANY</code> features to include all<br/>
possible cameras that may be on the device. Looking for <code>FEATURE_CAMERA</code><br/>
only looks for a rear facing camera, which certain tablets or Chrome OS<br/>
devices don't have, as well as newer device configurations and modes may place the<br/>
device in a state where the rear camera is not available. To fix the issue,<br/>
look for <code>FEATURE_CAMERA_ANY</code> instead.<br/>To suppress this error, use the issue id "UnsupportedChromeOsCameraSystemFeature" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedChromeOsHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported Chrome OS hardware feature. Any uses-feature not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a Chrome OS device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/topic/arc/manifest.html#incompat-entries">https://developer.android.com/topic/arc/manifest.html#incompat-entries</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnsupportedChromeOsHardware" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnsupportedTvHardware<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>&lt;uses-feature></code> element should not require this unsupported TV hardware feature. Any uses-feature not explicitly marked with <code>required="false"</code> is necessary on the device to be installed on. Ensure that any features that might prevent it from being installed on a TV device are reviewed and marked as not required in the manifest.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/tv/start/hardware.html#unsupported-features">https://developer.android.com/training/tv/start/hardware.html#unsupported-features</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnsupportedTvHardware" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Untranslatable<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Strings can be marked with <code>translatable=false</code> to indicate that they are not intended to be translated, but are present in the resource file for other purposes (for example for non-display strings that should vary by some other configuration qualifier such as screen size or API level).<br/>
<br/>
There are cases where translators accidentally translate these strings anyway, and lint will flag these occurrences with this lint check.<br/>To suppress this error, use the issue id "Untranslatable" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedAttribute<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
This check finds attributes set in XML files that were introduced in a version newer than the oldest version targeted by your application (with the <code>minSdkVersion</code> attribute).<br/>
<br/>
This is not an error; the application will simply ignore the attribute. However, if the attribute is important to the appearance or functionality of your application, you should consider finding an alternative way to achieve the same result with only available attributes, and then you can optionally create a copy of the layout in a layout-vNN folder which will be used on API NN or higher where you can take advantage of the newer attribute.<br/>
<br/>
Note: This check does not only apply to attributes. For example, some tags can be unused too, such as the new <code>&lt;tag></code> element in layouts introduced in API 21.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedAttribute" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedIds<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
This resource id definition appears not to be needed since it is not referenced from anywhere. Having id definitions, even if unused, is not necessarily a bad idea since they make working on layouts and menus easier, so there is not a strong reason to delete these.<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedIds" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedNamespace<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Unused namespace declarations take up space and require processing that is not necessary<br/>To suppress this error, use the issue id "UnusedNamespace" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedQuantity<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Android defines a number of different quantity strings, such as <code>zero</code>, <code>one</code>, <code>few</code> and <code>many</code>. However, many languages do not distinguish grammatically between all these different quantities.<br/>
<br/>
This lint check looks at the quantity strings defined for each translation and flags any quantity strings that are unused (because the language does not make that quantity distinction, and Android will therefore not look it up).<br/>
<br/>
For example, in Chinese, only the <code>other</code> quantity is used, so even if you provide translations for <code>zero</code> and <code>one</code>, these strings will <b>not</b> be returned when <code>getQuantityString()</code> is called, even with <code>0</code> or <code>1</code>.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/resources/string-resource.html#Plurals">https://developer.android.com/guide/topics/resources/string-resource.html#Plurals</a>
</div>To suppress this error, use the issue id "UnusedQuantity" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UnusedResources<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Unused resources make applications larger and slow down builds.<br/>
<br/>
The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.<br/>
<br/>
You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UnusedResources" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsableSpace<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When you need to allocate disk space for large files, consider using the new <code>allocateBytes(FileDescriptor, long)</code> API, which will automatically clear cached files belonging to other apps (as needed) to meet your request.<br/>
<br/>
When deciding if the device has enough disk space to hold your new data, call <code>getAllocatableBytes(UUID)</code> instead of using <code>getUsableSpace()</code>, since the former will consider any cached data that the system is willing to clear on your behalf.<br/>
<br/>
Note that these methods require API level 26. If your app is running on older devices, you will probably need to use both APIs, conditionally switching on <code>Build.VERSION.SDK_INT</code>. Lint only looks in the same compilation unit to see if you are already using both APIs, so if it warns even though you are already using the new API, consider moving the calls to the same file or suppressing the warning.<br/>To suppress this error, use the issue id "UsableSpace" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseAlpha2<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
For compatibility with earlier devices, you should only use 3-letter language and region codes when there is no corresponding 2 letter code.<br/><div class="moreinfo">More info: <a href="https://tools.ietf.org/html/bcp47">https://tools.ietf.org/html/bcp47</a>
</div>To suppress this error, use the issue id "UseAlpha2" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCheckPermission<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You normally want to use the result of checking a permission; these methods return whether the permission is held; they do not throw an error if the permission is not granted. Code which does not do anything with the return value probably meant to be calling the enforce methods instead, e.g. rather than <code>Context#checkCallingPermission</code> it should call <code>Context#enforceCallingPermission</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UseCheckPermission" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseCompoundDrawables<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A <code>LinearLayout</code> which contains an <code>ImageView</code> and a <code>TextView</code> can be more efficiently handled as a compound drawable (a single TextView, using the <code>drawableTop</code>, <code>drawableLeft</code>, <code>drawableRight</code> and/or <code>drawableBottom</code> attributes to draw one or more images adjacent to the text).<br/>
<br/>
If the two widgets are offset from each other with margins, this can be replaced with a <code>drawablePadding</code> attribute.<br/>
<br/>
There's a lint quickfix to perform this conversion in the Eclipse plugin.<br/>To suppress this error, use the issue id "UseCompoundDrawables" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseOfBundledGooglePlayServices<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Google Play services SDK's can be selectively included, which enables a smaller APK size. Consider declaring dependencies on individual Google Play services SDK's. If you are using Firebase API's (<a href="https://firebase.google.com/docs/android/setup">https://firebase.google.com/docs/android/setup</a>), Android Studio's Tools &#8594; Firebase assistant window can automatically add just the dependencies needed for each feature.<br/><div class="moreinfo">More info: <a href="https://developers.google.com/android/guides/setup#split">https://developers.google.com/android/guides/setup#split</a>
</div>To suppress this error, use the issue id "UseOfBundledGooglePlayServices" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseSparseArrays<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
For maps where the keys are of type integer, it's typically more efficient to use the Android <code>SparseArray</code> API. This check identifies scenarios where you might want to consider using <code>SparseArray</code> instead of <code>HashMap</code> for better performance.<br/>
<br/>
This is <b>particularly</b> useful when the value types are primitives like ints, where you can use <code>SparseIntArray</code> and avoid auto-boxing the values from <code>int</code> to <code>Integer</code>.<br/>
<br/>
If you need to construct a <code>HashMap</code> because you need to call an API outside of your control which requires a <code>Map</code>, you can suppress this warning using for example the <code>@SuppressLint</code> annotation.<br/>To suppress this error, use the issue id "UseSparseArrays" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UseValueOf<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
You should not call the constructor for wrapper classes directly, such as`new Integer(42)`. Instead, call the <code>valueOf</code> factory method, such as <code>Integer.valueOf(42)</code>. This will typically use less memory because common integers such as 0 and 1 will share a single instance.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UseValueOf" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UselessLeaf<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A layout that has no children or no background can often be removed (since it is invisible) for a flatter and more efficient layout hierarchy.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UselessLeaf" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UselessParent<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
A layout with children that has no siblings, is not a scrollview or a root layout, and does not have a background, can be removed and have its children moved directly into the parent for a flatter and more efficient layout hierarchy.<br/>To suppress this error, use the issue id "UselessParent" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsesMinSdkAttributes<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The manifest should contain a <code>&lt;uses-sdk></code> element which defines the minimum API Level required for the application to run, as well as the target version (the highest API level you have tested the version for).<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/manifest/uses-sdk-element.html">https://developer.android.com/guide/topics/manifest/uses-sdk-element.html</a>
</div>To suppress this error, use the issue id "UsesMinSdkAttributes" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingC2DM<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The C2DM library does not work on Android P or newer devices; you should migrate to Firebase Cloud Messaging to ensure reliable message delivery.<br/><div class="moreinfo">More info: <a href="https://developers.google.com/cloud-messaging/c2dm">https://developers.google.com/cloud-messaging/c2dm</a>
</div>To suppress this error, use the issue id "UsingC2DM" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">UsingHttp<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The Gradle Wrapper is available both via HTTP and HTTPS. HTTPS is more secure since it protects against man-in-the-middle attacks etc. Older projects created in Android Studio used HTTP but we now default to HTTPS and recommend upgrading existing projects.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "UsingHttp" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidActionsXml<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Ensures that an actions XML file is properly formed<br/>To suppress this error, use the issue id "ValidActionsXml" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ValidFragment<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
From the Fragment documentation:<br/>
<b>Every</b> fragment must have an empty constructor, so it can be instantiated when restoring its activity's state. It is strongly recommended that subclasses do not have other constructors with parameters, since these constructors will not be called when the fragment is re-instantiated; instead, arguments can be supplied by the caller with <code>setArguments(Bundle)</code> and later retrieved by the Fragment with <code>getArguments()</code>.<br/>
<br/>
Note that this is no longer true when you are using <code>androidx.fragment.app.Fragment</code>; with the <code>FragmentFactory</code> you can supply any arguments you want (as of version androidx version 1.1).<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/android/app/Fragment.html#Fragment()">https://developer.android.com/reference/android/app/Fragment.html#Fragment()</a>
</div>To suppress this error, use the issue id "ValidFragment" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VectorDrawableCompat<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
To use VectorDrawableCompat, you need to make two modifications to your project. First, set <code>android.defaultConfig.vectorDrawables.useSupportLibrary = true</code> in your <code>build.gradle</code> file, and second, use <code>app:srcCompat</code> instead of <code>android:src</code> to refer to vector drawables.<br/><div class="moreinfo">More info: <ul><li><a href="https://developer.android.com/guide/topics/graphics/vector-drawable-resources">https://developer.android.com/guide/topics/graphics/vector-drawable-resources</a>
<li><a href="https://medium.com/androiddevelopers/using-vector-assets-in-android-apps-4318fd662eb9">https://medium.com/androiddevelopers/using-vector-assets-in-android-apps-4318fd662eb9</a>
</ul></div>To suppress this error, use the issue id "VectorDrawableCompat" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VectorPath<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Using long vector paths is bad for performance. There are several ways to make the <code>pathData</code> shorter:<br/>
* Using less precision<br/>
* Removing some minor details<br/>
* Using the Android Studio vector conversion tool<br/>
* Rasterizing the image (converting to PNG)<br/>To suppress this error, use the issue id "VectorPath" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VectorRaster<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Vector icons require API 21 or API 24 depending on used features, but when <code>minSdkVersion</code> is less than 21 or 24 and Android Gradle plugin 1.4 or higher is used, a vector drawable placed in the <code>drawable</code> folder is automatically moved to <code>drawable-anydpi-v21</code> or <code>drawable-anydpi-v24</code> and bitmap images are generated for different screen resolutions for backwards compatibility.<br/>
<br/>
However, there are some limitations to this raster image generation, and this lint check flags elements and attributes that are not fully supported. You should manually check whether the generated output is acceptable for those older devices.<br/>To suppress this error, use the issue id "VectorRaster" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ViewConstructor<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Some layout tools (such as the Android layout editor) need to find a constructor with one of the following signatures:<br/>
* <code>View(Context context)</code><br/>
* <code>View(Context context, AttributeSet attrs)</code><br/>
* <code>View(Context context, AttributeSet attrs, int defStyle)</code><br/>
<br/>
If your custom view needs to perform initialization which does not apply when used in a layout editor, you can surround the given code with a check to see if <code>View#isInEditMode()</code> is false, since that method will return <code>false</code> at runtime but true within a user interface editor.<br/>To suppress this error, use the issue id "ViewConstructor" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ViewHolder<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When implementing a view Adapter, you should avoid unconditionally inflating a new layout; if an available item is passed in for reuse, you should try to use that one instead. This helps make for example <code>ListView</code> scrolling much smoother.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/topics/ui/layout/recyclerview#ViewHolder">https://developer.android.com/guide/topics/ui/layout/recyclerview#ViewHolder</a>
</div>To suppress this error, use the issue id "ViewHolder" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">ViewTag<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Prior to Android 4.0, the implementation of <code>View.setTag(int, Object)</code> would store the objects in a static map, where the values were strongly referenced. This means that if the object contains any references pointing back to the context, the context (which points to pretty much everything else) will leak. If you pass a view, the view provides a reference to the context that created it. Similarly, view holders typically contain a view, and cursors are sometimes also associated with views.<br/>To suppress this error, use the issue id "ViewTag" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VisibleForTests<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
With the <code>@VisibleForTesting</code> annotation you can specify an <code>otherwise=</code> attribute which specifies the intended visibility if the method had not been made more widely visible for the tests.<br/>
<br/>
This check looks for accesses from production code (e.g. not tests) where the access would not have been allowed with the intended production visibility.<br/>To suppress this error, use the issue id "VisibleForTests" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">VulnerableCordovaVersion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The version of Cordova used in the app is vulnerable to security issues. Please update to the latest Apache Cordova version.<br/><div class="moreinfo">More info: <a href="https://cordova.apache.org/announcements/2015/11/20/security.html">https://cordova.apache.org/announcements/2015/11/20/security.html</a>
</div>To suppress this error, use the issue id "VulnerableCordovaVersion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">Wakelock<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Failing to release a wakelock properly can keep the Android device in a high power mode, which reduces battery life. There are several causes of this, such as releasing the wake lock in <code>onDestroy()</code> instead of in <code>onPause()</code>, failing to call <code>release()</code> in all possible code paths after an <code>acquire()</code>, and so on.<br/>
<br/>
NOTE: If you are using the lock just to keep the screen on, you should strongly consider using <code>FLAG_KEEP_SCREEN_ON</code> instead. This window flag will be correctly managed by the platform as the user moves between applications and doesn't require a special permission. See <a href="https://developer.android.com/reference/android/view/WindowManager.LayoutParams.html#FLAG_KEEP_SCREEN_ON">https://developer.android.com/reference/android/view/WindowManager.LayoutParams.html#FLAG_KEEP_SCREEN_ON</a>.<br/>To suppress this error, use the issue id "Wakelock" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WakelockTimeout<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Wakelocks have two acquire methods: one with a timeout, and one without. You should generally always use the one with a timeout. A typical timeout is 10 minutes. If the task takes longer than it is critical that it happens (i.e. can't use <code>JobScheduler</code>) then maybe they should consider a foreground service instead (which is a stronger run guarantee and lets the user know something long/important is happening).<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "WakelockTimeout" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WearStandaloneAppFlag<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Wearable apps should specify whether they can work standalone, without a phone app. Add a valid meta-data entry for <code>com.google.android.wearable.standalone</code> to your application element and set the value to <code>true</code> or <code>false</code>.
<pre>
&lt;meta-data android:name="com.google.android.wearable.standalone"
           android:value="true"/>`
</pre>
<br/><div class="moreinfo">More info: <a href="https://developer.android.com/training/wearables/apps/packaging.html">https://developer.android.com/training/wearables/apps/packaging.html</a>
</div>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "WearStandaloneAppFlag" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WebViewApiAvailability<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>androidx.webkit</code> library is a static library you can add to your Android application allowing you to use new APIs on older platform versions, targeting more devices.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/reference/androidx/webkit/package-summary">https://developer.android.com/reference/androidx/webkit/package-summary</a>
</div>To suppress this error, use the issue id "WebViewApiAvailability" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WebViewLayout<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The WebView implementation has certain performance optimizations which will not work correctly if the parent view is using <code>wrap_content</code> rather than <code>match_parent</code>. This can lead to subtle UI bugs.<br/>To suppress this error, use the issue id "WebViewLayout" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WebpUnsupported<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The WebP format requires Android 4.0 (API 15). Certain features, such as lossless encoding and transparency, requires Android 4.2.1 (API 18; API 17 is 4.2.0.)<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "WebpUnsupported" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WeekBasedYear<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
The <code>DateTimeFormatter</code> pattern <code>YYYY</code> returns the <i>week</i> based year, not the era-based year. This means that 12/29/2019 will format to 2019, but 12/30/2019 will format to 2020!<br/>
<br/>
If you expected this to format as 2019, you should use the pattern <code>yyyy</code> instead.<br/><div class="moreinfo">More info: <a href="https://stackoverflow.com/questions/46847245/using-datetimeformatter-on-january-first-cause-an-invalid-year-value">https://stackoverflow.com/questions/46847245/using-datetimeformatter-on-january-first-cause-an-invalid-year-value</a>
</div>To suppress this error, use the issue id "WeekBasedYear" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WifiManagerLeak<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
On versions prior to Android N (24), initializing the <code>WifiManager</code> via <code>Context#getSystemService</code> can cause a memory leak if the context is not the application context. Change <code>context.getSystemService(...)</code> to <code>context.getApplicationContext().getSystemService(...)</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "WifiManagerLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WifiManagerPotentialLeak<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
On versions prior to Android N (24), initializing the <code>WifiManager</code> via <code>Context#getSystemService</code> can cause a memory leak if the context is not the application context.<br/>
<br/>
In many cases, it's not obvious from the code where the <code>Context</code> is coming from (e.g. it might be a parameter to a method, or a field initialized from various method calls). It's possible that the context being passed in is the application context, but to be on the safe side, you should consider changing <code>context.getSystemService(...)</code> to <code>context.getApplicationContext().getSystemService(...)</code>.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "WifiManagerPotentialLeak" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WorldReadableFiles<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
There are cases where it is appropriate for an application to write world readable files, but these should be reviewed carefully to ensure that they contain no private data that is leaked to other applications.<br/>To suppress this error, use the issue id "WorldReadableFiles" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WorldWriteableFiles<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
There are cases where it is appropriate for an application to write world writeable files, but these should be reviewed carefully to ensure that they contain no private data, and that if the file is modified by a malicious application it does not trick or compromise your application.<br/>To suppress this error, use the issue id "WorldWriteableFiles" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongCall<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Custom views typically need to call <code>measure()</code> on their children, not <code>onMeasure</code>. Ditto for onDraw, onLayout, etc.<br/>Note: This issue has an associated quickfix operation in Android Studio and IntelliJ IDEA.<br>
To suppress this error, use the issue id "WrongCall" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongConstant<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Ensures that when parameter in a method only allows a specific set of constants, calls obey those rules.<br/>To suppress this error, use the issue id "WrongConstant" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongRegion<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Android uses the letter codes ISO 639-1 for languages, and the letter codes ISO 3166-1 for the region codes. In many cases, the language code and the country where the language is spoken is the same, but it is also often not the case. For example, while 'se' refers to Sweden, where Swedish is spoken, the language code for Swedish is <b>not</b> <code>se</code> (which refers to the Northern Sami language), the language code is <code>sv</code>. And similarly the region code for <code>sv</code> is El Salvador.<br/>
<br/>
This lint check looks for suspicious language and region combinations, to help catch cases where you've accidentally used the wrong language or region code. Lint knows about the most common regions where a language is spoken, and if a folder combination is not one of these, it is flagged as suspicious.<br/>
<br/>
Note however that it may not be an error: you can theoretically have speakers of any language in any region and want to target that with your resources, so this check is aimed at tracking down likely mistakes, not to enforce a specific set of region and language combinations.<br/>To suppress this error, use the issue id "WrongRegion" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThread<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Ensures that a method which expects to be called on a specific thread, is actually called from that thread. For example, calls on methods in widgets should always be made on the UI thread.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div>To suppress this error, use the issue id "WrongThread" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongThreadInterprocedural<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Default<br/>
<div class="explanation">
Searches for interprocedural call paths that violate thread annotations in the program. Tracks the flow of instantiated types and lambda expressions to increase accuracy across method boundaries.<br/><div class="moreinfo">More info: <a href="https://developer.android.com/guide/components/processes-and-threads.html#Threads">https://developer.android.com/guide/components/processes-and-threads.html#Threads</a>
</div>To suppress this error, use the issue id "WrongThreadInterprocedural" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">WrongViewCast<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
Keeps track of the view types associated with ids and if it finds a usage of the id in the Java code it ensures that it is treated as the same type.<br/>To suppress this error, use the issue id "WrongViewCast" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
<div class="issue">
<div class="id">XmlEscapeNeeded<div class="issueSeparator"></div>
</div>
<div class="metadata">Disabled By: Project lint.xml file<br/>
<div class="explanation">
When a string contains characters that have special usage in XML, you must escape the characters.<br/>To suppress this error, use the issue id "XmlEscapeNeeded" as explained in the <a href="#SuppressInfo">Suppressing Warnings and Errors</a> section.<br/>
<br/></div>
</div>
</div>
</div>              </div>
              <div class="mdl-card__actions mdl-card--border">
<button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="SuppressedIssuesLink" onclick="reveal('SuppressedIssues');">
List Missing Issues</button><button class="mdl-button mdl-js-button mdl-js-ripple-effect" id="MissingIssuesCardLink" onclick="hideid('MissingIssuesCard');">
Dismiss</button>            </div>
            </div>
          </section>
<a name="SuppressInfo"></a>
<section class="section--center mdl-grid mdl-grid--no-spacing mdl-shadow--2dp" id="SuppressCard" style="display: block;">
            <div class="mdl-card mdl-cell mdl-cell--12-col">
  <div class="mdl-card__title">
    <h2 class="mdl-card__title-text">Suppressing Warnings and Errors</h2>
  </div>
              <div class="mdl-card__supporting-text">
Lint errors can be suppressed in a variety of ways:<br/>
<br/>
1. With a <code>@SuppressLint</code> annotation in the Java code<br/>
2. With a <code>tools:ignore</code> attribute in the XML file<br/>
3. With a //noinspection comment in the source code<br/>
4. With ignore flags specified in the <code>build.gradle</code> file, as explained below<br/>
5. With a <code>lint.xml</code> configuration file in the project<br/>
6. With a <code>lint.xml</code> configuration file passed to lint via the --config flag<br/>
7. With the --ignore flag passed to lint.<br/>
<br/>
To suppress a lint warning with an annotation, add a <code>@SuppressLint("id")</code> annotation on the class, method or variable declaration closest to the warning instance you want to disable. The id can be one or more issue id's, such as <code>"UnusedResources"</code> or <code>{"UnusedResources","UnusedIds"}</code>, or it can be <code>"all"</code> to suppress all lint warnings in the given scope.<br/>
<br/>
To suppress a lint warning with a comment, add a <code>//noinspection id</code> comment on the line before the statement with the error.<br/>
<br/>
To suppress a lint warning in an XML file, add a <code>tools:ignore="id"</code> attribute on the element containing the error, or one of its surrounding elements. You also need to define the namespace for the tools prefix on the root element in your document, next to the <code>xmlns:android</code> declaration:<br/>
<code>xmlns:tools="http://schemas.android.com/tools"</code><br/>
<br/>
To suppress a lint warning in a <code>build.gradle</code> file, add a section like this:<br/>

<pre>
android {
    lintOptions {
        disable 'TypographyFractions','TypographyQuotes'
    }
}
</pre>
<br/>
Here we specify a comma separated list of issue id's after the disable command. You can also use <code>warning</code> or <code>error</code> instead of <code>disable</code> to change the severity of issues.<br/>
<br/>
To suppress lint warnings with a configuration XML file, create a file named <code>lint.xml</code> and place it at the root directory of the module in which it applies.<br/>
<br/>
The format of the <code>lint.xml</code> file is something like the following:<br/>

<pre>
&lt;?xml version="1.0" encoding="UTF-8"?>
&lt;lint>
    &lt;!-- Ignore everything in the test source set -->
    &lt;issue id="all">
        &lt;ignore path="\*/test/\*" />
    &lt;/issue>

    &lt;!-- Disable this given check in this project -->
    &lt;issue id="IconMissingDensityFolder" severity="ignore" />

    &lt;!-- Ignore the ObsoleteLayoutParam issue in the given files -->
    &lt;issue id="ObsoleteLayoutParam">
        &lt;ignore path="res/layout/activation.xml" />
        &lt;ignore path="res/layout-xlarge/activation.xml" />
        &lt;ignore regexp="(foo|bar)\.java" />
    &lt;/issue>

    &lt;!-- Ignore the UselessLeaf issue in the given file -->
    &lt;issue id="UselessLeaf">
        &lt;ignore path="res/layout/main.xml" />
    &lt;/issue>

    &lt;!-- Change the severity of hardcoded strings to "error" -->
    &lt;issue id="HardcodedText" severity="error" />
&lt;/lint>
</pre>
<br/>
To suppress lint checks from the command line, pass the --ignore flag with a comma separated list of ids to be suppressed, such as:<br/>
<code>$ lint --ignore UnusedResources,UselessLeaf /my/project/path</code><br/>
<br/>
For more information, see <a href="https://developer.android.com/studio/write/lint.html#config">https://developer.android.com/studio/write/lint.html#config</a><br/>

            </div>
            </div>
          </section>    </div>
  </main>
</div>
</body>
</html>