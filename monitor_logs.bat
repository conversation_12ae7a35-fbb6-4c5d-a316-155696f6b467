@echo off
echo ========================================
echo 内存陷阱检测系统 - 日志监控工具
echo ========================================
echo.

:menu
echo 请选择监控模式:
echo [1] 监控所有相关日志
echo [2] 只监控检测事件
echo [3] 只监控系统状态
echo [4] 只监控测试日志
echo [5] 清除日志缓存
echo [0] 退出
echo.
set /p choice=请输入选择 (0-5): 

if "%choice%"=="1" goto all_logs
if "%choice%"=="2" goto detection_logs
if "%choice%"=="3" goto system_logs
if "%choice%"=="4" goto test_logs
if "%choice%"=="5" goto clear_logs
if "%choice%"=="0" goto exit
goto menu

:all_logs
echo.
echo ========================================
echo 监控所有相关日志 (按Ctrl+C停止)
echo ========================================
adb logcat -c
adb logcat | findstr /i "MemoryTrap MainActivity MemoryTrapTester"
goto menu

:detection_logs
echo.
echo ========================================
echo 监控检测事件 (按Ctrl+C停止)
echo ========================================
echo 等待修改器检测事件...
adb logcat -c
adb logcat | findstr /i "修改器检测 trap triggered onModifierDetected"
goto menu

:system_logs
echo.
echo ========================================
echo 监控系统状态 (按Ctrl+C停止)
echo ========================================
adb logcat -c
adb logcat | findstr /i "initialized monitoring started stopped"
goto menu

:test_logs
echo.
echo ========================================
echo 监控测试日志 (按Ctrl+C停止)
echo ========================================
adb logcat -c
adb logcat | findstr /i "test scan simulation"
goto menu

:clear_logs
echo.
echo 清除日志缓存...
adb logcat -c
echo 日志缓存已清除
echo.
goto menu

:exit
echo 退出监控工具
exit /b 0
