#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1083856 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=36900, tid=23600
#
# JRE version: OpenJDK Runtime Environment Temurin-21.0.7+6 (21.0.7+6) (build 21.0.7+6-LTS)
# Java VM: OpenJDK 64-Bit Server VM Temurin-21.0.7+6 (21.0.7+6-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, parallel gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-76fe49fd586dd2107a5683b086214548-sock

Host: Intel(R) Core(TM) i7-9700 CPU @ 3.00GHz, 8 cores, 31G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Wed Jul 30 09:21:22 2025  Windows 10 , 64 bit Build 19041 (10.0.19041.5915) elapsed time: 8.647174 seconds (0d 0h 0m 8s)

---------------  T H R E A D  ---------------

Current thread (0x000002b3fb0fe280):  JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=23600, stack(0x000000f0bae00000,0x000000f0baf00000) (1024K)]


Current CompileTask:
C2:8647 7697   !   4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::indexDocument (1168 bytes)

Stack: [0x000000f0bae00000,0x000000f0baf00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6ce119]
V  [jvm.dll+0x8a84a1]
V  [jvm.dll+0x8aa9ce]
V  [jvm.dll+0x8ab0b3]
V  [jvm.dll+0x27f8a6]
V  [jvm.dll+0xc507d]
V  [jvm.dll+0xc55b3]
V  [jvm.dll+0x2f2c6d]
V  [jvm.dll+0x5f6bca]
V  [jvm.dll+0x250cb2]
V  [jvm.dll+0x25106f]
V  [jvm.dll+0x249934]
V  [jvm.dll+0x246fc4]
V  [jvm.dll+0x1c760e]
V  [jvm.dll+0x25695a]
V  [jvm.dll+0x254efa]
V  [jvm.dll+0x3f03f6]
V  [jvm.dll+0x851f6b]
V  [jvm.dll+0x6cc7dd]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002b3869fc330, length=48, elements={
0x000002b3a1f94d60, 0x000002b3fa1f0410, 0x000002b3fa1f13c0, 0x000002b3faf05970,
0x000002b3faf06900, 0x000002b3faf07610, 0x000002b3faf09070, 0x000002b3faf09df0,
0x000002b3faf5daa0, 0x000002b3fb000730, 0x000002b3fb0fe280, 0x000002b3fb240450,
0x000002b3849b8f30, 0x000002b3849a2bf0, 0x000002b384845930, 0x000002b384739250,
0x000002b384c55f20, 0x000002b385272840, 0x000002b3847d86f0, 0x000002b3847d9aa0,
0x000002b3847da130, 0x000002b3847d79d0, 0x000002b3847dae50, 0x000002b3847d8060,
0x000002b3870b5bc0, 0x000002b3870b6250, 0x000002b3870b89b0, 0x000002b3870b68e0,
0x000002b3870b7600, 0x000002b3870b6f70, 0x000002b3870b9040, 0x000002b3870b7c90,
0x000002b3870b8320, 0x000002b386c76c60, 0x000002b386c74500, 0x000002b386c77980,
0x000002b386c765d0, 0x000002b386c78d30, 0x000002b386c73150, 0x000002b386c737e0,
0x000002b386c73e70, 0x000002b386c772f0, 0x000002b386c75f40, 0x000002b386c71da0,
0x000002b386c793c0, 0x000002b3847d8d80, 0x000002b388679d80, 0x000002b386c75220
}

Java Threads: ( => current thread )
  0x000002b3a1f94d60 JavaThread "main"                              [_thread_blocked, id=9072, stack(0x000000f0ba100000,0x000000f0ba200000) (1024K)]
  0x000002b3fa1f0410 JavaThread "Reference Handler"          daemon [_thread_blocked, id=28980, stack(0x000000f0ba500000,0x000000f0ba600000) (1024K)]
  0x000002b3fa1f13c0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=39700, stack(0x000000f0ba600000,0x000000f0ba700000) (1024K)]
  0x000002b3faf05970 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=30960, stack(0x000000f0ba700000,0x000000f0ba800000) (1024K)]
  0x000002b3faf06900 JavaThread "Attach Listener"            daemon [_thread_blocked, id=47092, stack(0x000000f0ba800000,0x000000f0ba900000) (1024K)]
  0x000002b3faf07610 JavaThread "Service Thread"             daemon [_thread_blocked, id=45432, stack(0x000000f0ba900000,0x000000f0baa00000) (1024K)]
  0x000002b3faf09070 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=13556, stack(0x000000f0baa00000,0x000000f0bab00000) (1024K)]
  0x000002b3faf09df0 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=38344, stack(0x000000f0bab00000,0x000000f0bac00000) (1024K)]
  0x000002b3faf5daa0 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=14236, stack(0x000000f0bac00000,0x000000f0bad00000) (1024K)]
  0x000002b3fb000730 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=19632, stack(0x000000f0bad00000,0x000000f0bae00000) (1024K)]
=>0x000002b3fb0fe280 JavaThread "C2 CompilerThread1"         daemon [_thread_in_native, id=23600, stack(0x000000f0bae00000,0x000000f0baf00000) (1024K)]
  0x000002b3fb240450 JavaThread "Notification Thread"        daemon [_thread_blocked, id=3368, stack(0x000000f0baf00000,0x000000f0bb000000) (1024K)]
  0x000002b3849b8f30 JavaThread "Active Thread: Equinox Container: bf87d527-6e51-4532-9e1e-d406c47d8166"        [_thread_blocked, id=46824, stack(0x000000f0bb700000,0x000000f0bb800000) (1024K)]
  0x000002b3849a2bf0 JavaThread "Refresh Thread: Equinox Container: bf87d527-6e51-4532-9e1e-d406c47d8166" daemon [_thread_blocked, id=40276, stack(0x000000f0bb900000,0x000000f0bba00000) (1024K)]
  0x000002b384845930 JavaThread "Framework Event Dispatcher: Equinox Container: bf87d527-6e51-4532-9e1e-d406c47d8166" daemon [_thread_blocked, id=42588, stack(0x000000f0bba00000,0x000000f0bbb00000) (1024K)]
  0x000002b384739250 JavaThread "Start Level: Equinox Container: bf87d527-6e51-4532-9e1e-d406c47d8166" daemon [_thread_blocked, id=46660, stack(0x000000f0bbb00000,0x000000f0bbc00000) (1024K)]
  0x000002b384c55f20 JavaThread "Bundle File Closer"         daemon [_thread_blocked, id=26072, stack(0x000000f0bbc00000,0x000000f0bbd00000) (1024K)]
  0x000002b385272840 JavaThread "SCR Component Actor"        daemon [_thread_blocked, id=2552, stack(0x000000f0bbd00000,0x000000f0bbe00000) (1024K)]
  0x000002b3847d86f0 JavaThread "Worker-JM"                         [_thread_blocked, id=38596, stack(0x000000f0bbf00000,0x000000f0bc000000) (1024K)]
  0x000002b3847d9aa0 JavaThread "JNA Cleaner"                daemon [_thread_blocked, id=21944, stack(0x000000f0bc000000,0x000000f0bc100000) (1024K)]
  0x000002b3847da130 JavaThread "Worker-0: Download Gradle Wrapper checksums"        [_thread_in_native, id=5692, stack(0x000000f0bb000000,0x000000f0bb100000) (1024K)]
  0x000002b3847d79d0 JavaThread "Worker-1"                          [_thread_blocked, id=6204, stack(0x000000f0bc100000,0x000000f0bc200000) (1024K)]
  0x000002b3847dae50 JavaThread "Java indexing"              daemon [_thread_in_vm, id=7316, stack(0x000000f0bc200000,0x000000f0bc300000) (1024K)]
  0x000002b3847d8060 JavaThread "Worker-2: Java indexing... "        [_thread_blocked, id=38684, stack(0x000000f0bc600000,0x000000f0bc700000) (1024K)]
  0x000002b3870b5bc0 JavaThread "Worker-3: Initialize workspace"        [_thread_blocked, id=3192, stack(0x000000f0bc700000,0x000000f0bc800000) (1024K)]
  0x000002b3870b6250 JavaThread "Thread-2"                   daemon [_thread_in_native, id=32852, stack(0x000000f0bc800000,0x000000f0bc900000) (1024K)]
  0x000002b3870b89b0 JavaThread "Thread-3"                   daemon [_thread_in_native, id=21840, stack(0x000000f0bc900000,0x000000f0bca00000) (1024K)]
  0x000002b3870b68e0 JavaThread "Thread-4"                   daemon [_thread_in_native, id=43192, stack(0x000000f0bca00000,0x000000f0bcb00000) (1024K)]
  0x000002b3870b7600 JavaThread "Thread-5"                   daemon [_thread_in_native, id=29132, stack(0x000000f0bcb00000,0x000000f0bcc00000) (1024K)]
  0x000002b3870b6f70 JavaThread "Thread-6"                   daemon [_thread_in_native, id=14876, stack(0x000000f0bcc00000,0x000000f0bcd00000) (1024K)]
  0x000002b3870b9040 JavaThread "Thread-7"                   daemon [_thread_in_native, id=44520, stack(0x000000f0bcd00000,0x000000f0bce00000) (1024K)]
  0x000002b3870b7c90 JavaThread "Thread-8"                   daemon [_thread_in_native, id=17440, stack(0x000000f0bce00000,0x000000f0bcf00000) (1024K)]
  0x000002b3870b8320 JavaThread "Thread-9"                   daemon [_thread_in_native, id=32980, stack(0x000000f0bcf00000,0x000000f0bd000000) (1024K)]
  0x000002b386c76c60 JavaThread "Thread-10"                  daemon [_thread_in_native, id=37808, stack(0x000000f0bd000000,0x000000f0bd100000) (1024K)]
  0x000002b386c74500 JavaThread "pool-2-thread-1"                   [_thread_blocked, id=35252, stack(0x000000f0bd100000,0x000000f0bd200000) (1024K)]
  0x000002b386c77980 JavaThread "WorkspaceEventsHandler"            [_thread_blocked, id=29840, stack(0x000000f0bd200000,0x000000f0bd300000) (1024K)]
  0x000002b386c765d0 JavaThread "pool-1-thread-1"                   [_thread_blocked, id=46108, stack(0x000000f0bd300000,0x000000f0bd400000) (1024K)]
  0x000002b386c78d30 JavaThread "Worker-4: Validating Gradle wrapper checksum..."        [_thread_blocked, id=22792, stack(0x000000f0bd400000,0x000000f0bd500000) (1024K)]
  0x000002b386c73150 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=44568, stack(0x000000f0bd500000,0x000000f0bd600000) (1024K)]
  0x000002b386c737e0 JavaThread "LocalFile Deleter"          daemon [_thread_blocked, id=38304, stack(0x000000f0bd600000,0x000000f0bd700000) (1024K)]
  0x000002b386c73e70 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=46524, stack(0x000000f0bd700000,0x000000f0bd800000) (1024K)]
  0x000002b386c772f0 JavaThread "Compiler Source File Reader" daemon [_thread_blocked, id=8500, stack(0x000000f0bd800000,0x000000f0bd900000) (1024K)]
  0x000002b386c75f40 JavaThread "Timer-0"                           [_thread_blocked, id=27868, stack(0x000000f0bd900000,0x000000f0bda00000) (1024K)]
  0x000002b386c71da0 JavaThread "Exec process"                      [_thread_blocked, id=42924, stack(0x000000f0bdb00000,0x000000f0bdc00000) (1024K)]
  0x000002b386c793c0 JavaThread "Exec process Thread 2"             [_thread_blocked, id=41176, stack(0x000000f0bdc00000,0x000000f0bdd00000) (1024K)]
  0x000002b3847d8d80 JavaThread "Exec process Thread 3"             [_thread_blocked, id=45584, stack(0x000000f0bdd00000,0x000000f0bde00000) (1024K)]
  0x000002b388679d80 JavaThread "C2 CompilerThread2"         daemon [_thread_in_native, id=21432, stack(0x000000f0bc300000,0x000000f0bc400000) (1024K)]
  0x000002b386c75220 JavaThread "Worker-5"                          [_thread_blocked, id=44224, stack(0x000000f0bda00000,0x000000f0bdb00000) (1024K)]
Total: 48

Other Threads:
  0x000002b3faf029e0 VMThread "VM Thread"                           [id=39496, stack(0x000000f0ba400000,0x000000f0ba500000) (1024K)]
  0x000002b3fa11cc70 WatcherThread "VM Periodic Task Thread"        [id=32532, stack(0x000000f0ba300000,0x000000f0ba400000) (1024K)]
  0x000002b3a1fb54a0 WorkerThread "GC Thread#0"                     [id=21728, stack(0x000000f0ba200000,0x000000f0ba300000) (1024K)]
  0x000002b38477b4c0 WorkerThread "GC Thread#1"                     [id=18208, stack(0x000000f0bb100000,0x000000f0bb200000) (1024K)]
  0x000002b384707f60 WorkerThread "GC Thread#2"                     [id=33616, stack(0x000000f0bb200000,0x000000f0bb300000) (1024K)]
  0x000002b384708300 WorkerThread "GC Thread#3"                     [id=15156, stack(0x000000f0bb300000,0x000000f0bb400000) (1024K)]
  0x000002b3847086a0 WorkerThread "GC Thread#4"                     [id=42832, stack(0x000000f0bb400000,0x000000f0bb500000) (1024K)]
  0x000002b384708a40 WorkerThread "GC Thread#5"                     [id=29060, stack(0x000000f0bb500000,0x000000f0bb600000) (1024K)]
  0x000002b384708de0 WorkerThread "GC Thread#6"                     [id=44992, stack(0x000000f0bb600000,0x000000f0bb700000) (1024K)]
  0x000002b38491b980 WorkerThread "GC Thread#7"                     [id=45196, stack(0x000000f0bb800000,0x000000f0bb900000) (1024K)]
Total: 10

Threads with active compile tasks:
C2 CompilerThread0  8693 7724       4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractReferenceFromConstantPool (358 bytes)
C2 CompilerThread1  8693 7697   !   4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::indexDocument (1168 bytes)
C2 CompilerThread2  8693 7722       4       java.net.URI$Parser::parse (243 bytes)
Total: 3

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000002b3b9000000-0x000002b3b9ba0000-0x000002b3b9ba0000), size 12189696, SharedBaseAddress: 0x000002b3b9000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002b3ba000000-0x000002b3fa000000, reserved size: 1073741824
Narrow klass base: 0x000002b3b9000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 CPUs: 8 total, 8 available
 Memory: 32701M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Alignments: Space 512K, Generation 512K, Heap 2M
 Heap Min Capacity: 100M
 Heap Initial Capacity: 100M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8

Heap:
 PSYoungGen      total 14336K, used 5502K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 31% used [0x00000000d5580000,0x00000000d59439a0,0x00000000d6180000)
  from space 2048K, 80% used [0x00000000d6380000,0x00000000d651c010,0x00000000d6580000)
  to   space 2048K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6380000)
 ParOldGen       total 79872K, used 79480K [0x0000000080000000, 0x0000000084e00000, 0x00000000d5580000)
  object space 79872K, 99% used [0x0000000080000000,0x0000000084d9e360,0x0000000084e00000)
 Metaspace       used 62380K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K

Card table byte_map: [0x000002b3b4360000,0x000002b3b4770000] _byte_map_base: 0x000002b3b3f60000

Marking Bits: (ParMarkBitMap*) 0x00007fffef4631f0
 Begin Bits: [0x000002b3b4a20000, 0x000002b3b6a20000)
 End Bits:   [0x000002b3b6a20000, 0x000002b3b8a20000)

Polling page: 0x000002b3a1e70000

Metaspace:

Usage:
  Non-class:     53.95 MB used.
      Class:      6.97 MB used.
       Both:     60.92 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      54.69 MB ( 85%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       7.56 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      62.25 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  8.98 MB
       Class:  8.50 MB
        Both:  17.48 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 97.31 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 1002.
num_arena_deaths: 14.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 996.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 23.
num_chunks_taken_from_freelist: 3464.
num_chunk_merges: 12.
num_chunk_splits: 2272.
num_chunks_enlarged: 1524.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=5478Kb max_used=5478Kb free=114522Kb
 bounds [0x000002b3acc50000, 0x000002b3ad1b0000, 0x000002b3b4180000]
CodeHeap 'profiled nmethods': size=120000Kb used=14710Kb max_used=14710Kb free=105289Kb
 bounds [0x000002b3a5180000, 0x000002b3a5fe0000, 0x000002b3ac6b0000]
CodeHeap 'non-nmethods': size=5760Kb used=1436Kb max_used=1501Kb free=4323Kb
 bounds [0x000002b3ac6b0000, 0x000002b3ac920000, 0x000002b3acc50000]
 total_blobs=7665 nmethods=6918 adapters=653
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 8.589 Thread 0x000002b388679d80 nmethod 7711 0x000002b3ad19ef10 code [0x000002b3ad19f460, 0x000002b3ad1a1ab0]
Event: 8.591 Thread 0x000002b3faf09df0 nmethod 7712 0x000002b3ad1a2810 code [0x000002b3ad1a2b00, 0x000002b3ad1a4598]
Event: 8.597 Thread 0x000002b3faf5daa0 7713       1       org.eclipse.jdt.internal.compiler.classfmt.FieldInfoWithAnnotation::getAnnotations (5 bytes)
Event: 8.597 Thread 0x000002b3faf5daa0 nmethod 7713 0x000002b3ad1a5590 code [0x000002b3ad1a5720, 0x000002b3ad1a57e8]
Event: 8.597 Thread 0x000002b388679d80 7714       4       org.eclipse.jdt.core.Signature::appendClassTypeSignature (323 bytes)
Event: 8.602 Thread 0x000002b3faf5daa0 7715       3       org.eclipse.jdt.internal.compiler.classfmt.MethodInfo::decodeParamAnnotations (125 bytes)
Event: 8.603 Thread 0x000002b3faf5daa0 nmethod 7715 0x000002b3a5fc9810 code [0x000002b3a5fc9a20, 0x000002b3a5fca238]
Event: 8.606 Thread 0x000002b3faf5daa0 7716       3       org.eclipse.jdt.internal.compiler.classfmt.AnnotationInfo::scanElementValue (256 bytes)
Event: 8.606 Thread 0x000002b388679d80 nmethod 7714 0x000002b3ad1a5890 code [0x000002b3ad1a5b40, 0x000002b3ad1a65c0]
Event: 8.606 Thread 0x000002b3faf5daa0 nmethod 7716 0x000002b3a5fca610 code [0x000002b3a5fca840, 0x000002b3a5fcb288]
Event: 8.609 Thread 0x000002b3faf5daa0 7717       3       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractReferenceFromConstantPool (358 bytes)
Event: 8.610 Thread 0x000002b3faf5daa0 nmethod 7717 0x000002b3a5fcb590 code [0x000002b3a5fcb960, 0x000002b3a5fcd380]
Event: 8.619 Thread 0x000002b3faf5daa0 7718       3       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractArgCount (316 bytes)
Event: 8.620 Thread 0x000002b3faf5daa0 nmethod 7718 0x000002b3a5fcde10 code [0x000002b3a5fce080, 0x000002b3a5fceb30]
Event: 8.620 Thread 0x000002b388679d80 7719       4       org.eclipse.jdt.core.Signature::encodeQualifiedName (207 bytes)
Event: 8.620 Thread 0x000002b3faf5daa0 7720       3       org.eclipse.jdt.internal.compiler.parser.Scanner::internalScanIdentifierOrKeyword (5041 bytes)
Event: 8.627 Thread 0x000002b388679d80 nmethod 7719 0x000002b3ad1a7010 code [0x000002b3ad1a7260, 0x000002b3ad1a7a40]
Event: 8.628 Thread 0x000002b3faf5daa0 nmethod 7720 0x000002b3a5fcef10 code [0x000002b3a5fcfee0, 0x000002b3a5fdb490]
Event: 8.636 Thread 0x000002b388679d80 7721       4       org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer::extractClassName (42 bytes)
Event: 8.640 Thread 0x000002b388679d80 nmethod 7721 0x000002b3ad1a8110 code [0x000002b3ad1a82e0, 0x000002b3ad1a8780]

GC Heap History (20 events):
Event: 7.894 GC heap before
{Heap before GC invocations=46 (full 3):
 PSYoungGen      total 15360K, used 15328K [0x00000000d5580000, 0x00000000d6700000, 0x0000000100000000)
  eden space 13824K, 100% used [0x00000000d5580000,0x00000000d6300000,0x00000000d6300000)
  from space 1536K, 97% used [0x00000000d6580000,0x00000000d66f8000,0x00000000d6700000)
  to   space 2048K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6500000)
 ParOldGen       total 69120K, used 66195K [0x0000000080000000, 0x0000000084380000, 0x00000000d5580000)
  object space 69120K, 95% used [0x0000000080000000,0x00000000840a4fa0,0x0000000084380000)
 Metaspace       used 60233K, committed 61504K, reserved 1114112K
  class space    used 6851K, committed 7424K, reserved 1048576K
}
Event: 7.895 GC heap after
{Heap after GC invocations=46 (full 3):
 PSYoungGen      total 15872K, used 1616K [0x00000000d5580000, 0x00000000d6700000, 0x0000000100000000)
  eden space 13824K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6300000)
  from space 2048K, 78% used [0x00000000d6300000,0x00000000d6494010,0x00000000d6500000)
  to   space 2048K, 0% used [0x00000000d6500000,0x00000000d6500000,0x00000000d6700000)
 ParOldGen       total 69120K, used 67543K [0x0000000080000000, 0x0000000084380000, 0x00000000d5580000)
  object space 69120K, 97% used [0x0000000080000000,0x00000000841f5fb0,0x0000000084380000)
 Metaspace       used 60233K, committed 61504K, reserved 1114112K
  class space    used 6851K, committed 7424K, reserved 1048576K
}
Event: 7.939 GC heap before
{Heap before GC invocations=47 (full 3):
 PSYoungGen      total 15872K, used 15440K [0x00000000d5580000, 0x00000000d6700000, 0x0000000100000000)
  eden space 13824K, 100% used [0x00000000d5580000,0x00000000d6300000,0x00000000d6300000)
  from space 2048K, 78% used [0x00000000d6300000,0x00000000d6494010,0x00000000d6500000)
  to   space 2048K, 0% used [0x00000000d6500000,0x00000000d6500000,0x00000000d6700000)
 ParOldGen       total 69120K, used 67543K [0x0000000080000000, 0x0000000084380000, 0x00000000d5580000)
  object space 69120K, 97% used [0x0000000080000000,0x00000000841f5fb0,0x0000000084380000)
 Metaspace       used 60335K, committed 61632K, reserved 1114112K
  class space    used 6864K, committed 7424K, reserved 1048576K
}
Event: 7.940 GC heap after
{Heap after GC invocations=47 (full 3):
 PSYoungGen      total 15872K, used 1804K [0x00000000d5580000, 0x00000000d6700000, 0x0000000100000000)
  eden space 13824K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6300000)
  from space 2048K, 88% used [0x00000000d6500000,0x00000000d66c3010,0x00000000d6700000)
  to   space 2048K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6500000)
 ParOldGen       total 69120K, used 68983K [0x0000000080000000, 0x0000000084380000, 0x00000000d5580000)
  object space 69120K, 99% used [0x0000000080000000,0x000000008435dfb0,0x0000000084380000)
 Metaspace       used 60335K, committed 61632K, reserved 1114112K
  class space    used 6864K, committed 7424K, reserved 1048576K
}
Event: 8.093 GC heap before
{Heap before GC invocations=48 (full 3):
 PSYoungGen      total 15872K, used 15628K [0x00000000d5580000, 0x00000000d6700000, 0x0000000100000000)
  eden space 13824K, 100% used [0x00000000d5580000,0x00000000d6300000,0x00000000d6300000)
  from space 2048K, 88% used [0x00000000d6500000,0x00000000d66c3010,0x00000000d6700000)
  to   space 2048K, 0% used [0x00000000d6300000,0x00000000d6300000,0x00000000d6500000)
 ParOldGen       total 69120K, used 68983K [0x0000000080000000, 0x0000000084380000, 0x00000000d5580000)
  object space 69120K, 99% used [0x0000000080000000,0x000000008435dfb0,0x0000000084380000)
 Metaspace       used 61380K, committed 62656K, reserved 1114112K
  class space    used 7015K, committed 7552K, reserved 1048576K
}
Event: 8.094 GC heap after
{Heap after GC invocations=48 (full 3):
 PSYoungGen      total 15360K, used 1098K [0x00000000d5580000, 0x00000000d6600000, 0x0000000100000000)
  eden space 13824K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6300000)
  from space 1536K, 71% used [0x00000000d6300000,0x00000000d6412b48,0x00000000d6480000)
  to   space 1536K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6600000)
 ParOldGen       total 71168K, used 70675K [0x0000000080000000, 0x0000000084580000, 0x00000000d5580000)
  object space 71168K, 99% used [0x0000000080000000,0x0000000084504fc0,0x0000000084580000)
 Metaspace       used 61380K, committed 62656K, reserved 1114112K
  class space    used 7015K, committed 7552K, reserved 1048576K
}
Event: 8.209 GC heap before
{Heap before GC invocations=49 (full 3):
 PSYoungGen      total 15360K, used 14903K [0x00000000d5580000, 0x00000000d6600000, 0x0000000100000000)
  eden space 13824K, 99% used [0x00000000d5580000,0x00000000d62fb390,0x00000000d6300000)
  from space 1536K, 71% used [0x00000000d6300000,0x00000000d6412b48,0x00000000d6480000)
  to   space 1536K, 0% used [0x00000000d6480000,0x00000000d6480000,0x00000000d6600000)
 ParOldGen       total 71168K, used 70675K [0x0000000080000000, 0x0000000084580000, 0x00000000d5580000)
  object space 71168K, 99% used [0x0000000080000000,0x0000000084504fc0,0x0000000084580000)
 Metaspace       used 61858K, committed 63168K, reserved 1114112K
  class space    used 7064K, committed 7680K, reserved 1048576K
}
Event: 8.211 GC heap after
{Heap after GC invocations=49 (full 3):
 PSYoungGen      total 13824K, used 1520K [0x00000000d5580000, 0x00000000d6900000, 0x0000000100000000)
  eden space 12288K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6180000)
  from space 1536K, 98% used [0x00000000d6480000,0x00000000d65fc020,0x00000000d6600000)
  to   space 3072K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6480000)
 ParOldGen       total 71680K, used 71654K [0x0000000080000000, 0x0000000084600000, 0x00000000d5580000)
  object space 71680K, 99% used [0x0000000080000000,0x00000000845f9b18,0x0000000084600000)
 Metaspace       used 61858K, committed 63168K, reserved 1114112K
  class space    used 7064K, committed 7680K, reserved 1048576K
}
Event: 8.286 GC heap before
{Heap before GC invocations=50 (full 3):
 PSYoungGen      total 13824K, used 13808K [0x00000000d5580000, 0x00000000d6900000, 0x0000000100000000)
  eden space 12288K, 100% used [0x00000000d5580000,0x00000000d6180000,0x00000000d6180000)
  from space 1536K, 98% used [0x00000000d6480000,0x00000000d65fc020,0x00000000d6600000)
  to   space 3072K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6480000)
 ParOldGen       total 71680K, used 71654K [0x0000000080000000, 0x0000000084600000, 0x00000000d5580000)
  object space 71680K, 99% used [0x0000000080000000,0x00000000845f9b18,0x0000000084600000)
 Metaspace       used 62250K, committed 63616K, reserved 1114112K
  class space    used 7115K, committed 7744K, reserved 1048576K
}
Event: 8.288 GC heap after
{Heap after GC invocations=50 (full 3):
 PSYoungGen      total 13312K, used 992K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6180000)
  from space 1024K, 96% used [0x00000000d6180000,0x00000000d6278000,0x00000000d6280000)
  to   space 2048K, 0% used [0x00000000d6380000,0x00000000d6380000,0x00000000d6580000)
 ParOldGen       total 73216K, used 73070K [0x0000000080000000, 0x0000000084780000, 0x00000000d5580000)
  object space 73216K, 99% used [0x0000000080000000,0x000000008475bb18,0x0000000084780000)
 Metaspace       used 62250K, committed 63616K, reserved 1114112K
  class space    used 7115K, committed 7744K, reserved 1048576K
}
Event: 8.373 GC heap before
{Heap before GC invocations=51 (full 3):
 PSYoungGen      total 13312K, used 13280K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 100% used [0x00000000d5580000,0x00000000d6180000,0x00000000d6180000)
  from space 1024K, 96% used [0x00000000d6180000,0x00000000d6278000,0x00000000d6280000)
  to   space 2048K, 0% used [0x00000000d6380000,0x00000000d6380000,0x00000000d6580000)
 ParOldGen       total 73216K, used 73070K [0x0000000080000000, 0x0000000084780000, 0x00000000d5580000)
  object space 73216K, 99% used [0x0000000080000000,0x000000008475bb18,0x0000000084780000)
 Metaspace       used 62372K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.375 GC heap after
{Heap after GC invocations=51 (full 3):
 PSYoungGen      total 14336K, used 1504K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6180000)
  from space 2048K, 73% used [0x00000000d6380000,0x00000000d64f8000,0x00000000d6580000)
  to   space 2048K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6380000)
 ParOldGen       total 74240K, used 73932K [0x0000000080000000, 0x0000000084880000, 0x00000000d5580000)
  object space 74240K, 99% used [0x0000000080000000,0x0000000084833300,0x0000000084880000)
 Metaspace       used 62372K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.433 GC heap before
{Heap before GC invocations=52 (full 3):
 PSYoungGen      total 14336K, used 13792K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 100% used [0x00000000d5580000,0x00000000d6180000,0x00000000d6180000)
  from space 2048K, 73% used [0x00000000d6380000,0x00000000d64f8000,0x00000000d6580000)
  to   space 2048K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6380000)
 ParOldGen       total 74240K, used 73932K [0x0000000080000000, 0x0000000084880000, 0x00000000d5580000)
  object space 74240K, 99% used [0x0000000080000000,0x0000000084833300,0x0000000084880000)
 Metaspace       used 62372K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.435 GC heap after
{Heap after GC invocations=52 (full 3):
 PSYoungGen      total 14336K, used 1552K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6180000)
  from space 2048K, 75% used [0x00000000d6180000,0x00000000d6304010,0x00000000d6380000)
  to   space 2048K, 0% used [0x00000000d6380000,0x00000000d6380000,0x00000000d6580000)
 ParOldGen       total 75776K, used 75312K [0x0000000080000000, 0x0000000084a00000, 0x00000000d5580000)
  object space 75776K, 99% used [0x0000000080000000,0x000000008498c340,0x0000000084a00000)
 Metaspace       used 62372K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.480 GC heap before
{Heap before GC invocations=53 (full 3):
 PSYoungGen      total 14336K, used 13840K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 100% used [0x00000000d5580000,0x00000000d6180000,0x00000000d6180000)
  from space 2048K, 75% used [0x00000000d6180000,0x00000000d6304010,0x00000000d6380000)
  to   space 2048K, 0% used [0x00000000d6380000,0x00000000d6380000,0x00000000d6580000)
 ParOldGen       total 75776K, used 75312K [0x0000000080000000, 0x0000000084a00000, 0x00000000d5580000)
  object space 75776K, 99% used [0x0000000080000000,0x000000008498c340,0x0000000084a00000)
 Metaspace       used 62373K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.482 GC heap after
{Heap after GC invocations=53 (full 3):
 PSYoungGen      total 14336K, used 1600K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6180000)
  from space 2048K, 78% used [0x00000000d6380000,0x00000000d6510000,0x00000000d6580000)
  to   space 2048K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6380000)
 ParOldGen       total 76800K, used 76648K [0x0000000080000000, 0x0000000084b00000, 0x00000000d5580000)
  object space 76800K, 99% used [0x0000000080000000,0x0000000084ada340,0x0000000084b00000)
 Metaspace       used 62373K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.527 GC heap before
{Heap before GC invocations=54 (full 3):
 PSYoungGen      total 14336K, used 13888K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 100% used [0x00000000d5580000,0x00000000d6180000,0x00000000d6180000)
  from space 2048K, 78% used [0x00000000d6380000,0x00000000d6510000,0x00000000d6580000)
  to   space 2048K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6380000)
 ParOldGen       total 76800K, used 76648K [0x0000000080000000, 0x0000000084b00000, 0x00000000d5580000)
  object space 76800K, 99% used [0x0000000080000000,0x0000000084ada340,0x0000000084b00000)
 Metaspace       used 62376K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.528 GC heap after
{Heap after GC invocations=54 (full 3):
 PSYoungGen      total 14336K, used 1567K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6180000)
  from space 2048K, 76% used [0x00000000d6180000,0x00000000d6307f70,0x00000000d6380000)
  to   space 2048K, 0% used [0x00000000d6380000,0x00000000d6380000,0x00000000d6580000)
 ParOldGen       total 78336K, used 78112K [0x0000000080000000, 0x0000000084c80000, 0x00000000d5580000)
  object space 78336K, 99% used [0x0000000080000000,0x0000000084c48360,0x0000000084c80000)
 Metaspace       used 62376K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.638 GC heap before
{Heap before GC invocations=55 (full 3):
 PSYoungGen      total 14336K, used 13855K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 100% used [0x00000000d5580000,0x00000000d6180000,0x00000000d6180000)
  from space 2048K, 76% used [0x00000000d6180000,0x00000000d6307f70,0x00000000d6380000)
  to   space 2048K, 0% used [0x00000000d6380000,0x00000000d6380000,0x00000000d6580000)
 ParOldGen       total 78336K, used 78112K [0x0000000080000000, 0x0000000084c80000, 0x00000000d5580000)
  object space 78336K, 99% used [0x0000000080000000,0x0000000084c48360,0x0000000084c80000)
 Metaspace       used 62380K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}
Event: 8.640 GC heap after
{Heap after GC invocations=55 (full 3):
 PSYoungGen      total 14336K, used 1648K [0x00000000d5580000, 0x00000000d6580000, 0x0000000100000000)
  eden space 12288K, 0% used [0x00000000d5580000,0x00000000d5580000,0x00000000d6180000)
  from space 2048K, 80% used [0x00000000d6380000,0x00000000d651c010,0x00000000d6580000)
  to   space 2048K, 0% used [0x00000000d6180000,0x00000000d6180000,0x00000000d6380000)
 ParOldGen       total 79872K, used 79480K [0x0000000080000000, 0x0000000084e00000, 0x00000000d5580000)
  object space 79872K, 99% used [0x0000000080000000,0x0000000084d9e360,0x0000000084e00000)
 Metaspace       used 62380K, committed 63744K, reserved 1114112K
  class space    used 7132K, committed 7744K, reserved 1048576K
}

Dll operation events (15 events):
Event: 0.009 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
Event: 0.029 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.084 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
Event: 0.088 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
Event: 0.090 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
Event: 0.093 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
Event: 0.107 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
Event: 0.180 Loaded shared library c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
Event: 1.110 Loaded shared library C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
Event: 2.353 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna18198238030756940158.dll
Event: 6.048 Loaded shared library C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
Event: 7.172 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
Event: 7.176 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
Event: 7.602 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
Event: 7.935 Loaded shared library C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll

Deoptimization events (20 events):
Event: 8.518 Thread 0x000002b3847dae50 DEOPT PACKING pc=0x000002b3ad02ea9c sp=0x000000f0bc2fe9f0
Event: 8.518 Thread 0x000002b3847dae50 DEOPT UNPACKING pc=0x000002b3ac703aa2 sp=0x000000f0bc2fe980 mode 2
Event: 8.609 Thread 0x000002b3847dae50 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002b3ad0d8940 relative=0x0000000000006bc0
Event: 8.609 Thread 0x000002b3847dae50 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002b3ad0d8940 method=org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer.extractArgCount([C[CZ)I @ 206 c2
Event: 8.609 Thread 0x000002b3847dae50 DEOPT PACKING pc=0x000002b3ad0d8940 sp=0x000000f0bc2fea30
Event: 8.609 Thread 0x000002b3847dae50 DEOPT UNPACKING pc=0x000002b3ac703aa2 sp=0x000000f0bc2fe980 mode 2
Event: 8.609 Thread 0x000002b3847dae50 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002b3acfe672c relative=0x00000000000007cc
Event: 8.609 Thread 0x000002b3847dae50 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002b3acfe672c method=org.eclipse.jdt.internal.core.search.indexing.BinaryIndexer.extractArgCount([C[CZ)I @ 237 c2
Event: 8.609 Thread 0x000002b3847dae50 DEOPT PACKING pc=0x000002b3acfe672c sp=0x000000f0bc2fea00
Event: 8.609 Thread 0x000002b3847dae50 DEOPT UNPACKING pc=0x000002b3ac703aa2 sp=0x000000f0bc2fe978 mode 2
Event: 8.619 Thread 0x000002b3847dae50 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002b3ad19f9cc relative=0x000000000000056c
Event: 8.619 Thread 0x000002b3847dae50 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002b3ad19f9cc method=org.eclipse.jdt.internal.compiler.parser.Scanner.internalScanIdentifierOrKeyword(II[C)Lorg/eclipse/jdt/internal/compiler/parser/TerminalToken; @ 3657 c2
Event: 8.619 Thread 0x000002b3847dae50 DEOPT PACKING pc=0x000002b3ad19f9cc sp=0x000000f0bc2fed80
Event: 8.619 Thread 0x000002b3847dae50 DEOPT UNPACKING pc=0x000002b3ac703aa2 sp=0x000000f0bc2fed10 mode 2
Event: 8.619 Thread 0x000002b3847dae50 DEOPT PACKING pc=0x000002b3a5fccf53 sp=0x000000f0bc2fe9d0
Event: 8.619 Thread 0x000002b3847dae50 DEOPT UNPACKING pc=0x000002b3ac704242 sp=0x000000f0bc2fdf38 mode 0
Event: 8.620 Thread 0x000002b3847dae50 DEOPT PACKING pc=0x000002b3a5e2dd26 sp=0x000000f0bc2fe6b0
Event: 8.620 Thread 0x000002b3847dae50 DEOPT UNPACKING pc=0x000002b3ac704242 sp=0x000000f0bc2fdb88 mode 0
Event: 8.638 Thread 0x000002b3847dae50 DEOPT PACKING pc=0x000002b3a5fccf53 sp=0x000000f0bc2fe9d0
Event: 8.638 Thread 0x000002b3847dae50 DEOPT UNPACKING pc=0x000002b3ac704242 sp=0x000000f0bc2fdf38 mode 0

Classes loaded (20 events):
Event: 8.355 Loading class java/util/OptionalInt
Event: 8.355 Loading class java/util/OptionalInt done
Event: 8.355 Loading class sun/net/www/http/KeepAliveStream
Event: 8.355 Loading class sun/net/www/http/Hurryable
Event: 8.355 Loading class sun/net/www/http/Hurryable done
Event: 8.355 Loading class sun/net/www/MeteredStream
Event: 8.355 Loading class sun/net/www/MeteredStream done
Event: 8.355 Loading class sun/net/www/http/KeepAliveStream done
Event: 8.355 Loading class sun/net/www/http/KeepAliveStreamCleaner
Event: 8.355 Loading class sun/net/www/http/KeepAliveStreamCleaner done
Event: 8.359 Loading class sun/net/www/http/KeepAliveStreamCleaner$1
Event: 8.359 Loading class sun/net/www/http/KeepAliveStreamCleaner$1 done
Event: 8.359 Loading class sun/net/www/http/KeepAliveStreamCleaner$2
Event: 8.359 Loading class sun/net/www/http/KeepAliveStreamCleaner$2 done
Event: 8.359 Loading class sun/security/ssl/Alert
Event: 8.359 Loading class sun/security/ssl/Alert done
Event: 8.360 Loading class sun/security/ssl/Alert$AlertConsumer
Event: 8.360 Loading class sun/security/ssl/Alert$AlertConsumer done
Event: 8.360 Loading class sun/security/ssl/Alert$Level
Event: 8.360 Loading class sun/security/ssl/Alert$Level done

Classes unloaded (7 events):
Event: 2.807 Thread 0x000002b3faf029e0 Unloading class 0x000002b3ba1a6c00 'java/lang/invoke/LambdaForm$MH+0x000002b3ba1a6c00'
Event: 2.807 Thread 0x000002b3faf029e0 Unloading class 0x000002b3ba1a6800 'java/lang/invoke/LambdaForm$MH+0x000002b3ba1a6800'
Event: 2.807 Thread 0x000002b3faf029e0 Unloading class 0x000002b3ba1a6400 'java/lang/invoke/LambdaForm$MH+0x000002b3ba1a6400'
Event: 2.807 Thread 0x000002b3faf029e0 Unloading class 0x000002b3ba1a6000 'java/lang/invoke/LambdaForm$MH+0x000002b3ba1a6000'
Event: 2.807 Thread 0x000002b3faf029e0 Unloading class 0x000002b3ba1a5c00 'java/lang/invoke/LambdaForm$BMH+0x000002b3ba1a5c00'
Event: 2.807 Thread 0x000002b3faf029e0 Unloading class 0x000002b3ba1a5800 'java/lang/invoke/LambdaForm$DMH+0x000002b3ba1a5800'
Event: 2.807 Thread 0x000002b3faf029e0 Unloading class 0x000002b3ba1a4800 'java/lang/invoke/LambdaForm$DMH+0x000002b3ba1a4800'

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 5.565 Thread 0x000002b3870b5bc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d579c9f8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object)'> (0x00000000d579c9f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.565 Thread 0x000002b3870b5bc0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d579fa08}: Found class java.lang.Object, but interface was expected> (0x00000000d579fa08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 5.577 Thread 0x000002b3870b5bc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d59feaa8}: method resolution failed> (0x00000000d59feaa8) 
thrown [s\src\hotspot\share\prims\methodHandles.cpp, line 1144]
Event: 5.633 Thread 0x000002b3870b5bc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d62e3f58}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d62e3f58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 5.634 Thread 0x000002b3870b5bc0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d63061a8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d63061a8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.034 Thread 0x000002b386c75220 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5eea798}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5eea798) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 6.236 Thread 0x000002b386c765d0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000000d5f613b0}: Found class java.lang.Object, but interface was expected> (0x00000000d5f613b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 840]
Event: 6.630 Thread 0x000002b3847dae50 Implicit null exception at 0x000002b3ad08edcf to 0x000002b3ad092b24
Event: 7.123 Thread 0x000002b386c75220 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d58dc120}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d58dc120) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.203 Thread 0x000002b386c75220 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000d617f898}: org/slf4j/impl/StaticMarkerBinder> (0x00000000d617f898) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 301]
Event: 7.270 Thread 0x000002b386c75220 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d560c3f8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d560c3f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.288 Thread 0x000002b386c75220 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5d22f90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000d5d22f90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.442 Thread 0x000002b386c71da0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000d5d1b3a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000000d5d1b3a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 773]
Event: 7.593 Thread 0x000002b386c765d0 Implicit null exception at 0x000002b3ad04d300 to 0x000002b3ad04d764
Event: 7.593 Thread 0x000002b386c765d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5942b08}> (0x00000000d5942b08) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.593 Thread 0x000002b386c765d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d5943158}> (0x00000000d5943158) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.594 Thread 0x000002b386c765d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d594e9c8}> (0x00000000d594e9c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.594 Thread 0x000002b386c765d0 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d596cf60}> (0x00000000d596cf60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.620 Thread 0x000002b3847da130 Exception <a 'sun/nio/fs/WindowsException'{0x00000000d618a6a8}> (0x00000000d618a6a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 520]
Event: 7.774 Thread 0x000002b3847da130 Implicit null exception at 0x000002b3acfde786 to 0x000002b3acfdf2a4

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 7.995 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 7.998 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 8.093 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 8.094 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 8.208 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 8.211 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 8.286 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 8.288 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 8.356 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 8.359 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 8.373 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 8.375 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 8.433 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 8.435 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 8.480 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 8.482 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 8.527 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 8.528 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done
Event: 8.638 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure)
Event: 8.640 Executing VM operation: ParallelGCFailedAllocation (Allocation Failure) done

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a28710
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a28a90
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a2ab10
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a37c90
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a3be10
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a4ec10
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a5d210
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a65d10
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a80690
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a82d90
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5a87610
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5aa6590
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5aa8090
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5aa9990
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5aaa390
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5aaa810
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5aaab90
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5aba910
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5abc810
Event: 7.683 Thread 0x000002b3faf029e0 flushing  nmethod 0x000002b3a5ac1090

Events (20 events):
Event: 3.907 Thread 0x000002b3a1f94d60 Thread added: 0x000002b386c765d0
Event: 4.019 Thread 0x000002b3847d79d0 Thread added: 0x000002b386c78d30
Event: 5.487 Thread 0x000002b3870b5bc0 Thread added: 0x000002b386c73150
Event: 5.494 Thread 0x000002b386c73150 Thread added: 0x000002b386c737e0
Event: 5.519 Thread 0x000002b3870b5bc0 Thread added: 0x000002b386c73e70
Event: 5.520 Thread 0x000002b3870b5bc0 Thread added: 0x000002b386c772f0
Event: 5.609 Thread 0x000002b3870b5bc0 Thread added: 0x000002b386c75f40
Event: 5.666 Thread 0x000002b3870b5bc0 Thread added: 0x000002b386c75220
Event: 7.420 Thread 0x000002b386c75220 Thread added: 0x000002b386c71da0
Event: 7.445 Thread 0x000002b386c71da0 Thread added: 0x000002b386c793c0
Event: 7.445 Thread 0x000002b386c71da0 Thread added: 0x000002b3847d8d80
Event: 7.511 Thread 0x000002b386a04960 Thread exited: 0x000002b386a04960
Event: 7.567 Thread 0x000002b386c75220 Thread exited: 0x000002b386c75220
Event: 7.581 Thread 0x000002b3faf5daa0 Thread added: 0x000002b388679d80
Event: 7.724 Thread 0x000002b3847d79d0 Thread added: 0x000002b386c75220
Event: 7.849 Thread 0x000002b3842dde10 Thread exited: 0x000002b3842dde10
Event: 8.295 Thread 0x000002b3847da130 Thread added: 0x000002b386c72430
Event: 8.296 Thread 0x000002b386c72430 Thread exited: 0x000002b386c72430
Event: 8.627 Thread 0x000002b3847da130 Thread added: 0x000002b386c72430
Event: 8.629 Thread 0x000002b386c72430 Thread exited: 0x000002b386c72430


Dynamic libraries:
0x00007ff67eac0000 - 0x00007ff67eace000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.exe
0x00007ff8640b0000 - 0x00007ff8642a8000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff8634e0000 - 0x00007ff8635a2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ff861740000 - 0x00007ff861a36000 	C:\Windows\System32\KERNELBASE.dll
0x00007ff861f20000 - 0x00007ff862020000 	C:\Windows\System32\ucrtbase.dll
0x00007ff807520000 - 0x00007ff807538000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jli.dll
0x00007ff862ed0000 - 0x00007ff86306d000 	C:\Windows\System32\USER32.dll
0x00007ff862050000 - 0x00007ff862072000 	C:\Windows\System32\win32u.dll
0x00007ff854c70000 - 0x00007ff854d79000 	C:\Windows\SYSTEM32\winhafnt64.dll
0x00007ff863db0000 - 0x00007ff863ddb000 	C:\Windows\System32\GDI32.dll
0x00007ff861b50000 - 0x00007ff861c69000 	C:\Windows\System32\gdi32full.dll
0x00007ff861e80000 - 0x00007ff861f1d000 	C:\Windows\System32\msvcp_win.dll
0x00007ff800500000 - 0x00007ff80051e000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\VCRUNTIME140.dll
0x00007ff8526c0000 - 0x00007ff85295a000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ff8635b0000 - 0x00007ff86364e000 	C:\Windows\System32\msvcrt.dll
0x00007ff8620d0000 - 0x00007ff862181000 	C:\Windows\System32\ADVAPI32.dll
0x00007ff8636c0000 - 0x00007ff86375f000 	C:\Windows\System32\sechost.dll
0x00007ff863c80000 - 0x00007ff863da3000 	C:\Windows\System32\RPCRT4.dll
0x00007ff862020000 - 0x00007ff862047000 	C:\Windows\System32\bcrypt.dll
0x00007ff85b820000 - 0x00007ff85b82a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ff862370000 - 0x00007ff86239f000 	C:\Windows\System32\IMM32.DLL
0x00007ff8544a0000 - 0x00007ff854b9c000 	C:\Windows\SYSTEM32\winhadnt64.dll
0x00007ff862190000 - 0x00007ff8621eb000 	C:\Windows\System32\SHLWAPI.dll
0x00007ff862700000 - 0x00007ff862e6e000 	C:\Windows\System32\SHELL32.dll
0x00007ff8625c0000 - 0x00007ff8626eb000 	C:\Windows\System32\ole32.dll
0x00007ff854ba0000 - 0x00007ff854bbd000 	C:\Windows\SYSTEM32\MPR.dll
0x00007ff863920000 - 0x00007ff863c73000 	C:\Windows\System32\combase.dll
0x00007ff863de0000 - 0x00007ff863ead000 	C:\Windows\System32\OLEAUT32.dll
0x00007ff863650000 - 0x00007ff8636bb000 	C:\Windows\System32\WS2_32.dll
0x00007ff8593e0000 - 0x00007ff859407000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ff861ac0000 - 0x00007ff861b42000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ff8540b0000 - 0x00007ff8542eb000 	C:\Windows\SYSTEM32\dtframe64.dll
0x00007ff854070000 - 0x00007ff8540a2000 	C:\Windows\SYSTEM32\TIjtDrvd64.dll
0x00007ff854bc0000 - 0x00007ff854c64000 	C:\Windows\SYSTEM32\winspool.drv
0x00007ff862430000 - 0x00007ff8624dd000 	C:\Windows\System32\shcore.dll
0x00007ff853f40000 - 0x00007ff854063000 	C:\Windows\SYSTEM32\dtsframe64.dll
0x00007ff860e60000 - 0x00007ff860eca000 	C:\Windows\SYSTEM32\mswsock.dll
0x00007ff863fe0000 - 0x00007ff863fe8000 	C:\Windows\System32\psapi.dll
0x00007ff853e80000 - 0x00007ff853e8c000 	C:\Windows\SYSTEM32\WinUsb.dll
0x00007ff863070000 - 0x00007ff8634e0000 	C:\Windows\System32\setupapi.dll
0x00007ff862080000 - 0x00007ff8620ce000 	C:\Windows\System32\cfgmgr32.dll
0x00007ff853d60000 - 0x00007ff853e7a000 	C:\Windows\SYSTEM32\TMailHook64.dll
0x00007ff853b40000 - 0x00007ff853d53000 	C:\Windows\SYSTEM32\winncap364.dll
0x00007ff83ad70000 - 0x00007ff83ad7c000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\vcruntime140_1.dll
0x00007ff8001d0000 - 0x00007ff80025d000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\msvcp140.dll
0x00007fffee7b0000 - 0x00007fffef540000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server\jvm.dll
0x00007ff861150000 - 0x00007ff86119b000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ff861100000 - 0x00007ff861112000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ff85ffb0000 - 0x00007ff85ffc2000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ff832980000 - 0x00007ff83298a000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\jimage.dll
0x00007ff85f2f0000 - 0x00007ff85f4f1000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ff856d00000 - 0x00007ff856d34000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ff851f60000 - 0x00007ff851f6f000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\instrument.dll
0x00007ff8004e0000 - 0x00007ff8004ff000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\java.dll
0x00007ff85f500000 - 0x00007ff85fca4000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff861120000 - 0x00007ff86114b000 	C:\Windows\SYSTEM32\Wldp.dll
0x00007ff861670000 - 0x00007ff861695000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ff8004c0000 - 0x00007ff8004d8000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\zip.dll
0x00007ff856990000 - 0x00007ff8569a0000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\net.dll
0x00007ff85b8e0000 - 0x00007ff85b9ea000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ff856970000 - 0x00007ff856986000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\nio.dll
0x00007ff827500000 - 0x00007ff827510000 	c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\verify.dll
0x00007ff84cd30000 - 0x00007ff84cd75000 	C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702\eclipse_11911.dll
0x00007ff861060000 - 0x00007ff861078000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ff860720000 - 0x00007ff860758000 	C:\Windows\system32\rsaenh.dll
0x00007ff8615f0000 - 0x00007ff86161e000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ff861050000 - 0x00007ff86105c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ff860b40000 - 0x00007ff860b7b000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ff8626f0000 - 0x00007ff8626f8000 	C:\Windows\System32\NSI.dll
0x00007ff83c160000 - 0x00007ff83c1a9000 	C:\Users\<USER>\AppData\Local\Temp\jna-146731693\jna18198238030756940158.dll
0x00007ff85c020000 - 0x00007ff85c037000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ff85bf90000 - 0x00007ff85bfad000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ff856940000 - 0x00007ff856967000 	C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64\native-platform.dll
0x00007ff856830000 - 0x00007ff85683a000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management.dll
0x00007ff83da10000 - 0x00007ff83da1b000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\management_ext.dll
0x00007ff84cdc0000 - 0x00007ff84cdcc000 	C:\Windows\SYSTEM32\secur32.dll
0x00007ff861620000 - 0x00007ff861652000 	C:\Windows\SYSTEM32\SSPICLI.DLL
0x00007ff8275f0000 - 0x00007ff8275fe000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\sunmscapi.dll
0x00007ff861c70000 - 0x00007ff861dcd000 	C:\Windows\System32\CRYPT32.dll
0x00007ff8611e0000 - 0x00007ff861207000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ff8611a0000 - 0x00007ff8611db000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ff860b80000 - 0x00007ff860c4a000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00000000605a0000 - 0x00000000605c6000 	C:\Program Files\Bonjour\mdnsNSP.dll
0x00007ff85aa70000 - 0x00007ff85aa7a000 	C:\Windows\System32\rasadhlp.dll
0x00007ff85a6a0000 - 0x00007ff85a720000 	C:\Windows\System32\fwpuclnt.dll
0x00007ff820100000 - 0x00007ff820109000 	C:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\jre\21.0.7-win32-x86_64\bin\server;C:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win\org.eclipse.equinox.launcher\org.eclipse.equinox.launcher.win32.win32.x86_64_1.2.1300.v20250331-1702;C:\Users\<USER>\AppData\Local\Temp\jna-146731693;C:\Users\<USER>\.gradle\native\e1d6ef7f7dcc3fd88c89a11ec53ec762bb8ba0a96d01ffa2cd45eb1d1d8dd5c5\windows-amd64;C:\Program Files\Bonjour

VM Arguments:
jvm_args: --add-modules=ALL-SYSTEM --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/sun.nio.fs=ALL-UNNAMED -Declipse.application=org.eclipse.jdt.ls.core.id1 -Dosgi.bundles.defaultStartLevel=4 -Declipse.product=org.eclipse.jdt.ls.core.product -Djava.import.generatesMetadataFilesAtProjectRoot=false -DDetectVMInstallationsJob.disabled=true -Dfile.encoding=utf8 -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx2G -Xms100m -Xlog:disable -javaagent:c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\lombok\lombok-1.18.39-4050.jar -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java -Daether.dependencyCollector.impl=bf 
java_command: c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar -configuration c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\redhat.java\1.43.1\config_win -data c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java\jdt_ws --pipe=\\.\pipe\lsp-76fe49fd586dd2107a5683b086214548-sock
java_class_path (initial): c:\Users\<USER>\.vscode\extensions\redhat.java-1.43.1-win32-x64\server\plugins\org.eclipse.equinox.launcher_1.7.0.v20250519-0528.jar
Launcher Type: SUN_STANDARD

[Global flags]
    uintx AdaptiveSizePolicyWeight                 = 90                                        {product} {command line}
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
    uintx GCTimeRatio                              = 4                                         {product} {command line}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
    ccstr HeapDumpPath                             = c:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\dd097198ecdd2f7c85ba2be09709448d\redhat.java         {manageable} {command line}
   size_t InitialHeapSize                          = 104857600                                 {product} {command line}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 715653120                                 {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 524288                                    {product} {ergonomic}
   size_t MinHeapSize                              = 104857600                                 {product} {command line}
   size_t NewSize                                  = 34603008                                  {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
   size_t OldSize                                  = 70254592                                  {product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}
     bool UseParallelGC                            = true                                      {product} {command line}

Logging:
Log output configuration:
 #0: stdout all=off uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=C:\Program Files\Java\jdk1.8.0_261
PATH=C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;E:\git\Git\cmd;C:\Program Files\Java\jdk1.8.0_261\lib\dt.jar;C:\Program Files\Java\jdk1.8.0_261\lib\tools.jar;C:\Program Files\Java\jdk1.8.0_261\bin;C:\Program Files\Java\jdk1.8.0_261\jre\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\23.1.7779620;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;C:\Users\<USER>\AppData\Local\Programs\Python\Python38;E:\python2.7;E:\python2.7\Scripts;C:\Users\<USER>\AppData\Local\Programs\Python\Python38\Scripts;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\30.0.3;C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Scripts;C:\Program Files (x86)\EasyShare\x86\;C:\Program Files (x86)\EasyShare\x64\;C:\Program Files\dotnet\;F:\GSDK_HUB\GSDK-Hub;f:\Cursor\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Android\Sdk\emulator;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Programs\Python\Python311;E:\VS\Microsoft VS Code\bin;F:\flutter\flutter\bin;F:\flutter\flutter\bin\cache\dart-sdk;E:\pycharm\PyCharm 2022.3.2\bin;;E:\pycharm\PyCharm Community Edition 2022.3.2\bin;;F:\maven\apache-maven-3.9.5\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\.dotnet\tools;F:\Cursor\cursor\resources\app\bin
USERNAME=Administrator
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 158 Stepping 13, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 5 days 16:32 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 1 threads per core) family 6 model 158 stepping 13 microcode 0xb8, cx8, cmov, fxsr, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3000, Current Mhz: 3000, Mhz Limit: 3000

Memory: 4k page, system-wide physical 32701M (1434M free)
TotalPageFile size 61318M (AvailPageFile size 7M)
current process WorkingSet (physical memory assigned to process): 317M, peak: 317M
current process commit charge ("private bytes"): 386M, peak: 387M

vm_info: OpenJDK 64-Bit Server VM (21.0.7+6-LTS) for windows-amd64 JRE (21.0.7+6-LTS), built on 2025-04-15T00:00:00Z by "admin" with MS VC++ 17.7 (VS2022)

END.
