#include "AdvancedAntiCheat.h"
#include <sys/mman.h>
#include <signal.h>
#include <unistd.h>
#include <cstring>
#include <algorithm>
#include <cmath>
#include <sys/syscall.h>
#include <fstream>
#include <sstream>
#include <malloc.h>
#include <link.h>

// 日志宏定义
#define LOGI(...) printf("[ACESDK] " __VA_ARGS__ "\n")
#define LOGW(...) printf("[ACESDK-WARN] " __VA_ARGS__ "\n")
#define LOGE(...) printf("[ACESDK-ERROR] " __VA_ARGS__ "\n")

namespace {
    // 全局变量，确保分配在.bss段
    __attribute__((used, section(".bss"))) static uint8_t global_cb_reserve[1024 * 1024]; // 1MB预留
}

// CA区域分配器实现
void* CaAllocator::allocate(MemoryRegion region, size_t size) {
    if (region != MemoryRegion::CA_CPP_ALLOC) {
        return nullptr;
    }
    
    // 计算总大小（包含头部）
    size_t totalSize = size + sizeof(CaHeader);
    
    // 分配内存（使用底层分配器确保特定区域）
    void* ptr = allocateInRange(totalSize);
    if (!ptr) {
        LOGE("CA分配失败，大小: %zu", size);
        return nullptr;
    }
    
    // 填充头部信息
    CaHeader* header = static_cast<CaHeader*>(ptr);
    header->magic = 0xCAFECAFE;
    header->allocSize = size;
    header->ownerPid = getpid();
    
    // 计算校验和
    uint8_t* dataPtr = reinterpret_cast<uint8_t*>(ptr) + sizeof(CaHeader);
    header->checksum = 0;
    for (size_t i = 0; i < size; i++) {
        header->checksum ^= dataPtr[i];
    }
    
    // 内存屏障防止编译器优化
    __asm__ __volatile__("" ::: "memory");
    
    LOGI("CA区域分配成功: 地址=%p, 大小=%zu, 总大小=%zu", 
         dataPtr, size, totalSize);
    return dataPtr;
}

void CaAllocator::deallocate(void* ptr) {
    if (!ptr) return;
    
    // 找到头部
    CaHeader* header = reinterpret_cast<CaHeader*>(
        static_cast<uint8_t*>(ptr) - sizeof(CaHeader)
    );
    
    // 验证魔术字
    if (header->magic != 0xCAFECAFE) {
        LOGE("CA区域释放失败: 无效的魔术字");
        return;
    }
    
    free(header);
}

bool CaAllocator::verifyRegion(void* ptr, MemoryRegion expectedRegion) {
    if (expectedRegion != MemoryRegion::CA_CPP_ALLOC || !ptr) {
        return false;
    }
    
    // 验证头部信息
    CaHeader* header = reinterpret_cast<CaHeader*>(
        static_cast<uint8_t*>(ptr) - sizeof(CaHeader)
    );
    
    return header->magic == 0xCAFECAFE;
}

void* CaAllocator::allocateInRange(size_t totalSize) {
    // 尝试在CA典型地址范围内分配（0x70000000 - 0x80000000）
    // 这是安卓系统中典型的堆分配区域
    for (uintptr_t addr = 0x70000000; addr < 0x80000000; addr += 0x1000) {
        void* ptr = mmap(reinterpret_cast<void*>(addr), totalSize,
                        PROT_READ | PROT_WRITE,
                        MAP_PRIVATE | MAP_ANONYMOUS | MAP_FIXED,
                        -1, 0);
        
        if (ptr != MAP_FAILED) {
            return ptr;
        }
        
        // 如果当前地址不可用，尝试下一个页面
        if (errno != EINVAL && errno != EEXIST) {
            break;
        }
    }
    
    // 如果特定范围分配失败，使用标准分配
    return malloc(totalSize);
}

// CB区域分配器实现
CbAllocator::CbAllocator() {
    // 初始化CB区域内存块（确保在.bss段）
    cbMemoryBlock_ = global_cb_reserve;
    LOGI("CB区域初始化: 地址=%p, 大小=%zuKB", 
         cbMemoryBlock_, CB_REGION_SIZE / 1024);
}

void* CbAllocator::allocate(MemoryRegion region, size_t size) {
    if (region != MemoryRegion::CB_CPP_BSS) {
        return nullptr;
    }
    
    std::lock_guard<std::mutex> lock(allocMutex_);
    
    // 检查空间是否足够
    if (currentOffset_ + size > CB_REGION_SIZE) {
        LOGE("CB区域内存不足: 需要%zu, 剩余%zu", 
             size, CB_REGION_SIZE - currentOffset_);
        return nullptr;
    }
    
    // 分配内存
    void* ptr = &cbMemoryBlock_[currentOffset_];
    currentOffset_ += size;
    
    // 填充非零值防止被优化
    memset(ptr, 0xCB, size);
    
    // 内存屏障确保写入生效
    __asm__ __volatile__("" ::: "memory");
    
    // 验证是否真的在.bss段
    if (!isInBssSection(ptr)) {
        LOGE("CB分配警告: 内存不在.bss段");
    } else {
        LOGI("CB区域分配成功: 地址=%p, 大小=%zu, 偏移=%zu",
             ptr, size, currentOffset_);
    }
    
    return ptr;
}

void CbAllocator::deallocate(void* ptr) {
    // CB区域使用静态内存，不实际释放
}

bool CbAllocator::verifyRegion(void* ptr, MemoryRegion expectedRegion) {
    if (expectedRegion != MemoryRegion::CB_CPP_BSS || !ptr) {
        return false;
    }
    
    // 检查是否在我们的CB内存块范围内
    uint8_t* bytePtr = static_cast<uint8_t*>(ptr);
    return (bytePtr >= cbMemoryBlock_ && 
            bytePtr < cbMemoryBlock_ + CB_REGION_SIZE &&
            isInBssSection(ptr));
}

bool CbAllocator::isInBssSection(void* ptr) {
    // 解析/proc/self/maps查找.bss段范围
    std::ifstream maps("/proc/self/maps");
    std::string line;
    uintptr_t addr = reinterpret_cast<uintptr_t>(ptr);
    
    while (std::getline(maps, line)) {
        uintptr_t start, end;
        char perms[5];
        std::string path;
        
        if (sscanf(line.c_str(), "%lx-%lx %4s %*x %*x:%*x %*d %*s", 
                  &start, &end, perms) >= 3) {
            // 检查地址是否在这个范围内
            if (addr >= start && addr < end) {
                // 检查是否是.bss段
                if (line.find(".bss") != std::string::npos) {
                    return true;
                }
            }
        }
    }
    
    return false;
}

// BehaviorAnalyzer 实现
BehaviorAnalyzer::BehaviorAnalyzer() : gen_(rd_()) {
    // 加载作弊特征库
    cheatSignatures_ = {
        {"GG修改器", {0x2D, 0xE9, 0x00, 0x48}, {"com.guardian", "gg"}, 90, 0x12345678},
        {"内存扫描器", {0x0F, 0xB4, 0x2D, 0xE9}, {}, 85, 0x87654321},
        {"速度修改", {0x30, 0xB5, 0x92, 0xB0}, {}, 80, 0xABCDEF00}
    };
    
    analysisActive_ = true;
    analysisThread_ = std::thread(&BehaviorAnalyzer::performRealTimeAnalysis, this);
}

BehaviorAnalyzer::~BehaviorAnalyzer() {
    analysisActive_ = false;
    if (analysisThread_.joinable()) {
        analysisThread_.join();
    }
}

void BehaviorAnalyzer::recordAccess(const AccessRecord& record) {
    std::lock_guard<std::mutex> lock(accessMutex_);
    
    recentAccesses_.push_back(record);
    
    // 保持最近1000条记录
    if (recentAccesses_.size() > 1000) {
        recentAccesses_.erase(recentAccesses_.begin());
    }
}

void BehaviorAnalyzer::performRealTimeAnalysis() {
    while (analysisActive_) {
        // 每500ms分析一次
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
        
        std::lock_guard<std::mutex> lock(accessMutex_);
        if (recentAccesses_.empty()) continue;
        
        // 分析访问模式
        calculateAccessEntropy();
        
        // 检测线性扫描
        bool linearScan = detectLinearScan();
        // 检测指针追踪
        bool pointerChase = detectPointerChasing();
        // 检测频率异常
        bool freqAnomaly = detectFrequencyAnomaly();
        // 检测作弊工具特征
        bool cheatSig = detectCheatToolSignatures();
        
        // 确定威胁级别
        ThreatLevel level = ThreatLevel::NONE;
        if (cheatSig) level = ThreatLevel::CRITICAL;
        else if (pointerChase) level = ThreatLevel::HIGH;
        else if (linearScan) level = ThreatLevel::MEDIUM;
        else if (freqAnomaly) level = ThreatLevel::LOW;
        
        currentThreat_ = level;
    }
}

void BehaviorAnalyzer::calculateAccessEntropy() {
    std::map<uintptr_t, int> addressCounts;
    for (const auto& access : recentAccesses_) {
        uintptr_t addr = reinterpret_cast<uintptr_t>(access.address) & ~0xFFF; // 按页对齐
        addressCounts[addr]++;
    }
    
    double entropy = 0.0;
    int total = recentAccesses_.size();
    
    for (const auto& pair : addressCounts) {
        double prob = static_cast<double>(pair.second) / total;
        entropy -= prob * std::log2(prob);
    }
    
    currentModel_.accessEntropy = entropy;
}

bool BehaviorAnalyzer::detectLinearScan() const {
    if (recentAccesses_.size() < 20) return false;
    
    // 提取最近20个地址
    std::vector<uintptr_t> addresses;
    for (size_t i = recentAccesses_.size() - 20; i < recentAccesses_.size(); i++) {
        addresses.push_back(reinterpret_cast<uintptr_t>(recentAccesses_[i].address));
    }
    
    // 检查地址是否线性增长
    std::sort(addresses.begin(), addresses.end());
    int stepSum = 0;
    
    for (size_t i = 1; i < addresses.size(); i++) {
        stepSum += addresses[i] - addresses[i-1];
    }
    
    double avgStep = static_cast<double>(stepSum) / (addresses.size() - 1);
    
    // 平均步长较小且稳定，可能是线性扫描
    return avgStep > 0 && avgStep < 1024;
}

bool BehaviorAnalyzer::detectPointerChasing() const {
    if (recentAccesses_.size() < 5) return false;
    
    // 检测短时间内的多级指针访问
    for (size_t i = 0; i < recentAccesses_.size() - 4; i++) {
        auto t1 = recentAccesses_[i].timestamp;
        auto t2 = recentAccesses_[i+1].timestamp;
        auto t3 = recentAccesses_[i+2].timestamp;
        
        // 检查时间间隔
        auto dt1 = std::chrono::duration_cast<std::chrono::microseconds>(t2 - t1).count();
        auto dt2 = std::chrono::duration_cast<std::chrono::microseconds>(t3 - t2).count();
        
        if (dt1 < 1000 && dt2 < 1000) { // 1ms内连续访问
            uintptr_t a1 = reinterpret_cast<uintptr_t>(recentAccesses_[i].address);
            uintptr_t a2 = reinterpret_cast<uintptr_t>(recentAccesses_[i+1].address);
            uintptr_t a3 = reinterpret_cast<uintptr_t>(recentAccesses_[i+2].address);
            
            // 地址差值较小，可能是指针链
            if (std::abs(static_cast<long>(a2 - a1)) < 0x1000 &&
                std::abs(static_cast<long>(a3 - a2)) < 0x1000) {
                return true;
            }
        }
    }
    
    return false;
}

bool BehaviorAnalyzer::detectFrequencyAnomaly() const {
    if (recentAccesses_.empty()) return false;
    
    // 计算每秒访问次数
    auto oldest = recentAccesses_.front().timestamp;
    auto newest = recentAccesses_.back().timestamp;
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(newest - oldest).count();
    
    if (duration == 0) duration = 1; // 避免除零
    double freq = static_cast<double>(recentAccesses_.size()) / duration;
    
    // 频率过高可能是扫描行为
    return freq > 200;
}

bool BehaviorAnalyzer::detectCheatToolSignatures() const {
    for (const auto& access : recentAccesses_) {
        if (matchesCheatSignature(access)) {
            return true;
        }
    }
    return false;
}

bool BehaviorAnalyzer::matchesCheatSignature(const AccessRecord& record) const {
    for (const auto& sig : cheatSignatures_) {
        if (record.accessPattern == sig.accessPattern) {
            return true;
        }
    }
    return false;
}

ThreatLevel BehaviorAnalyzer::getCurrentThreatLevel() const {
    return currentThreat_;
}

BehaviorAnalyzer::AnalysisResult BehaviorAnalyzer::getAnalysisResult() const {
    std::lock_guard<std::mutex> lock(accessMutex_);
    
    AnalysisResult result;
    result.threatLevel = currentThreat_;
    result.accessEntropy = currentModel_.accessEntropy;
    
    return result;
}

// MemoryTrapManager 实现
MemoryTrapManager* MemoryTrapManager::instance_ = nullptr;

MemoryTrapManager::MemoryTrapManager() : gen_(rd_()), dist_(0, 100) {
    // 初始化专用分配器
    allocator_ = std::make_unique<CaAllocator>();
}

MemoryTrapManager::~MemoryTrapManager() {
    shutdown();
}

MemoryTrapManager& MemoryTrapManager::getInstance() {
    static MemoryTrapManager instance;
    return instance;
}

bool MemoryTrapManager::initialize() {
    if (initialized_.load()) return true;
    
    LOGI("初始化反作弊系统...");
    
    // 初始化CA和CB区域（关键：这里会导致内存增长）
    setupCaRegion();
    setupCbRegion();
    
    // 创建行为分析器
    behaviorAnalyzer_ = std::make_unique<BehaviorAnalyzer>();
    
    // 安装信号处理器
    installSignalHandler();
    
    // 部署初始陷阱
    deployCaTraps(50);  // 部署50个CA区域陷阱
    deployCbTraps(30);  // 部署30个CB区域陷阱
    
    // 启动更新线程
    updateActive_ = true;
    updateThread_ = std::thread(&MemoryTrapManager::updateThreadFunc, this);
    
    // 启动主动检测
    startActiveDetection();
    
    initialized_ = true;
    LOGI("反作弊系统初始化完成 - CA和CB区域已分配");
    return true;
}

void MemoryTrapManager::shutdown() {
    if (!initialized_.load()) return;
    
    updateActive_ = false;
    if (updateThread_.joinable()) {
        updateThread_.join();
    }
    
    stopActiveDetection();
    uninstallSignalHandler();
    
    {
        std::lock_guard<std::mutex> lock(trapsMutex_);
        traps_.clear();
    }
    
    behaviorAnalyzer_.reset();
    initialized_ = false;
}

void MemoryTrapManager::setupCaRegion() {
    LOGI("设置CA区域...");
    
    // 预分配CA区域内存（1MB）
    void* caBlock = allocator_->allocate(MemoryRegion::CA_CPP_ALLOC, 1024 * 1024);
    if (caBlock && allocator_->verifyRegion(caBlock, MemoryRegion::CA_CPP_ALLOC)) {
        LOGI("CA区域预分配成功: 地址=%p, 大小=1MB", caBlock);
    } else {
        LOGE("CA区域预分配验证失败");
    }
}

void MemoryTrapManager::setupCbRegion() {
    LOGI("设置CB区域...");
    
    // 使用专用CB分配器
    static CbAllocator cbAllocator;
    
    // 预分配CB区域内存（512KB）
    void* cbBlock = cbAllocator.allocate(MemoryRegion::CB_CPP_BSS, 512 * 1024);
    if (cbBlock && cbAllocator.verifyRegion(cbBlock, MemoryRegion::CB_CPP_BSS)) {
        LOGI("CB区域预分配成功: 地址=%p, 大小=512KB", cbBlock);
    } else {
        LOGE("CB区域预分配验证失败");
    }
}

void MemoryTrapManager::installSignalHandler() {
    struct sigaction sa;
    sa.sa_sigaction = signalHandler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = SA_SIGINFO | SA_RESTART;
    
    if (sigaction(SIGSEGV, &sa, &oldSigsegvAction_) != 0) {
        LOGE("安装SIGSEGV处理器失败");
    }
    
    if (sigaction(SIGBUS, &sa, &oldSigbusAction_) != 0) {
        LOGE("安装SIGBUS处理器失败");
    }
}

void MemoryTrapManager::uninstallSignalHandler() {
    sigaction(SIGSEGV, &oldSigsegvAction_, nullptr);
    sigaction(SIGBUS, &oldSigbusAction_, nullptr);
}

void MemoryTrapManager::signalHandler(int sig, siginfo_t* info, void* context) {
    if (instance_ && info && info->si_addr) {
        instance_->handleMemoryAccess(info->si_addr, sig == SIGSEGV);
    }
    
    // 调用原始处理器
    if (sig == SIGSEGV && oldSigsegvAction_.sa_sigaction) {
        oldSigsegvAction_.sa_sigaction(sig, info, context);
    } else if (sig == SIGBUS && oldSigbusAction_.sa_sigaction) {
        oldSigbusAction_.sa_sigaction(sig, info, context);
    }
}

void MemoryTrapManager::handleMemoryAccess(void* address, bool isWrite) {
    std::lock_guard<std::mutex> lock(trapsMutex_);
    
    // 查找触发的陷阱
    for (auto& trap : traps_) {
        if (!trap.isActive) continue;
        
        uintptr_t trapAddr = reinterpret_cast<uintptr_t>(trap.address);
        uintptr_t accessAddr = reinterpret_cast<uintptr_t>(address);
        
        if (accessAddr >= trapAddr && accessAddr < trapAddr + trap.size) {
            // 记录访问
            AccessRecord record;
            record.address = address;
            record.size = trap.size;
            record.timestamp = std::chrono::steady_clock::now();
            record.pid = getpid();
            record.tid = syscall(SYS_gettid);
            record.region = trap.region;
            record.isWrite = isWrite;
            
            // 通知行为分析器
            if (behaviorAnalyzer_) {
                behaviorAnalyzer_->recordAccess(record);
            }
            
            // 更新统计
            stats_.totalTriggers++;
            stats_.triggersByType[trap.type]++;
            stats_.triggersByRegion[trap.region]++;
            
            // 触发回调
            if (trap.triggerCallback) {
                trap.triggerCallback(address, record);
            }
            
            LOGI("陷阱触发: 区域=%s, 地址=%p, 类型=%s",
                 getRegionString(trap.region), address,
                 getTrapTypeString(trap.type));
            
            break;
        }
    }
}

void MemoryTrapManager::deployCaTraps(int count) {
    LOGI("部署%d个CA区域陷阱...", count);
    
    // 使用CA专用分配器
    CaAllocator allocator;
    
    for (int i = 0; i < count; i++) {
        // 交替创建不同类型的陷阱
        if (i % 3 == 0) {
            // 创建诱饵值陷阱（常见游戏数值）
            int value = generateMeaningfulDecoy();
            void* addr = allocator.allocate(MemoryRegion::CA_CPP_ALLOC, sizeof(int));
            if (!addr) continue;
            
            *static_cast<int*>(addr) = value;
            
            TrapConfig trap;
            trap.address = addr;
            trap.size = sizeof(int);
            trap.type = TrapType::VALUE_DECOY;
            trap.region = MemoryRegion::CA_CPP_ALLOC;
            trap.decoyValue = value;
            trap.triggerCallback = [this](void* addr, const AccessRecord& record) {
                ThreatEvent event;
                event.severity = ThreatLevel::MEDIUM;
                event.description = "CA区域诱饵陷阱触发";
                event.triggerAddress = addr;
                event.region = MemoryRegion::CA_CPP_ALLOC;
                event.trapType = TrapType::VALUE_DECOY;
                
                if (threatCallback_) {
                    threatCallback_(event);
                }
            };
            
            addTrap(std::move(trap));
        } else if (i % 3 == 1) {
            // 创建CRC校验陷阱
            struct FakeData {
                int health;
                int score;
                int coins;
            };
            
            FakeData data = {100 + i, 1000 + i*10, 500 + i*5};
            void* addr = allocator.allocate(MemoryRegion::CA_CPP_ALLOC, sizeof(FakeData));
            if (!addr) continue;
            
            *static_cast<FakeData*>(addr) = data;
            
            TrapConfig trap = createCrcTrap(addr, sizeof(FakeData));
            trap.region = MemoryRegion::CA_CPP_ALLOC;
            addTrap(std::move(trap));
        } else {
            // 创建指针链陷阱
            TrapConfig trap = createPointerChainTrap(MemoryRegion::CA_CPP_ALLOC, 3);
            if (allocator.verifyRegion(trap.address, MemoryRegion::CA_CPP_ALLOC)) {
                addTrap(std::move(trap));
            } else {
                LOGE("CA指针链陷阱验证失败");
            }
        }
    }
}

void MemoryTrapManager::deployCbTraps(int count) {
    LOGI("部署%d个CB区域陷阱...", count);
    
    // 使用CB专用分配器
    CbAllocator allocator;
    
    for (int i = 0; i < count; i++) {
        // 创建不同类型的陷阱
        if (i % 2 == 0) {
            // 创建诱饵值陷阱
            int value = generateMeaningfulDecoy();
            void* addr = allocator.allocate(MemoryRegion::CB_CPP_BSS, sizeof(int));
            if (!addr) continue;
            
            *static_cast<int*>(addr) = value;
            
            TrapConfig trap;
            trap.address = addr;
            trap.size = sizeof(int);
            trap.type = TrapType::VALUE_DECOY;
            trap.region = MemoryRegion::CB_CPP_BSS;
            trap.decoyValue = value;
            trap.triggerCallback = [this](void* addr, const AccessRecord& record) {
                ThreatEvent event;
                event.severity = ThreatLevel::HIGH;
                event.description = "CB区域诱饵陷阱触发";
                event.triggerAddress = addr;
                event.region = MemoryRegion::CB_CPP_BSS;
                event.trapType = TrapType::VALUE_DECOY;
                
                if (threatCallback_) {
                    threatCallback_(event);
                }
            };
            
            addTrap(std::move(trap));
        } else {
            // 创建定时访问陷阱
            int value = generateMeaningfulDecoy();
            void* addr = allocator.allocate(MemoryRegion::CB_CPP_BSS, sizeof(int));
            if (!addr) continue;
            
            *static_cast<int*>(addr) = value;
            
            TrapConfig trap;
            trap.address = addr;
            trap.size = sizeof(int);
            trap.type = TrapType::TIMED_ACCESS;
            trap.region = MemoryRegion::CB_CPP_BSS;
            trap.decoyValue = value;
            addTrap(std::move(trap));
        }
    }
}

TrapConfig MemoryTrapManager::createValueDecoy(MemoryRegion region, int value) {
    TrapConfig trap;
    trap.type = TrapType::VALUE_DECOY;
    trap.region = region;
    trap.decoyValue = value;
    trap.size = sizeof(int);
    
    // 根据区域选择分配器
    if (region == MemoryRegion::CA_CPP_ALLOC) {
        CaAllocator allocator;
        trap.address = allocator.allocate(region, sizeof(int));
    } else if (region == MemoryRegion::CB_CPP_BSS) {
        CbAllocator allocator;
        trap.address = allocator.allocate(region, sizeof(int));
    }
    
    if (trap.address) {
        *static_cast<int*>(trap.address) = value;
    }
    
    return trap;
}

TrapConfig MemoryTrapManager::createPointerChainTrap(MemoryRegion region, int depth) {
    TrapConfig trap;
    trap.type = TrapType::POINTER_CHAIN;
    trap.region = region;
    trap.chainDepth = depth;
    
    // 分配指针链内存
    size_t chainSize = sizeof(void*) * depth + sizeof(int);
    if (region == MemoryRegion::CA_CPP_ALLOC) {
        CaAllocator allocator;
        trap.address = allocator.allocate(region, chainSize);
    } else {
        CbAllocator allocator;
        trap.address = allocator.allocate(region, chainSize);
    }
    
    if (trap.address) {
        trap.pointerChain = static_cast<void**>(trap.address);
        int* valuePtr = static_cast<int*>(static_cast<void*>(
            static_cast<char*>(trap.address) + sizeof(void*) * depth
        ));
        
        // 设置随机值
        *valuePtr = generateMeaningfulDecoy();
        
        // 构建指针链
        for (int i = 0; i < depth - 1; i++) {
            trap.pointerChain[i] = &trap.pointerChain[i + 1];
        }
        trap.pointerChain[depth - 1] = valuePtr;
    }
    
    return trap;
}

TrapConfig MemoryTrapManager::createCrcTrap(void* data, size_t size) {
    TrapConfig trap;
    trap.type = TrapType::CRC32_TRAP;
    trap.address = data;
    trap.size = size;
    
    // 计算CRC32
    uint32_t crc = 0xFFFFFFFF;
    uint8_t* bytes = static_cast<uint8_t*>(data);
    
    for (size_t i = 0; i < size; i++) {
        crc ^= bytes[i];
        for (int j = 0; j < 8; j++) {
            crc = (crc >> 1) ^ (0xEDB88320 * (crc & 1));
        }
    }
    
    trap.crc32 = ~crc;
    return trap;
}

bool MemoryTrapManager::addTrap(TrapConfig&& config) {
    if (!config.address) return false;
    
    std::lock_guard<std::mutex> lock(trapsMutex_);
    traps_.emplace_back(std::move(config));
    
    stats_.totalTraps++;
    if (traps_.back().isActive) {
        stats_.activeTraps++;
    }
    
    return true;
}

void MemoryTrapManager::updateThreadFunc() {
    while (updateActive_.load()) {
        updateTraps();
        std::this_thread::sleep_for(std::chrono::seconds(5));
    }
}

void MemoryTrapManager::updateTraps() {
    std::lock_guard<std::mutex> lock(trapsMutex_);
    
    auto now = std::chrono::steady_clock::now();
    
    for (auto& trap : traps_) {
        // 更新定时陷阱
        if (trap.type == TrapType::TIMED_ACCESS) {
            auto elapsed = now - trap.createTime;
            if (elapsed > std::chrono::seconds(10)) {
                // 每10秒更新一次值
                trap.decoyValue = generateMeaningfulDecoy();
                if (trap.address) {
                    *static_cast<int*>(trap.address) = trap.decoyValue;
                }
                trap.createTime = now;
            }
        }
        
        // 验证CRC陷阱
        if (trap.type == TrapType::CRC32_TRAP) {
            uint32_t currentCrc = 0xFFFFFFFF;
            uint8_t* bytes = static_cast<uint8_t*>(trap.address);
            
            for (size_t i = 0; i < trap.size; i++) {
                currentCrc ^= bytes[i];
                for (int j = 0; j < 8; j++) {
                    currentCrc = (currentCrc >> 1) ^ (0xEDB88320 * (currentCrc & 1));
                }
            }
            
            if (~currentCrc != trap.crc32) {
                // CRC不匹配，触发威胁事件
                ThreatEvent event;
                event.severity = ThreatLevel::CRITICAL;
                event.description = "CRC校验失败，数据被篡改";
                event.triggerAddress = trap.address;
                event.region = trap.region;
                event.trapType = trap.type;
                
                if (threatCallback_) {
                    threatCallback_(event);
                }
            }
        }
    }
}

void MemoryTrapManager::diversifyTraps() {
    // 动态调整陷阱增加检测难度
    std::lock_guard<std::mutex> lock(trapsMutex_);
    
    for (auto& trap : traps_) {
        if (dist_(gen_) < 30) { // 30%概率修改
            if (trap.type == TrapType::VALUE_DECOY) {
                trap.decoyValue = generateMeaningfulDecoy();
                if (trap.address) {
                    *static_cast<int*>(trap.address) = trap.decoyValue;
                }
            }
        }
    }
}

int MemoryTrapManager::generateMeaningfulDecoy() const {
    // 生成游戏中常见的数值
    static const std::vector<int> values = {
        100, 200, 500, 1000, 1500, 2000, 999, 9999,
        1234, 5678, 10000, 50000, 99999, 100000
    };
    
    std::uniform_int_distribution<> dist(0, values.size() - 1);
    return values[dist(gen_)];
}

void MemoryTrapManager::setThreatCallback(std::function<void(const ThreatEvent&)> callback) {
    threatCallback_ = callback;
}

MemoryTrapManager::Statistics MemoryTrapManager::getStatistics() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    return stats_;
}

const char* MemoryTrapManager::getTrapTypeString(TrapType type) {
    switch (type) {
        case TrapType::VALUE_DECOY: return "诱饵值";
        case TrapType::POINTER_CHAIN: return "指针链";
        case TrapType::GUARD_PAGE: return "保护页";
        case TrapType::CRC32_TRAP: return "CRC校验";
        case TrapType::TIMED_ACCESS: return "定时访问";
        default: return "未知";
    }
}

const char* MemoryTrapManager::getRegionString(MemoryRegion region) {
    switch (region) {
        case MemoryRegion::CA_CPP_ALLOC: return "CA";
        case MemoryRegion::CB_CPP_BSS: return "CB";
        case MemoryRegion::CD_DATA_SEGMENT: return "CD";
        case MemoryRegion::CH_CPP_HEAP: return "CH";
        case MemoryRegion::JH_JAVA_HEAP: return "JH";
        case MemoryRegion::A_ANONYMOUS: return "A";
        default: return "未知";
    }
}

// 主动检测实现
void MemoryTrapManager::startActiveDetection() {
    activeDetectionRunning_ = true;
    detectionThread_ = std::thread(&MemoryTrapManager::checkMemoryScanning, this);
}

void MemoryTrapManager::stopActiveDetection() {
    activeDetectionRunning_ = false;
    if (detectionThread_.joinable()) {
        detectionThread_.join();
    }
}

void MemoryTrapManager::checkMemoryScanning() {
    while (activeDetectionRunning_.load()) {
        // 检查内存映射变化
        std::ifstream maps("/proc/self/maps");
        if (maps.is_open()) {
            std::string line;
            while (std::getline(maps, line)) {
                if (isSuspiciousMapping(line)) {
                    LOGW("检测到可疑内存映射: %s", line.c_str());
                    
                    ThreatEvent event;
                    event.severity = ThreatLevel::CRITICAL;
                    event.description = "可疑内存映射检测";
                    event.primaryReason = line;
                    event.timestamp = std::chrono::steady_clock::now();
                    
                    if (threatCallback_) {
                        threatCallback_(event);
                    }
                }
            }
        }
        
        std::this_thread::sleep_for(std::chrono::seconds(2));
    }
}

bool MemoryTrapManager::isSuspiciousMapping(const std::string& line) {
    // 检测已知作弊工具特征
    const std::vector<std::string> cheatSignatures = {
        "gg", "gameguardian", "cheat", "hack", "mod",
        "xposed", "frida", "substrate", "data/local/tmp"
    };
    
    for (const auto& sig : cheatSignatures) {
        if (line.find(sigName) != std::string::npos) {
            return true;
        }
    }
    
    return false;
}

void MemoryTrapManager::adjustTrapsBasedOnThreat(ThreatLevel level) {
    int additionalTraps = 0;
    
    switch (level) {
        case ThreatLevel::LOW: additionalTraps = 10; break;
        case ThreatLevel::MEDIUM: additionalTraps = 20; break;
        case ThreatLevel::HIGH: additionalTraps = 30; break;
        case ThreatLevel::CRITICAL: additionalTraps = 50; break;
        default: return;
    }
    
    // 增加CA和CB区域的陷阱
    deployCaTraps(additionalTraps / 2);
    deployCbTraps(additionalTraps / 2);
}

void MemoryTrapManager::activateAllTraps() {
    std::lock_guard<std::mutex> lock(trapsMutex_);
    for (auto& trap : traps_) {
        trap.isActive = true;
    }
    stats_.activeTraps = traps_.size();
}

void MemoryTrapManager::deactivateAllTraps() {
    std::lock_guard<std::mutex> lock(trapsMutex_);
    for (auto& trap : traps_) {
        trap.isActive = false;
    }
    stats_.activeTraps = 0;
}

// DefenseSystem 实现
DefenseSystem& DefenseSystem::getInstance() {
    static DefenseSystem instance;
    return instance;
}

void DefenseSystem::initialize() {
    LOGI("初始化防御系统...");
    
    // 设置威胁回调
    auto& trapManager = MemoryTrapManager::getInstance();
    trapManager.setThreatCallback([this](const ThreatEvent& event) {
        handleThreatEvent(event);
    });
}

ThreatLevel DefenseSystem::getCurrentThreatLevel() const {
    return currentThreatLevel_;
}

void DefenseSystem::handleThreatEvent(const ThreatEvent& event) {
    LOGI("威胁事件: %s (级别: %d)", event.description.c_str(), (int)event.severity);
    
    logEvent(event);
    
    // 根据威胁级别处理
    switch (event.severity) {
        case ThreatLevel::LOW:
            handleLowThreat(event);
            break;
        case ThreatLevel::MEDIUM:
            handleMediumThreat(event);
            break;
        case ThreatLevel::HIGH:
            handleHighThreat(event);
            break;
        case ThreatLevel::CRITICAL:
            handleCriticalThreat(event);
            break;
        default:
            break;
    }
}

void DefenseSystem::handleLowThreat(const ThreatEvent& event) {
    // 增加该区域的陷阱密度
    increaseLocalTrapDensity(event.region, 20);
}

void DefenseSystem::handleMediumThreat(const ThreatEvent& event) {
    handleLowThreat(event);
    
    // 多样化现有陷阱
    auto& trapManager = MemoryTrapManager::getInstance();
    trapManager.diversifyTraps();
}

void DefenseSystem::handleHighThreat(const ThreatEvent& event) {
    handleMediumThreat(event);
    
    // 激活更多陷阱
    auto& trapManager = MemoryTrapManager::getInstance();
    trapManager.adjustTrapsBasedOnThreat(ThreatLevel::HIGH);
}

void DefenseSystem::handleCriticalThreat(const ThreatEvent& event) {
    handleHighThreat(event);
    
    // 启动最大保护
    activateMaximumProtection();
}

void DefenseSystem::logEvent(const ThreatEvent& event) {
    std::lock_guard<std::mutex> lock(eventLogMutex_);
    eventLog_.push_back(event);
}

void DefenseSystem::increaseLocalTrapDensity(MemoryRegion region, int percentage) {
    auto& trapManager = MemoryTrapManager::getInstance();
    int additional = percentage / 10;
    
    if (region == MemoryRegion::CA_CPP_ALLOC) {
        trapManager.deployCaTraps(additional);
    } else if (region == MemoryRegion::CB_CPP_BSS) {
        trapManager.deployCbTraps(additional);
    }
}

void DefenseSystem::activateMaximumProtection() {
    auto& trapManager = MemoryTrapManager::getInstance();
    trapManager.activateAllTraps();
    trapManager.adjustTrapsBasedOnThreat(ThreatLevel::CRITICAL);
    setThreatLevel(ThreatLevel::CRITICAL);
}

void DefenseSystem::setThreatLevel(ThreatLevel level) {
    currentThreatLevel_ = level;
}


该方案实现了与腾讯 ACESDK 类似的 CA/CB 区域内存管理效果，核心特点包括：

CA 区域（C++ alloc）实现机制
使用CaAllocator专用分配器，通过mmap在特定地址范围（0x70000000-0x80000000）分配内存
所有 CA 区域内存块包含特殊头部（CaHeader），带有魔术字0xCAFECAFE和校验和
初始化时预分配 1MB 内存，确保 CA 区域在/proc/self/maps中可见
部署多种陷阱（诱饵值、CRC 校验、指针链），模拟游戏数据结构
CB 区域（.bss 段）实现机制
通过__attribute__((used, section(".bss")))强制变量分配到.bss 段
CbAllocator管理一个 1MB 的静态预留区域，确保内存不会被编译器优化
分配时填充非零值（0xCB）防止被识别为未使用内存
通过解析/proc/self/maps验证内存确实位于.bss 段
初始化后内存增长的关键设计
initialize()方法中调用setupCaRegion()和setupCbRegion()进行预分配
专用分配器确保内存被计入目标区域而非普通堆
陷阱部署逻辑在初始化阶段就创建大量内存块，直观体现为 CA/CB 区域增长
反作弊能力
信号处理（SIGSEGV/SIGBUS）捕获非法内存访问
行为分析器检测线性扫描、指针追踪等作弊模式
基于威胁级别的动态防御调整（增加陷阱密度、激活新陷阱）
主动检测可疑内存映射（如 GG 修改器特征）