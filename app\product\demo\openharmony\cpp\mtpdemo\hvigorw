#!/bin/bash

# ----------------------------------------------------------------------------
#  Hvigor startup script, version 1.0.0
#
#  Required ENV vars:
#  ------------------
#    NODE_HOME - location of a Node home dir
#    or
#    Add /usr/local/nodejs/bin to the PATH environment variable
# ----------------------------------------------------------------------------

HVIGOR_APP_HOME="`pwd -P`"
HVIGOR_WRAPPER_SCRIPT=${HVIGOR_APP_HOME}/hvigor/hvigor-wrapper.js
#NODE_OPTS="--max-old-space-size=4096"

fail() {
  echo "$*"
  exit 1
}

set_executable_node() {
  EXECUTABLE_NODE="${NODE_HOME}/bin/node"
  if [ -x "$EXECUTABLE_NODE" ]; then
    return
  fi

  EXECUTABLE_NODE="${NODE_HOME}/node"
  if [ -x "$EXECUTABLE_NODE" ]; then
    return
  fi
  fail "ERROR: NODE_HOME is set to an invalid directory,check $NODE_HOME\n\nPlease set NODE_HOME in your environment to the location where your nodejs installed"
}

# Determine node to start hvigor wrapper script
if [ -n "${NODE_HOME}" ]; then
  set_executable_node
else
  EXECUTABLE_NODE="node"
  command -v ${EXECUTABLE_NODE} &> /dev/null || fail "ERROR: NODE_HOME not set and 'node' command not found"
fi

# Check hvigor wrapper script
if [ ! -r "$HVIGOR_WRAPPER_SCRIPT" ]; then
  fail "ERROR: Couldn't find hvigor/hvigor-wrapper.js in ${HVIGOR_APP_HOME}"
fi

if [ -z "${NODE_OPTS}" ]; then
  NODE_OPTS="--"
fi

# start hvigor-wrapper script
exec "${EXECUTABLE_NODE}" "${NODE_OPTS}" \
  "${HVIGOR_WRAPPER_SCRIPT}" "$@"
