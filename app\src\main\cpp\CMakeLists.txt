# CMake版本要求
cmake_minimum_required(VERSION 3.18.1)

# 项目名称
project("memorytrap")

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项 - 添加特定标志模拟真正的C++分配器
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-omit-frame-pointer")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -funwind-tables")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fstack-protector-strong")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC")
# 修复：添加段属性支持和内存布局优化
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fdata-sections -ffunction-sections")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-common")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-strict-aliasing")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")

# 包含目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# 源文件 - 包含高级内存陷阱系统
set(SOURCES
    memory_trap_sdk.cpp
    memory_trap_sdk_jni.cpp
    mem_trap_detector.cpp
    comprehensive_detector.cpp  # 启用综合检测器
    AdvancedMemoryTrap.cpp      # 新的高级反作弊系统
    AdvancedAntiCheatJNI.cpp    # JNI接口
    cb_trap_lib.cpp             # Cb区域陷阱专用库
    DpMemoryTrap.cpp            # dp方案纯净内存陷阱系统
    DpMemoryTrapJNI.cpp         # dp方案JNI接口
    comprehensive_trap_system.cpp  # 综合陷阱系统
    comprehensive_trap_jni.cpp     # 综合陷阱系统JNI接口
    tencent_style_trap.cpp      # 腾讯风格内存陷阱系统
    gg_compatible_trap.cpp      # GG兼容内存陷阱系统
    # 暂时注释掉旧文件避免冲突
    # enhanced_detector.cpp
    # simple_detector.cpp
    # memory_trap_jni.cpp
    # memory_trap.cpp
)

# 头文件
set(HEADERS
    memory_trap_sdk.h
    memory_trap.h
    AdvancedMemoryTrap.h        # 新的高级反作弊系统头文件
    comprehensive_trap_system.h # 综合陷阱系统头文件
)

# 创建共享库
add_library(
    # 库名
    memorytrap
    
    # 库类型
    SHARED
    
    # 源文件
    ${SOURCES}
)

# 查找并链接所需的库
find_library(
    # 设置变量名
    log-lib
    
    # 指定NDK库名称
    log
)

find_library(
    # Android库
    android-lib
    android
)

# 链接库
target_link_libraries(
    memorytrap
    ${log-lib}
    ${android-lib}
)

# 设置库的属性
set_target_properties(memorytrap PROPERTIES
    VERSION 1.0
    SOVERSION 1
)

# 编译器特定设置
if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
    target_compile_options(memorytrap PRIVATE
        -fvisibility=hidden
        -ffunction-sections
        -fdata-sections
    )

    target_link_options(memorytrap PRIVATE
        -Wl,--gc-sections
        -Wl,--build-id
        -Wl,--hash-style=gnu
        -Wl,--no-undefined-version
        -Wl,-z,noexecstack
        -Wl,-z,relro
        -Wl,-z,now
        # 修复：确保段符号正确生成
        -Wl,--orphan-handling=place
        -Wl,--print-memory-usage
    )
endif()

# 调试信息
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    message(STATUS "Building in Debug mode")
    target_compile_definitions(memorytrap PRIVATE DEBUG=1)
else()
    message(STATUS "Building in Release mode")
    target_compile_definitions(memorytrap PRIVATE NDEBUG=1)
endif()

# 打印构建信息
message(STATUS "CMake version: ${CMAKE_VERSION}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "C++ flags: ${CMAKE_CXX_FLAGS}")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Android ABI: ${ANDROID_ABI}")
message(STATUS "Android API level: ${ANDROID_PLATFORM}")

# 安装规则（可选）
install(TARGETS memorytrap
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES ${HEADERS}
    DESTINATION include
)