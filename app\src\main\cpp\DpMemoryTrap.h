#ifndef DP_MEMORY_TRAP_H
#define DP_MEMORY_TRAP_H

#include <cstdint>
#include <vector>
#include <mutex>
#include <thread>
#include <atomic>
#include <android/log.h>
#include "memory_trap_sdk.h"

// V4.0: ELF段属性声明 - 模拟真实游戏的段结构
#ifdef __cplusplus
extern "C" {
#endif

// 声明特定的ELF段符号，让链接器生成正确的段信息
extern char __start_ca_alloc_section[];
extern char __stop_ca_alloc_section[];
extern char __start_cb_bss_section[];
extern char __stop_cb_bss_section[];

#ifdef __cplusplus
}
#endif

#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, "DpMemoryTrap", __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, "DpMemoryTrap", __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, "DpMemoryTrap", __VA_ARGS__)

namespace DpTrap {

// dp方案：Ca区域自定义分配器
class CAAllocator {
public:
    static constexpr uint32_t MAGIC_HEADER = 0xCAFEC1A1; // CA专属魔数

    struct TrapHeader {
        uint32_t magic = 0xCAFEC1A1;       // CA专属魔数（区别于普通堆）
        uint32_t allocatorTag = 0x41435452; // "ACTR"标识（CA Trap的缩写）
        size_t allocSize;
        uint32_t trapType;
        uint32_t checksum;
    };

    static void* Allocate(size_t size);
    static void Deallocate(void* ptr);
    static bool Verify(void* ptr);

private:
    static void* MapHighAddressMemory(size_t size);
    static void FillDecoyValues(void* addr, size_t size);
    static uint32_t CalculateChecksum(void* addr, size_t size);
    static uint32_t SelectTrapType();
};

// dp方案：Cb区域BSS段陷阱管理器
class CBTrapManager {
public:
    static CBTrapManager& GetInstance();
    
    void Initialize();
    void DeployTraps();
    void VerifyTraps();
    
    size_t GetTotalBssSize() const;
    void* GetBssAreaAddress() const;
    void* GetBssArrayAddress() const;

private:
    CBTrapManager() = default;
    void InitializeBssArea();
    void InitializeBssArray();
    void FillMemoryPattern(void* addr, size_t size);
    
    bool initialized_ = false;
    std::mutex mutex_;
};

// dp方案：主内存陷阱系统
class DpMemoryTrapSystem {
public:
    static DpMemoryTrapSystem& GetInstance();
    
    bool Initialize();
    void Shutdown();
    
    // Ca区域陷阱部署
    void DeployCaTraps(int count = 50);
    
    // Cb区域陷阱部署
    void DeployCbTraps();
    
    // 预分配策略
    void PreallocateMemory();
    
    // 验证和诊断
    void VerifyTraps();
    void DumpMemoryInfo();
    void VerifyMemoryRegions(); // 新增：验证内存区域映射
    
    // 检测控制
    void StartDetection();
    void StopDetection();
    
    // 回调设置
    void SetDetectionCallback(MemoryTrapSDK::DetectionCallback callback);
    
    // 统计信息
    size_t GetCaMemorySize() const { return caMemorySize_; }
    size_t GetCbMemorySize() const { return cbMemorySize_; }
    size_t GetDataSegmentMemorySize() const { return dataSegmentMemorySize_; }
    size_t GetCaTrapCount() const { return caTrapCount_; }
    size_t GetCbTrapCount() const { return cbTrapCount_; }

private:
    DpMemoryTrapSystem();
    ~DpMemoryTrapSystem();
    
    // 禁止拷贝
    DpMemoryTrapSystem(const DpMemoryTrapSystem&) = delete;
    DpMemoryTrapSystem& operator=(const DpMemoryTrapSystem&) = delete;

    // 强制内存使用，确保物理页分配
    void forceMemoryUsage();

    // 持续内存使用线程
    void StartMemoryUsageThread();
    void StopMemoryUsageThread();
    void MemoryUsageWorker();

    // GG修改器识别优化 - 基于腾讯策略
    void CreateGGRecognizableMemory();

    // 腾讯策略实现
    void CreateRandomTraps();           // random_trap机制
    void CreateCustomMmapRegions();     // ms_mmap自定义内存管理
    void FillRandomTrapData(void* ptr, size_t size, int trap_id);
    void FillTencentPattern(void* ptr, size_t size);

    // 新增：CB内存保持活跃机制
    void StartCbMemoryKeepAlive();

    // V4.0: ELF段属性初始化
    void InitializeElfSections();

    // 新增：陷阱保护相关方法
    bool ProtectTrapMemory(void* addr, size_t size);
    void* AllocateProtectedMemory(size_t size);

    bool initialized_ = false;
    std::mutex mutex_;
    std::atomic<bool> isDetecting_{false};
    
    // 内存大小统计
    size_t caMemorySize_ = 0;
    size_t cbMemorySize_ = 0;
    size_t dataSegmentMemorySize_ = 0;
    size_t caTrapCount_ = 0;
    size_t cbTrapCount_ = 0;
    
    // 陷阱容器
    std::vector<void*> caTraps_;
    std::vector<void*> cbTraps_;
    
    // 回调函数
    MemoryTrapSDK::DetectionCallback detectionCallback_;
    
    // 检测线程
    std::thread detectionThread_;
    
    // 持续内存使用线程
    std::atomic<bool> memoryUsageRunning_{false};
    std::thread memoryUsageThread_;
    
    void DetectionLoop();
};

} // namespace DpTrap

// C接口用于JNI调用
extern "C" {
    void dp_initialize_memory_traps();
    void dp_deploy_ca_traps(int count);
    void dp_deploy_cb_traps();
    void dp_verify_traps();
    void dp_dump_memory_info();
    void dp_shutdown();
}

#endif // DP_MEMORY_TRAP_H
