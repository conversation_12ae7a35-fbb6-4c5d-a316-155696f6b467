2025-08-01 08:57:46.437  8609-8609  MainActivity            com.sy.newfwg                        I  🚀 [手动测试] 用户点击：初始化腾讯ACE SDK
2025-08-01 08:57:46.452  8609-8609  MainActivity            com.sy.newfwg                        I  ✅ [腾讯ACE] SDK初始化成功
2025-08-01 08:57:46.453  8609-8609  MainActivity            com.sy.newfwg                        I  👤 [腾讯ACE] 模拟用户登录完成
2025-08-01 08:57:46.790  8609-8609  Thread-6                com.sy.newfwg                        I  type=1400 audit(0.0:16185): avc: denied { read } for name="libc.so" dev="sda6" ino=2820 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:unlabeled:s0 tclass=lnk_file permissive=1 app=com.sy.newfwg
2025-08-01 08:57:48.456  8609-8667  ACE-SDK                 com.sy.newfwg                        D  🔍 [腾讯检测] 类型:1 信息:id=8|name=NeteaseX86
2025-08-01 08:57:48.843  8609-8609  Thread-7                com.sy.newfwg                        I  type=1400 audit(0.0:16190): avc: denied { connectto } for path="/dev/socket/dnsproxyd" scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:r:init:s0 tclass=unix_stream_socket permissive=1 app=com.sy.newfwg
2025-08-01 08:57:52.458  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=0|pid=8609|time=1754009871
2025-08-01 08:58:02.406  1054-1054  vold                    pid-1054                             W  ignore the failure of setting attributes for /data/media/0/Android/data/com.sy.newfwg/
2025-08-01 08:58:02.406  1054-1054  vold                    pid-1054                             W  ignore the failure of setting attributes for /data/media/0/Android/data/com.sy.newfwg/files/
2025-08-01 08:58:02.403  1054-1054  Binder:1054_2           pid-1054                             I  type=1400 audit(0.0:16208): avc: denied { ioctl } for path="/data/media/0/Android/data/com.sy.newfwg" dev="sdc3" ino=4458604 ioctlcmd=0x581f scontext=u:r:init:s0 tcontext=u:object_r:media_rw_data_file:s0 tclass=dir permissive=1
2025-08-01 08:58:02.403  1054-1054  Binder:1054_2           pid-1054                             I  type=1400 audit(0.0:16209): avc: denied { ioctl } for path="/data/media/0/Android/data/com.sy.newfwg" dev="sdc3" ino=4458604 ioctlcmd=0x5820 scontext=u:r:init:s0 tcontext=u:object_r:media_rw_data_file:s0 tclass=dir permissive=1
2025-08-01 08:58:02.403  1054-1054  Binder:1054_2           pid-1054                             I  type=1400 audit(0.0:16210): avc: denied { ioctl } for path="/data/media/0/Android/data/com.sy.newfwg/files" dev="sdc3" ino=4458607 ioctlcmd=0x581f scontext=u:r:init:s0 tcontext=u:object_r:media_rw_data_file:s0 tclass=dir permissive=1
2025-08-01 08:58:02.403  1054-1054  Binder:1054_2           pid-1054                             I  type=1400 audit(0.0:16211): avc: denied { ioctl } for path="/data/media/0/Android/data/com.sy.newfwg/files" dev="sdc3" ino=4458607 ioctlcmd=0x5820 scontext=u:r:init:s0 tcontext=u:object_r:media_rw_data_file:s0 tclass=dir permissive=1
2025-08-01 08:58:02.410  8609-8609  Thread-18               com.sy.newfwg                        I  type=1400 audit(0.0:16212): avc: denied { read } for name="max_user_watches" dev="proc" ino=51419 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:proc:s0 tclass=file permissive=1 app=com.sy.newfwg
2025-08-01 08:58:02.410  8609-8609  Thread-18               com.sy.newfwg                        I  type=1400 audit(0.0:16213): avc: denied { open } for path="/proc/sys/fs/inotify/max_user_watches" dev="proc" ino=51419 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:proc:s0 tclass=file permissive=1 app=com.sy.newfwg
2025-08-01 08:58:02.473  8609-8667  ACE-SDK                 com.sy.newfwg                        D  🔍 [腾讯检测] 类型:1 信息:id=8|name=NeteaseX86
2025-08-01 08:58:10.480  8609-8667  ACE-SDK                 com.sy.newfwg                        D  🔍 [腾讯检测] 类型:1 信息:id=10|root=1|x86=1|apk_cnt=0|adb=0|machine=333|sys_ver=12|root_record=0
2025-08-01 08:58:10.481  8609-8667  ACE-SDK                 com.sy.newfwg                        D  🔍 [腾讯检测] 类型:1 信息:id=10|apk_name=com.sy.newfwg|app_name=NewFWG|app_ver=1.0|sdk_ver=7.7.11.40593|app_name_b64=TmV3RldH
2025-08-01 08:58:10.482  8609-8667  ACE-SDK                 com.sy.newfwg                        D  🔍 [腾讯检测] 类型:1 信息:id=10|files_dir=/data/user/0/com.sy.newfwg/files|sd_dir=/storage/emulated/0/Android/data/com.sy.newfwg|lib_dir=/data/app/~~4K_IKUvPPT8iVgXN-cn-Gg==/com.sy.newfwg-orshNiEUKAwse2WLFC8AQw==/lib/arm64
2025-08-01 08:58:10.483  8609-8667  ACE-SDK                 com.sy.newfwg                        D  🔍 [腾讯检测] 类型:1 信息:id=10|cert_md5=b5ff9fbd7a41391155db23f97461cfd1|apk_hash_1=0x201cfc5d|apk_hash_2=0x201cfc5d|txt_seg_crc=0x4d718cca
2025-08-01 08:58:14.223  8609-8609  Thread-21               com.sy.newfwg                        I  type=1400 audit(0.0:16227): avc: denied { open } for path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=12491 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1 app=com.sy.newfwg
2025-08-01 08:58:14.223  8609-8609  Thread-21               com.sy.newfwg                        I  type=1400 audit(0.0:16228): avc: denied { getattr } for path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=12491 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1 app=com.sy.newfwg
2025-08-01 08:58:14.223  8609-8609  Thread-21               com.sy.newfwg                        I  type=1400 audit(0.0:16229): avc: denied { map } for path="/dev/__properties__/u:object_r:vendor_default_prop:s0" dev="tmpfs" ino=12491 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:vendor_default_prop:s0 tclass=file permissive=1 app=com.sy.newfwg
2025-08-01 08:58:14.223  8609-8609  Thread-21               com.sy.newfwg                        I  type=1400 audit(0.0:16230): avc: denied { read } for name="u:object_r:build_bootimage_prop:s0" dev="tmpfs" ino=12296 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:build_bootimage_prop:s0 tclass=file permissive=1 app=com.sy.newfwg
2025-08-01 08:58:14.223  8609-8609  Thread-21               com.sy.newfwg                        I  type=1400 audit(0.0:16231): avc: denied { open } for path="/dev/__properties__/u:object_r:build_bootimage_prop:s0" dev="tmpfs" ino=12296 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:build_bootimage_prop:s0 tclass=file permissive=1 app=com.sy.newfwg
2025-08-01 08:58:14.223  8609-8609  Thread-21               com.sy.newfwg                        I  type=1400 audit(0.0:16232): avc: denied { getattr } for path="/dev/__properties__/u:object_r:build_bootimage_prop:s0" dev="tmpfs" ino=12296 scontext=u:r:untrusted_app:s0:c171,c257,c512,c768 tcontext=u:object_r:build_bootimage_prop:s0 tclass=file permissive=1 app=com.sy.newfwg
2025-08-01 08:58:16.495  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=1|pid=8609|time=1754009896
2025-08-01 08:58:36.543  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=2|pid=8609|time=1754009916
2025-08-01 08:58:36.543  8609-8667  ACE-SDK                 com.sy.newfwg                        D  🔍 [腾讯检测] 类型:1 信息:id=9|info=foo
2025-08-01 08:58:56.587  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=3|pid=8609|time=1754009936
2025-08-01 08:59:16.602  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=4|pid=8609|time=1754009956
2025-08-01 08:59:16.603  8609-8667  ACE-SDK                 com.sy.newfwg                        D  🔍 [腾讯检测] 类型:1 信息:id=9|info=foo
2025-08-01 08:59:36.643  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=5|pid=8609|time=1754009976
2025-08-01 08:59:58.687  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=6|pid=8609|time=1754009998
2025-08-01 09:00:18.699  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=7|pid=8609|time=1754010018
2025-08-01 09:00:38.715  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=8|pid=8609|time=1754010038
2025-08-01 09:00:58.742  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=9|pid=8609|time=1754010058
2025-08-01 09:01:20.788  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=10|pid=8609|time=1754010079
2025-08-01 09:01:40.802  8609-8667  ACE-SDK                 com.sy.newfwg                        D  💓 [腾讯心跳] 类型:2 信息:id=1|seq=11|pid=8609|time=1754010099
2025-08-01 09:01:53.876  1287-1384  NativeHelper            pid-1287                             I  com.sy.newfwg use max number of library use abi:arm64-v8a index:1
2025-08-01 09:01:53.904  1287-1375  NativeHelper            pid-1287                             I  com.sy.newfwg use max number of library use abi:arm64-v8a index:1
2025-08-01 09:01:53.905  1287-1375  NativeHelper            pid-1287                             I  com.sy.newfwg use max number of library use abi:arm64-v8a index:1
2025-08-01 09:01:53.908  1287-1375  NativeHelper            pid-1287                             I  com.sy.newfwg use max number of library use abi:arm64-v8a index:1
2025-08-01 09:01:53.912  1287-1315  ActivityManager         pid-1287                             I  Force stopping com.sy.newfwg appid=10427 user=-1: installPackageLI
2025-08-01 09:01:53.912  1287-1315  ActivityManager         pid-1287                             I  Killing 8609:com.sy.newfwg/u0a427 (adj 0): stop com.sy.newfwg due to installPackageLI
2025-08-01 09:01:53.912  1287-1315  ActivityTaskManager     pid-1287                             W  Force removing ActivityRecord{61e5687 u0 com.sy.newfwg/.MainActivity t607 f}}: app died, no saved state
2025-08-01 09:01:53.913  1287-1375  PackageManager          pid-1287                             I  Update package com.sy.newfwg code path from /data/app/~~4K_IKUvPPT8iVgXN-cn-Gg==/com.sy.newfwg-orshNiEUKAwse2WLFC8AQw== to /data/app/~~rN7txDYDiCAvahiezWVODw==/com.sy.newfwg-yyjZ4VHsXU6_AVe2vDFvQg==; Retain data and using new
2025-08-01 09:01:53.916  1287-1315  InputManager-JNI        pid-1287                             W  Input channel object '4124f8 com.sy.newfwg/com.sy.newfwg.MainActivity (client)' was disposed without first being removed with the input manager!
---------------------------- PROCESS ENDED (8609) for package com.sy.newfwg ----------------------------
2025-08-01 09:01:53.969  1287-4547  ActivityManager         pid-1287                             V  Got obituary of 8609:com.sy.newfwg
2025-08-01 09:01:53.971  1287-1375  ActivityManager         pid-1287                             I  Force stopping com.sy.newfwg appid=10427 user=0: pkg removed
2025-08-01 09:01:54.021  1287-1303  CompanionD...gerService pid-1287                             D  onPackageModified(packageName = com.sy.newfwg)
2025-08-01 09:01:54.024  2189-2189  MediaProvider           pid-2189                             I  Invalidating LocalCallingIdentity cache for package com.sy.newfwg. Reason: package android.intent.action.PACKAGE_REMOVED
2025-08-01 09:01:54.025  2344-2344  PackageReceiver         pid-2344                             D  action: android.intent.action.PACKAGE_REMOVED data: package:com.sy.newfwg com.sy.newfwg replacing =true
2025-08-01 09:01:54.025  2344-2344  PackageReceiver         pid-2344                             D  action: android.intent.action.PACKAGE_ADDED data: package:com.sy.newfwg com.sy.newfwg replacing =true
2025-08-01 09:01:54.025  2189-2189  MediaProvider           pid-2189                             I  Invalidating LocalCallingIdentity cache for package com.sy.newfwg. Reason: package android.intent.action.PACKAGE_ADDED
2025-08-01 09:01:54.046  1473-2053  Watchdog                pid-1473                             D  upload event: {"architecture":"x86_64","channel":"fabmnqmm","country":"zh-CN","engine":"NEMUX","fchannel":"nochannel-mumu12","language":"zh-Hans","mpid":"0","package":"mumu","product":"","product_version":"4.1.20.3657","usage":"0","uuid":"2cd69a6f-66f8-4178-bc39-4f3a46eddff5","version":"4.1.20.3657","x":{"apk_path":"socket:[67197]","app_name":"NewFWG","app_package":"com.sy.newfwg","app_version_code":1,"app_version_name":"1.0","error":{"code":0,"msg":"ok"},"extra":{"amp_name":"NewFWG","ampid":0},"has_installed":1,"token":"118acbd1-b5b2-4692-b9c1-098cbc2ce944"}}	type: InstallAPP	sign: NGGAxPRGH1EAaf9PhYLapau79Fg=	uid: 1000	json:{"apk_path":"socket:[67197]","app_name":"NewFWG","app_package":"com.sy.newfwg","app_version_code":1,"app_version_name":"1.0","error":{"code":0,"msg":"ok"},"extra":{"amp_name":"NewFWG","ampid":0},"has_installed":1,"token":"118acbd1-b5b2-4692-b9c1-098cbc2ce944"}
2025-08-01 09:01:54.047  1287-3645  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.android.gallery3d/.app.PackagesMonitor
2025-08-01 09:01:54.048  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.acc/.AccMsgReceiver
2025-08-01 09:01:54.048  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.launcher/.PackageChangedReceiver
2025-08-01 09:01:54.048  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.android.packageinstaller/.PackageInstalledReceiver
2025-08-01 09:01:54.048  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.android.gallery3d/.app.PackagesMonitor
2025-08-01 09:01:54.048  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.acc/.AccMsgReceiver
2025-08-01 09:01:54.048  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.store/.install.PackageReceiver
2025-08-01 09:01:54.048  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REPLACED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.launcher/.PackageChangedReceiver
2025-08-01 09:01:54.048  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REPLACED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.netease.nemu_vapi_android.nemu/com.netease.nemu_vapi_android.receiver.LaunchReceiver
2025-08-01 09:01:54.069  1716-1779  updatePackage           pid-1716                             D  version com.sy.newfwg 1
2025-08-01 09:01:54.120  1716-1779  updatePackage           pid-1716                             D  version com.sy.newfwg 1
2025-08-01 09:01:54.306  1716-1779  updatePackage           pid-1716                             D  version com.sy.newfwg 1
2025-08-01 09:14:37.501  1287-1315  ActivityManager         pid-1287                             I  Force stopping com.sy.newfwg appid=10427 user=0: deletePackageX
2025-08-01 09:14:37.528  1155-1383  installd                pid-1155                             E  Couldn't opendir /data/user/10/com.sy.newfwg: No such file or directory
2025-08-01 09:14:37.528  1155-1383  installd                pid-1155                             E  Failed to delete /data/user/10/com.sy.newfwg: No such file or directory
2025-08-01 09:14:37.528  1155-1383  installd                pid-1155                             E  Couldn't opendir /data/user_de/10/com.sy.newfwg: No such file or directory
2025-08-01 09:14:37.528  1155-1383  installd                pid-1155                             E  Failed to delete /data/user_de/10/com.sy.newfwg: No such file or directory
2025-08-01 09:14:37.529  1287-1375  PackageManager          pid-1287                             W  com.android.server.pm.Installer$InstallerException: android.os.ServiceSpecificException: Failed to delete /data/user_de/10/com.sy.newfwg (code 2)
2025-08-01 09:14:37.531  1287-1375  PackageManager          pid-1287                             I  Removing permission com.sy.newfwg.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION that used to be declared by com.sy.newfwg
2025-08-01 09:14:37.640  1287-1375  ActivityManager         pid-1287                             I  Force stopping com.sy.newfwg appid=10427 user=0: pkg removed
2025-08-01 09:14:37.668  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.android.gallery3d/.app.PackagesMonitor
2025-08-01 09:14:37.668  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.acc/.AccMsgReceiver
2025-08-01 09:14:37.668  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_REMOVED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.launcher/.PackageChangedReceiver
2025-08-01 09:14:37.669  1287-1303  CompanionD...gerService pid-1287                             D  onPackageRemoved(packageName = com.sy.newfwg, uid = 10427)
2025-08-01 09:14:37.669  1287-1417  StorageManagerService   pid-1287                             V  Package com.sy.newfwg does not have legacy storage
2025-08-01 09:14:37.669  2189-2189  MediaProvider           pid-2189                             I  Invalidating LocalCallingIdentity cache for package com.sy.newfwg. Reason: package android.intent.action.PACKAGE_REMOVED
2025-08-01 09:14:37.669  1287-1398  RollbackManager         pid-1287                             I  broadcast=ACTION_PACKAGE_FULLY_REMOVED pkg=com.sy.newfwg
2025-08-01 09:14:37.670  2344-2344  PackageReceiver         pid-2344                             D  action: android.intent.action.PACKAGE_REMOVED data: package:com.sy.newfwg com.sy.newfwg replacing =false
2025-08-01 09:14:37.670  1473-2053  Watchdog                pid-1473                             D  upload event: {"architecture":"x86_64","channel":"fabmnqmm","country":"zh-CN","engine":"NEMUX","fchannel":"nochannel-mumu12","language":"zh-Hans","mpid":"0","package":"mumu","product":"","product_version":"4.1.20.3657","usage":"0","uuid":"2cd69a6f-66f8-4178-bc39-4f3a46eddff5","version":"4.1.20.3657","x":{"app_name":"NewFWG","app_package":"com.sy.newfwg","app_version_code":1,"app_version_name":"1.0","error":{"code":0,"msg":"ok"},"extra":{"amp_name":"NewFWG","ampid":0},"token":"118acbd1-b5b2-4692-b9c1-098cbc2ce944"}}	type: UninstallAPP	sign: Omc0hDh7tE6f7wCvpKoZGJnJCJQ=	uid: 1000	json:{"app_name":"NewFWG","app_package":"com.sy.newfwg","app_version_code":1,"app_version_name":"1.0","error":{"code":0,"msg":"ok"},"extra":{"amp_name":"NewFWG","ampid":0},"token":"118acbd1-b5b2-4692-b9c1-098cbc2ce944"}
2025-08-01 09:14:37.704  1287-1287  WifiService             pid-1287                             W  Couldn't get PackageInfo for package:com.sy.newfwg
2025-08-01 09:14:37.704  1287-1287  WifiService             pid-1287                             D  Remove settings for package:com.sy.newfwg
2025-08-01 09:14:37.704  1287-1424  WifiConfigManager       pid-1287                             D  Remove all networks for app ApplicationInfo{c52f1f4 com.sy.newfwg}
2025-08-01 09:14:37.704  1287-1287  GameManagerService      pid-1287                             I  Disabling downscale for com.sy.newfwg
2025-08-01 09:14:37.711  1287-1287  WifiService             pid-1287                             W  Couldn't get PackageInfo for package:com.sy.newfwg
2025-08-01 09:14:37.711  1287-1287  WifiService             pid-1287                             D  Remove settings for package:com.sy.newfwg
2025-08-01 09:14:37.711  1287-1424  PasspointManager        pid-1287                             I  No app ops listener found for com.sy.newfwg
2025-08-01 09:14:37.711  1287-1424  WifiConfigManager       pid-1287                             D  Remove all networks for app ApplicationInfo{642d5 com.sy.newfwg}
2025-08-01 09:14:37.718  1287-1287  ConditionProviders      pid-1287                             I  Disallowing condition provider com.sy.newfwg (userSet: true)
2025-08-01 09:14:37.720  2189-2360  MediaProvider           pid-2189                             I  Begin Intent { act=android.intent.action.PACKAGE_FULLY_REMOVED dat=package:com.sy.newfwg flg=0x5000010 cmp=com.android.providers.media.module/com.android.providers.media.MediaService (has extras) }
2025-08-01 09:14:37.723  2344-2344  PackageReceiver         pid-2344                             D  action: android.intent.action.PACKAGE_FULLY_REMOVED data: package:com.sy.newfwg com.sy.newfwg replacing =false
2025-08-01 09:14:37.723  2189-2360  MediaProvider           pid-2189                             D  Deleted 0 Android/media items belonging to com.sy.newfwg on /data/user/0/com.android.providers.media.module/databases/external.db
2025-08-01 09:14:37.735  2189-2360  MediaProvider           pid-2189                             I  End Intent { act=android.intent.action.PACKAGE_FULLY_REMOVED dat=package:com.sy.newfwg flg=0x5000010 cmp=com.android.providers.media.module/com.android.providers.media.MediaService (has extras) }
2025-08-01 09:14:37.735  1287-1424  PasspointManager        pid-1287                             I  No app ops listener found for com.sy.newfwg
2025-08-01 09:14:44.970  1287-4738  ActivityManager         pid-1287                             W  Invalid packageName: com.sy.newfwg
2025-08-01 09:14:44.970  1287-4738  ActivityManager         pid-1287                             W  Invalid packageName: com.sy.newfwg
2025-08-01 09:14:44.993  3106-3106  studio.deploy           pid-3106                             E  Could not get package user id: run-as: unknown package: com.sy.newfwg
2025-08-01 09:14:44.998  3106-3106  studio.deploy           pid-3106                             E  Could not find apks for this package: com.sy.newfwg
2025-08-01 09:14:45.008  3106-3106  studio.deploy           pid-3106                             E  Could not get package user id: run-as: unknown package: com.sy.newfwg
2025-08-01 09:14:45.014  3106-3106  studio.deploy           pid-3106                             E  Could not find apks for this package: com.sy.newfwg
2025-08-01 09:14:45.561  1287-1384  NativeHelper            pid-1287                             I  com.sy.newfwg use max number of library use abi:arm64-v8a index:1
2025-08-01 09:14:45.589  1287-1375  NativeHelper            pid-1287                             I  com.sy.newfwg use max number of library use abi:arm64-v8a index:1
2025-08-01 09:14:45.590  1287-1375  NativeHelper            pid-1287                             I  com.sy.newfwg use max number of library use abi:arm64-v8a index:1
2025-08-01 09:14:45.593  1287-1375  NativeHelper            pid-1287                             I  com.sy.newfwg use max number of library use abi:arm64-v8a index:1
2025-08-01 09:14:45.627  2189-2701  MediaProvider           pid-2189                             I  Invalidating LocalCallingIdentity cache for package com.sy.newfwg. Reason: op android:read_external_storage
2025-08-01 09:14:45.628  2189-2701  MediaProvider           pid-2189                             I  Invalidating LocalCallingIdentity cache for package com.sy.newfwg. Reason: op android:read_external_storage
2025-08-01 09:14:45.628  2189-2701  MediaProvider           pid-2189                             I  Invalidating LocalCallingIdentity cache for package com.sy.newfwg. Reason: op android:read_external_storage
2025-08-01 09:14:45.629  1287-1375  BackupManagerService    pid-1287                             V  [UserID:0] restoreAtInstall pkg=com.sy.newfwg token=1e restoreSet=0
2025-08-01 09:14:45.643  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.android.packageinstaller/.PackageInstalledReceiver
2025-08-01 09:14:45.644  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.android.gallery3d/.app.PackagesMonitor
2025-08-01 09:14:45.644  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.acc/.AccMsgReceiver
2025-08-01 09:14:45.644  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_ADDED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.store/.install.PackageReceiver
2025-08-01 09:14:45.645  2344-2344  PackageReceiver         pid-2344                             D  action: android.intent.action.PACKAGE_ADDED data: package:com.sy.newfwg com.sy.newfwg replacing =false
2025-08-01 09:14:45.647  2189-2189  MediaProvider           pid-2189                             I  Invalidating LocalCallingIdentity cache for package com.sy.newfwg. Reason: package android.intent.action.PACKAGE_ADDED
2025-08-01 09:14:45.658  1473-2053  Watchdog                pid-1473                             D  upload event: {"architecture":"x86_64","channel":"fabmnqmm","country":"zh-CN","engine":"NEMUX","fchannel":"nochannel-mumu12","language":"zh-Hans","mpid":"0","package":"mumu","product":"","product_version":"4.1.20.3657","usage":"0","uuid":"2cd69a6f-66f8-4178-bc39-4f3a46eddff5","version":"4.1.20.3657","x":{"apk_path":"socket:[67474]","app_name":"NewFWG","app_package":"com.sy.newfwg","app_version_code":1,"app_version_name":"1.0","error":{"code":0,"msg":"ok"},"extra":{"amp_name":"NewFWG","ampid":0},"has_installed":0,"token":"118acbd1-b5b2-4692-b9c1-098cbc2ce944"}}	type: InstallAPP	sign: WEhG+K7UiZ5Abs/492E3TNu0CaM=	uid: 1000	json:{"apk_path":"socket:[67474]","app_name":"NewFWG","app_package":"com.sy.newfwg","app_version_code":1,"app_version_name":"1.0","error":{"code":0,"msg":"ok"},"extra":{"amp_name":"NewFWG","ampid":0},"has_installed":0,"token":"118acbd1-b5b2-4692-b9c1-098cbc2ce944"}
2025-08-01 09:14:45.662  1287-1303  CompanionD...gerService pid-1287                             D  onPackageModified(packageName = com.sy.newfwg)
2025-08-01 09:14:45.664  1287-4547  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_CHANGED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.android.gallery3d/.app.PackagesMonitor
2025-08-01 09:14:45.664  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=android.intent.action.PACKAGE_CHANGED dat=package:com.sy.newfwg flg=0x4000010 (has extras) } to com.mumu.launcher/.PackageChangedReceiver
2025-08-01 09:14:45.687  1716-1779  updatePackage           pid-1716                             D  version com.sy.newfwg 1
2025-08-01 09:14:45.689  1716-1779  updatePackage           pid-1716                             D  version com.sy.newfwg 1
2025-08-01 09:14:45.788  1716-1779  updatePackage           pid-1716                             D  version com.sy.newfwg 1
2025-08-01 09:14:46.429  1287-1919  ActivityManager         pid-1287                             I  Force stopping com.sy.newfwg appid=10428 user=0: from pid 8878
2025-08-01 09:14:46.474  1287-1919  ActivityTaskManager     pid-1287                             I  START u0 {act=android.intent.action.MAIN cat=[android.intent.category.LAUNCHER] flg=0x10000000 cmp=com.sy.newfwg/.MainActivity} from uid 2000, pid 8886
2025-08-01 09:14:46.477  1287-1919  ActivityTaskManager     pid-1287                             I  [KeepAlive] disabled for com.sy.newfwg
2025-08-01 09:14:46.497  1287-1316  ActivityManager         pid-1287                             I  Start proc 8889:com.sy.newfwg/u0a428 for pre-top-activity {com.sy.newfwg/com.sy.newfwg.MainActivity}
2025-08-01 09:14:46.498  8889-8889  Zygote                  pid-8889                             I  fakeProductInfoIfNeed callded! com.sy.newfwg
2025-08-01 09:14:46.501  8889-8889  Zygote                  pid-8889                             I  fake Product info com.sy.newfwg not matched any pattern
2025-08-01 09:14:46.501  8889-8889  Zygote                  pid-8889                             I  fakeProductInfoFromProperties callded! com.sy.newfwg
2025-08-01 09:14:46.522  8889-8889  com.sy.newfwg           pid-8889                             I  Late-enabling -Xcheck:jni
2025-08-01 09:14:46.549  8889-8889  com.sy.newfwg           pid-8889                             W  Unexpected CPU variant for X86 using defaults: x86_64
2025-08-01 09:14:46.549  8889-8889  libnb                   pid-8889                             V  enter native_bridge2_initialize /data/user/0/com.sy.newfwg/code_cache arm64
2025-08-01 09:14:46.569  8889-8889  HP                      pid-8889                             W  x_initialize private_dir:/data/user/0/com.sy.newfwg/code_cache instruction_set:arm64 getMethodShorty:0x7849a7d3c340 ret:1
2025-08-01 09:14:46.597  1287-1306  RecentTaskMonitor       pid-1287                             D  VAddressShellNotifier#notifyOpenTab :{"uri":"player\/tab","need_response":false,"params":{"id":"task:608","displayId":0,"userId":0,"name":"NewFWG","originName":"NewFWG","packageName":"com.sy.newfwg","launcher":false,"newTask":true,"icon":"iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAPmSURBVEiJtZW\/i11VEMc\/M+fHvfe9tz+yu0m0SAI2NjaKKNqIKZSAhbGL2oiBBDvBylK0SKeFgvjjD7AMCIKNhZUWaSLYKAkGWZOsZt++l3ff\/XHG4t683XXjBgsHDsyXc+4w85m558iLv156SkQ+bxOP5TiZ02IYQ4k01jKnBWDDjbjdTgBY0YLtNAPAIYw0X+icQCmNidlNcfqmF\/SraTs\/BSAESmoMCDgqa5jTANBYorQagIHFha8ImYWFFoEy1QIcL9r4hSazk\/xfJhzzhQRREQAKCThzHSLNCObx97LWyNAyAIYuW1SmCEPd1YUE1BSATLz4mVXM+iCJtECkJvsQ3ZWKaZp3H+IXviIE3EInSbvxJKH\/G57e\/EAiTrQvL+J7RCtakEtgs93usGhk1CMaaUbVV7bmRnh0oQt242Xi0bk1lKnuV9Wvmmma887RM5gZZarZd673kxnvHX+FSSr37FULf54a5IVfLtk9ZsWeMV2SnA034lTc4Fp9m5eXnyDTAECVGi7vXOG4W2Gc7vJ7fYc\/07SnEBY9KCTgD+M3cBnn15\/jZNw4sHd29UluNmM++OPyoT2Qc9c+toYEQC6BuXUVPF08wttHz3BvhA+zz7a+49vJ1QMVBBx+q53wT0SPxoe5sH4aFaFqa6IL9w1ctQ1BHa8feZar5Q2ulNcPILrvmF5YP83I5QCc++b9f838te8vMWsrCo1cXH0ere0golevfbIPkWF8eeI8XtwD0ey11hJv\/PAhbBTMfRfPo\/jb7c4+RC8tP\/6fgwM4Uc6eeIaPrn9NcySgwd9\/in689TMXf7pKRiCp0ZBAhRVfMLYSEWHoc6ZWgQpOlYFmTKmYUWPJqLdnhNUBhIBXFNe3wolyI5tQ5mPktwmtGhUt4pSNsMSWTREVVv2IbWaICqqOlTBgmxIECp\/jMWx7jq4V+KFGIh2SnEjEs\/RQDjJiuvkXlSREu6wr66oZhZxkICo45xj5TqNK4SNRPKJKVoLfSeWeHtS7f\/LRnFoHTDa3wISMyNhKQHB4xjYDFIcD064ChNq6GxlTBqk9\/E+Oa0MiDfWtHUQUcQKqiAjiFFS7J0wFQTrfCaCIdmd9IbHLAig04JPDgKFmRDxh3VG5jMG2MLMGUWUQcmozRBXnHEOf0\/TICp\/haRAnZC7gZ9Q2s0oA2rZdIJLE4sGxZcdSyJjdugN0uGbUgKDmCDjuMgcUQzpECCZiqsjmg2ZcRAhLBX5tiPSIUEH6hXa4Ftr1GFXHXlJ6q9D4qZkdy+juHAOiOFQEse6y86IsLY9oXSSbKYW1oII6JTpPQURUyFwEUcRpFUXe\/RvtGMnNj92tIAAAAABJRU5ErkJggg==","canChangeAppOrientation":false,"canReceiveFile":false,"pid":8889,"uid":10428,"action":"open"}}
---------------------------- PROCESS STARTED (8889) for package com.sy.newfwg ----------------------------
2025-08-01 09:14:46.659  8889-8889  NemuJavaHotPatchHelper  com.sy.newfwg                        D  onHandleBindApplication reportedPackageName:com.sy.newfwg isMatchJavaPatch:false
2025-08-01 09:14:46.765  8889-8889  Typeface                com.sy.newfwg                        I  Preloading /system/fonts/Roboto-Regular.ttf
2025-08-01 09:14:46.918  8889-8889  GraphicsEnvironment     com.sy.newfwg                        V  ANGLE Developer option for 'com.sy.newfwg' set to: 'default'
2025-08-01 09:14:46.918  8889-8889  GraphicsEnvironment     com.sy.newfwg                        V  ANGLE GameManagerService for com.sy.newfwg: false
2025-08-01 09:14:46.918  8889-8889  GraphicsEnvironment     com.sy.newfwg                        V  Neither updatable production driver nor prerelease driver is supported.
2025-08-01 09:14:46.920  8889-8889  NetworkSecurityConfig   com.sy.newfwg                        D  No Network Security Config specified, using platform default
2025-08-01 09:14:46.920  8889-8889  NetworkSecurityConfig   com.sy.newfwg                        D  No Network Security Config specified, using platform default
2025-08-01 09:14:46.925  8889-8889  FirebaseApp             com.sy.newfwg                        W  Default FirebaseApp failed to initialize because no default options were found. This usually means that com.google.gms:google-services was not applied to your gradle project.
2025-08-01 09:14:46.925  8889-8889  FirebaseInitProvider    com.sy.newfwg                        I  FirebaseApp initialization unsuccessful
2025-08-01 09:14:46.938  8889-8922  libEGL                  com.sy.newfwg                        D  loaded /system/lib64/egl/libEGL_emulation.so
2025-08-01 09:14:46.949  8889-8922  com.sy.newfwg           com.sy.newfwg                        D  Features: 0 0 0
2025-08-01 09:14:46.949  8889-8922  com.sy.newfwg           com.sy.newfwg                        E  vulkan version is 4206872
2025-08-01 09:14:46.955  8889-8922  libEGL                  com.sy.newfwg                        D  loaded /system/lib64/egl/libGLESv1_CM_emulation.so
2025-08-01 09:14:46.957  8889-8922  libEGL                  com.sy.newfwg                        D  loaded /system/lib64/egl/libGLESv2_emulation.so
2025-08-01 09:14:46.972  8889-8922  GraphicsEnv             com.sy.newfwg                        E  Failed to get gpu service
2025-08-01 09:14:47.042  8889-8889  com.sy.newfwg           com.sy.newfwg                        W  Accessing hidden method Landroid/view/View;->computeFitSystemWindows(Landroid/graphics/Rect;Landroid/graphics/Rect;)Z (unsupported, reflection, allowed)
2025-08-01 09:14:47.042  8889-8889  com.sy.newfwg           com.sy.newfwg                        W  Accessing hidden method Landroid/view/ViewGroup;->makeOptionalFitsSystemWindows()V (unsupported, reflection, allowed)
2025-08-01 09:14:47.048  8889-8889  Compatibil...geReporter com.sy.newfwg                        D  Compat change id reported: 171228096; UID 10428; state: ENABLED
2025-08-01 09:14:47.081  8889-8889  MainActivity            com.sy.newfwg                        I  🎯 [手动测试] 对比测试模式已启用
2025-08-01 09:14:47.081  8889-8889  MainActivity            com.sy.newfwg                        I  📋 [手动测试] 请点击按钮进行不同的初始化测试
2025-08-01 09:14:47.081  8889-8889  MainActivity            com.sy.newfwg                        I  🔍 [手动测试] 这样可以更精确地对比Ca/Cb区域变化
2025-08-01 09:14:47.081  8889-8889  ACE-SDK                 com.sy.newfwg                        D  📡 [腾讯ACE] 信息接收器已准备，等待手动初始化
2025-08-01 09:14:47.083  8889-8923  ziparchive              com.sy.newfwg                        E  Zip: lseek on fd -2 failed: Bad file descriptor
2025-08-01 09:14:47.117  8889-8923  MemoryTrapSDK_JNI       com.sy.newfwg                        I  📚 MemoryTrapSDK JNI库已加载
2025-08-01 09:14:47.117  8889-8923  ComprehensiveTrapJNI    com.sy.newfwg                        I  ✅ 综合陷阱系统本地库加载成功
2025-08-01 09:14:47.191  8889-8889  Settings                com.sy.newfwg                        W  Setting adb_enabled has moved from android.provider.Settings.Secure to android.provider.Settings.Global.
2025-08-01 09:14:47.247  1287-1315  BroadcastQueue          pid-1287                             W  Background execution not allowed: receiving Intent { act=nemu.intent.action.NEW_TASK dat=package:com.sy.newfwg flg=0x10 (has extras) } to com.mumu.acc/.AccMsgReceiver
2025-08-01 09:14:47.248  1473-1883  NewFileUpdater          pid-1473                             D  onOpenNewTask focusPkgName set to com.sy.newfwg
2025-08-01 09:14:47.249  1473-2053  Watchdog                pid-1473                             D  upload event: {"architecture":"x86_64","channel":"fabmnqmm","country":"zh-CN","engine":"NEMUX","fchannel":"nochannel-mumu12","language":"zh-Hans","mpid":"0","package":"mumu","product":"","product_version":"4.1.20.3657","usage":"0","uuid":"2cd69a6f-66f8-4178-bc39-4f3a46eddff5","version":"4.1.20.3657","x":{"app_name":"NewFWG","app_package":"com.sy.newfwg","app_version_code":1,"app_version_name":"1.0","error":{"code":0,"msg":"ok"},"extra":{"NB":"{\"NB_MD5\":\"6A997A644DC56222DBC85F7856A8EF2C\",\"NB_NAME\":\"libhoudini.so\",\"NB_64BIT\":\"true\",\"NB_STATUS\":\"1\"}","amp_name":"NewFWG","ampid":0},"token":"118acbd1-b5b2-4692-b9c1-098cbc2ce944"}}	type: LaunchAPP	sign: dB/N3US9XLbAQWJO96Wp4SfMsBA=	uid: 1000	json:{"app_name":"NewFWG","app_package":"com.sy.newfwg","app_version_code":1,"app_version_name":"1.0","error":{"code":0,"msg":"ok"},"extra":{"NB":"{\"NB_MD5\":\"6A997A644DC56222DBC85F7856A8EF2C\",\"NB_NAME\":\"libhoudini.so\",\"NB_64BIT\":\"true\",\"NB_STATUS\":\"1\"}","amp_name":"NewFWG","ampid":0},"token":"118acbd1-b5b2-4692-b9c1-098cbc2ce944"}
2025-08-01 09:14:47.255  1716-1779  updatePackage           pid-1716                             D  version com.sy.newfwg 1
2025-08-01 09:14:47.278  8889-8920  com.sy.newfwg           com.sy.newfwg                        D  Features: 0 0 0
2025-08-01 09:14:47.278  8889-8920  com.sy.newfwg           com.sy.newfwg                        E  vulkan version is 4206872
2025-08-01 09:14:47.281  8889-8920  hw-ProcessState         com.sy.newfwg                        D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-08-01 09:14:47.282  8889-8920  HidlServiceManagement   com.sy.newfwg                        E  getService: Potential race detected, descriptor: android.hardware.configstore@1.0::ISurfaceFlingerConfigs instance: default
2025-08-01 09:14:47.283  8889-8920  OpenGLRenderer          com.sy.newfwg                        W  Failed to initialize 101010-2 format, error = EGL_SUCCESS
2025-08-01 09:14:47.294  8889-8920  HidlServiceManagement   com.sy.newfwg                        E  getService: Potential race detected, descriptor: android.hardware.graphics.mapper@4.0::IMapper instance: default
2025-08-01 09:14:47.294  8889-8920  Gralloc4                com.sy.newfwg                        I  mapper 4.x is not supported
2025-08-01 09:14:47.294  8889-8920  HidlServiceManagement   com.sy.newfwg                        E  getService: Potential race detected, descriptor: android.hardware.graphics.mapper@3.0::IMapper instance: default
2025-08-01 09:14:47.295  8889-8920  Gralloc3                com.sy.newfwg                        W  mapper 3.x is not supported
2025-08-01 09:14:47.295  8889-8920  HidlServiceManagement   com.sy.newfwg                        E  getService: Potential race detected, descriptor: android.hardware.graphics.mapper@2.0::IMapper instance: default
2025-08-01 09:14:47.296  8889-8920  HidlServiceManagement   com.sy.newfwg                        E  getService: Potential race detected, descriptor: android.hardware.graphics.allocator@4.0::IAllocator instance: default
2025-08-01 09:14:47.297  8889-8920  Gralloc4                com.sy.newfwg                        W  allocator 4.x is not supported
2025-08-01 09:14:47.297  8889-8920  HidlServiceManagement   com.sy.newfwg                        E  getService: Potential race detected, descriptor: android.hardware.graphics.allocator@3.0::IAllocator instance: default
2025-08-01 09:14:47.297  8889-8920  Gralloc3                com.sy.newfwg                        W  allocator 3.x is not supported
2025-08-01 09:14:47.297  8889-8920  HidlServiceManagement   com.sy.newfwg                        E  getService: Potential race detected, descriptor: android.hardware.graphics.allocator@2.0::IAllocator instance: default
2025-08-01 09:14:47.298  8889-8920  gralloc_x86             com.sy.newfwg                        D  gralloc_alloc: Creating ashmem region of size 8298496
2025-08-01 09:14:47.310  8889-8935  com.sy.newfwg           com.sy.newfwg                        D  Features: 0 0 0
2025-08-01 09:14:47.311  8889-8935  com.sy.newfwg           com.sy.newfwg                        E  vulkan version is 4206872
2025-08-01 09:14:47.312  8889-8935  gralloc_x86             com.sy.newfwg                        D  gralloc_alloc: Creating ashmem region of size 8298496
2025-08-01 09:14:47.314  8889-8935  gralloc_x86             com.sy.newfwg                        D  gralloc_alloc: Creating ashmem region of size 8298496
2025-08-01 09:14:47.391  1287-1303  system_server           pid-1287                             W  Failed to determine oat file name for dex location /data/app/~~HL8mem3ztP6L3BpxZq_u0g==/com.sy.newfwg-BEy3adBlRwZjPOFW6IMEYQ==/base.apk: Dalvik cache directory does not exist
2025-08-01 09:14:47.392  1287-1303  ActivityTaskManager     pid-1287                             I  Displayed com.sy.newfwg/.MainActivity: +916ms
2025-08-01 09:14:47.416  1473-2098  NewFileUpdater          pid-1473                             W  MSG_GET_PACKAGE_TAG_INFO, no available new tag for: com.sy.newfwg, workingTag: 12.1343
2025-08-01 09:14:47.535  1287-2996  InputManager-JNI        pid-1287                             W  Input channel object '39b1ea8 Splash Screen com.sy.newfwg (client)' was disposed without first being removed with the input manager!
2025-08-01 09:14:57.071  8889-8889  MainActivity            com.sy.newfwg                        I  🚀 [手动测试] 用户点击：初始化我们的实现
2025-08-01 09:14:57.074  8889-8889  ComprehensiveTrap       com.sy.newfwg                        I  🚀 开始初始化GG兼容内存陷阱系统...
2025-08-01 09:14:57.074  8889-8889  ComprehensiveTrap       com.sy.newfwg                        I  📋 基于腾讯TP2SDK成功案例的精确实现
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ GG兼容内存陷阱库加载成功
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  🚀 [GG-Init] 开始初始化腾讯TP2SDK精确复制版...
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  📋 [GG-Init] 模拟TP2Sdk.initEx(20616, "f38294be7e0a824dd5629e148b21fcac")
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔥 [GG-Init] 版本: TP2SDK复制版 v4.0 - 精确模拟腾讯成功模式
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  🚀 [GG-Init] 初始化TP2SDK精确复制版...
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔥 [GG-Init] TP2SDK复制版 v4.0 - 精确模拟腾讯成功模式
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [GG-Init] 模拟TP2Sdk.initEx(20616, "f38294be7e0a824dd5629e148b21fcac")
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [GG-Ca] 最小化测试：只分配4.10kB模拟腾讯
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔍 [GG-Ca] 关闭大量分配，专注腾讯模式...
2025-08-01 09:14:57.074  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [GG-Ca] 跳过大量分配，只保留腾讯4.10kB模拟
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [GG-Ca] 创建完成: 0个大块, 专注腾讯模拟
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [GG-Cb] 最小化测试：关闭Cb区域大量分配
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔍 [GG-Cb] 专注测试腾讯4.10kB Ca区域识别...
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [GG-Cb] 跳过Cb区域分配，专注Ca区域测试
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [GG-Cb] 创建完成: 0个内存块, 专注腾讯Ca模拟
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [GG-Init] 腾讯风格内存陷阱系统初始化完成
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [GG-Init] 腾讯风格内存陷阱系统初始化成功
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  📊 [GG-Status] ===== TP2SDK复制版内存状态 =====
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [GG-Ca] 陷阱数量: 0个, 估算大小: 0.00 MB
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [GG-Cb] 陷阱数量: 0个, 估算大小: 0.00 MB
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯分析] 腾讯SDK成功创建4.10kB Ca区域
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯分析] 开始模拟腾讯的精确分配模式...
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔥 [革命性] 标准malloc失败，尝试不同的分配API
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔍 [革命性] 腾讯可能使用了特殊的内存分配方式
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [方法1] 尝试C++ new操作符...
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [方法1] C++ new分配成功: 4198字节
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [方法2] 尝试calloc零初始化分配...
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [方法2] calloc分配成功: 4198字节
2025-08-01 09:14:57.075  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [方法3] 尝试realloc重新分配...
2025-08-01 09:14:57.081  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [方法3] realloc分配成功: 4198字节
2025-08-01 09:14:57.081  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [方法4] 尝试posix_memalign对齐分配...
2025-08-01 09:14:57.081  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [方法4] posix_memalign分配成功: 4198字节
2025-08-01 09:14:57.081  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔥 [革命性] 完成多种分配API测试，总计分配: 4个块
2025-08-01 09:14:57.081  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [革命性] 如果仍然失败，说明问题在于GG的识别算法
2025-08-01 09:14:57.081  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内存分析] 开始深度分析内存布局...
2025-08-01 09:14:57.081  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块0] 地址: 0x784b0b24b640
2025-08-01 09:14:57.082  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块0] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:57.082  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块0] 对齐: 8字节对齐 16字节对齐 32字节对齐 64字节对齐 
2025-08-01 09:14:57.085  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:57.085  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:57.085  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块1] 地址: 0x784b0b248010
2025-08-01 09:14:57.085  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块1] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:57.085  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块1] 对齐: 8字节对齐 16字节对齐 
2025-08-01 09:14:57.085  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块2] 地址: 0x784b0b24da60
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块2] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块2] 对齐: 8字节对齐 16字节对齐 32字节对齐 
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块3] 地址: 0x784b0b24fe80
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块3] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块3] 对齐: 8字节对齐 16字节对齐 32字节对齐 64字节对齐 
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [分布] 分析4个内存块的分布特征...
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [分布] 地址范围: 0x784b0b248010 - 0x784b0b24fe80
2025-08-01 09:14:57.086  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [分布] 跨度: 32368字节 (31.61KB)
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [分布] 地址连续性: 分散
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  📊 [GG-Status] ===== 状态结束 =====
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 开始分析腾讯ACE SDK内存模式...
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 策略1: 模拟ACE引擎初始化
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔧 [ACE引擎] 模拟引擎初始化内存分配...
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔧 [ACE引擎] 分配引擎上下文: 304字节
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 策略2: 模拟反作弊模块加载
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🛡️ [反作弊] 模拟反作弊模块内存分配...
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🛡️ [反作弊] 分配反作弊模块: 312字节
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 策略3: 模拟内存保护机制
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔒 [内存保护] 模拟内存保护机制...
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔒 [内存保护] 分配保护内存: 1024字节
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 策略4: 模拟SDK特征标记
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  📝 [SDK签名] 模拟SDK特征标记...
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  📝 [SDK签名] 分配SDK签名: 104字节
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 完成所有模拟策略，总计新增: 4个特��块
2025-08-01 09:14:57.087  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [GG-Test] 请使用GG修改器测试Ca/Cb区域是否显示非零值
2025-08-01 09:14:57.088  8889-8889  ComprehensiveTrap       com.sy.newfwg                        I  ✅ GG兼容内存陷阱系统初始化完成
2025-08-01 09:14:59.090  8889-8889  ComprehensiveTrap       com.sy.newfwg                        I  🧪 测试GG兼容内存陷阱效果...
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🧪 [GG-Test] 测试腾讯风格内存陷阱效果...
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  📋 [GG-Test] 当前内存状态:
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  📊 [GG-Status] ===== TP2SDK复制版内存状态 =====
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [GG-Ca] 陷阱数量: 0个, 估算大小: 0.00 MB
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [GG-Cb] 陷阱数量: 0个, 估算大小: 0.00 MB
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯分析] 腾讯SDK成功创建4.10kB Ca区域
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯分析] 开始模拟腾讯的精确分配模式...
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔥 [革命性] 标准malloc失败，尝试不同的分配API
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔍 [革命性] 腾讯可能使用了特殊的内存分配方式
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [方法1] 尝试C++ new操作符...
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [方法1] C++ new分配成功: 4198字节
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [方法2] 尝试calloc零初始化分配...
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [方法2] calloc分配成功: 4198字节
2025-08-01 09:14:59.090  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [方法3] 尝试realloc重新分配...
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [方法3] realloc分配成功: 4198字节
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [方法4] 尝试posix_memalign对齐分配...
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [方法4] posix_memalign分配成功: 4198字节
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔥 [革命性] 完成多种分配API测试，总计分配: 12个块
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [革命性] 如果仍然失败，说明问题在于GG的识别算法
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内存分析] 开始深度分析内存布局...
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块0] 地址: 0x784b0b24b640
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块0] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块0] 对齐: 8字节对齐 16字节对齐 32字节对齐 64字节对齐 
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块1] 地址: 0x784b0b248010
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块1] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块1] 对齐: 8字节对齐 16字节对齐 
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块2] 地址: 0x784b0b24da60
2025-08-01 09:14:59.091  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块2] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块2] 对齐: 8字节对齐 16字节对齐 32字节对齐 
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块3] 地址: 0x784b0b24fe80
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块3] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块3] 对齐: 8字节对齐 16字节对齐 32字节对齐 64字节对齐 
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块4] 地址: 0x784a6b2d2590
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块4] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块4] 对齐: 8字节对齐 16字节对齐 
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 否, 有模式: 否
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块5] 地址: 0x784a6b2d0750
2025-08-01 09:14:59.092  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块5] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块5] 对齐: 8字节对齐 16字节对齐 
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 否, 有模式: 否
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块6] 地址: 0x784aab25b050
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块6] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块6] 对齐: 8字节对齐 16字节对齐 
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 否, 有模式: 否
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块7] 地址: 0x784a1b25e4e0
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块7] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块7] 对齐: 8字节对齐 16字节对齐 32字节对齐 
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 否, 有模式: 否
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块8] 地址: 0x784b0b24a430
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块8] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块8] 对齐: 8字节对齐 16字节对齐 
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块9] 地址: 0x784b0b25c530
2025-08-01 09:14:59.093  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块9] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块9] 对齐: 8字节对齐 16字节对齐 
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块10] 地址: 0x784b0b25d740
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块10] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块10] 对齐: 8字节对齐 16字节对齐 32字节对齐 64字节对齐 
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块11] 地址: 0x784b0b25b340
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块11] 地址范围: 高地址区域 (可能是栈区)
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [块11] 对齐: 8字节对齐 16字节对齐 32字节对齐 64字节对齐 
2025-08-01 09:14:59.094  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 全零: 否, 全相同: 是, 有模式: 是
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [内容] 模式值: 0xACACACAC
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [分布] 分析12个内存块的分布特征...
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [分布] 地址范围: 0x784a1b25e4e0 - 0x784b0b25d740
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [分布] 跨度: 4026528352字节 (3932156.59KB)
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔬 [分布] 地址连续性: 分散
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  📊 [GG-Status] ===== 状态结束 =====
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 开始分析腾讯ACE SDK内存模式...
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 策略1: 模拟ACE引擎初始化
2025-08-01 09:14:59.095  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔧 [ACE引擎] 模拟引擎初始化内存分配...
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔧 [ACE引擎] 分配引擎上下文: 304字节
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 策略2: 模拟反作弊模块加载
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🛡️ [反作弊] 模拟反作弊模块内存分配...
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🛡️ [反作弊] 分配反作弊模块: 312字节
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 策略3: 模拟内存保护机制
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔒 [内存保护] 模拟内存保护机制...
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔒 [内存保护] 分配保护内存: 1024字节
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 策略4: 模拟SDK特征标记
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  📝 [SDK签名] 模拟SDK特征标记...
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  📝 [SDK签名] 分配SDK签名: 104字节
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🎯 [腾讯逆向] 完成所有模拟策略，总计新增: 4个特征块
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  🔍 [GG-Test] 请检查GG修改器中的Ca/Cb区域大小
2025-08-01 09:14:59.096  8889-8889  GG-Trap                 com.sy.newfwg                        I  ✅ [GG-Test] 如果显示非零值，说明腾讯风格修复成功
2025-08-01 09:15:27.290  8889-8889  Thread-2                com.sy.newfwg                        I  type=1400 audit(0.0:16580): avc: denied { connectto } for path="/dev/socket/dnsproxyd" scontext=u:r:untrusted_app:s0:c172,c257,c512,c768 tcontext=u:r:init:s0 tclass=unix_stream_socket permissive=1 app=com.sy.newfwg
2025-08-01 09:15:33.256  8889-8889  FastPrintWriter         com.sy.newfwg                        W  Write failure
                                                                                                    java.io.IOException: write failed: EPIPE (Broken pipe)
                                                                                                    	at libcore.io.IoBridge.write(IoBridge.java:654)
                                                                                                    	at java.io.FileOutputStream.write(FileOutputStream.java:401)
                                                                                                    	at com.android.internal.util.FastPrintWriter.flushBytesLocked(FastPrintWriter.java:354)
                                                                                                    	at com.android.internal.util.FastPrintWriter.flushLocked(FastPrintWriter.java:377)
                                                                                                    	at com.android.internal.util.FastPrintWriter.flush(FastPrintWriter.java:412)
                                                                                                    	at android.app.ActivityThread.handleDumpActivity(ActivityThread.java:4744)
                                                                                                    	at android.app.ActivityThread.access$2800(ActivityThread.java:260)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2238)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8060)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
                                                                                                    Caused by: android.system.ErrnoException: write failed: EPIPE (Broken pipe)
                                                                                                    	at libcore.io.Linux.writeBytes(Native Method)
                                                                                                    	at libcore.io.Linux.write(Linux.java:296)
                                                                                                    	at libcore.io.ForwardingOs.write(ForwardingOs.java:951)
                                                                                                    	at libcore.io.BlockGuardOs.write(BlockGuardOs.java:447)
                                                                                                    	at libcore.io.ForwardingOs.write(ForwardingOs.java:951)
                                                                                                    	at libcore.io.IoBridge.write(IoBridge.java:649)
                                                                                                    	at java.io.FileOutputStream.write(FileOutputStream.java:401) 
                                                                                                    	at com.android.internal.util.FastPrintWriter.flushBytesLocked(FastPrintWriter.java:354) 
                                                                                                    	at com.android.internal.util.FastPrintWriter.flushLocked(FastPrintWriter.java:377) 
                                                                                                    	at com.android.internal.util.FastPrintWriter.flush(FastPrintWriter.java:412) 
                                                                                                    	at android.app.ActivityThread.handleDumpActivity(ActivityThread.java:4744) 
                                                                                                    	at android.app.ActivityThread.access$2800(ActivityThread.java:260) 
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2238) 
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106) 
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201) 
                                                                                                    	at android.os.Looper.loop(Looper.java:288) 
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8060) 
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method) 
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571) 
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091) 
2025-08-01 09:15:33.306  3254-3273  AndroidService          pid-3254                             E  Failed load icon for /data/data/com.sy.newfwg/code_cache/install_server-69880af5
2025-08-01 09:15:34.332  3254-3254  AndroidService          pid-3254                             E  vs: /data/app/~~RVtt3PM6RBX16LpJZcNq3Q==/com.sy.newfwg-mZNaeB-dBq544c14KDERBA==/lib/arm64 => /data/app/~~HL8mem3ztP6L3BpxZq_u0g==/com.sy.newfwg-BEy3adBlRwZjPOFW6IMEYQ==/lib/arm64
2025-08-01 09:15:34.332  3254-3254  AndroidService          pid-3254                             E  used: ProcessInfo [cmdline=com.sy.newfwg, name=NewFWG, packageName=com.sy.newfwg, icon=android.graphics.drawable.AdaptiveIconDrawable@73db66b, pid=8889, uid=10428, isSystem=false, isGame=false, weight=3022000, main=true, order=0, x64=true, rss=200208, getTracer()=0, getTrace()=-1]
2025-08-01 09:15:34.337  3367-3367  android-daemon          pid-3367                             I  package name: com.sy.newfwg
2025-08-01 09:15:34.337  3367-3367  android-daemon          pid-3367                             I  Libs path: /data/app/~~HL8mem3ztP6L3BpxZq_u0g==/com.sy.newfwg-BEy3adBlRwZjPOFW6IMEYQ==/lib/arm64
2025-08-01 09:15:34.347  3367-3367  android-daemon          pid-3367                             I  package name: com.sy.newfwg
2025-08-01 09:15:34.347  3367-3367  android-daemon          pid-3367                             I  Libs path: /data/app/~~HL8mem3ztP6L3BpxZq_u0g==/com.sy.newfwg-BEy3adBlRwZjPOFW6IMEYQ==/lib/arm64
2025-08-01 09:15:45.600  8889-8889  FastPrintWriter         com.sy.newfwg                        W  Write failure
                                                                                                    java.io.IOException: write failed: EPIPE (Broken pipe)
                                                                                                    	at libcore.io.IoBridge.write(IoBridge.java:654)
                                                                                                    	at java.io.FileOutputStream.write(FileOutputStream.java:401)
                                                                                                    	at com.android.internal.util.FastPrintWriter.flushBytesLocked(FastPrintWriter.java:354)
                                                                                                    	at com.android.internal.util.FastPrintWriter.flushLocked(FastPrintWriter.java:377)
                                                                                                    	at com.android.internal.util.FastPrintWriter.appendLocked(FastPrintWriter.java:322)
                                                                                                    	at com.android.internal.util.FastPrintWriter.write(FastPrintWriter.java:640)
                                                                                                    	at java.io.PrintWriter.print(PrintWriter.java:505)
                                                                                                    	at java.io.PrintWriter.println(PrintWriter.java:641)
                                                                                                    	at android.view.autofill.AutofillManager.dump(AutofillManager.java:2827)
                                                                                                    	at android.app.Activity.dumpAutofillManager(Activity.java:7394)
                                                                                                    	at android.app.Activity.dumpInner(Activity.java:7384)
                                                                                                    	at android.app.Activity.dump(Activity.java:7329)
                                                                                                    	at androidx.fragment.app.FragmentActivity.dump(FragmentActivity.java:411)
                                                                                                    	at android.app.ActivityThread.handleDumpActivity(ActivityThread.java:4743)
                                                                                                    	at android.app.ActivityThread.access$2800(ActivityThread.java:260)
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2238)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8060)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091)
                                                                                                    Caused by: android.system.ErrnoException: write failed: EPIPE (Broken pipe)
                                                                                                    	at libcore.io.Linux.writeBytes(Native Method)
                                                                                                    	at libcore.io.Linux.write(Linux.java:296)
                                                                                                    	at libcore.io.ForwardingOs.write(ForwardingOs.java:951)
                                                                                                    	at libcore.io.BlockGuardOs.write(BlockGuardOs.java:447)
                                                                                                    	at libcore.io.ForwardingOs.write(ForwardingOs.java:951)
                                                                                                    	at libcore.io.IoBridge.write(IoBridge.java:649)
                                                                                                    	at java.io.FileOutputStream.write(FileOutputStream.java:401) 
                                                                                                    	at com.android.internal.util.FastPrintWriter.flushBytesLocked(FastPrintWriter.java:354) 
                                                                                                    	at com.android.internal.util.FastPrintWriter.flushLocked(FastPrintWriter.java:377) 
                                                                                                    	at com.android.internal.util.FastPrintWriter.appendLocked(FastPrintWriter.java:322) 
                                                                                                    	at com.android.internal.util.FastPrintWriter.write(FastPrintWriter.java:640) 
                                                                                                    	at java.io.PrintWriter.print(PrintWriter.java:505) 
                                                                                                    	at java.io.PrintWriter.println(PrintWriter.java:641) 
                                                                                                    	at android.view.autofill.AutofillManager.dump(AutofillManager.java:2827) 
                                                                                                    	at android.app.Activity.dumpAutofillManager(Activity.java:7394) 
                                                                                                    	at android.app.Activity.dumpInner(Activity.java:7384) 
                                                                                                    	at android.app.Activity.dump(Activity.java:7329) 
                                                                                                    	at androidx.fragment.app.FragmentActivity.dump(FragmentActivity.java:411) 
                                                                                                    	at android.app.ActivityThread.handleDumpActivity(ActivityThread.java:4743) 
                                                                                                    	at android.app.ActivityThread.access$2800(ActivityThread.java:260) 
                                                                                                    	at android.app.ActivityThread$H.handleMessage(ActivityThread.java:2238) 
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:106) 
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201) 
                                                                                                    	at android.os.Looper.loop(Looper.java:288) 
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:8060) 
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method) 
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:571) 
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:1091) 
