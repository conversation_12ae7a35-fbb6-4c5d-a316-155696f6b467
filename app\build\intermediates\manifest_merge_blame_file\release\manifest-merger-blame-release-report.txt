1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.sy.newfwg"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml
10
11    <!-- 权限声明 -->
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:8:5-81
12-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:8:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:9:5-80
13-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:9:22-77
14
15    <!-- 防止应用被调试 -->
16    <uses-permission android:name="android.permission.INTERNET" />
16-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:12:5-67
16-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:12:22-64
17    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
17-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\3943456226ca63fd316ca1177aac8b73\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:5-110
17-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\3943456226ca63fd316ca1177aac8b73\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:22-107
18    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
18-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:5-79
18-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:22:22-76
19    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
19-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:5-77
19-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:23:22-74
20    <uses-permission android:name="android.permission.WAKE_LOCK" /> <!-- Required by older versions of Google Play services to create IID tokens -->
20-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:5-68
20-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:24:22-65
21    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
21-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
21-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
22    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
22-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
22-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
23    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
23-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
23-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
24    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
24-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
24-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
25
26    <permission
26-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8ae4478ecf3312e5bcfba423f6800a0\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
27        android:name="com.sy.newfwg.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
27-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8ae4478ecf3312e5bcfba423f6800a0\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
28        android:protectionLevel="signature" />
28-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8ae4478ecf3312e5bcfba423f6800a0\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
29
30    <uses-permission android:name="com.sy.newfwg.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
30-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8ae4478ecf3312e5bcfba423f6800a0\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
30-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8ae4478ecf3312e5bcfba423f6800a0\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
31
32    <application
32-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:14:5-41:19
33        android:allowBackup="false"
33-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:15:9-36
34        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
34-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8ae4478ecf3312e5bcfba423f6800a0\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
35        android:dataExtractionRules="@xml/data_extraction_rules"
35-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:16:9-65
36        android:debuggable="false"
36-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:23:9-35
37        android:extractNativeLibs="true"
37-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:24:9-41
38        android:fullBackupContent="@xml/backup_rules"
38-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:17:9-54
39        android:hardwareAccelerated="true"
39-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:25:9-43
40        android:icon="@mipmap/ic_launcher"
40-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:18:9-43
41        android:label="@string/app_name"
41-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:19:9-41
42        android:roundIcon="@mipmap/ic_launcher_round"
42-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:20:9-54
43        android:supportsRtl="true"
43-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:21:9-35
44        android:theme="@style/Theme.NewFWG" >
44-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:22:9-44
45        <activity
45-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:27:9-40:20
46            android:name="com.sy.newfwg.MainActivity"
46-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:28:13-41
47            android:exported="true"
47-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:29:13-36
48            android:launchMode="singleTask"
48-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:30:13-44
49            android:screenOrientation="portrait" >
49-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:31:13-49
50            <intent-filter>
50-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:32:13-35:29
51                <action android:name="android.intent.action.MAIN" />
51-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:33:17-69
51-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:33:25-66
52
53                <category android:name="android.intent.category.LAUNCHER" />
53-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:34:17-77
53-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:34:27-74
54            </intent-filter>
55
56            <meta-data
56-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:37:13-39:46
57                android:name="android.app.lib_name"
57-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:38:17-52
58                android:value="memorytrap" />
58-->F:\obj_project\NewFWG-2\app\src\main\AndroidManifest.xml:39:17-43
59        </activity>
60        <activity
60-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:23:9-27:75
61            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
61-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:24:13-93
62            android:excludeFromRecents="true"
62-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:25:13-46
63            android:exported="false"
63-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:26:13-37
64            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
64-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:27:13-72
65        <!--
66            Service handling Google Sign-In user revocation. For apps that do not integrate with
67            Google Sign-In, this service will never be started.
68        -->
69        <service
69-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:33:9-37:51
70            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
70-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:34:13-89
71            android:exported="true"
71-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:35:13-36
72            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
72-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:36:13-107
73            android:visibleToInstantApps="true" />
73-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\edde958e2812b0f1730f01e578e1a08a\transformed\jetified-play-services-auth-21.4.0\AndroidManifest.xml:37:13-48
74
75        <receiver
75-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
76            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
76-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
77            android:exported="true"
77-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
78            android:permission="com.google.android.c2dm.permission.SEND" >
78-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
79            <intent-filter>
79-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:33:13-35:29
80                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
80-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:34:17-81
80-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:34:25-78
81            </intent-filter>
82
83            <meta-data
83-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
84                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
84-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
85                android:value="true" />
85-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
86        </receiver>
87        <!--
88             FirebaseMessagingService performs security checks at runtime,
89             but set to not exported to explicitly avoid allowing another app to call it.
90        -->
91        <service
91-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
92            android:name="com.google.firebase.messaging.FirebaseMessagingService"
92-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
93            android:directBootAware="true"
93-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
94            android:exported="false" >
94-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
95            <intent-filter android:priority="-500" >
95-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:13-52:29
95-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:50:28-51
96                <action android:name="com.google.firebase.MESSAGING_EVENT" />
96-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:51:17-78
96-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:51:25-75
97            </intent-filter>
98        </service>
99        <service
99-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:54:9-63:19
100            android:name="com.google.firebase.components.ComponentDiscoveryService"
100-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:55:13-84
101            android:directBootAware="true"
101-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
102            android:exported="false" >
102-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:56:13-37
103            <meta-data
103-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
104                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
104-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
106            <meta-data
106-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
107                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
107-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\6836ed984164ef4c5fb6fc7d0ab2e84d\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
109            <meta-data
109-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
110                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
110-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\98bd6072aea3a16b8e719e8ed7af172b\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
112            <meta-data
112-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\200343ed6bcba2adc79bb858f4dbc1b0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
113                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
113-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\200343ed6bcba2adc79bb858f4dbc1b0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\200343ed6bcba2adc79bb858f4dbc1b0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
115            <meta-data
115-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\200343ed6bcba2adc79bb858f4dbc1b0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
116                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
116-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\200343ed6bcba2adc79bb858f4dbc1b0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\200343ed6bcba2adc79bb858f4dbc1b0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
118            <meta-data
118-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5b6a3ee038a8bfa59e6a7396ad9203e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
119                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
119-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5b6a3ee038a8bfa59e6a7396ad9203e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e5b6a3ee038a8bfa59e6a7396ad9203e\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
121            <meta-data
121-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
122                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
122-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
124            <meta-data
124-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9283cf694da06aa8038f8d6ca6619642\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
125                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
125-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9283cf694da06aa8038f8d6ca6619642\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9283cf694da06aa8038f8d6ca6619642\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
127        </service>
128
129        <provider
129-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
130            android:name="com.google.firebase.provider.FirebaseInitProvider"
130-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
131            android:authorities="com.sy.newfwg.firebaseinitprovider"
131-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
132            android:directBootAware="true"
132-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
133            android:exported="false"
133-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
134            android:initOrder="100" />
134-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d9ffadf769a7993dcea08d43418c19a\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
135
136        <service
136-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\aa5805a217544dbccf8626a29fa87c5c\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
137            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
137-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\aa5805a217544dbccf8626a29fa87c5c\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
138            android:exported="false" >
138-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\aa5805a217544dbccf8626a29fa87c5c\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
139            <meta-data
139-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\aa5805a217544dbccf8626a29fa87c5c\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
140                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
140-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\aa5805a217544dbccf8626a29fa87c5c\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
141                android:value="cct" />
141-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\aa5805a217544dbccf8626a29fa87c5c\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
142        </service>
143        <service
143-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c939689702a1c732c24fd070322562e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
144            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
144-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c939689702a1c732c24fd070322562e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
145            android:exported="false"
145-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c939689702a1c732c24fd070322562e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
146            android:permission="android.permission.BIND_JOB_SERVICE" >
146-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c939689702a1c732c24fd070322562e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
147        </service>
148
149        <receiver
149-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c939689702a1c732c24fd070322562e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
150            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
150-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c939689702a1c732c24fd070322562e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
151            android:exported="false" />
151-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\transforms-3\c939689702a1c732c24fd070322562e3\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
152
153        <provider
153-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0faff0e2a5604be5582246baf4ff6f3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:26:9-34:20
154            android:name="androidx.startup.InitializationProvider"
154-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0faff0e2a5604be5582246baf4ff6f3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:27:13-67
155            android:authorities="com.sy.newfwg.androidx-startup"
155-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0faff0e2a5604be5582246baf4ff6f3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:28:13-68
156            android:exported="false" >
156-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0faff0e2a5604be5582246baf4ff6f3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:29:13-37
157            <meta-data
157-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0faff0e2a5604be5582246baf4ff6f3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:31:13-33:52
158                android:name="androidx.emoji2.text.EmojiCompatInitializer"
158-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0faff0e2a5604be5582246baf4ff6f3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:32:17-75
159                android:value="androidx.startup" />
159-->[androidx.emoji2:emoji2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0faff0e2a5604be5582246baf4ff6f3\transformed\jetified-emoji2-1.0.0\AndroidManifest.xml:33:17-49
160            <meta-data
160-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\50c4e24e48083eb3369774b401b9867a\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:31:13-33:52
161                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
161-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\50c4e24e48083eb3369774b401b9867a\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:32:17-78
162                android:value="androidx.startup" />
162-->[androidx.lifecycle:lifecycle-process:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\50c4e24e48083eb3369774b401b9867a\transformed\jetified-lifecycle-process-2.4.0\AndroidManifest.xml:33:17-49
163        </provider>
164
165        <receiver
165-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
166            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
166-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
167            android:enabled="true"
167-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
168            android:exported="false" >
168-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
169        </receiver>
170
171        <service
171-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
172            android:name="com.google.android.gms.measurement.AppMeasurementService"
172-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
173            android:enabled="true"
173-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
174            android:exported="false" />
174-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
175        <service
175-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
176            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
176-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
177            android:enabled="true"
177-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
178            android:exported="false"
178-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
179            android:permission="android.permission.BIND_JOB_SERVICE" />
179-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\388ac6a41fc7363f26ca2afaf0fadecc\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
180
181        <activity
181-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\c59332e3f034a6a2f9539be7fa3a570e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
182            android:name="com.google.android.gms.common.api.GoogleApiActivity"
182-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\c59332e3f034a6a2f9539be7fa3a570e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
183            android:exported="false"
183-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\c59332e3f034a6a2f9539be7fa3a570e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
184            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
184-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\c59332e3f034a6a2f9539be7fa3a570e\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
185
186        <uses-library
186-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\9f1db6a9ba318b7d2097b834bb474aae\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
187            android:name="android.ext.adservices"
187-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\9f1db6a9ba318b7d2097b834bb474aae\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
188            android:required="false" />
188-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\transforms-3\9f1db6a9ba318b7d2097b834bb474aae\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
189
190        <meta-data
190-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\0397c9f28e57c7dc6d10bfd5c0f25393\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
191            android:name="com.google.android.gms.version"
191-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\0397c9f28e57c7dc6d10bfd5c0f25393\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
192            android:value="@integer/google_play_services_version" />
192-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-3\0397c9f28e57c7dc6d10bfd5c0f25393\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
193    </application>
194
195</manifest>
