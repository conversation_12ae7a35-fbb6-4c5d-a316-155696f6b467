{"logs": [{"outputFile": "F:\\obj_project\\NewFWG-2\\app\\build\\intermediates\\incremental\\mergeDebugResources\\merged.dir\\values-es-rUS\\values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\7bd0790a3a25cc28fd6b5cec3d8d9121\\transformed\\material-1.6.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,233,319,421,549,630,695,790,860,923,1016,1088,1151,1225,1289,1345,1463,1521,1583,1639,1719,1853,1942,2023,2134,2215,2295,2385,2452,2518,2594,2676,2764,2837,2914,2984,3061,3150,3224,3318,3420,3492,3573,3677,3730,3797,3890,3979,4041,4105,4168,4279,4376,4478,4576", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,55,117,57,61,55,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "228,314,416,544,625,690,785,855,918,1011,1083,1146,1220,1284,1340,1458,1516,1578,1634,1714,1848,1937,2018,2129,2210,2290,2380,2447,2513,2589,2671,2759,2832,2909,2979,3056,3145,3219,3313,3415,3487,3568,3672,3725,3792,3885,3974,4036,4100,4163,4274,4371,4473,4571,4654"}, "to": {"startLines": "2,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,2999,3085,3187,3315,5725,5790,5885,5955,6018,6111,6183,6246,6320,6384,6440,6558,6616,6678,6734,6814,6948,7037,7118,7229,7310,7390,7480,7547,7613,7689,7771,7859,7932,8009,8079,8156,8245,8319,8413,8515,8587,8668,8772,8825,8892,8985,9074,9136,9200,9263,9374,9471,9573,9671", "endLines": "5,33,34,35,36,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104", "endColumns": "12,85,101,127,80,64,94,69,62,92,71,62,73,63,55,117,57,61,55,79,133,88,80,110,80,79,89,66,65,75,81,87,72,76,69,76,88,73,93,101,71,80,103,52,66,92,88,61,63,62,110,96,101,97,82", "endOffsets": "278,3080,3182,3310,3391,5785,5880,5950,6013,6106,6178,6241,6315,6379,6435,6553,6611,6673,6729,6809,6943,7032,7113,7224,7305,7385,7475,7542,7608,7684,7766,7854,7927,8004,8074,8151,8240,8314,8408,8510,8582,8663,8767,8820,8887,8980,9069,9131,9195,9258,9369,9466,9568,9666,9749"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c59332e3f034a6a2f9539be7fa3a570e\\transformed\\jetified-play-services-base-18.5.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "37,38,39,40,41,42,43,44,46,47,48,49,50,51,52,53,54", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3396,3504,3670,3802,3910,4071,4202,4325,4577,4748,4857,5027,5160,5337,5515,5585,5647", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "3499,3665,3797,3905,4066,4197,4320,4426,4743,4852,5022,5155,5332,5510,5580,5642,5720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0397c9f28e57c7dc6d10bfd5c0f25393\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "45", "startColumns": "4", "startOffsets": "4431", "endColumns": "145", "endOffsets": "4572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8ae4478ecf3312e5bcfba423f6800a0\\transformed\\core-1.9.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "106", "startColumns": "4", "startOffsets": "9837", "endColumns": "100", "endOffsets": "9933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b54ff934aa86605c4ea6b03bbbb5a0cb\\transformed\\appcompat-1.4.2\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,910,1001,1094,1189,1283,1382,1475,1574,1670,1761,1852,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "220,329,437,522,624,740,825,905,996,1089,1184,1278,1377,1470,1569,1665,1756,1847,1929,2036,2135,2234,2342,2450,2557,2716,2816,2899"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "283,403,512,620,705,807,923,1008,1088,1179,1272,1367,1461,1560,1653,1752,1848,1939,2030,2112,2219,2318,2417,2525,2633,2740,2899,9754", "endColumns": "119,108,107,84,101,115,84,79,90,92,94,93,98,92,98,95,90,90,81,106,98,98,107,107,106,158,99,82", "endOffsets": "398,507,615,700,802,918,1003,1083,1174,1267,1362,1456,1555,1648,1747,1843,1934,2025,2107,2214,2313,2412,2520,2628,2735,2894,2994,9832"}}]}]}