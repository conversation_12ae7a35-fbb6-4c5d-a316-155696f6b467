#pragma once

#include "memory_trap_sdk.h"

#ifndef COMPREHENSIVE_TRAP_SYSTEM_H
#define COMPREHENSIVE_TRAP_SYSTEM_H

#include <cstddef>

namespace ComprehensiveTrap {

class ComprehensiveTrapSystem {
public:
    static ComprehensiveTrapSystem& GetInstance();
    
    void Initialize();
    void DeployTraps();
    void StartDetection();
    void SetDetectionCallback(void (*callback)(int, const char*));
    void Shutdown();

private:
    ComprehensiveTrapSystem();
    ~ComprehensiveTrapSystem();
    
    // 禁止拷贝
    ComprehensiveTrapSystem(const ComprehensiveTrapSystem&) = delete;
    ComprehensiveTrapSystem& operator=(const ComprehensiveTrapSystem&) = delete;
    
    // 部署各区域陷阱
    void DeployATraps();
    void DeployCdTraps();
    void DeployCbTraps();
    void DeployCaTraps();
    void DeployChTraps();
    void DeployJhTraps();
    
    // 新增：在Java层创建陷阱
    void CreateJavaTraps();
};

// C接口
extern "C" {
    bool init_comprehensive_trap_system();
    bool deploy_comprehensive_traps();
    bool start_comprehensive_detection();
    bool shutdown_comprehensive_trap_system();
}

} // namespace ComprehensiveTrap

#endif // COMPREHENSIVE_TRAP_SYSTEM_H
